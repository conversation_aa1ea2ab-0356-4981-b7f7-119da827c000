---
description: Index and overview of all design guidelines and rules for eb-lms-app
globs:
alwaysApply: true
---

# eb-lms-app Design Guidelines Index

## Overview

This directory contains comprehensive design guidelines, component specifications, and development rules for eb-lms-app. All files have been organized into logical categories for easier management and reference.

## 📁 File Organization

### 🎨 Core Design System
- **`ui-design-system-summary.mdc`** - Complete design system overview
- **`color-guidelines.mdc`** - Color palette and usage rules
- **`typography-guidelines.mdc`** - Font specifications and hierarchy
- **`button-design-guidelines.mdc`** - Comprehensive button design system

### 🎨 Colors
- **`colors/color-palette.mdc`** - Core color palette and brand colors
- **`colors/color-usage.mdc`** - Color usage guidelines and implementation

### ✍️ Typography
- **`typography/typography-system.mdc`** - Typography system and font guidelines

### 🧩 Components
- **`components/base-components.mdc`** - Core UI components (Text, Button, Screen, etc.)
- **`components/component-patterns.mdc`** - Development patterns and best practices
- **`components/lms-components.mdc`** - LMS-specific components (CourseCard, Progress, etc.)
- **`components/input-components.mdc`** - Input and form component specifications
- **`components/display-components.mdc`** - Display components (Avatar, Card, List, Progress)
- **`components/navigation-components.mdc`** - Navigation and header components
- **`components/component-testing.mdc`** - Testing patterns and performance optimization

### 🧭 Navigation
- **`navigation/navigation-architecture.mdc`** - Navigation structure and configuration
- **`navigation-guidelines.mdc`** - Complete navigation patterns and hooks

### 🔌 API & Services
- **`api/api-architecture.mdc`** - API architecture and client configuration
- **`api/api-services.mdc`** - API service implementations and patterns

### 🗃️ State Management
- **`state/state-architecture.mdc`** - State management architecture and patterns

### 📐 Layout & Spacing
- **`layout/spacing-system.mdc`** - 8px grid system and spacing rules
- **`spacing-layout-guidelines.mdc`** - Complete layout patterns

### 🎓 LMS-Specific
- **`lms/lms-visual-patterns.mdc`** - LMS visual design patterns and layouts
- **`lms/lms-component-specs.mdc`** - Detailed LMS component specifications

### 📱 Screen-Specific
- **`login-screen-guidelines.mdc`** - Login screen specifications
- **`welcome-screen-guidelines.mdc`** - Welcome screen design patterns

## 🎯 Key Design Principles

### MANDATORY Rules (Non-Negotiable)
1. **White Background:** All screens and cards MUST use white background (#FFFFFF)
2. **Font Weight 300:** All descriptions and body text MUST use light font weight (300)
3. **Horizontal Padding:** All screens MUST have 12px padding from edges
4. **Orange Accent:** Progress elements MUST use orange (#FFBB50)
5. **Button Height:** All buttons MUST be 44px for accessibility

### Border Radius Standards
- **Main Action Buttons:** 16px (Login, Welcome, Reset Password)
- **Social Login Buttons:** 12px (Google, Facebook, Apple, Phone)
- **Cards:** 12px for modern appearance
- **Play Buttons:** 20px (circular)
- **Default Components:** 8px (fallback)

### Typography Hierarchy
- **Page Titles:** Bold (700) - "Welcome to VANTIS"
- **Section Headers:** Medium (500) - "E-Learning Platform"
- **Body Text:** Light (300) - All descriptions, button text
- **Form Labels:** Medium (500) for emphasis

### Spacing System (8px Grid)
- **xs (8px):** Internal padding, small gaps
- **sm (12px):** MANDATORY horizontal padding from screen edges
- **md (16px):** Standard component spacing
- **lg (24px):** Section spacing
- **xl (32px):** Major layout divisions

## 🔍 Quick Reference

### Button Specifications
```typescript
// Primary Button
height: 44px
borderRadius: 16px
backgroundColor: #3B82F6 (Royal Blue)
textColor: #FFFFFF (White)
fontWeight: 300 (Light)

// Secondary Button
height: 44px
borderRadius: 16px
backgroundColor: #FFFFFF (White)
borderColor: #D1D5DB (Light Gray)
textColor: #374151 (Dark)
fontWeight: 300 (Light)
```

### Color Palette
```typescript
// Primary Colors
primary: #3B82F6      // Royal Blue
background: #FFFFFF   // White (MANDATORY)
text: #132339        // Dark Blue

// Accent Colors
accent: #FFBB50      // Orange (MANDATORY for progress)
success: #10B981     // Green
warning: #F59E0B     // Amber
error: #EF4444       // Red

// Neutral Colors
neutral100: #F4F2F1  // Light Gray
neutral300: #D1D5DB  // Medium Gray
neutral500: #6B7280  // Dark Gray
```

### Screen Layout Pattern
```typescript
const $screenLayout: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  backgroundColor: "#FFFFFF", // MANDATORY white background
  paddingHorizontal: spacing.sm, // 12px MANDATORY horizontal padding
  paddingTop: spacing.lg, // 24px top spacing
})
```

## 📋 Quality Assurance Checklists

### Design Compliance
- [ ] White background used for all screens and cards
- [ ] Font weight 300 applied to all descriptions and button text
- [ ] Orange accent color (#FFBB50) used for progress elements
- [ ] 12px horizontal padding from screen edges (MANDATORY)
- [ ] 16px border radius for main action buttons
- [ ] 44px height for all buttons (accessibility)
- [ ] Proper color contrast (WCAG 2.1 AA)

### Component Development
- [ ] TypeScript interfaces properly defined
- [ ] Theming support implemented
- [ ] Accessibility props included
- [ ] Error handling implemented
- [ ] Performance optimizations applied
- [ ] Tests written and passing
- [ ] Documentation complete

### Navigation
- [ ] Type-safe navigation implemented
- [ ] Authentication flow properly configured
- [ ] Deep linking working correctly
- [ ] Screen transitions smooth and native
- [ ] Navigation state persistence enabled

## 🚀 Getting Started

### For Designers
1. Review `ui-design-system-summary.mdc` for complete design system
2. Check `color-guidelines.mdc` for color usage rules
3. Reference `button-design-guidelines.mdc` for button specifications
4. Follow `spacing-system.mdc` for layout consistency

### For Developers
1. Start with `components/base-components.mdc` for component interfaces
2. Follow `components/component-patterns.mdc` for development patterns
3. Use `navigation/navigation-architecture.mdc` for navigation setup
4. Reference `components/component-testing.mdc` for testing patterns

### For QA Engineers
1. Use quality assurance checklists in each guideline file
2. Verify design compliance with MANDATORY rules
3. Test accessibility requirements (44px touch targets, WCAG compliance)
4. Validate performance standards and error handling

## 📝 File Maintenance

### When to Update Guidelines
- New components are added to the design system
- Design patterns change or evolve
- Accessibility requirements are updated
- Performance standards are modified
- User feedback indicates UX improvements needed

### How to Update
1. Update the relevant specific guideline file
2. Update this README.mdc index if structure changes
3. Ensure all cross-references remain accurate
4. Update quality assurance checklists as needed
5. Notify team of changes and rationale

## 🔗 Related Resources

### External Documentation
- [React Navigation v7 Documentation](https://reactnavigation.org/)
- [React Native Accessibility Guide](https://reactnative.dev/docs/accessibility)
- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Material Design Guidelines](https://material.io/design)

### Internal Resources
- Design System Figma Files
- Component Storybook
- Testing Documentation
- Performance Monitoring Dashboard

This comprehensive guideline system ensures consistent, accessible, and high-quality development across the entire eb-lms-app project.
