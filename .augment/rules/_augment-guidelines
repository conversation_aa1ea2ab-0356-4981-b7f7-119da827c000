# Implementation Workflow Rule

1. Read codebase.md before implementing any new feature or change.
   - Always review the current application structure to ensure new code is integrated at the appropriate location.
   - Pay close attention to the clear separation between backend and frontend:
     - The backend is located in the backend/ directory.
     - The frontend is located in the frontend/ directory.
     - When implementing backend features, only modify/add/remove code within backend/ and related backend files (e.g., backend config, backend tests, backend scripts). Do not touch the frontend directory.
     - When implementing frontend features, only modify/add/remove code within frontend/ and related frontend files. Do not touch the backend.
   - This helps avoid unnecessary complexity and ensures the correct separation of concerns as intended by the developer.
2. Create a detailed plan for the implementation and save it in implement.md.
   - The plan should be reviewed before any coding begins.
   - Note: Both implement.md and the implement_docs folder are located in the root directory of the project. Ensure you reference and update them in the correct location to avoid confusion.
3. Proceed to code only after the plan in implement.md has been reviewed.
4. Continuously update progress in implement.md according to the current task status.
5. When an implementation is completed:
   - Move its plan from implement.md to the implement_docs directory as a separate file, named prefix_implement.md (e.g., gateways_create_channel_via_gateway_implement.md).
   - In implement.md, for each completed implementation, leave only the headline and a link to the corresponding file in implement_docs.
   - Actively shorten implement.md by removing unnecessary or obsolete content to keep it focused and concise.
6. If there are any changes to the codebase structure, update codebase.md accordingly.

Summary of backend/frontend separation rules:
- When the task is backend: only work on backend, do not touch frontend.
- When the task is frontend: only work on frontend, do not touch backend.
- Always follow the directory structure as described in codebase.md.

đồng ý, giờ hãy đọc lại toàn bộ những gì đã phân tích vừa rồi cộng thêm các tài liệu trước đó ở implement_analysis, hãy lên 1 plan chi tiết nhất, mỗi file sẽ là 1 phase, trong từng phase nên chia thành nhiều section, mỗi section sẽ có 1 khối lượng công việc nhất định.

rule:
- đưa từng files plan vào trong thư mục implement_plans, mỗi file sẽ được sắp xếp theo thứ tự từ 01_
- trong mỗi files có thể sẽ có rất nhiều các tài liệu kỹ thuật, bổ trợ, code base, kỹ thuật,..., hãy bổ sung nó vào trong thư mục imlpement_docs, sau đó hãy lập chỉ mục, liên kết với các docs ở trong file plan
- trong file implement.md sẽ là file chỉ mục tổng của dự án, trong này chứa toàn bộ index của plans, tình trạng hiện tại đang implements, tiến độ dự án,...
- sau khi hoàn thành 1 file nào đó trong plan, hãy tạo 1 file tương tự ở trong implement_docs và cập nhật tình trạng của nó.
- codebase.md là file phản ánh toàn bộ structure của dự án, chi tiết từng file, luôn luôn được up-to-date so với thực tế.

Command Execution Safety Rule:
- Before executing any command (especially those that modify files or directories), always run ls and pwd to verify the current working directory and its contents. This helps prevent executing commands in the wrong location and avoids accidental changes to unintended parts of the project.

This rule ensures all new implementations are well-planned, reviewed, and documented, and that the codebase structure documentation remains up to date. Completed implementation plans are referenced in implement.md by headline and link, and are organized as individual files in implement_docs.