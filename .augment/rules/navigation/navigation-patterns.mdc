---
description: Navigation patterns and styling guidelines for eb-lms-app based on current implementation
globs:
alwaysApply: true
---

# Navigation Patterns Guidelines

## Overview

This document defines navigation patterns and styling guidelines based on the current implementation in eb-lms-app, covering auth screens, student screens, and navigation components.

## 1. Auth Screen Navigation Patterns

### Header Pattern (Back Button + Title)
```typescript
// Pattern used in Forgot Password, Email Recovery, Phone Recovery screens
const AuthHeader = ({ title, onBack }) => {
  const { themed } = useAppTheme()
  
  return (
    <View style={themed($headerContainer)}>
      <TouchableOpacity
        onPress={onBack}
        style={themed($backButton)}
        activeOpacity={0.7}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Icon
          icon="back"
          size={20}
          color="#202244"
        />
      </TouchableOpacity>
      <Text style={themed($headerTitle)}>{title}</Text>
    </View>
  )
}

// Styles from current implementation
const $headerContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35, // x:35 from Figma
  paddingTop: 25, // y:69 - y:44 (status bar)
  paddingBottom: 20,
  width: "100%",
})

const $backButton: ThemedStyle<ViewStyle> = () => ({
  marginRight: 16,
  padding: 8,
})

const $headerTitle: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Nunito Sans",
  fontWeight: "600", // SemiBold for headers
  fontSize: 18,
  lineHeight: 24,
  color: "#202244",
})
```

### Auth Screen Background
```typescript
// Consistent background for all auth screens
const $authBackground: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#F5F9FF", // Light blue background - CURRENT IMPLEMENTATION
})
```

## 2. Student Screen Navigation Patterns

### Bottom Tab Navigation
```typescript
// Bottom tab styling from current implementation
const $tabBar: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#FFFFFF",
  borderTopWidth: 1,
  borderTopColor: "#E0E0E0",
  height: 80,
  paddingBottom: 20,
  paddingTop: 8,
})

const $tabBarActiveText: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Nunito Sans",
  fontWeight: "300", // Light weight - CONFIRMED
  fontSize: 12,
  color: "#0961F5", // Active tab color
})

const $tabBarInactiveText: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Nunito Sans",
  fontWeight: "300", // Light weight - CONFIRMED
  fontSize: 12,
  color: "#666666", // Inactive tab color
})
```

### Screen Headers (Student Screens)
```typescript
// Header pattern for student screens (Home, Profile, etc.)
const StudentHeader = ({ title, onBack, rightComponent }) => {
  const { themed } = useAppTheme()
  
  return (
    <View style={themed($studentHeaderContainer)}>
      {onBack && (
        <TouchableOpacity onPress={onBack} style={themed($backButton)}>
          <Icon icon="back" size={20} color="#202244" />
        </TouchableOpacity>
      )}
      <Text style={themed($studentHeaderTitle)}>{title}</Text>
      {rightComponent && (
        <View style={themed($rightComponent)}>
          {rightComponent}
        </View>
      )}
    </View>
  )
}

const $studentHeaderContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: 12, // 12px horizontal padding - CONFIRMED
  paddingVertical: 16,
  backgroundColor: "#FFFFFF", // White background for student screens
})

const $studentHeaderTitle: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Lexend Deca",
  fontWeight: "600", // SemiBold for headers
  fontSize: 18,
  lineHeight: 24,
  color: "#202244",
  flex: 1,
  textAlign: "center",
})
```

## 3. Navigation Transitions

### Screen Transitions
```typescript
// Standard navigation options
const navigationOptions = {
  headerShown: false, // Custom headers used throughout app
  gestureEnabled: true,
  cardStyleInterpolator: CardStyleInterpolators.forHorizontalIOS,
}

// Auth stack navigation
const AuthStackNavigator = () => (
  <Stack.Navigator
    screenOptions={navigationOptions}
    initialRouteName="Login"
  >
    <Stack.Screen name="Login" component={LoginScreen} />
    <Stack.Screen name="Signup" component={SignupScreen} />
    <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    {/* ... other auth screens */}
  </Stack.Navigator>
)
```

## 4. Navigation State Management

### Navigation Handlers
```typescript
// Standard navigation patterns
const useNavigationHandlers = (navigation) => {
  const handleBackPress = () => {
    if (navigation.canGoBack()) {
      navigation.goBack()
    }
  }

  const handleNavigateToScreen = (screenName, params = {}) => {
    navigation.navigate(screenName, params)
  }

  return {
    handleBackPress,
    handleNavigateToScreen,
  }
}
```

## 5. Safe Area Handling

### Safe Area Patterns
```typescript
// Screen with safe area handling
const ScreenWithSafeArea = ({ children, backgroundColor = "#FFFFFF" }) => (
  <Screen
    preset="scroll"
    safeAreaEdges={["top"]}
    backgroundColor={backgroundColor}
  >
    {children}
  </Screen>
)

// Auth screens safe area
const AuthScreenSafeArea = ({ children }) => (
  <Screen
    preset="scroll"
    safeAreaEdges={["top", "bottom"]}
    backgroundColor="#F5F9FF" // Auth background color
  >
    {children}
  </Screen>
)
```

## 6. Implementation Guidelines

### Navigation Best Practices
- ✅ Use custom headers instead of default navigation headers
- ✅ Maintain consistent back button styling across screens
- ✅ Apply proper safe area handling for all screens
- ✅ Use light font weight (300) for navigation text
- ✅ Follow established color patterns per screen type
- ✅ Implement proper gesture handling for navigation

### Screen Background Patterns
- ✅ **Auth Screens:** Light blue (#F5F9FF) background
- ✅ **Student Screens:** White (#FFFFFF) background
- ✅ **Profile Screens:** Light blue (#F5F9FF) background
- ✅ **Consistency:** Maintain current patterns per screen type

### Typography in Navigation
- ✅ **Header Titles:** Font weight 600 (SemiBold), 18px
- ✅ **Tab Labels:** Font weight 300 (Light), 12px
- ✅ **Navigation Text:** Font weight 300 (Light)
- ✅ **Font Family:** Nunito Sans throughout

This navigation pattern guide ensures consistent navigation experience across all screens in eb-lms-app.
