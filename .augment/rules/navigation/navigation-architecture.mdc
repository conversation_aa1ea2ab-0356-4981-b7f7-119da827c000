---
description: Navigation architecture and structure for eb-lms-app
globs:
alwaysApply: true
---

# Navigation Architecture

## Overview

This document defines the navigation architecture, structure, and configuration patterns for eb-lms-app using React Navigation v7.

## 1. Navigation Structure

### Hierarchical Navigation Tree
```
AppNavigator (Root)
├── AuthStack (Conditional - Unauthenticated)
│   ├── LoginScreen
│   ├── ForgotPasswordScreen
│   └── PhoneLoginScreen
└── MainStack (Conditional - Authenticated)
    ├── WelcomeScreen
    └── DemoNavigator (Nested)
        ├── DemoShowroomScreen
        ├── DemoCommunityScreen
        ├── DemoPodcastListScreen
        └── DemoDebugScreen
```

### Navigator Types
- **Native Stack Navigator:** Primary navigation with native transitions
- **Bottom Tab Navigator:** Tab-based navigation for main sections
- **Drawer Navigator:** Side menu navigation (future implementation)
- **Modal Stack:** Overlay screens and modals

## 2. Type-Safe Navigation

### Parameter Lists Definition
```typescript
// Root navigation types
export type AppStackParamList = {
  Welcome: undefined
  Login: undefined
  ForgotPassword: undefined
  PhoneLogin: undefined
  Demo: NavigatorScreenParams<DemoStackParamList>
  Main: NavigatorScreenParams<MainTabParamList>
}

// Demo stack types
export type DemoStackParamList = {
  DemoShowroom: { queryIndex?: string; itemIndex?: string }
  DemoDebug: undefined
  DemoPodcastList: undefined
  DemoCommunity: undefined
}

// Main tab types
export type MainTabParamList = {
  Home: undefined
  Courses: undefined
  Progress: undefined
  Profile: undefined
}

// Course-specific navigation
export type CourseStackParamList = {
  CourseDetails: { courseId: string }
  LessonView: { courseId: string; lessonId: string }
  LessonComplete: { courseId: string; lessonId: string }
}
```

### Navigation Hook
```typescript
import { useNavigation } from "@react-navigation/native"
import type { NativeStackNavigationProp } from "@react-navigation/native-stack"

export const useAppNavigation = () => {
  return useNavigation<NativeStackNavigationProp<AppStackParamList>>()
}
```

## 3. AppNavigator Configuration

### Root Navigator Structure
```typescript
// app/navigators/AppNavigator.tsx
import { NavigationContainer } from "@react-navigation/native"
import { createNativeStackNavigator } from "@react-navigation/native-stack"

const Stack = createNativeStackNavigator<AppStackParamList>()

export const AppNavigator = () => {
  const { authenticationStore } = useStores()
  const { themed } = useAppTheme()

  return (
    <NavigationContainer
      linking={linking}
      theme={navigationTheme}
    >
      <Stack.Navigator
        screenOptions={{
          headerShown: false,
          gestureEnabled: true,
          animation: "slide_from_right",
        }}
      >
        {authenticationStore.isAuthenticated ? (
          <>
            <Stack.Screen name="Welcome" component={WelcomeScreen} />
            <Stack.Screen name="Main" component={MainTabNavigator} />
            <Stack.Screen name="Demo" component={DemoNavigator} />
          </>
        ) : (
          <>
            <Stack.Screen name="Login" component={LoginScreen} />
            <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
            <Stack.Screen name="PhoneLogin" component={PhoneLoginScreen} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  )
}
```

### Authentication Flow
```typescript
// Conditional navigation based on auth state
export const AuthenticatedNavigator = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Welcome" component={WelcomeScreen} />
    <Stack.Screen name="Main" component={MainTabNavigator} />
  </Stack.Navigator>
)

export const UnauthenticatedNavigator = () => (
  <Stack.Navigator screenOptions={{ headerShown: false }}>
    <Stack.Screen name="Login" component={LoginScreen} />
    <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
    <Stack.Screen name="PhoneLogin" component={PhoneLoginScreen} />
  </Stack.Navigator>
)
```

## 4. Screen Configuration

### Screen Wrapper Pattern
```typescript
import { Screen } from "@/components"

export const MyScreen = () => {
  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      contentContainerStyle={themed($container)}
      backgroundColor={colors.background} // Always white
    >
      {/* Screen content */}
    </Screen>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
  paddingTop: spacing.lg,
})
```

### Screen Props Interface
```typescript
interface ScreenProps {
  preset?: "fixed" | "scroll" | "auto"
  backgroundColor?: string
  statusBarStyle?: "light" | "dark"
  safeAreaEdges?: Edge[]
  contentContainerStyle?: StyleProp<ViewStyle>
  keyboardOffset?: number
  KeyboardAvoidingViewProps?: KeyboardAvoidingViewProps
}
```

## 5. Deep Linking

### URL Configuration
```typescript
// app.config.ts - Linking configuration
const linking = {
  prefixes: [
    Linking.createURL("/"),
    "eblms://",
    "https://eblms.app",
  ],
  config: {
    screens: {
      Login: {
        path: "",
      },
      Welcome: "welcome",
      ForgotPassword: "forgot-password",
      PhoneLogin: "phone-login",
      Demo: {
        screens: {
          DemoShowroom: {
            path: "showroom/:queryIndex?/:itemIndex?",
          },
          DemoDebug: "debug",
          DemoPodcastList: "podcast",
          DemoCommunity: "community",
        },
      },
      Main: {
        screens: {
          Home: "home",
          Courses: "courses",
          Progress: "progress",
          Profile: "profile",
        },
      },
    },
  },
}
```

### URL Examples
```typescript
// Deep link examples:
// eblms://welcome
// eblms://showroom/1/2
// eblms://courses
// eblms://forgot-password
// https://eblms.app/debug
```

## 6. Navigation Theme

### Theme Configuration
```typescript
import { DefaultTheme } from "@react-navigation/native"

export const navigationTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: "#3B82F6",      // Royal blue
    background: "#FFFFFF",   // White - MANDATORY
    card: "#FFFFFF",         // White cards
    text: "#132339",         // Dark blue text
    border: "#F4F2F1",       // Light gray borders
    notification: "#FFBB50", // Orange notifications
  },
}
```

### Header Styling
```typescript
const screenOptions: NativeStackNavigationOptions = {
  headerShown: true,
  headerStyle: {
    backgroundColor: colors.background, // White
  },
  headerTintColor: colors.text,
  headerTitleStyle: {
    fontFamily: typography.primary.medium,
    fontSize: 18,
    fontWeight: "500",
  },
  headerBackTitleVisible: false,
  headerShadowVisible: true,
}
```

## 7. Navigation State Management

### State Persistence
```typescript
import AsyncStorage from "@react-native-async-storage/async-storage"

export const NAVIGATION_PERSISTENCE_KEY = "NAVIGATION_STATE"

export const AppNavigator = () => {
  const {
    initialNavigationState,
    onNavigationStateChange,
    isRestored: isNavigationStateRestored,
  } = useNavigationPersistence(AsyncStorage, NAVIGATION_PERSISTENCE_KEY)

  if (!isNavigationStateRestored) {
    return <LoadingScreen />
  }

  return (
    <NavigationContainer
      initialState={initialNavigationState}
      onStateChange={onNavigationStateChange}
      linking={linking}
      theme={navigationTheme}
    >
      {/* Navigation structure */}
    </NavigationContainer>
  )
}
```

### Navigation Utilities
```typescript
// app/navigators/navigationUtilities.ts
import { createNavigationContainerRef } from "@react-navigation/native"

export const navigationRef = createNavigationContainerRef<AppStackParamList>()

export const navigationUtilities = {
  navigate: (name: keyof AppStackParamList, params?: any) => {
    if (navigationRef.isReady()) {
      navigationRef.navigate(name, params)
    }
  },

  goBack: () => {
    if (navigationRef.isReady() && navigationRef.canGoBack()) {
      navigationRef.goBack()
    }
  },

  reset: (routeName: keyof AppStackParamList) => {
    if (navigationRef.isReady()) {
      navigationRef.reset({
        index: 0,
        routes: [{ name: routeName }],
      })
    }
  },

  getCurrentRoute: () => {
    if (navigationRef.isReady()) {
      return navigationRef.getCurrentRoute()
    }
    return null
  },
}
```

## 8. Quality Assurance

### Navigation Architecture Checklist
- [ ] Type-safe navigation implemented
- [ ] Authentication flow properly configured
- [ ] Deep linking working correctly
- [ ] Screen transitions smooth and native
- [ ] Navigation state persistence enabled
- [ ] Error handling for navigation failures
- [ ] Accessibility support for navigation
- [ ] Performance optimization applied

### Design Compliance
- [ ] White backgrounds for all screens (MANDATORY)
- [ ] Consistent header styling
- [ ] Proper safe area handling
- [ ] 12px horizontal padding (MANDATORY)
- [ ] Smooth transitions and animations
- [ ] Proper loading states
- [ ] Error boundary protection

This navigation architecture ensures a robust, type-safe, and user-friendly navigation experience throughout eb-lms-app.
