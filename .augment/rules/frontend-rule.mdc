---
description: 
globs: 
alwaysApply: true
---
A consolidated playing front‑end apps inside **We with Supabase back‑ends—just clear, action
## 1. Execution nt

*   Runs entirely; no native binarie.
*   Available ru  `node` (latest LTS)
 ` – **standard‑library on`pip`). Mention this whenest Python.
*   Git, `g++`, `make` and similar CLI tools are absent.
*   Shell commands allowed (see list in prompt) – prefer Node scripts when automation is needed.

## 2. Project Scaffolding & Tooling

*   **Always start with Vite** (`npm create vite@latest -- --yes`).
*   Use the framework most suited to UI tasks (React, Svelte, Solid, Vue). When not specified, default to **React + TypeScript**.
*   For styling, lean on **Tailwind CSS**.
*   Development lifecycle

    ```bash
    npm install                # after editing package.json
    npm run dev                # start local dev server
    ```

    Never auto‑open the browser—tell the user the URL instead.

## 3. Python Usage

*   Import from the standard library only (`json`, `csv`, `itertools` …).
*   State limitations up front if a user asks for third‑party modules or ML libraries.

## 4. Database Guidelines (Supabase‑First)

*   Ask the user to connect Supabase (“*Please run `supabase link` in the terminal or paste your project URL / anon key*”).
*   Create a `.env` to store `SUPABASE_URL` and `SUPABASE_ANON_KEY` if it doesn't exist.
*   Use `@supabase/supabase-js` and export a singleton client from `src/lib/supabase.ts`.

### 4.1 Writing Migrations

1.  **One SQL file per logical change** inside `supabase/migrations/`.
2.  Begin with a Markdown comment header:

    ```sql
    /*
      # Add posts table

      1. New Tables
        - posts
          - id uuid pk
          - title text not null
          - …
      2. Security
        - RLS enabled
        - Policies for owner read/write
    */
    ```
3.  Full SQL only—no diffs. Guard with `IF NOT EXISTS / IF EXISTS` where appropriate.
4.  Enable RLS and add CRUD policies for every table.
5.  Apply with `supabase db push` or the SQL editor.
6.  **Prohibit destructive SQL** (`DROP`, `DELETE FROM` without `WHERE`, altering column types irreversibly, explicit `BEGIN/COMMIT/ROLLBACK`).

### 4.2 Authentication

*   Stick to email + password (`supabase.auth.signUp` / `signInWithPassword`).
*   No magic links or social providers unless explicitly requested.

## 5. Coding Standards

*   **2‑space indentation** everywhere.
*   Split logic into small, focused modules.
*   Use descriptive filenames; no numeric prefixes.
*   Provide *full file contents*; never elide with “// …”.

## 6. Response Format (to the user)

1.  **Concise plan first** – 2‑4 bullet points outlining your approach.
2.  Then deliver code & commands in `markdown` fenced blocks:

    ```tsx [src/App.tsx]
    // complete file here
    ```

    ```bash
    npm install
    ```
3.  Lean prose; avoid verbosity unless clarification is asked.
4.  Allowed HTML tags: `<a>`, `<b>`, `<code>`, `<em>`, headings, lists, etc.

## 7. UI & UX Guidelines (detailed)

> **Goal:** Ship interfaces that feel polished, predictable, and performant—without bloating the bundle or breaking design consistency.

---

### 7.1 Design Principles

* **Clarity first** – every screen answers one primary question *instantly* (“What is this? What can I do now?”).  
* **Minimal cognitive load** – remove ornamental UI unless it reinforces the task.  
* **Progressive disclosure** – surface basics first, reveal advanced options with accordions, dialogs or nested routes.  
* **User-controlled** – any disruptive action (delete, irreversible state change) **must** provide an *undo* or confirmation modal.  
* **Mobile-first** – design at `sm:` breakpoint; upscale gracefully to `md:`/`lg:`.

---

### 7.2 Layout & Spacing

| Token | Tailwind class | Usage                                       |
|-------|----------------|---------------------------------------------|
| `spacing-sm` | `p-2` / `gap-2` | Tight lists, form controls |
| `spacing-md` | `p-4` / `gap-4` | Default cards, modals         |
| `spacing-lg` | `p-6` / `gap-6` | Hero sections, dashboards      |

* Stick to **8 px grid** multiples (`2`, `4`, `6`, `8`).  
* Use **CSS Grid** for macro-layout (sidebar/content, cards), **Flexbox** for micro-layout (toolbars, menus).  
* **Never** rely on absolute positioning for primary flow—reserve it for toasts or drag-and-drop ghosts.

---

### 7.3 Typography

| Style        | Tailwind               | Typical role                 |
|--------------|------------------------|------------------------------|
| Display 1    | `text-4xl lg:text-5xl` | Empty state headlines        |
| Heading 1    | `text-2xl font-semibold` | Page titles                  |
| Heading 2    | `text-xl font-medium`  | Section headers              |
| Body         | `text-base leading-6` | Standard content, forms      |
| Caption/Meta | `text-sm text-slate-500` | Hints, timestamps, labels    |

* **System stack** fonts by default (`font-sans`); bring in custom fonts only if they substantially improve brand identity.  
* Keep line-length ≤ 70 ch for readability.

---

### 7.4 Color & Theme

* **Semantic palette** via Tailwind’s `slate`, `sky`, `rose`, `emerald`, etc. Define extended shades in `tailwind.config.js` if brand colors are required.  
* **Light/dark switch** – wrap the root in `<html class="dark">` toggle; define `dark:` variants **everywhere** (`bg-slate-900 dark:bg-slate-100`).  
* **Contrast ratio ≥ 4.5:1** for body text; test with the Tailwind-powered [a11y-light](https://tailwindcss.com/docs/customizing-colors#using-css-variables) plugin if uncertain.  
* **State colors** (success, warning, error) map to **`emerald-500` / `amber-500` / `rose-500`** by default—override only with a documented token.

---

### 7.5 Components & Patterns

* **Re-use over rebuild** – import from **shadcn/ui** when a component matches requirements *exactly*. Strip unused props; tree-shake aggressively.  
* **Atomic hierarchy**

  | Layer | Folder        | Examples                         |
  |-------|---------------|----------------------------------|
  | Atoms | `src/components/ui/` | `Button.tsx`, `Input.tsx`       |
  | Molecules | `src/components/composed/` | `UserCard.tsx`, `NavItem.tsx` |
  | Organisms | `src/features/*/` | `PostEditor.tsx`, `ProfileForm.tsx` |

* Break complex flows into **wizard steps** (`Stepper`, `ProgressBar`). Persist progress to localStorage.  
* Provide **skeleton loaders** (`animate-pulse bg-slate-200`) for async content.

---

### 7.6 Motion & Interaction

| Tool             | Use-case                       | Guideline                              |
|------------------|--------------------------------|----------------------------------------|
| `framer-motion`  | Page transitions, micro-interactions | Max 400 ms; use `easeInOut` curves     |
| `@headlessui`    | Accessible dialogs, menus     | Compose with Tailwind for styling      |
| CSS `transition` | Hover/active states           | 150 ms, `ease-out`, property subset    |

* **Performance** – limit simultaneous animating props to `opacity`, `transform`.  
* Prefer **reduced motion** respect: wrap motion components in `prefers-reduced-motion` guard.

---

### 7.7 Accessibility (a11y)

* **Keyboard navigation** – every interactive component must be reachable via `Tab` and operable with `Space/Enter`.  
* **Focus ring** – show custom outline (`focus-visible:outline-2 focus-visible:outline-sky-500`).  
* Use **ARIA roles** sparingly—prefer native elements (`<button>`, `<dialog>`, `<details>`).  
* Provide **alt text** for images; hide decorative SVGs with `aria-hidden="true"`.  
* **Live regions** (`role="status"`) announce async success/error to assistive tech.

---

### 7.8 Performance & Progressive Enhancement

* **First Contentful Paint ≤ 1.8 s** on mid-range mobile—lazy-load off-screen images (`loading="lazy"`).  
* **Code-split** routes with `React.lazy` / `Suspense`. Show `<Spinner />` fallback.  
* Prefer **client hints** (`Accept-CH: DPR, Viewport-Width`) to deliver appropriately sized images.  
* **Service Worker** optional; if enabled, cache static assets with a max-age of **1 week**.

---

### 7.9 Documentation & Handoff

* Keep **Storybook** stories next to components (`Component.stories.tsx`); minimal controls, default scenario per variant.  
* Export **Figma tokens** (`spacing-md`, color palette) to Tailwind config to ensure parity.  
* For every new feature, append a **“Usage”** section to the README including:

  ```md
  ### <ComponentName>

  | Prop         | Type    | Default | Description             |
  |--------------|---------|---------|-------------------------|
  | `variant`    | string  | `'solid'` | `'solid' | 'outline'`   |
  | `disabled`   | boolean | `false` | Disables interaction    |
````

* Provide **accessibility acceptance criteria** in pull-request description.

---

> **Remember:** UI exists to serve user goals, not to showcase clever code. Prioritize *speed*, *clarity*, and *delight*—in that order.

## 8. Example: Creating a Users Table

```bash
# 1. Generate migration file
mkdir -p supabase/migrations
cat <<'SQL' > supabase/migrations/create_users.sql
/*
  # Create users table

  1. New Tables
    - users (id, email, created_at)
  2. Security
    - RLS enabled, owner‑only access
*/
CREATE TABLE IF NOT EXISTS users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  email text UNIQUE NOT NULL,
  created_at timestamptz DEFAULT now()
);

ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users: self‑select" ON users
  FOR SELECT USING (auth.uid() = id);
SQL

# 2. Push to database (after connecting)
supabase db push
```

## 9. Example Projects

### 9.1 JavaScript Factorial Function (CLI)

**Plan**

1.  Create `index.js` with a recursive factorial.
2.  Run with `node index.js 5`.

```js [index.js]
#!/usr/bin/env node
function factorial(n) {
  if (n < 0) throw new Error('n must be non‑negative');
  return n <= 1 ? 1 : n * factorial(n - 1);
}

const arg = process.argv[2] ?? '0';
console.log(factorial(Number(arg)));
```

```bash
node index.js 5 # → 120
```

---

### 9.2 Snake Game (Vanilla JS + Vite)

**Plan**

1.  Scaffold with Vite.
2.  Implement `index.html`, `src/main.js`, and canvas‑based game logic.
3.  Style with Tailwind.

```bash
npm create vite@latest snake-game -- --yes
cd snake-game
npm install
npm run dev
```

```html [snake-game/index.html]
<!doctype html>
<html class="h-full">
  <head>
    <meta charset="utf-8" />
    <title>Snake</title>
    <script type="module" src="/src/main.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="h-full flex items-center justify-center bg-gray-800">
    <canvas id="game" width="400" height="400" class="border border-white"></canvas>
  </body>
</html>
```

```js [snake-game/src/main.js]
import './style.css';
const canvas = document.getElementById('game');
const ctx = canvas.getContext('2d');
// basic snake implementation … (full code here)
```

---

### 9.3 Bouncing Ball with React + TypeScript

**Plan**

1.  Scaffold React project with Vite.
2.  Install `framer-motion` for physics.
3.  Implement `Ball` component with y‑axis spring animation.

```bash
npm create vite@latest bouncing-ball -- --template react-ts --yes
cd bouncing-ball
npm install framer-motion tailwindcss
npx tailwindcss init --yes
npm run dev
```

```tsx [bouncing-ball/src/App.tsx]
import { motion, useAnimationControls } from 'framer-motion';
import { useEffect } from 'react';
import './index.css';

export default function App() {
  const controls = useAnimationControls();
  useEffect(() => {
    controls.start({ y: [0, 300, 0], transition: { repeat: Infinity, ease: 'easeIn', duration: 2 } });
  }, []);
  return (
    <div className="h-screen flex items-center justify-center bg-slate-900">
      <motion.div
        className="w-16 h-16 rounded-full bg-rose-400 shadow-lg"
        animate={controls}
      />
    </div>
  );
}
```
## 10. Starter Templates

<details>
<summary>Available templates (click to expand)</summary>

<template>
  <name>blank</name>
  <description>Empty starter for simple scripts and trivial tasks that don't require a full template setup</description>
  <tags>basic, script</tags>
</template>

<template>
  <name>expo-app</name>
  <description>Expo starter template for building cross-platform mobile apps</description>
  <tags>mobile, expo, mobile-app, android, iphone</tags>
  <githubRepo>xKevIsDev/bolt-expo-template</githubRepo>
</template>

<template>
  <name>bolt-astro-basic</name>
  <description>Lightweight Astro starter template for building fast static websites</description>
  <tags>astro, blog, performance</tags>
  <githubRepo>xKevIsDev/bolt-astro-basic-template</githubRepo>
</template>

<template>
  <name>nextjs-shadcn</name>
  <description>Next.js starter fullstack template integrated with shadcn/ui components and styling system</description>
  <tags>nextjs, react, typescript, shadcn, tailwind</tags>
  <githubRepo>xKevIsDev/bolt-nextjs-shadcn-template</githubRepo>
</template>

<template>
  <name>vite-shadcn</name>
  <description>Vite starter fullstack template integrated with shadcn/ui components and styling system</description>
  <tags>vite, react, typescript, shadcn, tailwind</tags>
  <githubRepo>xKevIsDev/vite-shadcn</githubRepo>
</template>

<template>
  <name>bolt-qwik-ts</name>
  <description>Qwik framework starter with TypeScript for building resumable applications</description>
  <tags>qwik, typescript, performance, resumable</tags>
  <githubRepo>xKevIsDev/bolt-qwik-ts-template</githubRepo>
</template>

<template>
  <name>bolt-remix-ts</name>
  <description>Remix framework starter with TypeScript for full-stack web applications</description>
  <tags>remix, typescript, fullstack, react</tags>
  <githubRepo>xKevIsDev/bolt-remix-ts-template</githubRepo>
</template>

<template>
  <name>bolt-slidev</name>
  <description>Slidev starter template for creating developer-friendly presentations using Markdown</description>
  <tags>slidev, presentation, markdown</tags>
  <githubRepo>xKevIsDev/bolt-slidev-template</githubRepo>
</template>

<template>
  <name>bolt-sveltekit</name>
  <description>SvelteKit starter template for building fast, efficient web applications</description>
  <tags>svelte, sveltekit, typescript</tags>
  <githubRepo>bolt-sveltekit-template</githubRepo>
</template>

<template>
  <name>vanilla-vite</name>
  <description>Minimal Vite starter template for vanilla JavaScript projects</description>
  <tags>vite, vanilla-js, minimal</tags>
  <githubRepo>xKevIsDev/vanilla-vite-template</githubRepo>
</template>

<template>
  <name>bolt-vite-react</name>
  <description>React starter template powered by Vite for fast development experience</description>
  <tags>react, vite, frontend, website, app</tags>
  <githubRepo>xKevIsDev/bolt-vite-react-ts-template</githubRepo>
</template>

<template>
  <name>bolt-vite-ts</name>
  <description>Vite starter template with TypeScript configuration for type-safe development</description>
  <tags>vite, typescript, minimal</tags>
  <githubRepo>xKevIsDev/bolt-vite-ts-template</githubRepo>
</template>

<template>
  <name>bolt-vue</name>
  <description>Vue.js starter template with modern tooling and best practices</description>
  <tags>vue, typescript, frontend</tags>
  <githubRepo>xKevIsDev/bolt-vue-template</githubRepo>
</template>

<template>
  <name>bolt-angular</name>
  <description>A modern Angular starter template with TypeScript support and best practices configuration</description>
  <tags>angular, typescript, frontend, spa</tags>
  <githubRepo>xKevIsDev/bolt-angular-template</githubRepo>
</template>

</details>