---
description: Input field design guidelines for light and bright theme in eb-lms-app
globs:
alwaysApply: true
---

# Input Field Guidelines (Light & Bright Theme)

## 1. Design Philosophy

The eb-lms-app uses a **light and bright** design approach for all input fields to ensure:
- **Better readability** with high contrast text
- **Professional appearance** with clean, modern styling
- **Accessibility compliance** with proper color contrast ratios
- **Brand consistency** with orange accent colors for focus states

## 2. Color Specifications

### Input Field Color Palette
```typescript
const inputFieldColors = {
  // Background colors (MANDATORY light theme)
  background: "#FFFFFF",         // Pure white background - ALWAYS
  backgroundLight: "#FAFBFC",    // Very light gray alternative
  backgroundDisabled: "#F8F9FA", // Very light gray for disabled state
  
  // Border colors (light and subtle)
  border: "#F1F3F4",            // Very light gray border (default)
  borderHover: "#E8EAED",       // Slightly darker on hover
  borderFocus: "#FFBB50",       // Orange border on focus (brand accent)
  borderError: "#FECACA",       // Light red border for errors
  borderSuccess: "#D1FAE5",     // Light green border for success
  
  // Text colors (high contrast)
  text: "#202124",              // Dark text for input content
  placeholder: "#505050",       // Placeholder color from auth screens (current implementation)
  label: "#5F6368",             // Medium gray for labels
  helper: "#5F6368",            // Medium gray for helper text
  error: "#DC2626",             // Red for error messages
}
```

### Color Usage Rules
- **MANDATORY:** Always use white (#FFFFFF) background for input fields
- **MANDATORY:** Use orange (#FFBB50) for focus states to match brand
- **MANDATORY:** Maintain light, bright appearance for better UX
- **FORBIDDEN:** Dark or heavy background colors for input fields
- **FORBIDDEN:** Low contrast color combinations

## 3. Input Field States

### Default State
```typescript
const defaultInputStyle = {
  backgroundColor: "#FFFFFF",    // Pure white background
  borderColor: "#F1F3F4",       // Very light gray border
  borderWidth: 1,
  borderRadius: 8,              // Modern rounded corners
  color: "#202124",             // Dark text for contrast
}
```

### Hover State
```typescript
const hoverInputStyle = {
  backgroundColor: "#FFFFFF",    // Keep white background
  borderColor: "#E8EAED",       // Slightly darker border
  borderWidth: 1,
  borderRadius: 8,
}
```

### Focus State (Brand Consistency)
```typescript
const focusInputStyle = {
  backgroundColor: "#FFFFFF",    // Keep white background
  borderColor: "#FFBB50",       // Orange border - MANDATORY brand color
  borderWidth: 1,
  borderRadius: 8,
  // Optional: subtle shadow for depth
  shadowColor: "#FFBB50",
  shadowOffset: { width: 0, height: 0 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
}
```

### Error State
```typescript
const errorInputStyle = {
  backgroundColor: "#FFFFFF",    // Keep white background
  borderColor: "#FECACA",       // Light red border (not harsh)
  borderWidth: 1,
  borderRadius: 8,
}
```

### Success State
```typescript
const successInputStyle = {
  backgroundColor: "#FFFFFF",    // Keep white background
  borderColor: "#D1FAE5",       // Light green border
  borderWidth: 1,
  borderRadius: 8,
}
```

### Disabled State
```typescript
const disabledInputStyle = {
  backgroundColor: "#F8F9FA",    // Very light gray background
  borderColor: "#F1F3F4",       // Light gray border
  borderWidth: 1,
  borderRadius: 8,
  color: "#9AA0A6",             // Light gray text
}
```

## 4. Typography in Input Fields

### Input Text
```typescript
const inputTextStyle = {
  fontFamily: "lexendDecaRegular", // Primary font
  fontSize: 16,                    // Standard readable size
  fontWeight: "400",               // Normal weight
  color: "#202124",                // Dark text for contrast
  lineHeight: 24,                  // Comfortable line height
}
```

### Placeholder Text
```typescript
const placeholderTextStyle = {
  fontFamily: "lexendDecaRegular",
  fontSize: 16,
  fontWeight: "300",               // Light weight for placeholders
  color: "#505050",                // Placeholder color from auth screens (current implementation)
}
```

### Label Text
```typescript
const labelTextStyle = {
  fontFamily: "lexendDecaRegular",
  fontSize: 14,                    // Slightly smaller than input
  fontWeight: "400",               // Normal weight
  color: "#5F6368",                // Medium gray
  marginBottom: 8,                 // 8px spacing to input
}
```

### Helper Text
```typescript
const helperTextStyle = {
  fontFamily: "lexendDecaRegular",
  fontSize: 14,
  fontWeight: "300",               // Light weight for helper text
  color: "#5F6368",                // Medium gray
  marginTop: 8,                    // 8px spacing from input
}
```

### Error Text
```typescript
const errorTextStyle = {
  fontFamily: "lexendDecaRegular",
  fontSize: 14,
  fontWeight: "400",               // Normal weight for visibility
  color: "#DC2626",                // Red for errors
  marginTop: 8,
}
```

## 5. Spacing and Layout

### Input Field Dimensions
```typescript
const inputDimensions = {
  height: 48,                      // Standard height for touch targets
  minHeight: 48,                   // Minimum for accessibility
  paddingHorizontal: 16,           // 16px horizontal padding
  paddingVertical: 12,             // 12px vertical padding
  borderRadius: 8,                 // Modern rounded corners
  borderWidth: 1,                  // Subtle border
}
```

### Multiline Input
```typescript
const multilineInputDimensions = {
  minHeight: 112,                  // Minimum height for multiline
  maxHeight: 200,                  // Maximum before scrolling
  paddingHorizontal: 16,
  paddingVertical: 12,
  borderRadius: 8,
  borderWidth: 1,
}
```

### Spacing Between Elements
```typescript
const inputSpacing = {
  labelToInput: 8,                 // 8px from label to input
  inputToHelper: 8,                // 8px from input to helper text
  inputToInput: 16,                // 16px between input fields
  sectionSpacing: 24,              // 24px between form sections
}
```

## 6. Accessibility Guidelines

### Color Contrast Requirements
- **Input text on white background:** 15.8:1 (Excellent)
- **Placeholder text on white background:** 4.6:1 (Good)
- **Label text on white background:** 7.2:1 (Excellent)
- **Error text on white background:** 8.9:1 (Excellent)
- **Focus border visibility:** High contrast orange ensures visibility

### Touch Target Guidelines
- **Minimum touch target:** 44px × 44px
- **Recommended input height:** 48px
- **Comfortable padding:** 16px horizontal, 12px vertical

### Screen Reader Support
```typescript
// Accessible input implementation
<TextInput
  accessibilityLabel="Email address"
  accessibilityHint="Enter your email address"
  accessibilityRole="text"
  placeholder="Enter email"
  // ... other props
/>
```

## 7. Auth Screen Input Patterns (Current Implementation)

### Custom Input Field with Icon (Login/Signup Screens)
```typescript
// Pattern used in current auth screens
const AuthInputField = ({ icon, placeholder, value, onChangeText, secureTextEntry }) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($textField)}>
      <View style={themed($inputContainer)}>
        <Icon
          icon={icon}
          size={19}
          color="#545454"
          style={themed($inputIcon)}
        />
        <TextInput
          value={value}
          onChangeText={onChangeText}
          style={themed($input)}
          placeholder={placeholder}
          placeholderTextColor="#505050" // Exact color from current implementation
          secureTextEntry={secureTextEntry}
          autoCapitalize="none"
          autoCorrect={false}
        />
      </View>
    </View>
  )
}

// Styles from current auth screens
const $textField: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  marginBottom: 16,
})

const $inputContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF",
  borderRadius: 12,
  paddingHorizontal: 16,
  paddingVertical: 12,
  borderWidth: 1,
  borderColor: "#E0E0E0",
})

const $inputIcon: ThemedStyle<ViewStyle> = () => ({
  marginRight: 12,
})

const $input: ThemedStyle<TextStyle> = () => ({
  flex: 1,
  fontFamily: "Nunito Sans",
  fontWeight: "300", // Light weight - CONFIRMED from current implementation
  fontSize: 16,
  lineHeight: 22,
  color: "#202244",
})
```

### Form Container Spacing (Auth Screens)
```typescript
// Exact spacing from current auth screens
const $formContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Exact padding from Figma and current implementation
  alignItems: "center",
})
```

## 8. Implementation Examples

### Basic Input Field
```typescript
const BasicInputField = ({ label, placeholder, value, onChangeText, error }) => {
  const [isFocused, setIsFocused] = useState(false)
  const { themed } = useAppTheme()
  
  return (
    <View style={themed($container)}>
      {label && (
        <Text style={themed($label)}>{label}</Text>
      )}
      <TextInput
        style={[
          themed($input),
          isFocused && themed($inputFocused),
          error && themed($inputError),
        ]}
        placeholder={placeholder}
        placeholderTextColor="#9AA0A6"
        value={value}
        onChangeText={onChangeText}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
      />
      {error && (
        <Text style={themed($errorText)}>{error}</Text>
      )}
    </View>
  )
}

// Styles following light & bright guidelines
const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md, // 16px between fields
})

const $label: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  fontSize: 14,
  fontWeight: "400",
  color: colors.inputLabel, // #5F6368
  marginBottom: spacing.xs, // 8px to input
})

const $input: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.inputBackground, // #FFFFFF - MANDATORY
  borderColor: colors.inputBorder,         // #F1F3F4
  borderWidth: 1,
  borderRadius: 8,
  height: 48,
  paddingHorizontal: spacing.md,           // 16px
  paddingVertical: spacing.sm,             // 12px
  fontSize: 16,
  color: colors.inputText,                 // #202124
})

const $inputFocused: ThemedStyle<ViewStyle> = ({ colors }) => ({
  borderColor: colors.inputBorderFocus,    // #FFBB50 - Orange brand color
})

const $inputError: ThemedStyle<ViewStyle> = ({ colors }) => ({
  borderColor: colors.inputError,          // #FECACA - Light red
})

const $errorText: ThemedStyle<TextStyle> = ({ colors, spacing }) => ({
  fontSize: 14,
  color: colors.inputError,
  marginTop: spacing.xs, // 8px from input
})
```

## 8. Best Practices

### Do's
- ✅ **Always use white backgrounds** for input fields
- ✅ **Use orange accent color** for focus states
- ✅ **Maintain high contrast** between text and background
- ✅ **Use light, subtle borders** for clean appearance
- ✅ **Provide clear visual feedback** for different states
- ✅ **Follow accessibility guidelines** for color contrast
- ✅ **Use consistent spacing** throughout forms
- ✅ **Test with real users** for usability

### Don'ts
- ❌ **Don't use dark backgrounds** for input fields
- ❌ **Don't use low contrast colors** that are hard to read
- ❌ **Don't ignore focus states** - always provide clear indication
- ❌ **Don't use harsh, bright colors** for error states
- ❌ **Don't make touch targets too small** (minimum 44px)
- ❌ **Don't rely solely on color** to convey state
- ❌ **Don't use inconsistent styling** across different inputs
- ❌ **Don't forget to test accessibility** with screen readers

### Light & Bright Theme Principles
1. **Prioritize readability** with high contrast text
2. **Use subtle, light colors** for borders and backgrounds
3. **Maintain brand consistency** with orange accent colors
4. **Ensure accessibility** with proper contrast ratios
5. **Create clean, modern appearance** with appropriate spacing
6. **Provide clear visual feedback** for all interaction states

## 9. Testing Guidelines

### Visual Testing
- Test input fields in different lighting conditions
- Verify color contrast ratios meet WCAG 2.1 AA standards
- Check appearance on different screen sizes and resolutions
- Validate focus states are clearly visible

### Usability Testing
- Test with users who have visual impairments
- Verify touch targets are comfortable to use
- Check that error states are clearly communicated
- Ensure placeholder text is helpful but not overwhelming

### Technical Testing
- Test with screen readers for accessibility
- Verify keyboard navigation works properly
- Check that focus management is correct
- Validate form submission and error handling
