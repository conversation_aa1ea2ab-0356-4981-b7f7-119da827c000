---
description: Comprehensive icon usage guidelines for eb-lms-app based on UI design analysis
globs:
alwaysApply: true
---

# Icon Usage Guidelines

## 1. Icon System Overview

Based on the UI design analysis, the eb-lms-app uses a consistent icon system with specific patterns for different contexts and interactions.

### Icon Design Principles
- **Consistent Sizing:** Standard sizes for different use cases
- **Color Coding:** Semantic colors for different states and actions
- **Context Awareness:** Icons match their functional context
- **Accessibility:** Clear, recognizable symbols with proper contrast

## 2. Icon Size Standards

### Size Hierarchy
```typescript
export const iconSizes = {
  xs: 16,   // Inline text icons, small indicators
  sm: 20,   // List item icons, form field icons
  md: 24,   // Standard action icons (MOST COMMON)
  lg: 32,   // Large action icons, tab icons
  xl: 48,   // Hero icons, empty states
  xxl: 64,  // Feature icons, onboarding
}
```

### Usage by Context
- **Navigation:** 24px (md) for tab bar and header icons
- **Actions:** 24px (md) for buttons and interactive elements
- **Status:** 20px (sm) for progress and state indicators
- **Content:** 16px (xs) for inline text and small indicators
- **Features:** 32px+ (lg/xl) for prominent feature representations

## 3. Icon Color System

### Color Patterns by Function
```typescript
export const iconColors = {
  // Primary actions and navigation
  primary: "#132339",      // Dark blue - main actions, navigation
  
  // Secondary and inactive states
  secondary: "#978F8A",    // Gray - inactive, secondary actions
  
  // Accent and interactive elements
  accent: "#FFBB50",       // Orange - play buttons, progress, active states
  
  // Status and feedback colors
  success: "#10B981",      // Green - completed, success states
  warning: "#F59E0B",      // Amber - warning, pending states
  error: "#DC2626",        // Red - error, destructive actions
  
  // Special contexts
  onPrimary: "#FFFFFF",    // White - icons on colored backgrounds
  disabled: "#D1D5DB",     // Light gray - disabled states
}
```

## 4. Icon Usage by Context

### Navigation Icons
**Pattern:** Tab bar and header navigation
```typescript
interface NavigationIconProps {
  name: string
  active: boolean
  size?: number
}

// Tab bar icons
const TabIcon = ({ name, active }: NavigationIconProps) => (
  <Icon
    name={name}
    size={24} // Standard tab icon size
    color={active ? "#FFBB50" : "#978F8A"} // Orange active, gray inactive
    variant={active ? "filled" : "outline"} // Filled when active
  />
)

// Header action icons
const HeaderIcon = ({ name, onPress }: { name: string; onPress: () => void }) => (
  <Pressable onPress={onPress} style={$headerIconButton}>
    <Icon name={name} size={24} color="#132339" /> {/* Dark blue */}
  </Pressable>
)
```

**Common Navigation Icons:**
- **Home:** House icon for main dashboard
- **Courses:** Book or grid icon for course listing
- **Progress:** Chart or trophy icon for progress tracking
- **Profile:** Person or user icon for profile section
- **Search:** Magnifying glass for search functionality
- **Menu:** Hamburger or dots for additional options
- **Back:** Arrow left for navigation back

### Action Icons
**Pattern:** Interactive buttons and controls
```typescript
// Primary action icons
const ActionIcon = ({ name, onPress, variant = "primary" }: {
  name: string
  onPress: () => void
  variant?: "primary" | "secondary" | "accent"
}) => {
  const colorMap = {
    primary: "#132339",   // Dark blue
    secondary: "#978F8A", // Gray
    accent: "#FFBB50",    // Orange
  }
  
  return (
    <Pressable onPress={onPress} style={$actionButton}>
      <Icon name={name} size={24} color={colorMap[variant]} />
    </Pressable>
  )
}
```

**Common Action Icons:**
- **Play:** Triangle for video/audio playback
- **Pause:** Double bars for pause functionality
- **Download:** Down arrow for downloading content
- **Share:** Share symbol for sharing functionality
- **Bookmark:** Heart or bookmark for saving items
- **Settings:** Gear or cog for configuration
- **Edit:** Pencil for editing content
- **Delete:** Trash can for removal actions

### Status Icons
**Pattern:** Progress and state indicators
```typescript
interface StatusIconProps {
  status: "complete" | "progress" | "locked" | "error"
  size?: number
}

const StatusIcon = ({ status, size = 20 }: StatusIconProps) => {
  const statusConfig = {
    complete: { name: "check-circle", color: "#10B981" }, // Green
    progress: { name: "clock", color: "#FFBB50" },        // Orange
    locked: { name: "lock", color: "#978F8A" },           // Gray
    error: { name: "x-circle", color: "#DC2626" },        // Red
  }
  
  const config = statusConfig[status]
  return <Icon name={config.name} size={size} color={config.color} />
}
```

**Common Status Icons:**
- **Complete:** Check mark or check circle (green)
- **In Progress:** Clock or partial circle (orange)
- **Locked:** Lock icon (gray)
- **Error:** X or warning triangle (red)
- **New:** Star or badge (orange)
- **Popular:** Fire or trending icon (orange)

### Media Icons
**Pattern:** Video and audio controls
```typescript
// Play button (special circular design)
const PlayButton = ({ onPress, size = 40 }: {
  onPress: () => void
  size?: number
}) => (
  <Pressable style={[$playButton, { width: size, height: size, borderRadius: size / 2 }]} onPress={onPress}>
    <Icon name="play" size={size * 0.5} color="#FFFFFF" />
  </Pressable>
)

const $playButton: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#FFBB50", // Orange background - MANDATORY
  alignItems: "center",
  justifyContent: "center",
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
})
```

**Common Media Icons:**
- **Play:** Triangle pointing right (white on orange)
- **Pause:** Two vertical bars
- **Stop:** Square
- **Volume:** Speaker icon with sound waves
- **Mute:** Speaker with X or slash
- **Fullscreen:** Expand arrows
- **Duration:** Clock icon for time display

### Content Icons
**Pattern:** Inline and content-related icons
```typescript
// Small content icons
const ContentIcon = ({ name, color = "#978F8A" }: {
  name: string
  color?: string
}) => (
  <Icon name={name} size={16} color={color} />
)
```

**Common Content Icons:**
- **Time:** Clock for duration/time information
- **Users:** People icon for participant count
- **Lessons:** List or numbered items for lesson count
- **Certificate:** Award or diploma for completion
- **Level:** Bars or steps for difficulty level
- **Rating:** Stars for course ratings

## 5. Icon Implementation Patterns

### Icon Button Component
```typescript
interface IconButtonProps {
  icon: string
  onPress: () => void
  size?: keyof typeof iconSizes
  color?: string
  backgroundColor?: string
  disabled?: boolean
  accessibilityLabel?: string
}

const IconButton = ({
  icon,
  onPress,
  size = "md",
  color = "#132339",
  backgroundColor = "transparent",
  disabled = false,
  accessibilityLabel,
}: IconButtonProps) => (
  <Pressable
    onPress={onPress}
    disabled={disabled}
    style={[
      $iconButton,
      { backgroundColor },
      disabled && $iconButtonDisabled,
    ]}
    accessibilityRole="button"
    accessibilityLabel={accessibilityLabel}
  >
    <Icon
      name={icon}
      size={iconSizes[size]}
      color={disabled ? "#D1D5DB" : color}
    />
  </Pressable>
)

const $iconButton: ThemedStyle<ViewStyle> = () => ({
  width: 44, // Minimum touch target
  height: 44,
  alignItems: "center",
  justifyContent: "center",
  borderRadius: 8,
})

const $iconButtonDisabled: ThemedStyle<ViewStyle> = () => ({
  opacity: 0.5,
})
```

### Icon with Text Component
```typescript
interface IconTextProps {
  icon: string
  text: string
  iconSize?: number
  iconColor?: string
  textStyle?: StyleProp<TextStyle>
  layout?: "horizontal" | "vertical"
  gap?: number
}

const IconText = ({
  icon,
  text,
  iconSize = 20,
  iconColor = "#978F8A",
  textStyle,
  layout = "horizontal",
  gap = 8,
}: IconTextProps) => (
  <View style={[
    $iconTextContainer,
    layout === "vertical" && $iconTextVertical,
    { gap }
  ]}>
    <Icon name={icon} size={iconSize} color={iconColor} />
    <Text style={[textStyle]}>{text}</Text>
  </View>
)

const $iconTextContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
})

const $iconTextVertical: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "column",
  alignItems: "center",
})
```

## 6. Icon Accessibility Guidelines

### Accessibility Requirements
```typescript
// Accessible icon implementation
const AccessibleIcon = ({ name, accessibilityLabel, decorative = false }: {
  name: string
  accessibilityLabel?: string
  decorative?: boolean
}) => (
  <Icon
    name={name}
    size={24}
    color="#132339"
    accessibilityLabel={decorative ? undefined : accessibilityLabel}
    accessibilityElementsHidden={decorative}
  />
)
```

### Best Practices
- **Provide Labels:** Always include accessibility labels for functional icons
- **Decorative Icons:** Mark purely decorative icons as hidden from screen readers
- **Color Independence:** Don't rely solely on color to convey meaning
- **Touch Targets:** Ensure minimum 44px touch area for interactive icons
- **Contrast:** Maintain sufficient color contrast (4.5:1 minimum)

## 7. Icon Animation Patterns

### Subtle Animations
```typescript
// Animated icon for loading states
const AnimatedIcon = ({ name, spinning = false }: {
  name: string
  spinning?: boolean
}) => {
  const rotation = useSharedValue(0)
  
  useEffect(() => {
    if (spinning) {
      rotation.value = withRepeat(
        withTiming(360, { duration: 1000 }),
        -1,
        false
      )
    }
  }, [spinning])
  
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${rotation.value}deg` }],
  }))
  
  return (
    <Animated.View style={animatedStyle}>
      <Icon name={name} size={24} color="#FFBB50" />
    </Animated.View>
  )
}
```

## 8. Icon Library Organization

### Icon Categories
```typescript
export const iconLibrary = {
  // Navigation icons
  navigation: {
    home: "home",
    courses: "book",
    progress: "chart-bar",
    profile: "user",
    search: "search",
    menu: "menu",
    back: "arrow-left",
  },
  
  // Action icons
  actions: {
    play: "play",
    pause: "pause",
    download: "download",
    share: "share",
    bookmark: "heart",
    settings: "settings",
    edit: "edit",
    delete: "trash",
  },
  
  // Status icons
  status: {
    complete: "check-circle",
    progress: "clock",
    locked: "lock",
    error: "x-circle",
    new: "star",
    popular: "fire",
  },
  
  // Content icons
  content: {
    time: "clock",
    users: "users",
    lessons: "list",
    certificate: "award",
    level: "bar-chart",
    rating: "star",
  },
}
```

## 9. Implementation Guidelines

### Do's
- **Use consistent sizes** across similar contexts
- **Apply semantic colors** based on function and state
- **Provide accessibility labels** for all functional icons
- **Maintain proper contrast** ratios
- **Use standard icon library** for consistency
- **Follow touch target guidelines** (44px minimum)
- **Test with screen readers** for accessibility

### Don'ts
- **Don't use arbitrary sizes** outside the defined scale
- **Don't rely solely on color** to convey meaning
- **Don't ignore accessibility** requirements
- **Don't use too many different icon styles** in one interface
- **Don't make touch targets too small** (below 44px)
- **Don't use complex icons** at small sizes
- **Don't forget to test** icon visibility in different contexts

### Quality Checklist
- [ ] Icon sizes follow the defined scale
- [ ] Colors match semantic meaning
- [ ] Accessibility labels provided
- [ ] Touch targets meet minimum size
- [ ] Contrast ratios are sufficient
- [ ] Icons are consistent across similar contexts
- [ ] Loading and error states are handled
- [ ] Screen reader compatibility verified
