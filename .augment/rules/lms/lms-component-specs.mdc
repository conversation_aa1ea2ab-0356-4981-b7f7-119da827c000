---
description: Detailed LMS component specifications and interfaces for eb-lms-app
globs:
alwaysApply: true
---

# LMS Component Specifications

## Overview

This document provides detailed specifications for LMS-specific UI components in eb-lms-app, including interfaces, design requirements, and implementation guidelines.

## 1. Course Information Card

**Purpose:** Display comprehensive course details with metadata and actions

```typescript
interface CourseInfoCardProps {
  title: string
  description: string
  studentCount?: number
  lessonCount?: number
  duration?: string
  students?: StudentAvatar[]
  instructor?: string
  thumbnail?: string
  progress?: number
  onPress?: () => void
  onEnrollPress?: () => void
  style?: StyleProp<ViewStyle>
}

interface StudentAvatar {
  id: string
  name: string
  avatar?: string
}
```

**Design Specifications:**
- **Background:** White (#FFFFFF) with elevation shadow
- **Border Radius:** 12px for modern appearance
- **Padding:** 16px all sides
- **Title:** SemiBold weight (600), 18px size, dark blue color
- **Description:** Light weight (300), 14px size, gray color - MANDATORY
- **Student Avatars:** Overlapping circular avatars (32px diameter)
- **Metadata:** Horizontal layout with icons and values
- **Progress Bar:** Orange (#FFBB50) progress indicator if applicable

**Implementation Example:**
```typescript
export const CourseInfoCard = (props: CourseInfoCardProps) => {
  const { themed } = useAppTheme()
  const { title, description, studentCount, students, onPress } = props

  return (
    <Pressable style={themed($container)} onPress={onPress}>
      <View style={themed($header)}>
        <Text style={themed($title)}>{title}</Text>
        <Text style={themed($description)}>{description}</Text>
      </View>
      
      <View style={themed($metadata)}>
        <View style={themed($studentAvatars)}>
          {students?.slice(0, 4).map((student, index) => (
            <Avatar
              key={student.id}
              source={student.avatar}
              size={32}
              style={themed([$avatar, { marginLeft: index > 0 ? -8 : 0 }])}
            />
          ))}
          {studentCount && studentCount > 4 && (
            <Text style={themed($moreStudents)}>+{studentCount - 4}</Text>
          )}
        </View>
      </View>
    </Pressable>
  )
}
```

## 2. Progress Statistics Grid

**Purpose:** Display numerical progress data with visual indicators

```typescript
interface ProgressStatsGridProps {
  stats: Array<{
    label: string
    value: number | string
    color: string
    icon?: string
    trend?: "up" | "down" | "neutral"
  }>
  columns?: number
  onStatPress?: (statIndex: number) => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Card Colors:**
  - Course: Dark blue (#132339)
  - Progress: Orange (#FFBB50) - MANDATORY
  - Completed: Light gray (#F4F2F1)
  - Active: Royal blue (#3B82F6)
- **Text:** White on dark backgrounds, dark on light backgrounds
- **Border Radius:** 8px for consistency
- **Aspect Ratio:** Square or slightly rectangular (1:1 or 1.2:1)
- **Typography:** Bold values (700), light labels (300) - MANDATORY
- **Spacing:** 8px gap between stat cards

## 3. Video Lesson List

**Purpose:** Display list of video lessons with play functionality and status

```typescript
interface VideoLessonListProps {
  lessons: Array<{
    id: string
    title: string
    duration: string
    isCompleted?: boolean
    isLocked?: boolean
    isCurrent?: boolean
    thumbnail?: string
  }>
  onLessonPress?: (lessonId: string) => void
  onPlayPress?: (lessonId: string) => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Play Button:** Orange circular button (#FFBB50) with white play icon - MANDATORY
- **Button Size:** 40px diameter
- **Title:** Regular weight (400), 16px size
- **Duration:** Light gray text (weight 300), 14px size - MANDATORY
- **Spacing:** 16px vertical between items
- **Lock Icon:** Gray color for locked content
- **Completion Indicator:** Green checkmark for completed lessons
- **Current Indicator:** Blue accent for current lesson

## 4. User Profile Header

**Purpose:** Display user information with avatar, greeting, and actions

```typescript
interface UserProfileHeaderProps {
  name: string
  username?: string
  avatar?: string
  greeting?: string
  showSearch?: boolean
  showNotifications?: boolean
  onAvatarPress?: () => void
  onSearchPress?: () => void
  onNotificationPress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Avatar:** 48px circular with subtle border
- **Greeting:** Light weight (300) for secondary text - MANDATORY
- **Name:** SemiBold weight (600) for prominence
- **Layout:** Horizontal with avatar left, text center, actions right
- **Background:** White (#FFFFFF) with optional subtle shadow
- **Touch Targets:** 44px minimum for interactive elements

## 5. Course Grid Layout

**Purpose:** Grid display of course cards with thumbnails and information

```typescript
interface CourseGridProps {
  courses: Array<{
    id: string
    title: string
    instructor?: string
    participants?: number
    lessons?: number
    duration?: string
    thumbnail?: string
    progress?: number
    difficulty?: "beginner" | "intermediate" | "advanced"
  }>
  columns?: number
  onCoursePress?: (courseId: string) => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Grid:** 2 columns with 16px gap
- **Card Aspect:** 1.2 ratio (slightly rectangular)
- **Thumbnail:** Top section with overlay text
- **Info Section:** White background with course details
- **Progress Bar:** Orange (#FFBB50) if course is started - MANDATORY
- **Typography:** Light weight (300) for descriptions - MANDATORY

## 6. Student Avatar Group

**Purpose:** Display multiple student avatars in overlapping layout

```typescript
interface StudentAvatarGroupProps {
  students: Array<{
    id: string
    name: string
    avatar?: string
  }>
  maxVisible?: number
  size?: number
  overlapOffset?: number
  onPress?: (studentId: string) => void
  onMorePress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Avatar Size:** 32px diameter for group display
- **Overlap:** -8px margin for overlapping effect
- **Border:** 2px white border for separation
- **Max Display:** Show 4-5 avatars, then "+X more"
- **Fallback:** Initials on colored background
- **Colors:** Varied background colors for initials

## 7. Course Progress Bar

**Purpose:** Visual progress indicator for course completion

```typescript
interface CourseProgressBarProps {
  progress: number // 0-100
  height?: number
  backgroundColor?: string
  progressColor?: string
  showPercentage?: boolean
  animated?: boolean
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Height:** 6px standard, 8px for emphasis
- **Background:** Light gray (#F4F2F1)
- **Progress Color:** Orange (#FFBB50) - MANDATORY
- **Border Radius:** 3px for smooth appearance
- **Animation:** Smooth progress transitions (300ms)
- **Percentage:** Optional text display with light weight (300)

## 8. Lesson Status Badge

**Purpose:** Indicate lesson completion and availability status

```typescript
interface LessonStatusBadgeProps {
  status: "completed" | "current" | "locked" | "available"
  size?: "small" | "medium"
  showLabel?: boolean
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Completed:** Green background (#10B981) with checkmark
- **Current:** Orange background (#FFBB50) with play icon - MANDATORY
- **Locked:** Gray background (#9CA3AF) with lock icon
- **Available:** Transparent with border
- **Size:** 24px small, 32px medium
- **Icons:** 16px for small, 20px for medium

## 9. Course Category Tag

**Purpose:** Display course categories and subject classifications

```typescript
interface CourseCategoryTagProps {
  category: string
  color?: string
  size?: "small" | "medium"
  onPress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** Light colored backgrounds
- **Text:** Dark text for contrast, light weight (300) - MANDATORY
- **Border Radius:** 16px for pill shape
- **Padding:** 8px horizontal, 4px vertical
- **Typography:** Light weight (300), 12px font size
- **Colors:** Category-specific colors (math: purple, science: cyan, etc.)

## 10. Information Block

**Purpose:** Structured information display with metadata

```typescript
interface InfoBlockProps {
  title: string
  description: string
  metadata: Array<{
    icon: string
    label: string
    value: string | number
  }>
  actions?: ReactElement[]
  variant?: "default" | "highlighted" | "compact"
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Title:** SemiBold weight (600), 18px size
- **Description:** Light weight (300), 14px size - MANDATORY
- **Metadata:** Horizontal layout with icons and values
- **Actions:** Right-aligned button group
- **Background:** White (#FFFFFF) with optional highlight
- **Spacing:** 16px internal padding, 24px between blocks

## 11. Quality Assurance

### LMS Component Checklist
- [ ] Orange accent color (#FFBB50) used for progress elements
- [ ] White backgrounds for all cards and containers
- [ ] Font weight 300 for descriptions and labels (MANDATORY)
- [ ] Proper touch targets (44px minimum)
- [ ] Accessibility labels for all interactive elements
- [ ] Loading and error states implemented
- [ ] Responsive design considerations
- [ ] Performance optimizations applied

### Design Compliance
- [ ] Typography hierarchy maintained
- [ ] Color contrast meets WCAG standards
- [ ] Consistent spacing (16px standard)
- [ ] Proper border radius (12px cards, 16px buttons)
- [ ] Shadow elevation appropriate
- [ ] Animation timing consistent (300ms)
- [ ] Icon sizes standardized (24px default)

### Educational UX Standards
- [ ] Clear progress indicators
- [ ] Intuitive lesson navigation
- [ ] Accessible content for all learners
- [ ] Consistent interaction patterns
- [ ] Motivational visual feedback
- [ ] Clear completion states
- [ ] Helpful error messages
- [ ] Offline capability considerations

These LMS component specifications ensure a cohesive, accessible, and engaging learning experience throughout eb-lms-app.
