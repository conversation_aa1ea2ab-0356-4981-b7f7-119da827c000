---
description: LMS visual design patterns and layout structures for eb-lms-app
globs:
alwaysApply: true
---

# LMS Visual Design Patterns

## Overview

This document defines the visual design patterns specific to learning management system features in eb-lms-app, based on comprehensive UI analysis and implementation requirements.

## 1. Visual Design Principles

### Core Design Philosophy
- **Clean White Backgrounds:** All screens use white (#FFFFFF) as the primary background - MANDATORY
- **Orange Progress Accents:** Orange/amber (#FFBB50) for all progress indicators - MANDATORY
- **Blue Brand Elements:** Deep navy (#132339) for navigation and primary text
- **Card-Based Layout:** Information organized in clean white cards with subtle shadows
- **Consistent Spacing:** 16px standard spacing between components
- **Typography Hierarchy:** Light weight (300) for descriptions, semiBold for titles

### Screen Layout Patterns
1. **Course Details Screen:** Course information with video list and student avatars
2. **Dashboard Screen:** User greeting, progress cards, and course grid
3. **Profile Screen:** User stats, progress indicators, and course list
4. **Welcome Screen:** Hero content with action buttons

## 2. Layout Patterns

### Screen Container Pattern
```typescript
const $screenContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 1,
  backgroundColor: colors.background, // Always white - MANDATORY
  paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
})

const $contentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.md, // 16px
  gap: spacing.md, // 16px gap between elements
})
```

### Header Section Pattern
```typescript
const $headerSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.md, // 16px
  paddingVertical: spacing.sm, // 12px
  backgroundColor: "#FFFFFF", // White background
  marginBottom: spacing.lg, // 24px below header
})
```

### Card Container Pattern
```typescript
const $cardContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12, // Modern rounded corners
  padding: spacing.md, // 16px internal padding
  marginBottom: spacing.md, // 16px between cards
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
})
```

### Content Block Pattern
```typescript
const $contentBlock: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg, // 24px between major content blocks
  paddingHorizontal: spacing.md, // 16px internal padding
})

const $blockHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm, // 12px below header
  alignItems: "center",
})

const $blockContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.xs, // 8px gap between content elements
})
```

## 3. Color Application Patterns

### Progress and Status Colors
```typescript
const progressColors = {
  primary: "#FFBB50",      // Orange for main progress - MANDATORY
  completed: "#10B981",    // Green for completed items
  pending: "#F59E0B",      // Amber for pending items
  inactive: "#F4F2F1",     // Light gray for inactive
  current: "#3B82F6",      // Blue for current/active items
}
```

### Background Color Hierarchy
```typescript
const backgroundColors = {
  screen: "#FFFFFF",       // Main screen background - MANDATORY
  card: "#FFFFFF",         // Card backgrounds - MANDATORY
  accent: "#F8F9FA",       // Subtle accent backgrounds
  overlay: "rgba(0,0,0,0.5)", // Modal overlays
  section: "#FFFFFF",      // Section backgrounds
}
```

### Interactive Element Colors
```typescript
const interactiveColors = {
  primary: "#3B82F6",      // Primary buttons (Royal blue)
  secondary: "#FFFFFF",    // Secondary buttons (White with border)
  accent: "#FFBB50",       // Play buttons, highlights - MANDATORY
  disabled: "#D1D5DB",     // Disabled states
  border: "#F4F2F1",       // Subtle borders
}
```

## 4. Typography Patterns

### Text Hierarchy in LMS Components
```typescript
// Course titles
const $courseTitle: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.semiBold, // 600 weight
  fontSize: 18,
  lineHeight: 24,
  color: colors.text,
  marginBottom: 8,
})

// Course descriptions (MANDATORY light weight)
const $courseDescription: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 14,
  lineHeight: 20,
  color: colors.textDim,
})

// Statistics and numbers
const $statisticValue: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.bold, // 700 weight
  fontSize: 24,
  lineHeight: 28,
  textAlign: "center",
})

// Labels and captions
const $componentLabel: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.textDim,
})

// User greetings
const $userGreeting: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 16,
  lineHeight: 20,
  color: colors.textDim,
})

// User names
const $userName: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.semiBold, // 600 weight
  fontSize: 18,
  lineHeight: 22,
  color: colors.text,
})
```

## 5. Spacing and Sizing Standards

### Component Spacing
```typescript
const componentSpacing = {
  cardPadding: 16,         // Internal card padding
  itemGap: 16,             // Gap between items
  sectionGap: 24,          // Gap between sections
  screenPadding: 12,       // Screen edge padding - MANDATORY
  heroSpacing: 32,         // Hero section spacing
}
```

### Element Sizing
```typescript
const elementSizes = {
  avatarSmall: 32,         // Small avatars in groups
  avatarMedium: 48,        // Profile avatars
  avatarLarge: 64,         // Large profile displays
  buttonHeight: 44,        // Standard button height
  inputHeight: 48,         // Text input height
  headerHeight: 56,        // Navigation header height
  playButtonSize: 40,      // Play button diameter
}
```

### Border Radius Standards
```typescript
const borderRadius = {
  small: 4,                // Small elements
  medium: 8,               // Default components
  large: 12,               // Main cards
  button: 16,              // Main action buttons
  social: 12,              // Social login buttons
  play: 20,                // Play buttons (circular)
  round: 999,              // Fully circular elements
}
```

## 6. Animation and Interaction Patterns

### Progress Animation
```typescript
const progressAnimation = {
  duration: 300,           // Smooth progress updates
  easing: "ease-out",      // Natural easing
  delay: 0,                // Immediate start
  type: "timing",          // Animation type
}
```

### Card Interaction
```typescript
const cardInteraction = {
  pressScale: 0.98,        // Subtle press feedback
  shadowIncrease: 0.2,     // Hover shadow increase
  borderHighlight: "#FFBB50", // Orange highlight on focus
  animationDuration: 150,  // Quick response
}
```

### Button States
```typescript
const buttonStates = {
  default: { opacity: 1, scale: 1 },
  pressed: { opacity: 0.8, scale: 0.96 },
  disabled: { opacity: 0.5, scale: 1 },
  loading: { opacity: 0.7, scale: 1 },
}
```

## 7. Line Elements & Visual Separators

### Divider Lines
```typescript
interface DividerProps {
  variant?: "full" | "inset" | "middle"
  color?: string
  thickness?: number
  spacing?: number
}

const $divider: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  height: 1,
  backgroundColor: "#F4F2F1", // Light gray
  marginVertical: spacing.md, // 16px top/bottom
})
```

### Progress Lines
```typescript
interface ProgressLineProps {
  progress: number // 0-100
  height?: number
  backgroundColor?: string
  progressColor?: string
  animated?: boolean
}

const $progressLine: ThemedStyle<ViewStyle> = () => ({
  height: 6,
  backgroundColor: "#F4F2F1", // Light gray background
  borderRadius: 3,
  overflow: "hidden",
})

const $progressFill: ThemedStyle<ViewStyle> = () => ({
  height: "100%",
  backgroundColor: "#FFBB50", // Orange progress - MANDATORY
  borderRadius: 3,
})
```

### Border Lines
```typescript
const borderStyles = {
  subtle: {
    borderWidth: 1,
    borderColor: "#F4F2F1", // Light gray
  },
  medium: {
    borderWidth: 1,
    borderColor: "#D1D5DB", // Medium gray
  },
  strong: {
    borderWidth: 2,
    borderColor: "#3B82F6", // Blue accent
  },
  accent: {
    borderWidth: 1,
    borderColor: "#FFBB50", // Orange accent
  },
}
```

## 8. Implementation Guidelines

### Component Structure
1. **Props Interface:** Define clear TypeScript interfaces
2. **Default Values:** Provide sensible defaults
3. **Style Overrides:** Allow style customization
4. **Accessibility:** Include proper accessibility props
5. **Theming:** Use themed styles throughout

### Code Organization
1. **Component File:** Main component implementation
2. **Styles:** Themed style objects at bottom
3. **Types:** Interface definitions at top
4. **Tests:** Comprehensive test coverage

### Performance Considerations
1. **Memoization:** Use React.memo for expensive components
2. **Lazy Loading:** Implement for large lists
3. **Image Optimization:** Proper image sizing and caching
4. **Animation:** Use native driver when possible

## 9. Quality Assurance

### Visual Design Compliance
- [ ] White backgrounds used for all screens and cards (MANDATORY)
- [ ] Orange accent color (#FFBB50) used for progress elements (MANDATORY)
- [ ] Light font weight (300) applied to descriptions (MANDATORY)
- [ ] 12px horizontal padding from screen edges (MANDATORY)
- [ ] 16px standard spacing between components
- [ ] 12px border radius for cards
- [ ] 16px border radius for main action buttons
- [ ] Proper shadow elevation for cards

### LMS-Specific Standards
- [ ] Course cards have proper information hierarchy
- [ ] Progress indicators use orange accent color
- [ ] Student avatars are properly sized and positioned
- [ ] Video lessons have play button styling
- [ ] Statistics cards use appropriate background colors
- [ ] User profile headers have correct layout
- [ ] Navigation follows established patterns

This visual pattern guide ensures consistent and engaging LMS-specific design throughout eb-lms-app.
