---
description: API architecture and client configuration for eb-lms-app
globs: 
alwaysApply: true
---

# API Architecture

## Overview

This document defines the API architecture, client configuration, and service patterns for eb-lms-app using Apisauce and standardized response handling.

## 1. Technology Stack

### Core Technologies
- **Apisauce:** HTTP client built on Axios with standardized responses
- **Base URL Configuration:** Environment-specific API endpoints
- **Request/Response Transformation:** Consistent data formatting
- **Error Handling:** Standardized error responses
- **Authentication:** Token-based authentication with automatic refresh

### API Client Structure
```
app/services/
└── api/
    ├── api.ts              # Main API client configuration
    ├── apiProblem.ts       # Error handling types
    ├── api.types.ts        # API response types
    └── endpoints/          # Endpoint-specific modules
        ├── auth.ts         # Authentication endpoints
        ├── episodes.ts     # Episode/content endpoints
        ├── courses.ts      # Course management endpoints
        └── user.ts         # User management endpoints
```

## 2. Base API Client Configuration

### Main API Class
```typescript
// app/services/api/api.ts
import { ApisauceInstance, create } from "apisauce"
import Config from "@/config"
import { ApiResponse, GeneralApiProblem } from "./api.types"

export class Api {
  apisauce: ApisauceInstance
  
  constructor(config: { url: string; timeout?: number } = { url: Config.API_URL }) {
    this.apisauce = create({
      baseURL: config.url,
      timeout: config.timeout || 10000,
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    })
    
    // Request/Response transforms
    this.apisauce.addRequestTransform(this.addAuthToken)
    this.apisauce.addRequestTransform(this.addRequestMetadata)
    this.apisauce.addResponseTransform(this.transformResponse)
    this.apisauce.addResponseTransform(this.handleAuthErrors)
  }
  
  private addAuthToken = (request: any) => {
    const authToken = getAuthToken() // Get from storage or store
    if (authToken) {
      request.headers.Authorization = `Bearer ${authToken}`
    }
  }
  
  private addRequestMetadata = (request: any) => {
    request.headers["X-Request-ID"] = generateRequestId()
    request.headers["X-App-Version"] = Config.APP_VERSION
    request.headers["X-Platform"] = Platform.OS
  }
  
  private transformResponse = (response: any) => {
    // Transform response data if needed
    if (response.data && response.data.data) {
      response.data = response.data.data
    }
  }
  
  private handleAuthErrors = (response: any) => {
    if (response.status === 401) {
      // Token expired, handle logout
      this.handleTokenExpiration()
    }
  }
  
  private handleTokenExpiration() {
    // Clear auth data and redirect to login
    clearAuthData()
    navigationRef.current?.reset({
      index: 0,
      routes: [{ name: "Login" }],
    })
  }
}
```

### Environment Configuration
```typescript
// app/config/config.base.ts
export interface ConfigBaseProps {
  persistNavigation: "always" | "dev" | "prod" | "never"
  catchErrors: "always" | "dev" | "prod" | "never"
  exitRoutes: string[]
  API_URL: string
  API_TIMEOUT: number
  APP_VERSION: string
}

export type PersistNavigationConfig = ConfigBaseProps["persistNavigation"]

const BaseConfig: ConfigBaseProps = {
  persistNavigation: "dev",
  catchErrors: "always",
  exitRoutes: ["Welcome"],
  API_URL: "https://api.eblms.app/v1",
  API_TIMEOUT: 10000,
  APP_VERSION: "1.0.0",
}

export default BaseConfig
```

### Development vs Production Config
```typescript
// app/config/config.dev.ts
import BaseConfig from "./config.base"

export default {
  ...BaseConfig,
  API_URL: "https://dev-api.eblms.app/v1",
  API_TIMEOUT: 15000, // Longer timeout for dev
}

// app/config/config.prod.ts
import BaseConfig from "./config.base"

export default {
  ...BaseConfig,
  API_URL: "https://api.eblms.app/v1",
  API_TIMEOUT: 8000, // Shorter timeout for prod
}
```

## 3. Response Types and Error Handling

### API Response Types
```typescript
// app/services/api/api.types.ts
export type GeneralApiProblem =
  | { kind: "timeout"; temporary: true }
  | { kind: "cannot-connect"; temporary: true }
  | { kind: "server"; temporary: true }
  | { kind: "unauthorized"; temporary: false }
  | { kind: "forbidden"; temporary: false }
  | { kind: "not-found"; temporary: false }
  | { kind: "rejected"; temporary: false }
  | { kind: "unknown"; temporary: true }
  | { kind: "bad-data"; temporary: false }

export interface ApiOkResponse<T> {
  kind: "ok"
  data: T
}

export interface ApiErrorResponse {
  kind: "error"
  message: string
  problem: GeneralApiProblem
}

export type ApiResponse<T> = ApiOkResponse<T> | ApiErrorResponse

// Common response interfaces
export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

export interface LoginResponse {
  token: string
  refreshToken: string
  user: User
  expiresAt: string
}

export interface TokenResponse {
  token: string
  expiresAt: string
}
```

### Error Problem Detection
```typescript
// app/services/api/apiProblem.ts
import { ApiResponse } from "apisauce"

export const getGeneralApiProblem = (response: ApiResponse<any>): GeneralApiProblem | void => {
  switch (response.problem) {
    case "CONNECTION_ERROR":
      return { kind: "cannot-connect", temporary: true }
    case "NETWORK_ERROR":
      return { kind: "cannot-connect", temporary: true }
    case "TIMEOUT_ERROR":
      return { kind: "timeout", temporary: true }
    case "SERVER_ERROR":
      return { kind: "server", temporary: true }
    case "UNKNOWN_ERROR":
      return { kind: "unknown", temporary: true }
    case "CLIENT_ERROR":
      switch (response.status) {
        case 401:
          return { kind: "unauthorized", temporary: false }
        case 403:
          return { kind: "forbidden", temporary: false }
        case 404:
          return { kind: "not-found", temporary: false }
        default:
          return { kind: "rejected", temporary: false }
      }
    case "CANCEL_ERROR":
      return null
  }
  
  return null
}

export const getErrorMessage = (problem: GeneralApiProblem): string => {
  switch (problem.kind) {
    case "timeout":
      return "Request timed out. Please try again."
    case "cannot-connect":
      return "Cannot connect to server. Check your internet connection."
    case "server":
      return "Server error. Please try again later."
    case "unauthorized":
      return "You are not authorized. Please log in again."
    case "forbidden":
      return "You don't have permission to access this resource."
    case "not-found":
      return "The requested resource was not found."
    case "rejected":
      return "Request was rejected. Please check your input."
    case "bad-data":
      return "Invalid data received from server."
    default:
      return "An unexpected error occurred. Please try again."
  }
}
```

## 4. Request/Response Interceptors

### Request Interceptors
```typescript
// Authentication token injection
this.apisauce.addRequestTransform((request) => {
  const token = getAuthToken()
  if (token) {
    request.headers.Authorization = `Bearer ${token}`
  }
})

// Request tracking and metadata
this.apisauce.addRequestTransform((request) => {
  request.headers["X-Request-ID"] = generateRequestId()
  request.headers["X-App-Version"] = Config.APP_VERSION
  request.headers["X-Platform"] = Platform.OS
  request.headers["X-Device-ID"] = getDeviceId()
})

// Request logging in development
this.apisauce.addRequestTransform((request) => {
  if (__DEV__) {
    console.log(`API Request: ${request.method?.toUpperCase()} ${request.url}`, {
      headers: request.headers,
      data: request.data,
    })
  }
})
```

### Response Interceptors
```typescript
// Handle authentication errors
this.apisauce.addResponseTransform((response) => {
  if (response.status === 401) {
    // Token expired, redirect to login
    this.handleTokenExpiration()
  }
})

// Response logging in development
this.apisauce.addResponseTransform((response) => {
  if (__DEV__) {
    console.log(`API Response: ${response.config?.url}`, {
      status: response.status,
      data: response.data,
      problem: response.problem,
    })
  }
})

// Data transformation
this.apisauce.addResponseTransform((response) => {
  // Unwrap nested data structure if present
  if (response.data && response.data.data) {
    response.data = response.data.data
  }
})
```

## 5. Utility Functions

### Request ID Generation
```typescript
export const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}
```

### Device Information
```typescript
import DeviceInfo from 'react-native-device-info'

export const getDeviceId = async (): Promise<string> => {
  return await DeviceInfo.getUniqueId()
}

export const getDeviceInfo = async () => {
  return {
    deviceId: await DeviceInfo.getUniqueId(),
    brand: DeviceInfo.getBrand(),
    model: DeviceInfo.getModel(),
    systemVersion: DeviceInfo.getSystemVersion(),
    appVersion: DeviceInfo.getVersion(),
    buildNumber: DeviceInfo.getBuildNumber(),
  }
}
```

### Authentication Helpers
```typescript
import AsyncStorage from '@react-native-async-storage/async-storage'

const AUTH_TOKEN_KEY = 'auth_token'
const REFRESH_TOKEN_KEY = 'refresh_token'

export const getAuthToken = async (): Promise<string | null> => {
  return await AsyncStorage.getItem(AUTH_TOKEN_KEY)
}

export const setAuthToken = async (token: string): Promise<void> => {
  await AsyncStorage.setItem(AUTH_TOKEN_KEY, token)
}

export const getRefreshToken = async (): Promise<string | null> => {
  return await AsyncStorage.getItem(REFRESH_TOKEN_KEY)
}

export const setRefreshToken = async (token: string): Promise<void> => {
  await AsyncStorage.setItem(REFRESH_TOKEN_KEY, token)
}

export const clearAuthData = async (): Promise<void> => {
  await AsyncStorage.multiRemove([AUTH_TOKEN_KEY, REFRESH_TOKEN_KEY])
}
```

## 6. Quality Assurance

### API Architecture Checklist
- [ ] Consistent response types across all endpoints
- [ ] Proper error handling for all API calls
- [ ] Authentication token management implemented
- [ ] Request/response logging in development
- [ ] Environment-specific configuration
- [ ] Timeout handling configured
- [ ] Network error handling implemented
- [ ] Token refresh mechanism in place

### Security Considerations
- [ ] HTTPS only in production
- [ ] Secure token storage
- [ ] Request signing if required
- [ ] Rate limiting awareness
- [ ] Sensitive data not logged
- [ ] Certificate pinning (if required)
- [ ] API key protection
- [ ] CORS configuration awareness

### Performance Considerations
- [ ] Appropriate timeout values
- [ ] Request/response compression
- [ ] Caching strategy implemented
- [ ] Offline support considered
- [ ] Request deduplication
- [ ] Pagination for large datasets
- [ ] Image optimization for uploads
- [ ] Background sync capabilities

This API architecture ensures robust, secure, and maintainable API communication throughout eb-lms-app.
