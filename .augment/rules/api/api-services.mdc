---
description: API service implementations and patterns for eb-lms-app
globs: 
alwaysApply: true
---

# API Services

## Overview

This document defines specific API service implementations for eb-lms-app including authentication, course management, and content services with consistent patterns and error handling.

## 1. Authentication Service

### Authentication API
```typescript
// app/services/api/endpoints/auth.ts
export class AuthApi {
  constructor(private api: Api) {}
  
  async login(email: string, password: string): Promise<ApiResponse<LoginResponse>> {
    const response = await this.api.apisauce.post("/auth/login", {
      email,
      password,
    })
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: getErrorMessage(problem), 
          problem 
        }
      }
    }
    
    try {
      const data: LoginResponse = {
        token: response.data.token,
        refreshToken: response.data.refreshToken,
        user: transformUser(response.data.user),
        expiresAt: response.data.expiresAt,
      }
      return { kind: "ok", data }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid response format", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
  
  async logout(): Promise<ApiResponse<void>> {
    const response = await this.api.apisauce.post("/auth/logout")
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: getErrorMessage(problem), 
          problem 
        }
      }
    }
    
    // Clear local auth data regardless of API response
    await clearAuthData()
    return { kind: "ok", data: undefined }
  }
  
  async refreshToken(refreshToken: string): Promise<ApiResponse<TokenResponse>> {
    const response = await this.api.apisauce.post("/auth/refresh", {
      refreshToken,
    })
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: getErrorMessage(problem), 
          problem 
        }
      }
    }
    
    try {
      const data: TokenResponse = {
        token: response.data.token,
        expiresAt: response.data.expiresAt,
      }
      
      // Update stored token
      await setAuthToken(data.token)
      
      return { kind: "ok", data }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid token response", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
  
  async forgotPassword(email: string): Promise<ApiResponse<void>> {
    const response = await this.api.apisauce.post("/auth/forgot-password", {
      email,
    })
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: getErrorMessage(problem), 
          problem 
        }
      }
    }
    
    return { kind: "ok", data: undefined }
  }
  
  async resetPassword(token: string, newPassword: string): Promise<ApiResponse<void>> {
    const response = await this.api.apisauce.post("/auth/reset-password", {
      token,
      password: newPassword,
    })
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: getErrorMessage(problem), 
          problem 
        }
      }
    }
    
    return { kind: "ok", data: undefined }
  }
}
```

## 2. Course Service

### Course API
```typescript
// app/services/api/endpoints/courses.ts
export class CourseApi {
  constructor(private api: Api) {}
  
  async getCourses(params?: {
    page?: number
    limit?: number
    category?: string
    search?: string
  }): Promise<ApiResponse<PaginatedResponse<Course>>> {
    const response = await this.api.apisauce.get("/courses", params)
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: "Failed to fetch courses", 
          problem 
        }
      }
    }
    
    try {
      const courses = response.data.data.map(transformCourse)
      const data: PaginatedResponse<Course> = {
        data: courses,
        pagination: response.data.pagination,
      }
      return { kind: "ok", data }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid courses data", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
  
  async getCourse(id: string): Promise<ApiResponse<Course>> {
    const response = await this.api.apisauce.get(`/courses/${id}`)
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: "Course not found", 
          problem 
        }
      }
    }
    
    try {
      const course = transformCourse(response.data)
      return { kind: "ok", data: course }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid course data", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
  
  async enrollInCourse(courseId: string): Promise<ApiResponse<Enrollment>> {
    const response = await this.api.apisauce.post(`/courses/${courseId}/enroll`)
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: "Failed to enroll in course", 
          problem 
        }
      }
    }
    
    try {
      const enrollment = transformEnrollment(response.data)
      return { kind: "ok", data: enrollment }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid enrollment data", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
  
  async getCourseLessons(courseId: string): Promise<ApiResponse<Lesson[]>> {
    const response = await this.api.apisauce.get(`/courses/${courseId}/lessons`)
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: "Failed to fetch lessons", 
          problem 
        }
      }
    }
    
    try {
      const lessons = response.data.map(transformLesson)
      return { kind: "ok", data: lessons }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid lessons data", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
}
```

## 3. User Service

### User API
```typescript
// app/services/api/endpoints/user.ts
export class UserApi {
  constructor(private api: Api) {}
  
  async getProfile(): Promise<ApiResponse<User>> {
    const response = await this.api.apisauce.get("/user/profile")
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: "Failed to fetch profile", 
          problem 
        }
      }
    }
    
    try {
      const user = transformUser(response.data)
      return { kind: "ok", data: user }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid profile data", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
  
  async updateProfile(updates: Partial<User>): Promise<ApiResponse<User>> {
    const response = await this.api.apisauce.put("/user/profile", updates)
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: "Failed to update profile", 
          problem 
        }
      }
    }
    
    try {
      const user = transformUser(response.data)
      return { kind: "ok", data: user }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid profile data", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
  
  async getEnrollments(): Promise<ApiResponse<Enrollment[]>> {
    const response = await this.api.apisauce.get("/user/enrollments")
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: "Failed to fetch enrollments", 
          problem 
        }
      }
    }
    
    try {
      const enrollments = response.data.map(transformEnrollment)
      return { kind: "ok", data: enrollments }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid enrollments data", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
  
  async getProgress(): Promise<ApiResponse<UserProgress>> {
    const response = await this.api.apisauce.get("/user/progress")
    
    if (!response.ok) {
      const problem = getGeneralApiProblem(response)
      if (problem) {
        return { 
          kind: "error", 
          message: "Failed to fetch progress", 
          problem 
        }
      }
    }
    
    try {
      const progress = transformUserProgress(response.data)
      return { kind: "ok", data: progress }
    } catch {
      return { 
        kind: "error", 
        message: "Invalid progress data", 
        problem: { kind: "bad-data", temporary: false } 
      }
    }
  }
}
```

## 4. Data Transformation Functions

### User Transformation
```typescript
const transformUser = (raw: any): User => ({
  id: raw.id,
  email: raw.email,
  name: raw.name,
  avatar: raw.avatar_url,
  createdAt: new Date(raw.created_at),
  updatedAt: new Date(raw.updated_at),
  preferences: raw.preferences || {},
})
```

### Course Transformation
```typescript
const transformCourse = (raw: any): Course => ({
  id: raw.id,
  title: raw.title,
  description: raw.description,
  instructor: raw.instructor,
  thumbnail: raw.thumbnail_url,
  duration: raw.duration,
  difficulty: raw.difficulty,
  category: raw.category,
  lessons: raw.lessons_count,
  students: raw.students_count,
  rating: raw.rating,
  price: raw.price,
  createdAt: new Date(raw.created_at),
  updatedAt: new Date(raw.updated_at),
})
```

### Lesson Transformation
```typescript
const transformLesson = (raw: any): Lesson => ({
  id: raw.id,
  title: raw.title,
  description: raw.description,
  duration: raw.duration,
  videoUrl: raw.video_url,
  order: raw.order,
  isLocked: raw.is_locked,
  isCompleted: raw.is_completed,
  createdAt: new Date(raw.created_at),
})
```

### Enrollment Transformation
```typescript
const transformEnrollment = (raw: any): Enrollment => ({
  id: raw.id,
  courseId: raw.course_id,
  userId: raw.user_id,
  progress: raw.progress,
  completedLessons: raw.completed_lessons,
  totalLessons: raw.total_lessons,
  enrolledAt: new Date(raw.enrolled_at),
  lastAccessedAt: raw.last_accessed_at ? new Date(raw.last_accessed_at) : null,
})
```

## 5. Service Factory

### API Service Factory
```typescript
// app/services/api/index.ts
export class ApiService {
  private api: Api
  
  public auth: AuthApi
  public courses: CourseApi
  public user: UserApi
  
  constructor() {
    this.api = new Api()
    
    this.auth = new AuthApi(this.api)
    this.courses = new CourseApi(this.api)
    this.user = new UserApi(this.api)
  }
  
  // Utility methods
  setBaseURL(url: string) {
    this.api.apisauce.setBaseURL(url)
  }
  
  setTimeout(timeout: number) {
    this.api.apisauce.setTimeout(timeout)
  }
  
  addRequestTransform(transform: (request: any) => void) {
    this.api.apisauce.addRequestTransform(transform)
  }
  
  addResponseTransform(transform: (response: any) => void) {
    this.api.apisauce.addResponseTransform(transform)
  }
}

// Singleton instance
export const apiService = new ApiService()
```

## 6. Usage Examples

### Authentication Flow
```typescript
// Login example
const handleLogin = async (email: string, password: string) => {
  const response = await apiService.auth.login(email, password)
  
  if (response.kind === "ok") {
    // Store tokens and user data
    await setAuthToken(response.data.token)
    await setRefreshToken(response.data.refreshToken)
    
    // Navigate to main app
    navigation.navigate("Main")
  } else {
    // Show error message
    Alert.alert("Login Failed", response.message)
  }
}
```

### Course Fetching
```typescript
// Fetch courses with pagination
const fetchCourses = async (page = 1) => {
  setLoading(true)
  
  const response = await apiService.courses.getCourses({
    page,
    limit: 20,
    category: selectedCategory,
  })
  
  if (response.kind === "ok") {
    setCourses(response.data.data)
    setPagination(response.data.pagination)
  } else {
    showErrorMessage(response.message)
  }
  
  setLoading(false)
}
```

## 7. Quality Assurance

### API Service Checklist
- [ ] Consistent error handling across all services
- [ ] Proper data transformation implemented
- [ ] Authentication token management
- [ ] Request/response logging in development
- [ ] Timeout handling configured
- [ ] Network error handling implemented
- [ ] Data validation before transformation
- [ ] Proper TypeScript types defined

### Testing Considerations
- [ ] Mock API responses for testing
- [ ] Error scenario testing
- [ ] Network failure simulation
- [ ] Authentication flow testing
- [ ] Data transformation testing
- [ ] Pagination testing
- [ ] Cache invalidation testing
- [ ] Offline behavior testing

These API services provide a robust foundation for all data communication in eb-lms-app with consistent patterns and comprehensive error handling.
