---
description: Component architecture and code organization guidelines for eb-lms-app
globs: 
alwaysApply: true
---

# Component Architecture & Code Organization Guidelines

## 1. Core Principles

### Micro-Component Philosophy
The eb-lms-app follows a **micro-component architecture** where every feature is broken down into the smallest possible, reusable components for maximum maintainability and testability.

### File Size Constraints
- **Maximum file size:** 200 lines of code per file
- **Optimal file size:** 50-150 lines of code
- **Target:** Keep components as small as possible while maintaining functionality

### Single Responsibility Principle
- Each component should have **one clear purpose**
- Each file should handle **one specific concern**
- If a component grows beyond 200 lines, it must be split

## 2. Component Breakdown Strategy

### Feature Decomposition
Every feature must be decomposed into:

1. **Container Components** (50-100 lines)
   - Handle business logic and state management
   - Connect to stores/APIs
   - Orchestrate child components

2. **Presentation Components** (30-80 lines)
   - Handle UI rendering only
   - Receive data via props
   - No business logic

3. **Utility Components** (10-50 lines)
   - Single-purpose UI elements
   - Highly reusable
   - Minimal logic

4. **Hook Components** (20-100 lines)
   - Custom hooks for shared logic
   - State management
   - Side effects

### Example Feature Breakdown
```
LoginFeature/
├── containers/
│   ├── LoginContainer.tsx          (80 lines)
│   └── LoginFormContainer.tsx      (60 lines)
├── components/
│   ├── LoginForm.tsx              (120 lines)
│   ├── LoginButton.tsx            (40 lines)
│   ├── LoginInput.tsx             (50 lines)
│   ├── ForgotPasswordLink.tsx     (25 lines)
│   └── SocialLoginButtons.tsx     (90 lines)
├── hooks/
│   ├── useLoginForm.ts            (80 lines)
│   ├── useLoginValidation.ts      (60 lines)
│   └── useLoginSubmit.ts          (70 lines)
└── types/
    └── login.types.ts             (30 lines)
```

## 3. File Organization Rules

### Directory Structure
```
feature/
├── components/           # UI components (max 200 lines each)
├── containers/          # Logic containers (max 200 lines each)
├── hooks/              # Custom hooks (max 200 lines each)
├── utils/              # Utility functions (max 200 lines each)
├── types/              # Type definitions (max 200 lines each)
├── constants/          # Constants (max 100 lines each)
├── styles/             # Styled components (max 150 lines each)
└── __tests__/          # Test files (max 300 lines each)
```

### Naming Conventions
- **Components:** PascalCase (`LoginButton.tsx`)
- **Hooks:** camelCase with 'use' prefix (`useLoginForm.ts`)
- **Utils:** camelCase (`formatUserName.ts`)
- **Types:** PascalCase with '.types' suffix (`User.types.ts`)
- **Constants:** UPPER_SNAKE_CASE (`API_ENDPOINTS.ts`)

## 4. Component Splitting Guidelines

### When to Split a Component
Split immediately when:
- File exceeds 200 lines
- Component has multiple responsibilities
- Logic can be reused elsewhere
- Testing becomes complex
- Code review becomes difficult

### How to Split Components

#### 1. Extract Subcomponents
```typescript
// Before (150+ lines)
const UserProfile = () => {
  return (
    <View>
      <UserAvatar />
      <UserInfo />
      <UserActions />
      <UserSettings />
    </View>
  )
}

// After - Split into multiple files
// UserProfile.tsx (40 lines)
// UserAvatar.tsx (30 lines)
// UserInfo.tsx (50 lines)
// UserActions.tsx (60 lines)
// UserSettings.tsx (80 lines)
```

#### 2. Extract Custom Hooks
```typescript
// Before (200+ lines with logic)
const LoginForm = () => {
  // 100+ lines of form logic
  // 100+ lines of UI
}

// After
// useLoginForm.ts (80 lines)
// LoginForm.tsx (60 lines)
```

#### 3. Extract Utility Functions
```typescript
// Before (180+ lines)
const DataProcessor = () => {
  // 80 lines of processing logic
  // 100 lines of UI
}

// After
// dataProcessingUtils.ts (80 lines)
// DataProcessor.tsx (50 lines)
```

## 5. Implementation Standards

### Component Template (Max 200 lines)
```typescript
// ComponentName.tsx (Target: 50-150 lines)
import React from 'react'
import { View } from 'react-native'
import { useAppTheme } from '@/utils/useAppTheme'
import { ComponentNameProps } from './ComponentName.types'
import { $container, $content } from './ComponentName.styles'

export const ComponentName: React.FC<ComponentNameProps> = ({
  prop1,
  prop2,
  onAction,
}) => {
  const { themed } = useAppTheme()
  
  // Minimal logic only (max 20-30 lines)
  const handleAction = () => {
    onAction?.()
  }
  
  return (
    <View style={themed($container)}>
      <View style={themed($content)}>
        {/* Simple UI structure */}
      </View>
    </View>
  )
}

// Export types and styles if small enough
export type { ComponentNameProps }
```

### Hook Template (Max 200 lines)
```typescript
// useFeatureName.ts (Target: 50-150 lines)
import { useState, useEffect } from 'react'
import { FeatureState, FeatureActions } from './feature.types'

export const useFeatureName = (initialState?: FeatureState) => {
  // State management (max 50 lines)
  const [state, setState] = useState(initialState)
  
  // Effects (max 50 lines)
  useEffect(() => {
    // Side effects
  }, [])
  
  // Actions (max 50 lines)
  const actions: FeatureActions = {
    action1: () => {},
    action2: () => {},
  }
  
  // Return interface (max 20 lines)
  return {
    state,
    actions,
    // Other exports
  }
}
```

## 6. Code Splitting Strategies

### Lazy Loading Components
```typescript
// Large feature components should be lazy loaded
const LazyUserDashboard = lazy(() => import('./UserDashboard'))
const LazyUserSettings = lazy(() => import('./UserSettings'))

// Use Suspense for loading states
<Suspense fallback={<LoadingSpinner />}>
  <LazyUserDashboard />
</Suspense>
```

### Dynamic Imports
```typescript
// Split large utilities
const loadDataProcessor = () => import('./utils/dataProcessor')
const loadChartUtils = () => import('./utils/chartUtils')
```

## 7. Testing Strategy for Small Components

### Test File Organization
```
component/
├── Component.tsx              (100 lines)
├── Component.test.tsx         (150 lines)
├── Component.types.ts         (30 lines)
└── Component.styles.ts        (40 lines)
```

### Testing Small Components
- Each component gets its own test file
- Test files can be up to 300 lines (exception to 200-line rule)
- Focus on unit testing individual components
- Integration tests for component combinations

## 8. Performance Benefits

### Advantages of Micro-Components
- **Faster compilation:** Smaller files compile quicker
- **Better tree-shaking:** Unused components are easily eliminated
- **Improved caching:** Smaller files cache more efficiently
- **Easier debugging:** Issues are isolated to specific files
- **Better code splitting:** More granular lazy loading

### Bundle Optimization
- Each small component can be individually optimized
- Better dead code elimination
- More efficient hot reloading during development

## 9. Enforcement Rules

### Pre-commit Hooks
```bash
# Check file size before commit
if [ $(wc -l < "$file") -gt 200 ]; then
  echo "Error: $file exceeds 200 lines"
  exit 1
fi
```

### ESLint Rules
```json
{
  "rules": {
    "max-lines": ["error", { "max": 200, "skipBlankLines": true }],
    "max-lines-per-function": ["error", { "max": 50 }],
    "complexity": ["error", { "max": 10 }]
  }
}
```

### Code Review Checklist
- [ ] No file exceeds 200 lines
- [ ] Each component has single responsibility
- [ ] Logic is extracted to custom hooks when appropriate
- [ ] Utility functions are in separate files
- [ ] Component can be easily tested in isolation

## 10. Migration Strategy

### Existing Large Components
1. **Identify** components over 200 lines
2. **Analyze** responsibilities and extract subcomponents
3. **Create** new small component files
4. **Refactor** gradually with proper testing
5. **Remove** old large component files

### Gradual Implementation
- Start with new features using micro-component architecture
- Refactor existing components during feature updates
- Set team goals for component size reduction
- Regular code reviews to enforce standards

This micro-component architecture ensures the eb-lms-app remains maintainable, testable, and scalable as it grows in complexity.
