---
description: Comprehensive overview of the eb-lms-app React Native project
globs: 
alwaysApply: true
---

# EB LMS App - Project Overview

## 1. Project Information

**Project Name:** eb-lms-app  
**Version:** 0.0.1  
**Type:** React Native Mobile Application (Learning Management System)  
**Framework:** Expo SDK 52.0.44  
**Platform Support:** iOS, Android, Web  

## 2. Technology Stack

### Core Technologies
- **React Native:** 0.76.9
- **React:** 18.3.1
- **Expo:** ^52.0.44
- **TypeScript:** ~5.3.3
- **Node.js:** ^18.18.0 || >=20.0.0

### Navigation & UI
- **Navigation:** React Navigation v7 (Native Stack, Bottom Tabs)
- **UI Components:** Custom component library with theming
- **Animations:** React Native Reanimated ~3.16.1
- **Gestures:** React Native Gesture Handler ~2.20.2
- **Lists:** Shopify FlashList 1.7.3

### State Management
- **State Management:** MobX State Tree 5.3.0
- **Reactive Programming:** MobX 6.10.2
- **React Integration:** MobX React Lite 4.0.5

### Internationalization
- **i18n Framework:** i18next ^23.14.0
- **React Integration:** react-i18next ^15.0.1
- **Localization:** expo-localization ~16.0.0
- **Supported Languages:** English, Arabic, Spanish, French, Hindi, Japanese, Korean

### Storage & Data
- **Local Storage:** React Native MMKV ^2.12.2
- **API Client:** Apisauce 3.0.1
- **Date Handling:** date-fns ^4.1.0

### Development Tools
- **Debugging:** Reactotron (Core, MST, React Native)
- **Testing:** Jest ^29.2.1, React Testing Library ^12.5.2
- **Linting:** ESLint ^8.57.0 with Expo config
- **Code Formatting:** Prettier ^3.3.3
- **Type Checking:** TypeScript with strict configuration

## 3. Project Architecture

### Directory Structure
```
eb-lms-app/
├── app/                    # Main application code
│   ├── components/         # Reusable UI components
│   ├── screens/           # Screen components
│   ├── navigators/        # Navigation configuration
│   ├── models/            # MobX State Tree models
│   ├── services/          # API and external services
│   ├── theme/             # Design system (colors, typography, spacing)
│   ├── utils/             # Utility functions and hooks
│   ├── i18n/              # Internationalization files
│   ├── config/            # App configuration
│   └── devtools/          # Development tools setup
├── assets/                # Static assets (images, icons)
├── ios/                   # iOS native code
├── android/               # Android native code
├── plugins/               # Expo config plugins
└── test/                  # Test configuration and utilities
```

### Architecture Patterns
- **Component-Based Architecture:** Modular, reusable components
- **State Management:** Centralized state with MobX State Tree
- **Theming System:** Comprehensive design system with light/dark mode
- **Navigation:** Stack-based navigation with type safety
- **Internationalization:** Multi-language support with dynamic loading

## 4. Key Features

### Core Functionality
- **Learning Management System:** Educational content delivery
- **Multi-platform Support:** iOS, Android, and Web
- **Offline Capability:** Local storage with MMKV
- **Internationalization:** Support for 7 languages
- **Theming:** Light and dark mode support
- **Authentication:** User authentication system

### UI/UX Features
- **Custom Component Library:** Consistent design system
- **Responsive Design:** Adaptive layouts for different screen sizes
- **Accessibility:** Screen reader support and keyboard navigation
- **Smooth Animations:** React Native Reanimated for performance
- **Gesture Support:** Touch interactions and gestures

## 5. Development Workflow

### Available Scripts
- **Development:** `npm start` - Start Expo dev server
- **Platform Specific:** `npm run ios`, `npm run android`, `npm run web`
- **Building:** EAS Build profiles for development, preview, and production
- **Testing:** `npm test` - Run Jest tests
- **Linting:** `npm run lint` - ESLint with auto-fix
- **Type Checking:** `npm run compile` - TypeScript compilation

### Build Profiles
- **Development:** Local development builds with dev client
- **Preview:** Internal testing builds
- **Production:** App store ready builds

## 6. Configuration

### Environment Support
- **Development:** Local development with hot reload
- **Staging:** Preview builds for testing
- **Production:** Optimized builds for app stores

### Platform Configuration
- **iOS:** Xcode project with privacy manifests
- **Android:** Gradle build system
- **Web:** Expo web support with Metro bundler

## 7. Quality Assurance

### Testing Strategy
- **Unit Tests:** Jest with React Testing Library
- **Type Safety:** TypeScript with strict mode
- **Code Quality:** ESLint + Prettier
- **E2E Testing:** Maestro test flows

### Performance Optimization
- **Bundle Optimization:** Metro bundler with tree shaking
- **Image Optimization:** Multiple resolution assets
- **Memory Management:** Efficient state management with MST
- **List Performance:** FlashList for large datasets

## 8. Deployment

### Build System
- **EAS Build:** Expo Application Services for cloud builds
- **Local Builds:** Support for local development builds
- **CI/CD Ready:** Configured for continuous integration

### Distribution
- **iOS:** App Store deployment ready
- **Android:** Google Play Store deployment ready
- **Web:** Static web deployment support
