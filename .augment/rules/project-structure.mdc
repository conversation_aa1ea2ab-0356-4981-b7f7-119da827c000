---
description: Detailed project structure and file organization guidelines for eb-lms-app
globs: 
alwaysApply: true
---

# Project Structure Guidelines

## 1. Root Directory Structure

```
eb-lms-app/
├── .augment/              # Augment AI configuration and rules
├── app/                   # Main application source code
├── assets/                # Static assets (images, icons, fonts)
├── android/               # Android native code and configuration
├── ios/                   # iOS native code and configuration
├── plugins/               # Expo config plugins
├── test/                  # Test configuration and utilities
├── types/                 # Global TypeScript type definitions
├── node_modules/          # Dependencies (auto-generated)
├── package.json           # Project dependencies and scripts
├── app.config.ts          # Expo configuration
├── app.json               # Static Expo configuration
├── tsconfig.json          # TypeScript configuration
├── babel.config.js        # Babel configuration
├── metro.config.js        # Metro bundler configuration
├── jest.config.js         # Jest testing configuration
├── eas.json               # EAS Build configuration
└── README.md              # Project documentation
```

## 2. App Directory Structure

### `/app` - Main Application Code

```
app/
├── app.tsx                # Root app component
├── components/            # Reusable UI components
│   ├── AutoImage.tsx      # Automatic image sizing component
│   ├── Button.tsx         # Custom button component
│   ├── Card.tsx           # Card container component
│   ├── EmptyState.tsx     # Empty state display component
│   ├── Header.tsx         # Screen header component
│   ├── Icon.tsx           # Icon component with type safety
│   ├── ListItem.tsx       # List item component
│   ├── ListView.tsx       # Optimized list view component
│   ├── Screen.tsx         # Base screen wrapper component
│   ├── Text.tsx           # Typography component with theming
│   ├── TextField.tsx      # Input field component
│   ├── Toggle/            # Toggle switch component directory
│   └── index.ts           # Component exports
├── screens/               # Screen components
│   ├── WelcomeScreen.tsx  # Welcome/landing screen
│   ├── LoginScreen.tsx    # Authentication screen
│   ├── DemoShowroomScreen/ # Demo showcase screen directory
│   ├── DemoCommunityScreen.tsx
│   ├── DemoDebugScreen.tsx
│   ├── DemoPodcastListScreen.tsx
│   ├── ErrorScreen/       # Error handling screen directory
│   └── index.ts           # Screen exports
├── navigators/            # Navigation configuration
│   ├── AppNavigator.tsx   # Main app navigation
│   ├── DemoNavigator.tsx  # Demo section navigation
│   ├── navigationUtilities.ts # Navigation helper functions
│   └── index.ts           # Navigator exports
├── models/                # MobX State Tree models
│   ├── RootStore.ts       # Root store configuration
│   ├── AuthenticationStore.ts # Authentication state
│   ├── Episode.ts         # Episode model (demo)
│   ├── EpisodeStore.ts    # Episode store (demo)
│   ├── helpers/           # Model helper functions
│   └── index.ts           # Model exports
├── services/              # External services and APIs
│   └── api/               # API service configuration
├── theme/                 # Design system
│   ├── colors.ts          # Color palette (light mode)
│   ├── colorsDark.ts      # Color palette (dark mode)
│   ├── typography.ts      # Font and text styling
│   ├── spacing.ts         # Spacing system (light mode)
│   ├── spacingDark.ts     # Spacing system (dark mode)
│   ├── styles.ts          # Common style utilities
│   ├── timing.ts          # Animation timing constants
│   └── index.ts           # Theme exports
├── utils/                 # Utility functions and hooks
│   ├── useAppTheme.ts     # Theme context and hooks
│   ├── useHeader.tsx      # Header configuration hook
│   ├── useIsMounted.ts    # Component mount status hook
│   ├── useSafeAreaInsetsStyle.ts # Safe area styling hook
│   ├── formatDate.ts      # Date formatting utilities
│   ├── delay.ts           # Async delay utility
│   ├── crashReporting.ts  # Error reporting utilities
│   ├── openLinkInBrowser.ts # External link handling
│   ├── gestureHandler.ts  # Gesture handling utilities
│   └── storage/           # Local storage utilities
├── i18n/                  # Internationalization
│   ├── i18n.ts            # i18n configuration
│   ├── translate.ts       # Translation utilities
│   ├── en.ts              # English translations
│   ├── ar.ts              # Arabic translations
│   ├── es.ts              # Spanish translations
│   ├── fr.ts              # French translations
│   ├── hi.ts              # Hindi translations
│   ├── ja.ts              # Japanese translations
│   ├── ko.ts              # Korean translations
│   ├── demo-*.ts          # Demo translations for each language
│   └── index.ts           # i18n exports
├── config/                # Application configuration
│   ├── config.base.ts     # Base configuration
│   ├── config.dev.ts      # Development configuration
│   ├── config.prod.ts     # Production configuration
│   └── index.ts           # Config exports
└── devtools/              # Development tools
    ├── ReactotronConfig.ts # Reactotron setup
    ├── ReactotronClient.ts # Reactotron client
    └── ReactotronClient.web.ts # Web-specific Reactotron
```

## 3. Assets Directory Structure

```
assets/
├── icons/                 # Application icons
│   ├── back.png           # Navigation back icon
│   ├── bell.png           # Notification icon
│   ├── caretLeft.png      # Left caret icon
│   ├── caretRight.png     # Right caret icon
│   ├── check.png          # Checkmark icon
│   ├── hidden.png         # Hide/visibility icon
│   ├── ladybug.png        # Debug icon
│   ├── lock.png           # Security/lock icon
│   ├── menu.png           # Menu hamburger icon
│   ├── more.png           # More options icon
│   ├── settings.png       # Settings gear icon
│   ├── view.png           # View/eye icon
│   ├── x.png              # Close/cancel icon
│   └── demo/              # Demo-specific icons
└── images/                # Application images
    ├── app-icon-all.png   # Universal app icon
    ├── app-icon-android-adaptive-background.png
    ├── app-icon-android-adaptive-foreground.png
    ├── app-icon-android-legacy.png
    ├── app-icon-ios.png   # iOS app icon
    ├── app-icon-web-favicon.png # Web favicon
    ├── logo.png           # Application logo
    ├── sad-face.png       # Error state illustration
    ├── welcome-face.png   # Welcome screen illustration
    └── demo/              # Demo-specific images
```

## 4. File Naming Conventions

### Components
- **PascalCase** for component files: `Button.tsx`, `TextField.tsx`
- **camelCase** for utility files: `formatDate.ts`, `useAppTheme.ts`
- **kebab-case** for asset files: `welcome-face.png`, `app-icon-ios.png`

### Directories
- **camelCase** for code directories: `components`, `navigators`
- **kebab-case** for asset directories when needed

### File Extensions
- **`.tsx`** for React components
- **`.ts`** for TypeScript utilities and configurations
- **`.js`** for JavaScript configuration files
- **`.json`** for data and configuration files

## 5. Import/Export Patterns

### Index Files
Each major directory should have an `index.ts` file that exports public APIs:

```typescript
// app/components/index.ts
export * from "./Button"
export * from "./Text"
export * from "./Screen"
// ... other exports
```

### Import Organization
1. React and React Native imports
2. Third-party library imports
3. Internal app imports (components, utils, etc.)
4. Relative imports
5. Type-only imports (with `type` keyword)

## 6. Platform-Specific Files

### Native Directories
- **`/ios`** - iOS-specific native code and configuration
- **`/android`** - Android-specific native code and configuration

### Platform Extensions
- **`.native.ts`** - React Native specific implementations
- **`.web.ts`** - Web-specific implementations
- **`.ios.ts`** - iOS-specific implementations
- **`.android.ts`** - Android-specific implementations

## 7. Configuration Files

### Root Level Configuration
- **`package.json`** - Dependencies and scripts
- **`tsconfig.json`** - TypeScript compiler options
- **`babel.config.js`** - Babel transformation rules
- **`metro.config.js`** - Metro bundler configuration
- **`jest.config.js`** - Jest testing framework setup
- **`eas.json`** - Expo Application Services build configuration

### App Configuration
- **`app.config.ts`** - Dynamic Expo configuration
- **`app.json`** - Static Expo configuration
- **`app/config/`** - Environment-specific app settings

## 8. Best Practices

### File Organization
- Keep related files together in logical directories
- Use index files to create clean import paths
- Separate concerns (UI, logic, data, configuration)
- Group by feature when appropriate

### Naming Consistency
- Use descriptive, self-documenting names
- Follow established conventions consistently
- Avoid abbreviations unless widely understood
- Use consistent prefixes for related files

### Dependency Management
- Keep dependencies organized by type (dependencies vs devDependencies)
- Use exact versions for critical dependencies
- Regular dependency updates and security audits
- Document any special dependency requirements
