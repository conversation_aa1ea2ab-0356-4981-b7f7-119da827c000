---
description: Complete UI design system summary for eb-lms-app based on visual design analysis
globs:
alwaysApply: true
---

# UI Design System Summary

## 1. Design Analysis Overview

Based on the comprehensive analysis of the provided UI design, the eb-lms-app follows a clean, professional learning management system design with these core principles:

### Visual Design Principles
- **Clean White Backgrounds:** All screens and cards use white (#FFFFFF) as the primary background
- **Orange Progress Accents:** Orange/amber (#FFBB50) for progress indicators and interactive elements
- **Blue Brand Identity:** Deep navy (#132339) for navigation and primary text elements
- **Card-Based Information Architecture:** Content organized in clean white cards with subtle shadows
- **Consistent Typography Hierarchy:** Light weight (300) for descriptions, semiBold for titles
- **8px Grid System:** All spacing follows multiples of 8px for consistency

## 2. Core Design Tokens

### Color Palette
```typescript
// Primary brand colors
const brandColors = {
  primaryDark: "#132339",    // Deep navy - navigation, primary text
  primaryMedium: "#23408B",  // Royal blue - buttons, links
  primaryLight: "#599FD6",   // Sky blue - accents, highlights
}

// Accent and progress colors
const accentColors = {
  progress: "#FFBB50",       // Orange - progress bars, play buttons
  success: "#10B981",        // Green - completed states
  warning: "#F59E0B",        // Amber - pending states
}

// Background colors (MANDATORY WHITE RULE)
const backgroundColors = {
  screen: "#FFFFFF",         // Main screen background - ALWAYS WHITE
  card: "#FFFFFF",           // Card backgrounds - ALWAYS WHITE
  accent: "#F8F9FA",         // Subtle accent backgrounds (rare use)
}
```

### Typography System
```typescript
// Font weights (Nunito Sans)
const fontWeights = {
  light: 300,      // MANDATORY for ALL descriptions
  regular: 400,    // Body text
  medium: 500,     // Labels, navigation
  semiBold: 600,   // Section headings, card titles
  bold: 700,       // Page titles, emphasis
}

// Font sizes
const fontSizes = {
  xs: 12,   // Captions, fine print
  sm: 14,   // Descriptions, secondary text
  md: 16,   // Body text, default
  lg: 18,   // Subheadings
  xl: 24,   // Page titles
  xxl: 32,  // Hero text
}
```

### Spacing System
```typescript
// 8px grid system
const spacing = {
  xs: 4,    // Minimal spacing
  sm: 8,    // Small spacing
  md: 16,   // Standard spacing (MOST COMMON)
  lg: 24,   // Section spacing
  xl: 32,   // Large spacing
  xxl: 48,  // Maximum spacing
}
```

## 3. Component Design Patterns

### Course Information Card
**Visual Pattern:** White card with course details and student avatars
- **Background:** White (#FFFFFF) with subtle shadow
- **Border Radius:** 12px
- **Padding:** 16px all sides
- **Student Avatars:** Overlapping circles with white borders
- **Progress Elements:** Orange accent color

### Progress Statistics Cards
**Visual Pattern:** Colored cards showing numerical data
- **Colors:** Dark blue (#132339), Orange (#FFBB50), Light gray (#F4F2F1)
- **Text:** White on dark backgrounds, dark on light
- **Layout:** Grid arrangement with equal spacing
- **Typography:** Bold numbers, light labels

### Video Lesson List
**Visual Pattern:** List items with play buttons and metadata
- **Play Button:** Orange circular button (#FFBB50)
- **Spacing:** 16px vertical between items
- **Typography:** Regular titles, light descriptions
- **Icons:** Gray for locked content

### User Profile Header
**Visual Pattern:** Horizontal layout with avatar and info
- **Avatar:** 48px circular with subtle border
- **Greeting:** Light weight (300) typography
- **Layout:** Space-between alignment
- **Background:** White with minimal spacing

## 4. Layout Patterns

### Screen Layout Structure
```typescript
// Standard screen container
const $screenContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flex: 1,
  backgroundColor: colors.background, // Always white
  paddingHorizontal: spacing.md,      // 16px from edges
})

// Content sections
const $contentSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg, // 24px between sections
})

// Card containers
const $cardContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#FFFFFF",
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  marginBottom: spacing.md,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
})
```

### Grid Layout Patterns
```typescript
// Two-column course grid
const $courseGrid: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  flexWrap: "wrap",
  gap: spacing.md, // 16px gap
  justifyContent: "space-between",
})

const $courseGridItem: ThemedStyle<ViewStyle> = () => ({
  width: "48%", // Two columns with gap
  aspectRatio: 1.2, // Slightly rectangular
})
```

## 5. Navigation Design Patterns

### Bottom Tab Navigation
- **Background:** White (#FFFFFF) with top border
- **Active Color:** Orange (#FFBB50)
- **Inactive Color:** Gray (#978F8A)
- **Height:** 60px with safe area padding

### Header Navigation
- **Background:** White (#FFFFFF) with bottom border
- **Height:** 56px standard
- **Title:** Center-aligned, semiBold weight
- **Icons:** Dark blue (#132339) for contrast

## 6. Interactive Element Patterns

### Button Design
```typescript
// Primary button (Login, Welcome, Reset Password screens)
const $primaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.primary500, // Royal blue
  borderRadius: 16, // Updated to match Login/Welcome screens
  height: 44,
  minHeight: 44,
  paddingHorizontal: 12,
  alignItems: "center",
  justifyContent: "center",
  shadowColor: colors.palette.primary700,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 3,
})

// Secondary button (outlined style)
const $secondaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background, // White
  borderColor: colors.palette.neutral300,
  borderWidth: 1,
  borderRadius: 16, // Match primary button
  height: 44,
  minHeight: 44,
  paddingHorizontal: 12,
  alignItems: "center",
  justifyContent: "center",
  shadowColor: colors.palette.neutral400,
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.1,
  shadowRadius: 2,
  elevation: 1,
})

// Social login button
const $socialButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background, // White
  borderColor: colors.palette.neutral300,
  borderWidth: 1,
  borderRadius: 12, // Slightly less rounded
  height: 44,
  minHeight: 44,
  paddingHorizontal: 12,
  alignItems: "center",
  justifyContent: "center",
})

// Play button (special case)
const $playButton: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#FFBB50", // Orange - MANDATORY accent color
  borderRadius: 20, // Circular
  width: 40,
  height: 40,
  alignItems: "center",
  justifyContent: "center",
})

// Button text styling (MANDATORY font weight 300)
const $buttonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Font weight 300 - MANDATORY
  fontSize: 16,
  textAlign: "center",
})
```

### Progress Indicators
```typescript
const $progressBar: ThemedStyle<ViewStyle> = () => ({
  height: 6,
  backgroundColor: "#F4F2F1", // Light gray background
  borderRadius: 3,
  overflow: "hidden",
})

const $progressFill: ThemedStyle<ViewStyle> = () => ({
  height: "100%",
  backgroundColor: "#FFBB50", // Orange progress
  borderRadius: 3,
})
```

## 7. Critical Design Rules

### 🚨 MANDATORY WHITE BACKGROUND RULE
- **Screen Backgrounds:** MUST always use white (#FFFFFF)
- **Card Backgrounds:** MUST always use white (#FFFFFF)
- **Override Policy:** Only change when explicitly requested by user
- **Rationale:** Ensures clean, professional appearance and optimal readability

### 🚨 MANDATORY DESCRIPTION TEXT RULE
- **Font Weight:** MUST always use light weight (300) for ALL descriptions
- **Consistency:** All captions, explanations, and secondary text use light weight
- **Override Policy:** Only change when explicitly requested by user
- **Rationale:** Creates clear visual hierarchy and optimal reading experience

### 🚨 ORANGE ACCENT COLOR RULE
- **Progress Elements:** MUST use orange (#FFBB50) for all progress indicators
- **Interactive Elements:** Use orange for play buttons and highlights
- **Consistency:** Maintain orange as the primary accent throughout the app

## 8. Implementation Guidelines

### Component Development
1. **Always use white backgrounds** for screens and cards
2. **Apply light font weight (300)** for all descriptions
3. **Use orange accent color** for progress and interactive elements
4. **Follow 16px standard spacing** between components
5. **Implement 12px border radius** for cards
6. **Use 16px border radius** for main action buttons (Login, Welcome, Reset Password)
7. **Use 12px border radius** for social login buttons
8. **Apply subtle shadows** for card elevation

### Code Patterns
```typescript
// Standard component structure
export const LMSComponent = (props: ComponentProps) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <Text style={themed($title)}>Title</Text>
      <Text style={themed($description)}>Description</Text>
    </View>
  )
}

// Themed styles
const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#FFFFFF", // MANDATORY white background
  borderRadius: 12,
  padding: spacing.md, // 16px standard padding
})

const $title: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.semiBold, // 600 weight
  fontSize: 16,
  color: colors.text,
})

const $description: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 14,
  color: colors.textDim,
})
```

## 9. Quality Assurance Checklist

### Visual Design Compliance
- [ ] White background used for all screens and cards
- [ ] Light font weight (300) applied to all descriptions and button text
- [ ] Orange accent color (#FFBB50) used for progress elements
- [ ] 16px spacing applied between components
- [ ] 12px border radius used for cards
- [ ] 16px border radius used for main action buttons (Login, Welcome, Reset Password)
- [ ] 12px border radius used for social login buttons
- [ ] Subtle shadows applied for elevation
- [ ] Typography hierarchy maintained
- [ ] Color contrast meets accessibility standards

### Component Implementation
- [ ] TypeScript interfaces defined
- [ ] Themed styles implemented
- [ ] Accessibility props included
- [ ] Responsive considerations addressed
- [ ] Performance optimizations applied
- [ ] Error handling implemented
- [ ] Tests written and passing

### Design System Adherence
- [ ] Follows established color palette
- [ ] Uses consistent spacing system
- [ ] Maintains typography hierarchy
- [ ] Implements proper navigation patterns
- [ ] Follows layout guidelines
- [ ] Respects component design patterns

## 10. Design System Benefits

### Consistency
- **Visual Cohesion:** Unified appearance across all screens
- **User Experience:** Predictable interactions and layouts
- **Brand Identity:** Strong, recognizable design language

### Maintainability
- **Code Reusability:** Standardized components and patterns
- **Easy Updates:** Centralized design token management
- **Scalability:** Clear guidelines for new feature development

### Accessibility
- **Color Contrast:** WCAG 2.1 AA compliance
- **Typography:** Optimized for readability
- **Touch Targets:** Minimum 44px for interactive elements

This design system ensures the eb-lms-app maintains a professional, consistent, and user-friendly interface that supports effective learning experiences.
