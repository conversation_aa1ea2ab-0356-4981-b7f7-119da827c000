---
description: Spacing and layout system for eb-lms-app based on 8px grid
globs:
alwaysApply: true
---

# Spacing & Layout System

## Overview

This document defines the comprehensive spacing and layout system for eb-lms-app based on an 8px grid system. All spacing values must be multiples of 8px for consistency and visual harmony.

## 1. Spacing Scale

### Base Spacing Tokens
```typescript
export const spacing = {
  xs: 8,   // Extra small - Internal padding, small gaps
  sm: 12,  // Small - MANDATORY horizontal padding from screen edges
  md: 16,  // Medium - Standard spacing between components
  lg: 24,  // Large - Section spacing, major gaps
  xl: 32,  // Extra large - Screen sections, major divisions
  xxl: 48, // Double extra large - Major layout sections
  xxxl: 64, // Triple extra large - Hero sections, major spacing
}
```

### Usage Guidelines
- **xs (8px):** Icon padding, small internal spacing
- **sm (12px):** MANDATORY horizontal padding from screen edges
- **md (16px):** Standard gap between components, button spacing
- **lg (24px):** Section spacing, major component separation
- **xl (32px):** Screen section divisions
- **xxl (48px):** Major layout sections
- **xxxl (64px):** Hero sections, welcome screen spacing

## 2. Horizontal Padding Rules

### MANDATORY Screen Edge Padding
```typescript
// All screens MUST have 12px horizontal padding
const $screenContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  flex: 1,
})

// Components within screens
const $componentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginHorizontal: spacing.sm, // 12px from screen edges
  marginVertical: spacing.md,   // 16px vertical spacing
})
```

### Container Padding Standards
```typescript
// Card internal padding
const $cardPadding: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.md, // 16px internal padding
})

// Button padding
const $buttonPadding: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px horizontal
  paddingVertical: spacing.sm,   // 12px vertical
})

// Input field padding
const $inputPadding: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.md, // 16px horizontal
  paddingVertical: spacing.sm,   // 12px vertical
})
```

## 3. Vertical Spacing

### Component Spacing
```typescript
// Between stacked components
const $componentSpacing: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md, // 16px between components
})

// Between sections
const $sectionSpacing: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg, // 24px between sections
})

// Between major layout areas
const $layoutSpacing: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xl, // 32px between major areas
})
```

### List Item Spacing
```typescript
// List items
const $listItemSpacing: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md, // 16px between list items
  paddingVertical: spacing.sm, // 12px internal vertical padding
})

// List sections
const $listSectionSpacing: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg, // 24px between list sections
})
```

## 4. Layout Patterns

### Screen Layout Structure
```typescript
// Standard screen layout
const $screenLayout: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
  paddingTop: spacing.lg, // 24px top spacing
  paddingBottom: spacing.md, // 16px bottom spacing
})

// Content container
const $contentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  gap: spacing.md, // 16px gap between child elements
})

// Header section
const $headerSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg, // 24px below header
  alignItems: "center",
})

// Footer section
const $footerSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.lg, // 24px above footer
  paddingTop: spacing.md, // 16px internal top padding
})
```

### Card Layout
```typescript
// Card container
const $cardContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#FFFFFF", // White background
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  marginBottom: spacing.md, // 16px between cards
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
})

// Card header
const $cardHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm, // 12px below header
  alignItems: "center",
})

// Card content
const $cardContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.xs, // 8px gap between content elements
})
```

## 5. Button Spacing

### Button Layout
```typescript
// Single button
const $singleButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  height: 44, // Standard button height
  paddingHorizontal: spacing.sm, // 12px horizontal padding
  marginVertical: spacing.sm, // 12px vertical margin
})

// Stacked buttons
const $stackedButtons: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.md, // 16px gap between stacked buttons
})

// Horizontal button group
const $horizontalButtons: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  gap: spacing.sm, // 12px gap between horizontal buttons
  justifyContent: "space-between",
})
```

### Button Container
```typescript
// Button section container
const $buttonContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.lg, // 24px above button section
  paddingHorizontal: spacing.sm, // 12px horizontal padding
  gap: spacing.md, // 16px gap between buttons
})
```

## 6. Form Spacing

### Auth Screen Form Layout (Current Implementation)
```typescript
// Auth form container (Login, Signup, Forgot Password)
const $authFormContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Exact padding from current auth screens (Figma x:34)
  alignItems: "center", // Center all form elements
})

// Standard form container
const $formContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.md, // 16px gap between form fields
  paddingHorizontal: spacing.sm, // 12px horizontal padding
})

// Form field
const $formField: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md, // 16px below each field
})

// Form section
const $formSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg, // 24px between form sections
})

// Form actions
const $formActions: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.lg, // 24px above form actions
  gap: spacing.md, // 16px gap between action buttons
})
```

## 7. Welcome Screen Specific Spacing

### Welcome Layout Proportions
```typescript
// Welcome screen layout
const $welcomeContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  backgroundColor: "#FFFFFF", // White background
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
})

// Top content area (70% of screen)
const $welcomeTopContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 0.7,
  justifyContent: "center",
  alignItems: "center",
  paddingVertical: spacing.lg, // 24px vertical padding
})

// Hero section
const $welcomeHeroSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  gap: spacing.md, // 16px gap between hero elements
  paddingHorizontal: spacing.xs, // 8px additional internal padding
})

// Bottom button area (30% of screen)
const $welcomeBottomContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 0.3,
  justifyContent: "center",
  paddingVertical: spacing.lg, // 24px vertical padding
  gap: spacing.md, // 16px gap between buttons
})
```

## 8. Responsive Spacing

### Platform-Specific Adjustments
```typescript
import { Platform } from "react-native"

const $responsiveSpacing: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: Platform.select({
    ios: spacing.sm,     // 12px on iOS
    android: spacing.sm, // 12px on Android
    web: spacing.md,     // 16px on web for better desktop experience
  }),
})

// Large screen adjustments
const $largeScreenSpacing: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.lg, // 24px on large screens
  maxWidth: 600, // Constrain content width
  alignSelf: "center",
})
```

## 9. Quality Assurance

### Spacing Compliance Checklist
- [ ] All spacing values are multiples of 8px
- [ ] 12px horizontal padding from screen edges (MANDATORY)
- [ ] 16px standard gap between components
- [ ] 24px spacing between major sections
- [ ] 44px minimum touch targets for buttons
- [ ] Consistent card padding (16px internal)
- [ ] Proper form field spacing (16px gaps)
- [ ] Welcome screen proportions (70/30 split)

### Layout Quality Standards
- [ ] White backgrounds used consistently (MANDATORY)
- [ ] Proper safe area handling
- [ ] Responsive design considerations
- [ ] Accessibility-compliant touch targets
- [ ] Consistent visual hierarchy
- [ ] Proper content alignment
- [ ] Smooth scrolling behavior
- [ ] Performance-optimized layouts

This spacing system ensures visual consistency, accessibility, and professional appearance across all screens in eb-lms-app.
