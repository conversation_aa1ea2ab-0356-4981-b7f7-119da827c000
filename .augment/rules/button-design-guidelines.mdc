---
description: Comprehensive button design guidelines for eb-lms-app based on Login, Welcome, and Reset Password screen implementations
globs:
alwaysApply: true
---

# Button Design Guidelines

## Overview

This document defines the comprehensive button design system for eb-lms-app based on the actual implementations in Login, Welcome, and Reset Password screens. All button designs must follow these specifications for consistency across the application.

## 1. Button Hierarchy & Types

### Primary Button (Filled)
**Purpose:** Main actions like "Login", "Get Started", "Send Reset Email"
**Visual Pattern:** Solid blue background with white text

```typescript
interface PrimaryButtonProps {
  title: string
  onPress: () => void
  disabled?: boolean
  loading?: boolean
}
```

**Design Specifications:**
- **Background:** Royal blue (#0961F5) - exact color from current implementation
- **Text Color:** White (#FFFFFF)
- **Height:** 60px (exact height from auth screens)
- **Width:** 350px (exact width from auth screens)
- **Border Radius:** 30px (exact border radius from current implementation)
- **Typography:** Light weight (300), 16px font size - CONFIRMED from current usage
- **Padding:** Centered alignment with fixed dimensions
- **Shadow:** shadowOffset: {1, 2}, shadowOpacity: 0.3, shadowRadius: 8, elevation: 4
- **Disabled State:** 50% opacity

### Secondary Button (Outlined)
**Purpose:** Secondary actions like "Browse Courses", "Cancel", "Back to Login"
**Visual Pattern:** White background with gray border

```typescript
interface SecondaryButtonProps {
  title: string
  onPress: () => void
  disabled?: boolean
  variant?: "outlined" | "ghost"
}
```

**Design Specifications:**
- **Background:** White (#FFFFFF / colors.background)
- **Border:** 1px solid light gray (#D1D5DB / colors.palette.neutral300)
- **Text Color:** Dark text (#374151 / colors.text)
- **Height:** 44px (minHeight: 44px)
- **Border Radius:** 16px (matches primary buttons)
- **Typography:** Light weight (300), 16px font size
- **Padding:** 12px horizontal, 12px vertical
- **Shadow:** Subtle elevation with shadowOffset: {0, 1}, opacity: 0.1

### Social Login Button
**Purpose:** Third-party authentication (Google, Facebook, Apple, Phone)
**Visual Pattern:** White background with provider icon and text

```typescript
interface SocialLoginButtonProps {
  provider: "google" | "facebook" | "apple" | "phone"
  text?: string
  onPress: () => void
  disabled?: boolean
}
```

**Design Specifications:**
- **Background:** White (#FFFFFF)
- **Border:** 1px solid light gray (#D1D5DB)
- **Height:** 44px minimum
- **Border Radius:** 12px (slightly less rounded than main buttons)
- **Typography:** Light weight (300), 16px font size
- **Icon:** 20px x 20px, positioned left of text
- **Gap:** 8px between icon and text (spacing.xs)
- **Padding:** 12px horizontal, 8px vertical

### Play Button
**Purpose:** Media controls and interactive content
**Visual Pattern:** Circular orange button with play icon

```typescript
interface PlayButtonProps {
  onPress: () => void
  size?: number
  disabled?: boolean
}
```

**Design Specifications:**
- **Background:** Orange (#FFBB50) - MANDATORY accent color
- **Size:** 40px diameter standard
- **Icon:** White play triangle, 20px
- **Border Radius:** 20px (circular)
- **Shadow:** Subtle elevation
- **Pressed State:** Scale 0.96, darker orange (#E6A545)

## 2. Border Radius Standards

### Hierarchy by Screen Context
- **Auth Screen Primary Buttons:** 30px (Login, Signup, Forgot Password screens)
- **Student Screen Buttons:** 16px (Home, Profile, Course screens)
- **Social Login Buttons:** 12px (Authentication providers)
- **Base Component Default:** 8px (Fallback for generic buttons)
- **Play Buttons:** 20px (Circular media controls)

### Implementation Rules
1. **Login Screen:** All buttons use 16px border radius
2. **Welcome Screen:** All buttons use 16px border radius
3. **Reset Password Screen:** All buttons use 16px border radius
4. **Social Authentication:** Use 12px border radius
5. **Media Controls:** Use 20px border radius (circular)

## 3. Typography Standards

### Font Weight Rule (MANDATORY)
- **ALL button text MUST use font weight 300 (Light)**
- **No exceptions** - this applies to primary, secondary, and social buttons
- **Rationale:** Consistent with app-wide typography guidelines

### Font Specifications
```typescript
const $buttonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Font weight 300 - MANDATORY
  fontSize: 16,
  lineHeight: 20,
  textAlign: "center",
})
```

## 4. Implementation Examples

### Primary Button Implementation (Auth Screens)
```typescript
const $primaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: "#0961F5", // Exact color from current implementation
  borderRadius: 30, // Exact border radius from auth screens
  height: 60, // Exact height from auth screens
  width: 350, // Exact width from auth screens
  alignSelf: "center", // Center the button
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 }, // Exact shadow from current implementation
  shadowOpacity: 0.3, // Exact shadow opacity
  shadowRadius: 8, // Exact shadow radius
  elevation: 4,
})
```

### Secondary Button Implementation
```typescript
const $secondaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background, // White
  borderColor: colors.palette.neutral300, // Light gray
  borderWidth: 1,
  borderRadius: 16, // Match primary button
  height: 44,
  minHeight: 44,
  shadowColor: colors.palette.neutral400,
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.1,
  shadowRadius: 2,
  elevation: 1,
})
```

### Button Text Implementation
```typescript
const $buttonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Font weight 300 - MANDATORY
  fontSize: 16,
})
```

## 5. Visual Consistency Rules

### Height & Touch Targets
1. **Standard Height:** Always 44px (minHeight: 44px)
2. **Touch Accessibility:** Minimum 44px for all interactive elements
3. **Padding:** 12px horizontal for adequate touch targets
4. **Vertical Padding:** 12px for primary/secondary, 8px for social

### Shadow & Elevation
1. **Primary Buttons:** Strong shadow (elevation: 3)
2. **Secondary Buttons:** Subtle shadow (elevation: 1)
3. **Social Buttons:** Minimal shadow
4. **Play Buttons:** Subtle shadow with press animation

### Spacing Between Buttons
1. **Stacked Buttons:** 16px gap (spacing.md)
2. **Horizontal Buttons:** 12px gap (spacing.sm)
3. **Section Separation:** 24px gap (spacing.lg)

## 6. Button Presets

### Available Presets
```typescript
type ButtonPreset = 
  | "filled"     // Primary actions - blue background
  | "default"    // Secondary actions - white background with border
  | "secondary"  // Sky blue background (special cases)
  | "reversed"   // Dark background for light themes
  | "play"       // Orange circular button

// Usage examples
<Button preset="filled" tx="login" style={themed($primaryButton)} />
<Button preset="default" tx="cancel" style={themed($secondaryButton)} />
<Button preset="play" onPress={playVideo} />
```

## 7. Quality Assurance Checklist

### Design Compliance
- [ ] Border radius matches screen context (16px for main screens)
- [ ] Font weight 300 applied to all button text
- [ ] Height is exactly 44px (minHeight: 44px)
- [ ] Proper shadow elevation applied
- [ ] Consistent padding (12px horizontal)
- [ ] Appropriate color contrast for accessibility

### Implementation Compliance
- [ ] Uses themed styles with design tokens
- [ ] Includes proper TypeScript interfaces
- [ ] Implements accessibility props
- [ ] Handles disabled and loading states
- [ ] Follows naming conventions

### Cross-Screen Consistency
- [ ] Login screen buttons match guidelines
- [ ] Welcome screen buttons match guidelines
- [ ] Reset password screen buttons match guidelines
- [ ] Social login buttons use 12px border radius
- [ ] All buttons use font weight 300

This button design system ensures consistent, accessible, and professional button implementations across the entire eb-lms-app.
