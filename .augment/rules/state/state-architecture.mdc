---
description: State management architecture and patterns for eb-lms-app
globs:
alwaysApply: true
---

# State Management Architecture

## Overview

This document defines the state management architecture for eb-lms-app using MobX-State-Tree (MST) with a modular store structure, reactive patterns, and persistent state handling.

## 1. Technology Stack

### Core Technologies
- **MobX-State-Tree (MST):** Reactive state management with type safety
- **AsyncStorage:** Persistent storage for React Native
- **React Context:** Store provider and dependency injection
- **TypeScript:** Full type safety for state models
- **Reactotron:** Development debugging and inspection

### Store Architecture
```
app/models/
├── RootStore.ts           # Root store combining all stores
├── AuthenticationStore.ts # Authentication and user session
├── EpisodeStore.ts        # Episode/content management
├── UserStore.ts           # User profile and preferences
├── CourseStore.ts         # Course data and enrollment
├── NavigationStore.ts     # Navigation state persistence
└── helpers/
    ├── getRootStore.ts    # Store access utilities
    ├── setupRootStore.ts  # Store initialization
    └── withSetupRootStore.ts # HOC for store setup
```

## 2. Root Store Configuration

### Root Store Model
```typescript
// app/models/RootStore.ts
import { Instance, SnapshotOut, types } from "mobx-state-tree"
import { AuthenticationStoreModel } from "./AuthenticationStore"
import { EpisodeStoreModel } from "./EpisodeStore"
import { UserStoreModel } from "./UserStore"
import { CourseStoreModel } from "./CourseStore"
import { NavigationStoreModel } from "./NavigationStore"

export const RootStoreModel = types.model("RootStore").props({
  authenticationStore: types.optional(AuthenticationStoreModel, {}),
  episodeStore: types.optional(EpisodeStoreModel, {}),
  userStore: types.optional(UserStoreModel, {}),
  courseStore: types.optional(CourseStoreModel, {}),
  navigationStore: types.optional(NavigationStoreModel, {}),
})

export interface RootStore extends Instance<typeof RootStoreModel> {}
export interface RootStoreSnapshot extends SnapshotOut<typeof RootStoreModel> {}
```

### Store Setup and Initialization
```typescript
// app/models/helpers/setupRootStore.ts
import { onSnapshot } from "mobx-state-tree"
import { RootStoreModel, RootStore } from "../RootStore"
import * as storage from "@/utils/storage"

const ROOT_STATE_STORAGE_KEY = "root-v1"

export async function setupRootStore(): Promise<RootStore> {
  let rootStore: RootStore
  let data: any

  // Load persisted state
  try {
    data = (await storage.load(ROOT_STATE_STORAGE_KEY)) || {}
  } catch (e) {
    console.error("Failed to load root state:", e)
    data = {}
  }

  // Create root store
  try {
    rootStore = RootStoreModel.create(data)
  } catch (e) {
    console.error("Failed to create root store:", e)
    rootStore = RootStoreModel.create({})
  }

  // Setup persistence
  if (__DEV__) {
    // Development: Save state on every change
    onSnapshot(rootStore, (snapshot) => {
      console.log("Root store snapshot:", snapshot)
      storage.save(ROOT_STATE_STORAGE_KEY, snapshot)
    })
  } else {
    // Production: Debounced saves
    let saveTimeout: NodeJS.Timeout
    onSnapshot(rootStore, (snapshot) => {
      clearTimeout(saveTimeout)
      saveTimeout = setTimeout(() => {
        storage.save(ROOT_STATE_STORAGE_KEY, snapshot)
      }, 1000)
    })
  }

  return rootStore
}
```

### Store Provider Setup
```typescript
// app/models/helpers/withSetupRootStore.tsx
import React, { ComponentType, useEffect, useState } from "react"
import { RootStore, setupRootStore } from "../"
import { RootStoreProvider } from "./getRootStore"

interface SetupRootStoreProps {
  onRootStoreReady?: (rootStore: RootStore) => void
}

export function withSetupRootStore<P extends object>(
  Component: ComponentType<P>,
  LoadingComponent?: ComponentType
) {
  return function WrappedComponent(props: P & SetupRootStoreProps) {
    const [rootStore, setRootStore] = useState<RootStore | undefined>(undefined)

    useEffect(() => {
      setupRootStore().then((store) => {
        setRootStore(store)
        props.onRootStoreReady?.(store)
      })
    }, [])

    if (!rootStore) {
      return LoadingComponent ? <LoadingComponent /> : null
    }

    return (
      <RootStoreProvider value={rootStore}>
        <Component {...props} />
      </RootStoreProvider>
    )
  }
}
```

## 3. Store Access Utilities

### Store Context and Hooks
```typescript
// app/models/helpers/getRootStore.ts
import { createContext, useContext } from "react"
import { RootStore } from "../RootStore"

const RootStoreContext = createContext<RootStore>({} as RootStore)

export const RootStoreProvider = RootStoreContext.Provider

export const useStores = (): RootStore => {
  const store = useContext(RootStoreContext)
  if (!store) {
    throw new Error("useStores must be used within a RootStoreProvider")
  }
  return store
}

// Individual store hooks for convenience
export const useAuthenticationStore = () => useStores().authenticationStore
export const useEpisodeStore = () => useStores().episodeStore
export const useUserStore = () => useStores().userStore
export const useCourseStore = () => useStores().courseStore
export const useNavigationStore = () => useStores().navigationStore
```

### Store Access in Non-React Code
```typescript
// app/models/helpers/getRootStore.ts (continued)
let _rootStore: RootStore

export function setRootStore(rootStore: RootStore) {
  _rootStore = rootStore
}

export function getRootStore(): RootStore {
  if (!_rootStore) {
    throw new Error("Root store not initialized")
  }
  return _rootStore
}

// Convenience getters
export const getAuthenticationStore = () => getRootStore().authenticationStore
export const getEpisodeStore = () => getRootStore().episodeStore
export const getUserStore = () => getRootStore().userStore
export const getCourseStore = () => getRootStore().courseStore
export const getNavigationStore = () => getRootStore().navigationStore
```

## 4. State Persistence

### Storage Utilities
```typescript
// app/utils/storage/storage.ts
import AsyncStorage from "@react-native-async-storage/async-storage"

export async function load(key: string): Promise<any | null> {
  try {
    const data = await AsyncStorage.getItem(key)
    return data ? JSON.parse(data) : null
  } catch (error) {
    console.error(`Failed to load ${key}:`, error)
    return null
  }
}

export async function save(key: string, value: any): Promise<boolean> {
  try {
    await AsyncStorage.setItem(key, JSON.stringify(value))
    return true
  } catch (error) {
    console.error(`Failed to save ${key}:`, error)
    return false
  }
}

export async function remove(key: string): Promise<void> {
  try {
    await AsyncStorage.removeItem(key)
  } catch (error) {
    console.error(`Failed to remove ${key}:`, error)
  }
}

export async function clear(): Promise<void> {
  try {
    await AsyncStorage.clear()
  } catch (error) {
    console.error("Failed to clear storage:", error)
  }
}
```

### Selective Persistence
```typescript
// app/models/helpers/persistenceConfig.ts
export const persistenceConfig = {
  // Always persist
  authenticationStore: true,
  userStore: true,
  
  // Conditionally persist
  episodeStore: {
    favorites: true,
    playbackPosition: true,
    downloadedEpisodes: false, // Too large for storage
  },
  
  courseStore: {
    enrollments: true,
    progress: true,
    cache: false, // Temporary data
  },
  
  // Never persist
  navigationStore: false,
}
```

## 5. Development Tools Integration

### Reactotron Setup
```typescript
// app/models/helpers/setupRootStore.ts (continued)
import { connectReduxDevtools } from "mst-middlewares"

export async function setupRootStore(): Promise<RootStore> {
  // ... existing setup code

  // Development tools
  if (__DEV__) {
    // Reactotron integration
    if (console.tron) {
      console.tron.trackMstNode(rootStore)
    }

    // Redux DevTools integration
    connectReduxDevtools(require("remotedev"), rootStore)
  }

  return rootStore
}
```

### State Debugging
```typescript
// app/models/helpers/debugStore.ts
import { getSnapshot, onSnapshot, onPatch } from "mobx-state-tree"
import { RootStore } from "../RootStore"

export function enableStoreDebugging(rootStore: RootStore) {
  if (!__DEV__) return

  // Log all state changes
  onSnapshot(rootStore, (snapshot) => {
    console.log("📸 Store snapshot:", snapshot)
  })

  // Log all patches (granular changes)
  onPatch(rootStore, (patch) => {
    console.log("🔧 Store patch:", patch)
  })

  // Expose store to global scope for debugging
  ;(global as any).store = rootStore
  ;(global as any).getStoreSnapshot = () => getSnapshot(rootStore)
}
```

## 6. Error Handling

### Store Error Boundaries
```typescript
// app/models/helpers/storeErrorBoundary.ts
import { addMiddleware } from "mobx-state-tree"
import { RootStore } from "../RootStore"

export function addErrorHandling(rootStore: RootStore) {
  addMiddleware(rootStore, (call, next) => {
    try {
      return next(call)
    } catch (error) {
      console.error("Store action error:", error)
      
      // Log to crash reporting service
      if (!__DEV__) {
        // crashlytics().recordError(error)
      }
      
      // Re-throw to maintain error propagation
      throw error
    }
  })
}
```

### Async Action Error Handling
```typescript
// Common pattern for async actions
const fetchData = flow(function* (this: StoreModel) {
  this.setLoading(true)
  this.setError(null)
  
  try {
    const result = yield apiService.getData()
    if (result.kind === "ok") {
      this.setData(result.data)
    } else {
      this.setError(result.message)
    }
  } catch (error) {
    this.setError("An unexpected error occurred")
    console.error("Fetch data error:", error)
  } finally {
    this.setLoading(false)
  }
})
```

## 7. Quality Assurance

### State Management Checklist
- [ ] All stores properly typed with MST models
- [ ] State persistence configured appropriately
- [ ] Error handling implemented for async actions
- [ ] Development tools integration working
- [ ] Store access utilities properly implemented
- [ ] Memory leaks prevented (proper cleanup)
- [ ] Performance optimized (selective updates)
- [ ] Testing utilities available

### Best Practices
- [ ] Use flow for async actions
- [ ] Implement proper loading and error states
- [ ] Keep stores focused and modular
- [ ] Use views for computed values
- [ ] Implement proper cleanup in components
- [ ] Test store actions and reactions
- [ ] Document store interfaces
- [ ] Monitor store performance

This state management architecture provides a robust, scalable, and maintainable foundation for eb-lms-app's reactive state needs.
