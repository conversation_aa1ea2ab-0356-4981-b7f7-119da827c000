---
description: Mandatory 12px horizontal padding rule for all components in eb-lms-app
globs:
alwaysApply: true
priority: HIGH
---

# 🚨 MANDATORY: 12px Horizontal Padding Rule

## Overview
**CRITICAL RULE:** All components in the eb-lms-app MUST have 12px padding from the left and right edges of the screen. This is a non-negotiable design requirement that ensures consistent visual alignment and professional appearance.

## The Rule

### **Universal Application**
```typescript
// MANDATORY PATTERN - Apply to ALL components
const $componentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY for ALL components
  // Other styles...
})
```

### **What This Means**
- **12px = spacing.sm** in the design system
- **Left padding:** 12px from left screen edge
- **Right padding:** 12px from right screen edge
- **Total content width:** Screen width - 24px (12px × 2)

## Components That MUST Follow This Rule

### ✅ **Screen Level Components**
```typescript
const $screenContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  backgroundColor: "#FFFFFF",
})

const $screenContentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexGrow: 1,
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  paddingVertical: spacing.lg,
})
```

### ✅ **Card Components**
```typescript
const $cardContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  paddingVertical: spacing.md,
  backgroundColor: "#FFFFFF",
  borderRadius: 12,
  marginBottom: spacing.md,
})
```

### ✅ **List Components**
```typescript
const $listContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  gap: spacing.md,
})

const $listItem: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  paddingVertical: spacing.sm,
  backgroundColor: "#FFFFFF",
  borderRadius: 8,
})
```

### ✅ **Form Components**
```typescript
const $formContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  gap: spacing.lg,
})

const $inputContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  marginBottom: spacing.md,
})
```

### ✅ **Header Components**
```typescript
const $headerContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  paddingVertical: spacing.sm,
  height: 56,
  backgroundColor: "#FFFFFF",
})
```

### ✅ **Footer Components**
```typescript
const $footerContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  paddingVertical: spacing.sm,
  backgroundColor: "#FFFFFF",
  borderTopWidth: 1,
  borderTopColor: "#F4F2F1",
})
```

### ✅ **Modal & Overlay Components**
```typescript
const $modalContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  paddingVertical: spacing.lg,
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
})

const $overlayContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  paddingVertical: spacing.md,
})
```

## Implementation Examples

### **Login Screen Example**
```typescript
const $loginContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  justifyContent: "center",
})

const $loginCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY (additional internal padding)
  paddingVertical: spacing.xl,
  backgroundColor: "#FFFFFF",
  borderRadius: 24,
  marginHorizontal: spacing.xs, // Small additional margin for visual breathing room
})
```

### **Course List Example**
```typescript
const $courseListContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
})

const $courseCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY (internal padding)
  paddingVertical: spacing.md,
  backgroundColor: "#FFFFFF",
  borderRadius: 12,
  marginBottom: spacing.md,
})
```

### **Profile Screen Example**
```typescript
const $profileContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
})

const $profileHeader: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY (additional internal padding)
  paddingVertical: spacing.lg,
  alignItems: "center",
})

const $profileStats: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  gap: spacing.sm,
  marginVertical: spacing.md,
})
```

## Why This Rule Exists

### **1. Visual Consistency**
- Creates uniform alignment across all screens
- Ensures professional, polished appearance
- Maintains visual rhythm and balance

### **2. User Experience**
- Provides comfortable reading margins
- Prevents content from touching screen edges
- Improves touch target accessibility

### **3. Design System Integrity**
- Enforces consistent spacing standards
- Simplifies layout decisions for developers
- Reduces design inconsistencies

### **4. Cross-Platform Compatibility**
- Works consistently across different screen sizes
- Maintains proportions on various devices
- Ensures responsive design principles

## Enforcement

### **Code Review Checklist**
- [ ] **All components have `paddingHorizontal: spacing.sm`**
- [ ] **No components touch the screen edges directly**
- [ ] **Consistent 12px spacing from left and right edges**
- [ ] **No arbitrary horizontal padding values used**

### **Common Mistakes to Avoid**
❌ **DON'T:**
```typescript
// Missing horizontal padding
const $badContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  // Missing paddingHorizontal: spacing.sm
})

// Using arbitrary values
const $badContainer2: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 16, // Should be spacing.sm (12px)
})

// Forgetting in nested components
const $badCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingVertical: spacing.md,
  // Missing paddingHorizontal: spacing.sm
})
```

✅ **DO:**
```typescript
// Correct implementation
const $goodContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flex: 1,
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
})

const $goodCard: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY
  paddingVertical: spacing.md,
})
```

## Exception Cases

### **Full-Width Elements**
Only these elements may extend to screen edges:
- **Background images/gradients**
- **Navigation bars (system level)**
- **Status bars**
- **Full-width dividers/separators**

Even these should contain content with proper 16px padding:
```typescript
const $fullWidthBackground: ThemedStyle<ViewStyle> = () => ({
  width: "100%", // Full width background
})

const $contentWithinBackground: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingHorizontal: spacing.sm, // 12px - MANDATORY for content
})
```

## Summary

**Remember:** Every component that contains content MUST have `paddingHorizontal: spacing.sm` (12px). This is not optional—it's a fundamental design requirement that ensures the app maintains professional, consistent visual standards.

**Quick Check:** If your component doesn't have `paddingHorizontal: spacing.sm`, it's likely incorrect and needs to be fixed.
