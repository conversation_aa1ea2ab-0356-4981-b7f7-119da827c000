---
description: Typography system and font guidelines for eb-lms-app
globs:
alwaysApply: true
---

# Typography System

## Overview

This document defines the comprehensive typography system for eb-lms-app using Be Vietnam Pro as the primary font family with specific weight guidelines for optimal readability and visual hierarchy.

## 🚨 CRITICAL RULE: FONT WEIGHT 500 POLICY

**MANDATORY FONT WEIGHT RULE:**
- ✅ **DEFAULT:** All descriptions, body text, and button text MUST use font weight 500 (medium)
- ✅ **CONSISTENCY:** Medium weight ensures optimal readability and modern appearance
- ✅ **ACCESSIBILITY:** Medium weight provides better visibility and reduces eye strain
- ✅ **BRAND:** Medium typography creates clear, professional aesthetic
- ❌ **EXCEPTIONS:** Only titles and headings may use heavier weights (600-700)
- ❌ **NO LIGHT TEXT:** Avoid using light weights for body content

## 1. Font Family

### Primary Font: Be Vietnam Pro (Google Fonts)

**Font Source**: Google Fonts - https://fonts.google.com/specimen/Be+Vietnam+Pro
**Why Be Vietnam Pro**:
- Perfect Vietnamese font with excellent readability
- Modern design optimized for digital interfaces
- Complete weight spectrum (9 weights available)
- Specifically designed for Vietnamese text with proper diacritics support
- Open source and free to use

```typescript
fontFamily: {
  primary: {
    light: "beVietnamProLight",      // 300 weight - for subtle elements
    normal: "beVietnamProRegular",   // 400 weight - for standard text
    medium: "beVietnamProMedium",    // 500 weight - MANDATORY for descriptions, body text, button text
    semiBold: "beVietnamProSemiBold", // 600 weight - for headings
    bold: "beVietnamProBold",        // 700 weight - for page titles only
  }
}
```

### Font Weight Mapping
```typescript
fontWeights: {
  light: "300",      // Subtle elements, captions
  normal: "400",     // Standard text, form inputs
  medium: "500",     // MANDATORY for descriptions, body text, button text
  semiBold: "600",   // Section headings, card titles
  bold: "700",       // Page titles, hero text only
}
```

## 2. Typography Hierarchy

### Page Titles (Hero Level)
```typescript
pageTitle: {
  fontFamily: "beVietnamProBold",     // 700 weight - ONLY for page titles
  fontSize: 28,
  lineHeight: 34,
  letterSpacing: -0.5,
  color: colors.text,               // Primary text (#333333)
}
```

### Section Headings
```typescript
sectionHeading: {
  fontFamily: "beVietnamProSemiBold", // 600 weight
  fontSize: 20,
  lineHeight: 26,
  letterSpacing: -0.3,
  color: colors.text,               // Primary text (#333333)
}
```

### Card Titles
```typescript
cardTitle: {
  fontFamily: "beVietnamProSemiBold", // 600 weight
  fontSize: 18,
  lineHeight: 24,
  letterSpacing: -0.2,
  color: colors.text,               // Primary text (#333333)
}
```

### Body Text (MANDATORY Medium Weight)
```typescript
bodyText: {
  fontFamily: "beVietnamProMedium",    // 500 weight - MANDATORY
  fontSize: 16,
  lineHeight: 22,
  letterSpacing: 0,
  color: colors.text,               // Primary text (#333333)
}
```

### Descriptions (MANDATORY Medium Weight)
```typescript
description: {
  fontFamily: "beVietnamProMedium",    // 500 weight - MANDATORY
  fontSize: 14,
  lineHeight: 20,
  letterSpacing: 0,
  color: colors.textDim,            // Dark blue (#1A3270)
}
```

### Button Text (MANDATORY Medium Weight)
```typescript
buttonText: {
  fontFamily: "beVietnamProMedium",    // 500 weight - MANDATORY
  fontSize: 16,
  lineHeight: 20,
  letterSpacing: 0.2,
  color: colors.textOnPrimary,      // White or appropriate contrast
}
```

### Form Labels
```typescript
formLabel: {
  fontFamily: "beVietnamProSemiBold",   // 600 weight - for labels
  fontSize: 14,
  lineHeight: 18,
  letterSpacing: 0,
  color: colors.text,               // Primary text (#333333)
}
```

### Caption Text
```typescript
caption: {
  fontFamily: "beVietnamProMedium",    // 500 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  letterSpacing: 0.1,
  color: colors.textDim,            // Dark blue (#1A3270)
}
```

### Navigation Text
```typescript
navigationText: {
  fontFamily: "beVietnamProMedium",    // 500 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  letterSpacing: 0.5,
  color: colors.navigationText,     // Light blue (#E8F2FF)
}
```

## 3. Typography Usage Guidelines

### Text Hierarchy Rules
1. **Page Titles:** Bold (700) - Only for main page titles like "Welcome to VANTIS"
2. **Section Headings:** SemiBold (600) - For major content sections
3. **Card Titles:** SemiBold (600) - For course cards, content cards
4. **Body Text:** Medium (500) - MANDATORY for all body content
5. **Descriptions:** Medium (500) - MANDATORY for all descriptions
6. **Button Text:** Medium (500) - MANDATORY for all button text
7. **Form Labels:** SemiBold (600) - For form field labels
8. **Captions:** Medium (500) - MANDATORY for small text

### Font Weight Usage by Context

#### MANDATORY Medium Weight (500) Usage
- ✅ **All descriptions** - Course descriptions, user bios, explanatory text
- ✅ **All button text** - Primary, secondary, social login buttons
- ✅ **Body paragraphs** - Main content text, article text
- ✅ **Navigation labels** - Tab bar text, menu items
- ✅ **Captions** - Image captions, metadata text
- ✅ **Placeholder text** - Input field placeholders
- ✅ **Helper text** - Form helper text, tooltips

#### Light Weight (300) Usage
- ✅ **Subtle elements** - Very light emphasis text
- ✅ **Secondary information** - Less important details

#### SemiBold Weight (600) Usage
- ✅ **Card titles** - Course titles, content titles
- ✅ **Section headings** - Major content sections
- ✅ **User names** - Profile names, author names
- ✅ **Form labels** - Input field labels

#### Bold Weight (700) Usage - LIMITED
- ✅ **Page titles only** - "Welcome to VANTIS", main hero text
- ❌ **NOT for body text** - Never use bold for descriptions or content

## 4. Implementation Examples

### Component Typography Styles
```typescript
// Page title styling
const $pageTitle: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.bold,     // 700 weight - page titles only (Be Vietnam Pro Bold)
  fontSize: 28,
  lineHeight: 34,
  color: colors.text,
  textAlign: "center",
})

// Section heading styling
const $sectionHeading: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.semiBold, // 600 weight (Be Vietnam Pro SemiBold)
  fontSize: 20,
  lineHeight: 26,
  color: colors.text,
  marginBottom: 16,
})

// Description styling - MANDATORY medium weight
const $description: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,   // 500 weight - MANDATORY (Be Vietnam Pro Medium)
  fontSize: 14,
  lineHeight: 20,
  color: colors.textDim,
  textAlign: "center",
})

// Button text styling - MANDATORY medium weight
const $buttonText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,   // 500 weight - MANDATORY (Be Vietnam Pro Medium)
  fontSize: 16,
  lineHeight: 20,
  color: colors.textOnPrimary,
  textAlign: "center",
})

// Body text styling - MANDATORY medium weight
const $bodyText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,   // 500 weight - MANDATORY (Be Vietnam Pro Medium)
  fontSize: 16,
  lineHeight: 22,
  color: colors.text,
})

// Form label styling
const $formLabel: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.semiBold,  // 600 weight (Be Vietnam Pro SemiBold)
  fontSize: 14,
  lineHeight: 18,
  color: colors.text,
  marginBottom: 8,
})

// Caption styling - MANDATORY medium weight
const $caption: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,   // 500 weight - MANDATORY (Be Vietnam Pro Medium)
  fontSize: 12,
  lineHeight: 16,
  color: colors.textDim,
})
```

### Text Component Usage
```typescript
// Page title
<Text style={themed($pageTitle)}>Welcome to VANTIS</Text>

// Section heading
<Text style={themed($sectionHeading)}>Featured Courses</Text>

// Description text - MANDATORY light weight
<Text style={themed($description)}>
  Discover our comprehensive e-learning platform designed for modern education.
</Text>

// Button text - MANDATORY light weight
<Text style={themed($buttonText)}>Get Started</Text>

// Body text - MANDATORY light weight
<Text style={themed($bodyText)}>
  Our platform offers interactive courses, progress tracking, and personalized learning paths.
</Text>
```

## 5. Responsive Typography

### Font Size Scaling
```typescript
// Base font sizes
const baseFontSizes = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 28,
}

// Responsive scaling
const getResponsiveFontSize = (baseSize: number, scale: number = 1) => {
  return Math.round(baseSize * scale)
}
```

### Platform-Specific Adjustments
```typescript
const $responsiveText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // 500 weight - MANDATORY (Be Vietnam Pro Medium)
  fontSize: Platform.select({
    ios: 16,
    android: 16,
    web: 14, // Slightly smaller on web
  }),
  lineHeight: Platform.select({
    ios: 22,
    android: 22,
    web: 20,
  }),
})
```

## 6. Quality Assurance

### Typography Checklist
- [ ] Font weight 500 used for all descriptions (MANDATORY)
- [ ] Font weight 500 used for all button text (MANDATORY)
- [ ] Font weight 500 used for all body text (MANDATORY)
- [ ] Bold weight (700) used only for page titles
- [ ] SemiBold weight (600) used for headings and card titles
- [ ] SemiBold weight (600) used for form labels
- [ ] Proper line heights for readability
- [ ] Consistent letter spacing
- [ ] Appropriate color contrast
- [ ] Responsive font sizing implemented

### Accessibility Standards
- [ ] Minimum 16px font size for body text
- [ ] Proper contrast ratios (4.5:1 minimum)
- [ ] Support for system font scaling
- [ ] Clear visual hierarchy
- [ ] Readable line heights (1.4x font size minimum)
- [ ] Sufficient letter spacing
- [ ] Color not sole indicator of meaning
- [ ] Focus indicators clearly visible

This typography system ensures consistent, readable, and accessible text throughout eb-lms-app using Be Vietnam Pro font from Google Fonts with mandatory medium weight for optimal learning experience.
