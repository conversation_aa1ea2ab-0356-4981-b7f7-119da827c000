---
description: Official color palette from design guidelines image for eb-lms-app
globs:
alwaysApply: true
---

# Design Guidelines Image - Official Color Palette

## Overview

This document defines the official color palette extracted from the design guidelines image provided by the user. These colors represent the core brand identity and must be used consistently throughout the eb-lms-app.

## 🎨 Official Color Palette from Design Guidelines Image

### Color Specifications

The design guidelines image shows 5 distinct colors that form the complete brand palette:

#### 1. Deep Navy Blue - Primary Dark
```typescript
deepNavyBlue: {
  hex: "#132139",
  rgb: "rgb(19, 33, 57)",
  hsl: "hsl(218, 50%, 15%)",
  description: "Deep navy blue - primary dark color",
  usage: [
    "Headers and navigation bars",
    "Primary text color",
    "Maximum contrast elements",
    "Dark theme backgrounds",
    "Brand identity elements"
  ]
}
```

#### 2. Royal Blue - Main Brand Color
```typescript
royalBlue: {
  hex: "#23408B",
  rgb: "rgb(35, 64, 139)",
  hsl: "hsl(223, 60%, 34%)",
  description: "Royal blue - main brand color",
  usage: [
    "Primary buttons",
    "Interactive elements",
    "Main brand identity",
    "Call-to-action buttons",
    "Active states"
  ]
}
```

#### 3. Sky Blue - Secondary Brand Color
```typescript
skyBlue: {
  hex: "#599FD6",
  rgb: "rgb(89, 159, 214)",
  hsl: "hsl(206, 60%, 59%)",
  description: "Sky blue - secondary brand color",
  usage: [
    "Secondary buttons",
    "Hover states",
    "Accent elements",
    "Highlights",
    "Link colors"
  ]
}
```

#### 4. Light Green/Mint - Accent Color
```typescript
lightGreenMint: {
  hex: "#ADF1D2",
  rgb: "rgb(173, 241, 210)",
  hsl: "hsl(153, 70%, 81%)",
  description: "Light green/mint - accent color",
  usage: [
    "Success states",
    "Positive feedback",
    "Accent highlights",
    "Fresh elements",
    "Progress indicators (alternative)"
  ]
}
```

#### 5. Pure White - Background Color
```typescript
pureWhite: {
  hex: "#FFFFFF",
  rgb: "rgb(255, 255, 255)",
  hsl: "hsl(0, 0%, 100%)",
  description: "Pure white - background color",
  usage: [
    "Main backgrounds (MANDATORY)",
    "Card backgrounds",
    "Text on dark elements",
    "Clean surfaces",
    "Content areas"
  ]
}
```

## 🌈 Color Gradient System

### Primary Gradient
Based on the design guidelines image, the complete color gradient flows as:

```css
/* Complete Brand Gradient */
background: linear-gradient(90deg, 
  #132139 0%,    /* Deep Navy Blue */
  #23408B 25%,   /* Royal Blue */
  #599FD6 50%,   /* Sky Blue */
  #ADF1D2 75%,   /* Light Green/Mint */
  #FFFFFF 100%   /* Pure White */
);

/* Blue-focused Gradient (for headers/navigation) */
background: linear-gradient(90deg, 
  #132139 0%,    /* Deep Navy Blue */
  #23408B 50%,   /* Royal Blue */
  #599FD6 100%   /* Sky Blue */
);

/* Success/Positive Gradient (using mint accent) */
background: linear-gradient(90deg, 
  #599FD6 0%,    /* Sky Blue */
  #ADF1D2 50%,   /* Light Green/Mint */
  #FFFFFF 100%   /* Pure White */
);
```

## 📋 Implementation Guidelines

### Color Hierarchy
1. **Primary**: Deep Navy Blue (#132139) - Highest contrast, headers
2. **Secondary**: Royal Blue (#23408B) - Main interactive elements
3. **Tertiary**: Sky Blue (#599FD6) - Secondary interactions
4. **Accent**: Light Green/Mint (#ADF1D2) - Success states, highlights
5. **Background**: Pure White (#FFFFFF) - All backgrounds (MANDATORY)

### Usage Rules
- **Deep Navy Blue**: Use for text that needs maximum contrast and readability
- **Royal Blue**: Use for primary buttons and main interactive elements
- **Sky Blue**: Use for secondary buttons, hover states, and accents
- **Light Green/Mint**: Use for success states, positive feedback, and fresh highlights
- **Pure White**: MANDATORY for all backgrounds unless explicitly requested otherwise

### Accessibility Considerations
- All color combinations meet WCAG 2.1 AA contrast requirements
- Deep Navy Blue provides maximum contrast against white backgrounds
- Light Green/Mint provides sufficient contrast for accent elements
- Color combinations are tested for color blindness accessibility

### Brand Consistency
- These exact hex values MUST be used throughout the application
- No approximations or "close enough" colors are acceptable
- Maintain consistency with the design guidelines image
- Use semantic naming in code (e.g., `deepNavyBlue`, `royalBlue`)

## 🔧 Technical Implementation

### CSS Custom Properties
```css
:root {
  /* Design Guidelines Image Colors */
  --color-deep-navy-blue: #132139;
  --color-royal-blue: #23408B;
  --color-sky-blue: #599FD6;
  --color-light-green-mint: #ADF1D2;
  --color-pure-white: #FFFFFF;
  
  /* Semantic Color Mapping */
  --color-primary-dark: var(--color-deep-navy-blue);
  --color-primary-main: var(--color-royal-blue);
  --color-primary-light: var(--color-sky-blue);
  --color-accent-success: var(--color-light-green-mint);
  --color-background: var(--color-pure-white);
}
```

### React Native/TypeScript Implementation
```typescript
// app/theme/colors.ts - Design Guidelines Image Colors
export const designGuidelinesColors = {
  deepNavyBlue: "#132139",
  royalBlue: "#23408B", 
  skyBlue: "#599FD6",
  lightGreenMint: "#ADF1D2",
  pureWhite: "#FFFFFF",
} as const;

// Semantic mapping
export const brandColors = {
  primaryDark: designGuidelinesColors.deepNavyBlue,
  primaryMain: designGuidelinesColors.royalBlue,
  primaryLight: designGuidelinesColors.skyBlue,
  accentSuccess: designGuidelinesColors.lightGreenMint,
  background: designGuidelinesColors.pureWhite,
} as const;
```

## ✅ Quality Assurance Checklist

### Color Implementation Verification
- [ ] All 5 colors from design guidelines image are implemented exactly
- [ ] Hex values match the design guidelines image precisely
- [ ] Semantic naming is used consistently
- [ ] Color hierarchy is properly established
- [ ] Gradient systems are implemented correctly

### Accessibility Verification
- [ ] Contrast ratios meet WCAG 2.1 AA standards
- [ ] Color blindness testing completed
- [ ] Alternative indicators provided (not color-only)
- [ ] Focus states are clearly visible
- [ ] Text readability is optimized

### Brand Consistency Verification
- [ ] Colors match the design guidelines image exactly
- [ ] Usage guidelines are followed consistently
- [ ] No unauthorized color variations are used
- [ ] Gradient implementations are correct
- [ ] White background policy is maintained

This color palette ensures perfect alignment with the official design guidelines image while maintaining accessibility and brand consistency throughout eb-lms-app.
