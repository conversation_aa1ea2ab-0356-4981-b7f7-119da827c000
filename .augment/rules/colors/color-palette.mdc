---
description: Core color palette and brand colors for eb-lms-app
globs:
alwaysApply: true
---

# Color Palette

## Overview

The eb-lms-app uses a sophisticated blue-based color system that creates a professional, trustworthy, and modern learning environment. The primary color palette features a gradient from deep navy to bright blue, supporting both light and dark themes.

## 🚨 CRITICAL RULE: WHITE BACKGROUND POLICY

**MANDATORY BACKGROUND COLOR RULE:**
- ✅ **DEFAULT:** App MUST always use WHITE (#FFFFFF) as the main background color
- ✅ **CONSISTENCY:** All screens, cards, and containers use white backgrounds by default
- ✅ **PROFESSIONAL:** White backgrounds ensure clean, professional appearance
- ✅ **ACCESSIBILITY:** White backgrounds provide optimal readability and contrast
- ❌ **OVERRIDE:** Background colors can ONLY be changed when explicitly requested by user
- ❌ **NO COLORED BACKGROUNDS:** Do not use blue or colored backgrounds as default

## 1. Design Philosophy

### Core Principles
- **White Background First:** Clean white backgrounds as the foundation - MANDATORY
- **Professional Learning Environment:** Blue tones convey trust, stability, and focus
- **Accessibility First:** All colors meet WCAG 2.1 AA contrast requirements
- **Semantic Naming:** Colors are named by purpose, not appearance
- **Theme Support:** Full light and dark mode compatibility
- **Gradient System:** Smooth transitions between color variations (when requested)

### Main Color Gradient (From Design Guidelines Image)
The app features a sophisticated color gradient based on the official design guidelines image:

```css
/* Primary Gradient - Exact from Design Guidelines Image */
background: linear-gradient(90deg, #132139 0%, #23408B 25%, #599FD6 50%, #ADF1D2 75%, #FFFFFF 100%);

/* Alternative Blue-focused Gradient */
background: linear-gradient(90deg, #132139 0%, #23408B 50%, #599FD6 100%);
```

**Color Breakdown from Design Guidelines Image:**
- **Deep Navy Blue**: `#132139` - Primary dark color for headers and navigation
- **Royal Blue**: `#23408B` - Main brand color for buttons and interactive elements
- **Sky Blue**: `#599FD6` - Secondary brand color for accents and highlights
- **Light Green/Mint**: `#ADF1D2` - Accent color for success states and highlights
- **Pure White**: `#FFFFFF` - Background and card colors

## 2. Core Brand Colors (Updated from Design Guidelines)

### 🎨 OFFICIAL BRAND COLORS FROM DESIGN GUIDELINES IMAGE

#### Deep Navy Blue - Primary Dark
```typescript
deepNavyBlue: {
  hex: "#132139",
  rgb: "rgb(19, 33, 57)",
  description: "Deep navy blue - primary dark color from design guidelines image",
  usage: "Headers, navigation bars, primary text, maximum contrast elements"
}
```

#### Royal Blue - Main Brand Color
```typescript
royalBlue: {
  hex: "#23408B",
  rgb: "rgb(35, 64, 139)",
  description: "Royal blue - main brand color from design guidelines image",
  usage: "Primary buttons, interactive elements, main brand identity"
}
```

#### Sky Blue - Secondary Brand Color
```typescript
skyBlue: {
  hex: "#599FD6",
  rgb: "rgb(89, 159, 214)",
  description: "Sky blue - secondary brand color from design guidelines image",
  usage: "Secondary buttons, hover states, accent elements, highlights"
}
```

#### Light Green/Mint - Accent Color
```typescript
lightGreenMint: {
  hex: "#ADF1D2",
  rgb: "rgb(173, 241, 210)",
  description: "Light green/mint - accent color from design guidelines image",
  usage: "Success states, positive feedback, accent highlights, fresh elements"
}
```

#### Pure White - Background Color
```typescript
pureWhite: {
  hex: "#FFFFFF",
  rgb: "rgb(255, 255, 255)",
  description: "Pure white - background color from design guidelines image",
  usage: "Main backgrounds, card backgrounds, text on dark elements"
}
```

## 3. Color Palette Structure

### Neutral Colors (Grayscale)
```typescript
// Light Theme
neutral100: "#FFFFFF"  // Pure white - backgrounds, cards - MANDATORY
neutral200: "#F4F2F1"  // Light gray - subtle backgrounds
neutral300: "#D7CEC9"  // Medium light gray - borders, separators
neutral400: "#B6ACA6"  // Medium gray - inactive elements
neutral500: "#978F8A"  // Balanced gray - secondary text
neutral600: "#564E4A"  // Dark gray - primary text
neutral700: "#3C3836"  // Darker gray - headings
neutral800: "#191015"  // Very dark gray - high contrast text
neutral900: "#000000"  // Pure black - maximum contrast

// Dark Theme (values are inverted)
neutral900: "#FFFFFF"  // White text on dark
neutral800: "#F4F2F1"  // Light text
neutral700: "#D7CEC9"  // Medium light text
neutral600: "#B6ACA6"  // Medium text
neutral500: "#978F8A"  // Balanced text
neutral400: "#564E4A"  // Dark elements
neutral300: "#3C3836"  // Darker elements
neutral200: "#191015"  // Dark background
neutral100: "#000000"  // Pure black background
```

### Primary Colors (Blue Brand System) - Updated from Design Guidelines Image
```typescript
// Light Theme - Blue Gradient System (Based on Design Guidelines Image)
primary100: "#E8F2FF"  // Lightest blue - backgrounds, subtle highlights
primary200: "#C2DDFF"  // Light blue - hover states, secondary buttons
primary300: "#599FD6"  // Sky blue - from design guidelines image
primary400: "#23408B"  // Royal blue - main brand color from design guidelines image
primary500: "#132139"  // Deep navy blue - primary dark color from design guidelines image
primary600: "#0961F5"  // Figma brand color - secondary usage
primary700: "#0F1B2A"  // Darker navy - headers, navigation, maximum contrast
primary800: "#0A1219"  // Darkest navy - dark theme backgrounds
primary900: "#050A0F"  // Deepest navy - maximum depth

// Dark Theme (adjusted for dark backgrounds)
primary900: "#E8F2FF"  // Light blue text on dark
primary800: "#C2DDFF"  // Light blue elements
primary700: "#8CC4F0"  // Medium blue elements
primary600: "#599FD6"  // Sky blue - main interactive color on dark
primary500: "#23408B"  // Royal blue - secondary on dark
primary400: "#1A3270"  // Dark blue elements
primary300: "#132339"  // Navy elements
primary200: "#0F1B2A"  // Dark backgrounds
primary100: "#0A1219"  // Darkest backgrounds
```

### Secondary Colors (Supporting)
```typescript
// Light Theme
secondary100: "#DCDDE9"  // Lightest blue-gray
secondary200: "#BCC0D6"  // Light blue-gray
secondary300: "#9196B9"  // Medium blue-gray
secondary400: "#626894"  // Dark blue-gray
secondary500: "#41476E"  // Darkest blue-gray

// Dark Theme (inverted)
secondary500: "#DCDDE9"  // Light secondary on dark
secondary400: "#BCC0D6"  // Medium light secondary
secondary300: "#9196B9"  // Medium secondary
secondary200: "#626894"  // Dark secondary
secondary100: "#41476E"  // Darkest secondary
```

### Accent Colors (Highlights) - Updated from Design Guidelines Image
```typescript
// Light Theme - Green/Mint Accent System (Based on Design Guidelines Image)
accent100: "#F0FDF4"   // Lightest green - subtle highlights
accent200: "#DCFCE7"   // Light green - notifications
accent300: "#BBF7D0"   // Medium light green - success backgrounds
accent400: "#ADF1D2"   // Light green/mint - from design guidelines image
accent500: "#FFBB50"   // Orange accent - call-to-action (preserved for existing usage)

// Dark Theme (adjusted)
accent500: "#F0FDF4"   // Light green on dark
accent400: "#DCFCE7"   // Medium light green
accent300: "#BBF7D0"   // Medium green
accent200: "#ADF1D2"   // Light green/mint
accent100: "#FFBB50"   // Orange accent
```

### Status Colors (Feedback)
```typescript
// Error/Angry Colors (consistent across themes)
angry100: "#F2D6CD"    // Light error background
angry500: "#C03403"    // Error text and borders

// Success Colors
success100: "#D1FAE5"  // Light success background
success500: "#10B981"  // Success text and borders

// Warning Colors
warning100: "#FEF3C7"  // Light warning background
warning500: "#F59E0B"  // Warning text and borders

// Info Colors
info100: "#DBEAFE"     // Light info background
info500: "#3B82F6"     // Info text and borders

// Overlay Colors (consistent across themes)
overlay20: "rgba(25, 16, 21, 0.2)"  // Light overlay
overlay50: "rgba(25, 16, 21, 0.5)"  // Medium overlay
```

## 4. Color Usage by Context

### Navigation & Headers
- **Primary:** `primary700` (#132339) - Deep navy for main navigation
- **Secondary:** `primary600` (#1A3270) - Dark blue for sub-navigation
- **Text on Primary:** `primary100` (#E8F2FF) - Light blue text

### Buttons & Interactive Elements (Updated from Design Guidelines Image)
- **Primary Button:** `primary400` (#23408B) - Royal blue from design guidelines image
- **Secondary Button:** `primary300` (#599FD6) - Sky blue from design guidelines image
- **Hover State:** `primary500` (#132139) - Deep navy blue from design guidelines image
- **Pressed State:** `primary600` (#0961F5) - Figma brand color
- **Disabled State:** `primary200` (#C2DDFF) - Light blue
- **Success Button:** `accent400` (#ADF1D2) - Light green/mint from design guidelines image

### Backgrounds & Surfaces
- **Main Background:** `#FFFFFF` - Pure white - MANDATORY
- **Card Background:** `#FFFFFF` - Pure white - MANDATORY
- **Accent Background:** `primary200` (#C2DDFF) - Light blue (special cases only)

## 5. Blue Color Psychology in Learning

### Psychological Impact
- **Deep Navy (#132339):** Conveys trust, professionalism, and authority
- **Royal Blue (#23408B):** Promotes focus, concentration, and learning
- **Sky Blue (#599FD6):** Creates calm, openness, and accessibility
- **Light Blue (#E8F2FF):** Provides comfort, clarity, and ease of reading

### Learning Environment Benefits
- **Trust Building:** Deep blues establish credibility and reliability
- **Focus Enhancement:** Medium blues improve concentration and attention
- **Stress Reduction:** Light blues create calming, comfortable learning spaces
- **Accessibility:** High contrast blue combinations ensure readability

## 6. Design Guidelines Implementation

### 🎨 Official Color Values from Design Guidelines Image (MANDATORY)

These colors are extracted from the official design guidelines image and MUST be used exactly as specified:

#### Primary Color Hierarchy from Design Guidelines Image
```typescript
// From Design Guidelines Image - Exact Color Values
const designGuidelineColors = {
  // Deep Navy Blue
  deepNavyBlue: {
    hex: "#132139",
    rgb: "rgb(19, 33, 57)",
    description: "Deep navy blue from design guidelines image",
    usage: "Headers, navigation, primary text, maximum contrast elements"
  },

  // Royal Blue - Main Brand Color
  royalBlue: {
    hex: "#23408B",
    rgb: "rgb(35, 64, 139)",
    description: "Royal blue - main brand color from design guidelines image",
    usage: "Primary buttons, interactive elements, main brand identity"
  },

  // Sky Blue - Secondary Brand Color
  skyBlue: {
    hex: "#599FD6",
    rgb: "rgb(89, 159, 214)",
    description: "Sky blue - secondary brand color from design guidelines image",
    usage: "Secondary buttons, hover states, accent elements, highlights"
  },

  // Light Green/Mint - Accent Color
  lightGreenMint: {
    hex: "#ADF1D2",
    rgb: "rgb(173, 241, 210)",
    description: "Light green/mint - accent color from design guidelines image",
    usage: "Success states, positive feedback, accent highlights, fresh elements"
  },

  // Pure White - Background Color
  pureWhite: {
    hex: "#FFFFFF",
    rgb: "rgb(255, 255, 255)",
    description: "Pure white - background color from design guidelines image",
    usage: "Main backgrounds, card backgrounds, text on dark elements"
  }
}
```

### Implementation Rules
1. **EXACT COLORS**: Use exact hex values from design guidelines
2. **NO APPROXIMATION**: Do not use similar or "close enough" colors
3. **CONSISTENT USAGE**: Apply colors according to their designated usage
4. **BRAND COMPLIANCE**: Maintain consistency with Figma designs
5. **GRADIENT SUPPORT**: Use official gradient when backgrounds are needed

### Color Mapping in Code
```typescript
// app/theme/colors.ts mapping - Updated from Design Guidelines Image
primary300: "#599FD6"  // ✅ Sky blue from design guidelines image
primary400: "#23408B"  // ✅ Royal blue (main brand) from design guidelines image
primary500: "#132139"  // ✅ Deep navy blue from design guidelines image
accent400: "#ADF1D2"   // ✅ Light green/mint from design guidelines image
background: "#FFFFFF"  // ✅ Pure white from design guidelines image
```

## 7. Quality Assurance

### Color Palette Checklist
- [ ] White backgrounds used for all screens and cards (MANDATORY)
- [ ] Blue color hierarchy properly implemented
- [ ] Accent orange (#FFBB50) used for progress elements (MANDATORY)
- [ ] Proper contrast ratios maintained (minimum 4.5:1)
- [ ] Color blindness considerations addressed
- [ ] Dark mode compatibility ensured
- [ ] Semantic color naming followed
- [ ] Brand consistency maintained

### Accessibility Standards
- [ ] WCAG 2.1 AA compliance verified
- [ ] Color combinations tested for contrast
- [ ] Alternative indicators provided (not color-only)
- [ ] Color blindness simulation tested
- [ ] High contrast mode support
- [ ] Focus indicators clearly visible
- [ ] Status colors distinguishable
- [ ] Text readability optimized

This color palette ensures a professional, accessible, and psychologically optimized learning environment throughout eb-lms-app.
