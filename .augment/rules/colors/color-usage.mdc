---
description: Color usage guidelines and implementation patterns for eb-lms-app
globs:
alwaysApply: true
---

# Color Usage Guidelines

## Overview

This document defines how to properly use colors throughout eb-lms-app, including semantic color mapping, usage patterns, and implementation examples with strict adherence to the white background policy.

## 1. Semantic Color Mapping

### Text Colors (Updated from Design Guidelines)
```typescript
text: palette.primary700        // Primary text color - deep navy (#0F1B2A)
textDim: palette.primary600     // Secondary/dimmed text - deep navy (#132339) - RGB(19, 33, 57)
textLight: palette.primary300   // Light text - sky blue (#599FD6) - RGB(89, 159, 214)
textOnPrimary: palette.primary100 // Text on primary backgrounds - light blue (#E8F2FF)
```

### Background Colors - CURRENT IMPLEMENTATION PATTERNS
```typescript
// Auth screens background (Login, Signup, Forgot Password)
authBackground: "#F5F9FF"       // Light blue background - CURRENT IMPLEMENTATION
// Student screens background (Home, Courses, Certificate)
studentBackground: "#FFFFFF"    // White background - CURRENT IMPLEMENTATION
// Profile screens background
profileBackground: "#F5F9FF"    // Light blue background - CURRENT IMPLEMENTATION
// Card backgrounds
backgroundCard: "#FFFFFF"       // Card backgrounds - pure white (MANDATORY)
backgroundAccent: palette.primary200 // Accent backgrounds - light blue (special cases only)
transparent: "rgba(0, 0, 0, 0)" // Transparent background
```

**CURRENT BACKGROUND PATTERNS:**
- **Auth Screens:** Light blue (#F5F9FF) - Login, Signup, Forgot Password, Phone Login
- **Student Screens:** White (#FFFFFF) - Home, Courses, Certificate, Schedule
- **Profile Screens:** Light blue (#F5F9FF) - Profile and settings screens
- **Consistency:** Maintain current patterns per screen type
- **Override Policy:** Background color patterns are established - do not change without explicit request

### Interactive Colors (Updated from Design Guidelines)
```typescript
tint: palette.primary500        // MAIN BRAND COLOR - royal blue (#23408B) - RGB(35, 64, 139)
tintSecondary: palette.primary400 // Secondary interactive - deep navy (#132339) - RGB(19, 33, 57)
tintPressed: palette.primary600 // Pressed state - figma brand (#0961F5)
tintInactive: palette.primary300 // Inactive interactive elements - sky blue (#599FD6)
```

### Navigation Colors (Updated from Design Guidelines)
```typescript
navigationPrimary: palette.primary400   // Main navigation - deep navy (#132339) - RGB(19, 33, 57)
navigationSecondary: palette.primary500 // Sub navigation - MAIN BRAND COLOR (#23408B) - RGB(35, 64, 139)
navigationText: palette.primary100      // Navigation text - light blue (#E8F2FF)
navigationActive: palette.primary300    // Active navigation item - sky blue (#599FD6) - RGB(89, 159, 214)
```

### UI Element Colors (Updated from Design Guidelines)
```typescript
border: palette.primary300      // Default border color - sky blue (#599FD6) - RGB(89, 159, 214)
borderStrong: palette.primary500 // Strong borders - MAIN BRAND COLOR (#23408B) - RGB(35, 64, 139)
separator: palette.primary200   // Line separators - very light blue (#C2DDFF)
```

### Status Colors
```typescript
error: palette.angry500         // Error messages and states (#C03403)
errorBackground: palette.angry100 // Error background areas (#F2D6CD)
success: "#10B981"             // Success states - green
warning: "#F59E0B"             // Warning states - amber
info: palette.primary400       // Info states - sky blue (#599FD6)
```

### Form Elements and Input Fields (Light & Bright Theme)
```typescript
// MANDATORY: Light and bright input field colors for better UX
inputBackground: "#FFFFFF"      // Pure white background for input fields
inputBackgroundLight: "#FAFBFC" // Very light gray background alternative
inputBorder: "#F1F3F4"         // Very light gray border (almost white)
inputBorderHover: "#E8EAED"    // Slightly darker on hover
inputBorderFocus: "#FFBB50"    // Orange border on focus (brand accent) - MANDATORY
inputPlaceholder: "#505050"    // Placeholder text color from auth screens (current implementation)
inputText: "#202124"           // Dark text for good contrast
inputLabel: "#5F6368"          // Medium gray for labels
inputError: "#FECACA"          // Light red border for errors
inputSuccess: "#D1FAE5"        // Light green border for success
inputDisabled: "#F8F9FA"       // Very light gray for disabled state
```

## 2. Usage Guidelines by Component Type

### Text Color Usage
- **Primary Text:** Use `colors.text` (#333333 - updated primary text color) for main content
- **Secondary Text:** Use `colors.textDim` (primary600 - dark blue) for supporting information
- **Light Text:** Use `colors.textLight` (primary400 - sky blue) for subtle information
- **Text on Primary:** Use `colors.textOnPrimary` (primary100 - light blue) for text on dark backgrounds
- **Interactive Text:** Use `colors.tint` (primary500 - royal blue) for links and buttons
- **Error Text:** Use `colors.error` for error messages

### Background Usage - WHITE BACKGROUND RULE
- **Main Background:** Use `colors.background` (#FFFFFF - WHITE) for ALL screen backgrounds
- **Card Backgrounds:** Use `colors.backgroundCard` (#FFFFFF - WHITE) for elevated surfaces
- **Accent Backgrounds:** Use `colors.backgroundAccent` (light blue) ONLY for special highlighted areas
- **Error Backgrounds:** Use `colors.errorBackground` for error states

**CRITICAL RULE: WHITE BACKGROUND POLICY**
- ✅ **DO:** Always use white (#FFFFFF) as the default background
- ✅ **DO:** Maintain white backgrounds for clean, professional appearance
- ✅ **DO:** Use white for optimal text readability and accessibility
- ❌ **DON'T:** Change background color unless explicitly requested by user
- ❌ **DON'T:** Use colored backgrounds as default
- ❌ **DON'T:** Apply blue backgrounds to main screen areas without permission

### Navigation Usage
- **Main Navigation Bar:** Use `colors.navigationPrimary` (primary700 - deep navy)
- **Sub Navigation:** Use `colors.navigationSecondary` (primary600 - dark blue)
- **Navigation Text:** Use `colors.navigationText` (primary100 - light blue)
- **Active Navigation:** Use `colors.navigationActive` (primary400 - sky blue)

### Interactive Element Usage
- **Primary Buttons:** Use `colors.tint` (primary500 - royal blue)
- **Secondary Buttons:** Use `colors.tintSecondary` (primary400 - sky blue)
- **Button Pressed State:** Use `colors.tintPressed` (primary600 - dark blue)
- **Disabled States:** Use `colors.tintInactive` (primary300 - light blue)
- **Links:** Use `colors.tint` with underline for accessibility

### Border and Separator Usage
- **Default Borders:** Use `colors.border` (primary300 - light blue)
- **Strong Borders:** Use `colors.borderStrong` (primary500 - royal blue)
- **Subtle Separators:** Use `colors.separator` (primary200 - very light blue)
- **Emphasis Borders:** Use `colors.tint` (primary500 - royal blue)

### Form Elements and Input Field Usage (Light & Bright Theme)
- **Input Background:** Use `colors.inputBackground` (#FFFFFF - pure white) for all input fields
- **Input Border:** Use `colors.inputBorder` (#F1F3F4 - very light gray) for default state
- **Input Focus:** Use `colors.inputBorderFocus` (#FFBB50 - orange) for focus state - brand consistency
- **Input Placeholder:** Use `colors.inputPlaceholder` (#9AA0A6 - light gray) for placeholder text
- **Input Text:** Use `colors.inputText` (#202124 - dark) for input content
- **Input Labels:** Use `colors.inputLabel` (#5F6368 - medium gray) for field labels
- **Error State:** Use `colors.inputError` (#FECACA - light red) for error borders
- **Success State:** Use `colors.inputSuccess` (#D1FAE5 - light green) for success borders
- **Disabled State:** Use `colors.inputDisabled` (#F8F9FA - very light gray) for disabled background

**CRITICAL INPUT FIELD RULES:**
- ✅ **DO:** Always use light, bright colors for better readability
- ✅ **DO:** Use white background for all input fields
- ✅ **DO:** Use orange accent for focus states to match brand
- ✅ **DO:** Maintain high contrast between text and background
- ❌ **DON'T:** Use dark or heavy backgrounds for input fields
- ❌ **DON'T:** Use colors that make text hard to read
- ❌ **DON'T:** Ignore accessibility contrast requirements

### Gradient Usage
- **Hero Sections:** Use the main gradient `linear-gradient(90deg, #132339 0%, #23408B 50%, #599FD6 100%)`
- **Button Gradients:** Use `linear-gradient(45deg, #23408B 0%, #599FD6 100%)`
- **Background Accents:** Use subtle gradients with primary200 to primary300

## 3. Implementation Examples

### Component Color Usage
```typescript
// Primary Button component
const $primaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.tint,           // Royal blue (#23408B)
  borderColor: colors.borderStrong,       // Royal blue border
})

const $primaryButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textOnPrimary,           // Light blue text (#E8F2FF)
})

// Secondary Button component
const $secondaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.tintSecondary,  // Sky blue (#599FD6)
  borderColor: colors.border,            // Light blue border
})

const $secondaryButtonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text,                    // Primary text color (#333333)
})

// Navigation Bar component
const $navigationBar: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.navigationPrimary, // Deep navy (#132339)
  borderBottomColor: colors.navigationSecondary, // Dark blue border
})

const $navigationText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.navigationText,          // Light blue (#E8F2FF)
})

// Screen Background component - ALWAYS WHITE
const $screenBackground: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background,     // WHITE (#FFFFFF) - MANDATORY
  flex: 1,
})

// Card component - WHITE background
const $card: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.backgroundCard, // WHITE (#FFFFFF) background
  borderColor: colors.separator,         // Very light blue border
  shadowColor: colors.palette.primary700, // Deep navy shadow
})

// Input Field component - Light & Bright
const $inputField: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.inputBackground, // WHITE (#FFFFFF)
  borderColor: colors.inputBorder,        // Very light gray (#F1F3F4)
  borderWidth: 1,
  borderRadius: 8,
  paddingHorizontal: 16,
  paddingVertical: 12,
})

const $inputFieldFocused: ThemedStyle<ViewStyle> = ({ colors }) => ({
  borderColor: colors.inputBorderFocus,   // Orange (#FFBB50) - MANDATORY
  shadowColor: colors.inputBorderFocus,
  shadowOffset: { width: 0, height: 0 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
})
```

### Theme Context Usage
```typescript
// Using colors in components
const MyComponent = () => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <Text style={themed($text)}>Hello World</Text>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background, // Always white
})

const $text: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.text, // Deep navy
})
```

## 4. Quality Assurance

### Color Usage Checklist
- [ ] White backgrounds used for all screens and cards (MANDATORY)
- [ ] Semantic color names used instead of hardcoded values
- [ ] Proper contrast ratios maintained (minimum 4.5:1)
- [ ] Orange accent (#FFBB50) used for progress and focus states (MANDATORY)
- [ ] Light, bright colors used for input fields
- [ ] Color accessibility standards met
- [ ] Dark mode compatibility ensured
- [ ] Brand consistency maintained

### Implementation Standards
- [ ] ThemedStyle functions used for all color applications
- [ ] Color values accessed through theme context
- [ ] Consistent color patterns across components
- [ ] Proper error and success state colors
- [ ] Navigation colors follow established hierarchy
- [ ] Interactive states clearly defined
- [ ] Form elements use light, bright color scheme
- [ ] Background color policy strictly followed

## 5. Design Guidelines Implementation (MANDATORY)

### Official Color Values
These colors are extracted from the official design guidelines and MUST be used exactly:

```typescript
// Design Guidelines Color Mapping
const designGuidelineColors = {
  deepNavy: "#132339",    // RGB(19, 33, 57) - Màu chủ đạo
  royalBlue: "#23408B",   // RGB(35, 64, 139) - Màu bổ trợ
  skyBlue: "#599FD6",     // RGB(89, 159, 214) - Màu bổ trợ
  brandPrimary: "#0961F5" // Exact from Figma - Main brand color
}
```

### Implementation Rules
1. **EXACT COLORS**: Use exact hex values from design guidelines
2. **NO APPROXIMATION**: Do not use similar or "close enough" colors
3. **CONSISTENT USAGE**: Apply colors according to their designated usage
4. **BRAND COMPLIANCE**: Maintain consistency with Figma designs
5. **GRADIENT SUPPORT**: Use official gradient when backgrounds are needed

### Updated Component Examples
```typescript
// Primary Button - Updated with Design Guidelines
const $primaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.tint,         // MAIN BRAND COLOR (#23408B)
  borderColor: colors.borderStrong,     // MAIN BRAND COLOR border
})

// Secondary Button - Updated with Design Guidelines
const $secondaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.tintInactive, // Sky blue from guidelines (#599FD6)
  borderColor: colors.border,           // Sky blue border
})

// Navigation Bar - Updated with Design Guidelines
const $navigationBar: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.tintSecondary, // Deep navy from guidelines (#132339)
  borderBottomColor: colors.tint,       // MAIN BRAND COLOR border (#23408B)
})
```

This color usage guide ensures consistent, accessible, and professional color application throughout eb-lms-app, with full compliance to the official design guidelines.
