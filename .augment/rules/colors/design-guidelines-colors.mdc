---
description: Official design guidelines color implementation for eb-lms-app
globs:
alwaysApply: true
---

# Design Guidelines Color Implementation

## 🎨 Official Brand Colors (MANDATORY)

This document contains the exact color specifications from the official design guidelines image provided by the design team.

### Color Palette Overview

The design guidelines specify three main colors that form the foundation of the brand identity:

1. **<PERSON><PERSON>u chủ đạo (Primary Dark)**: RGB(19, 33, 57) = #132339
2. **<PERSON><PERSON><PERSON> bổ trợ (Primary Medium)**: RGB(35, 64, 139) = #23408B  
3. **<PERSON><PERSON><PERSON> bổ trợ (Primary Light)**: RGB(89, 159, 214) = #599FD6

### Exact Color Specifications

#### Primary Text Color - Updated
```typescript
primaryText: {
  hex: "#333333",
  rgb: "rgb(51, 51, 51)",
  usage: [
    "Primary text content",
    "Headings and titles",
    "Body text",
    "Main readable content",
    "Course titles and descriptions"
  ],
  accessibility: "Provides excellent contrast against white backgrounds"
}
```

#### Deep Navy - Màu chủ đạo
```typescript
deepNavy: {
  hex: "#132339",
  rgb: "rgb(19, 33, 57)",
  cmyk: "cmyk(57%, 81%, 48%, 57%)",
  usage: [
    "Main navigation headers",
    "Primary brand identity",
    "App bar backgrounds",
    "Footer backgrounds"
  ],
  accessibility: "Provides excellent contrast against white backgrounds"
}
```

#### Royal Blue - Màu bổ trợ
```typescript
royalBlue: {
  hex: "#23408B", 
  rgb: "rgb(35, 64, 139)",
  cmyk: "cmyk(100%, 89%, 11%, 2%)",
  usage: [
    "Secondary buttons",
    "Interactive elements",
    "Pressed/active states", 
    "Links and anchors",
    "Secondary brand elements"
  ],
  accessibility: "Good contrast for interactive elements"
}
```

#### Sky Blue - Màu bổ trợ  
```typescript
skyBlue: {
  hex: "#599FD6",
  rgb: "rgb(89, 159, 214)",
  cmyk: "cmyk(63%, 26%, 0%, 0%)",
  usage: [
    "Accent highlights",
    "Hover states",
    "Secondary buttons",
    "Progress indicators",
    "Active tab indicators"
  ],
  accessibility: "Suitable for accent elements with proper contrast"
}
```

#### MAIN BRAND COLOR (Primary Medium) - OFFICIAL
```typescript
brandPrimary: {
  hex: "#23408B",
  rgb: "rgb(35, 64, 139)",
  usage: [
    "Primary action buttons",
    "Main CTAs",
    "Brand identity",
    "Login/signup buttons",
    "All primary interactive elements"
  ],
  note: "MAIN BRAND COLOR from design guidelines - use for all primary elements"
}
```

#### Secondary Brand Color (Figma)
```typescript
brandSecondary: {
  hex: "#0961F5",
  rgb: "rgb(9, 97, 245)",
  usage: [
    "Secondary buttons",
    "Pressed states",
    "Special interactive elements",
    "Figma consistency"
  ],
  note: "Secondary brand color from Figma - use for special cases"
}
```

## Implementation Guidelines

### 1. Color Hierarchy in Code

```typescript
// app/theme/colors.ts - Primary Colors Mapping
const palette = {
  // Updated from Design Guidelines
  primary300: "#599FD6",  // Sky blue - RGB(89, 159, 214)
  primary400: "#132339",  // Deep navy - RGB(19, 33, 57)
  primary500: "#23408B",  // MAIN BRAND COLOR - RGB(35, 64, 139)
  primary600: "#0961F5",  // Secondary brand - exact from Figma
}
```

### 2. Gradient Implementation

```css
/* Official Brand Gradient */
.brand-gradient {
  background: linear-gradient(90deg, #132339 0%, #23408B 50%, #599FD6 100%);
}

/* Alternative with Figma Brand Color */
.brand-gradient-alt {
  background: linear-gradient(90deg, #132339 0%, #0961F5 50%, #599FD6 100%);
}
```

### 3. Usage Rules (MANDATORY)

#### ✅ DO:
- Use exact hex values from design guidelines
- Apply colors according to their designated usage
- Maintain consistency with Figma designs
- Test contrast ratios for accessibility
- Use white backgrounds as default (mandatory)

#### ❌ DON'T:
- Approximate or modify the official colors
- Use similar colors that are "close enough"
- Apply colors outside their designated usage
- Override brand colors without explicit permission
- Use colored backgrounds as default

### 4. Component-Specific Implementation

#### Buttons
```typescript
// Primary Button (Main CTAs)
backgroundColor: "#23408B"  // MAIN BRAND COLOR from design guidelines

// Secondary Button
backgroundColor: "#599FD6"  // Sky blue from guidelines

// Pressed State
backgroundColor: "#0961F5"  // Figma brand color for pressed states

// Deep Navy State
backgroundColor: "#132339"  // Deep navy from guidelines
```

#### Navigation
```typescript
// Main Navigation
backgroundColor: "#132339"  // Deep navy - màu chủ đạo

// Sub Navigation
backgroundColor: "#23408B"  // MAIN BRAND COLOR - màu bổ trợ
```

#### Interactive Elements
```typescript
// Hover States
backgroundColor: "#599FD6"  // Sky blue - màu bổ trợ

// Active States
backgroundColor: "#23408B"  // MAIN BRAND COLOR - màu bổ trợ

// Pressed States
backgroundColor: "#0961F5"  // Figma brand color
```

## Quality Assurance Checklist

### Design Guidelines Compliance
- [ ] All colors match exact hex values from guidelines
- [ ] Color usage follows designated purposes
- [ ] Brand consistency maintained with Figma
- [ ] No approximated or modified colors used
- [ ] Gradient implementation matches specifications

### Accessibility Standards
- [ ] Contrast ratios meet WCAG 2.1 AA standards
- [ ] Color combinations tested for readability
- [ ] Alternative indicators provided (not color-only)
- [ ] Color blindness considerations addressed

### Implementation Standards
- [ ] Colors properly mapped in theme/colors.ts
- [ ] Component usage follows guidelines
- [ ] White background policy maintained
- [ ] Brand color hierarchy respected
- [ ] Documentation updated with changes

This implementation ensures 100% compliance with the official design guidelines while maintaining accessibility and brand consistency throughout the eb-lms-app.
