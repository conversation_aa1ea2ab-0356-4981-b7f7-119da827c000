---
description: Navigation and header component specifications for eb-lms-app
globs:
alwaysApply: true
---

# Navigation Components

## Overview

This document defines navigation components including headers, tab bars, and navigation controls for eb-lms-app with consistent styling and behavior.

## 1. Header Components

### Screen Header
**Purpose:** Standard screen header with navigation and actions

```typescript
interface ScreenHeaderProps {
  title: string
  showBack?: boolean
  onBackPress?: () => void
  rightActions?: ReactElement[]
  variant?: "default" | "large"
  backgroundColor?: string
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** White (#FFFFFF) - MANDATORY
- **Height:** 56px standard, 72px large variant
- **Title:** Center-aligned, semiBold weight (600), 18px
- **Back Button:** Left-aligned arrow, 24px icon
- **Actions:** Right-aligned icons, 44px touch targets
- **Border:** Bottom border light gray (#F4F2F1)
- **Safe Area:** Automatic top safe area handling

**Implementation Example:**
```typescript
export const ScreenHeader = (props: ScreenHeaderProps) => {
  const { themed } = useAppTheme()
  const { title, showBack, onBackPress, rightActions } = props

  return (
    <View style={themed($header)}>
      <View style={themed($headerContent)}>
        {showBack && (
          <Pressable 
            style={themed($backButton)} 
            onPress={onBackPress}
            accessibilityRole="button"
            accessibilityLabel="Go back"
          >
            <Icon icon="arrow-left" size={24} />
          </Pressable>
        )}
        
        <Text style={themed($headerTitle)}>{title}</Text>
        
        <View style={themed($headerActions)}>
          {rightActions}
        </View>
      </View>
    </View>
  )
}
```

### Profile Header
**Purpose:** User profile header with avatar and greeting

```typescript
interface ProfileHeaderProps {
  name: string
  greeting?: string
  avatar?: string
  showSearch?: boolean
  showNotifications?: boolean
  onSearchPress?: () => void
  onNotificationPress?: () => void
  onAvatarPress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Layout:** Horizontal with avatar left, content center, actions right
- **Avatar:** 48px circular with subtle border
- **Greeting:** Light weight (300), 14px - MANDATORY
- **Name:** SemiBold weight (600), 18px
- **Search:** Right-aligned icon, 24px
- **Spacing:** 16px padding, 12px between elements
- **Background:** White (#FFFFFF) with optional subtle shadow

## 2. Bottom Tab Bar

### Tab Bar Container
**Purpose:** Main navigation tab bar for app sections

```typescript
interface BottomTabProps {
  routes: Array<{
    name: string
    icon: string
    label: string
    badge?: number
  }>
  activeRoute: string
  onRoutePress: (route: string) => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** White (#FFFFFF) - MANDATORY
- **Height:** 60px + safe area bottom
- **Border:** Top border light gray (#F4F2F1)
- **Active:** Orange (#FFBB50) icon and text - MANDATORY
- **Inactive:** Gray (#978F8A) icon and text
- **Typography:** 12px regular weight labels
- **Shadow:** Subtle top shadow for elevation

### Tab Item
**Purpose:** Individual tab item with icon and label

```typescript
interface TabItemProps {
  icon: string
  label: string
  isActive: boolean
  badge?: number
  onPress: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Icon Size:** 24px standard
- **Label:** 12px font size, light weight (300) - MANDATORY
- **Active Color:** Orange (#FFBB50) - MANDATORY
- **Inactive Color:** Gray (#978F8A)
- **Touch Target:** Full tab area, minimum 44px height
- **Badge:** Red circle with white text, positioned top-right
- **Animation:** Smooth color transitions (200ms)

## 3. Navigation Controls

### Back Button
**Purpose:** Standard back navigation control

```typescript
interface BackButtonProps {
  onPress: () => void
  color?: string
  size?: number
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Icon:** Left arrow, 24px standard
- **Color:** Dark blue (#132339) default
- **Touch Target:** 44px minimum
- **Padding:** 10px for comfortable touch
- **Animation:** Subtle press feedback

### Menu Button
**Purpose:** Hamburger menu or drawer toggle

```typescript
interface MenuButtonProps {
  onPress: () => void
  color?: string
  size?: number
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Icon:** Three horizontal lines, 24px
- **Color:** Dark blue (#132339) default
- **Touch Target:** 44px minimum
- **Animation:** Rotate to X when active

### Close Button
**Purpose:** Modal and overlay close control

```typescript
interface CloseButtonProps {
  onPress: () => void
  variant?: "x" | "arrow-down"
  color?: string
  size?: number
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Icon:** X or down arrow, 24px
- **Color:** Dark gray (#6B7280) default
- **Touch Target:** 44px minimum
- **Position:** Top-right of modal/overlay

## 4. Breadcrumb Navigation

### Breadcrumb
**Purpose:** Hierarchical navigation indicator

```typescript
interface BreadcrumbProps {
  items: Array<{
    label: string
    onPress?: () => void
  }>
  separator?: string
  maxItems?: number
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Typography:** Light weight (300), 14px - MANDATORY
- **Separator:** "/" or ">" between items
- **Active:** Dark text, pressable
- **Current:** Light gray, non-pressable
- **Max Items:** Collapse middle items with "..."

## 5. Search Header

### Search Bar Header
**Purpose:** Search-focused header with input

```typescript
interface SearchHeaderProps {
  placeholder?: string
  value?: string
  onChangeText?: (text: string) => void
  onSubmit?: () => void
  onCancel?: () => void
  showCancel?: boolean
  autoFocus?: boolean
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** White (#FFFFFF)
- **Input Background:** Light gray (#F8F9FA)
- **Border Radius:** 20px (pill shape)
- **Height:** 40px input
- **Icon:** Search icon left, 20px gray
- **Cancel:** Right-aligned text button
- **Typography:** Regular weight, 16px

## 6. Action Bar

### Action Bar
**Purpose:** Contextual actions for selected items

```typescript
interface ActionBarProps {
  selectedCount: number
  actions: Array<{
    icon: string
    label: string
    onPress: () => void
    destructive?: boolean
  }>
  onCancel: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** Dark blue (#132339)
- **Text:** White text and icons
- **Height:** 56px
- **Actions:** Horizontal layout with icons
- **Cancel:** Left-aligned X button
- **Count:** Center text showing selection

## 7. Floating Action Button

### FAB
**Purpose:** Primary floating action for screens

```typescript
interface FABProps {
  icon: string
  onPress: () => void
  size?: "small" | "medium" | "large"
  color?: string
  position?: "bottom-right" | "bottom-center"
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** Orange (#FFBB50) - MANDATORY
- **Size:** 56px standard, 40px small, 72px large
- **Icon:** White icon, 24px standard
- **Shadow:** Strong elevation (elevation: 6)
- **Position:** 16px from edges
- **Animation:** Scale on press, rotate if needed

## 8. Quality Assurance

### Navigation Component Checklist
- [ ] White backgrounds for headers and tab bars (MANDATORY)
- [ ] Orange accent color (#FFBB50) for active states (MANDATORY)
- [ ] Light font weight (300) for labels and secondary text (MANDATORY)
- [ ] Touch targets minimum 44px
- [ ] Proper safe area handling
- [ ] Accessibility labels and roles
- [ ] Smooth animations and transitions
- [ ] Consistent icon sizes (24px standard)

### Design Compliance
- [ ] Typography hierarchy maintained
- [ ] Color contrast meets WCAG standards
- [ ] Consistent spacing and padding
- [ ] Proper elevation and shadows
- [ ] Platform-appropriate behavior
- [ ] Responsive design considerations
- [ ] Performance optimizations applied

### Navigation UX Standards
- [ ] Clear navigation hierarchy
- [ ] Intuitive back navigation
- [ ] Consistent interaction patterns
- [ ] Proper focus management
- [ ] Keyboard navigation support
- [ ] Screen reader compatibility
- [ ] Loading states for navigation
- [ ] Error handling for navigation failures

These navigation components ensure consistent, accessible, and intuitive navigation throughout eb-lms-app.
