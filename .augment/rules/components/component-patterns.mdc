---
description: Component development patterns and best practices for eb-lms-app
globs:
alwaysApply: true
---

# Component Development Patterns

## Overview

This document outlines the development patterns, conventions, and best practices for building components in eb-lms-app. Following these patterns ensures consistency, maintainability, and performance.

## 1. Component Architecture Patterns

### Component Types Hierarchy
```
Base Components (/app/components/)
├── Primitives (Text, Button, Icon)
├── Form Components (TextField, Switch)
├── Layout Components (Screen, Card)
└── Composite Components (Header, List)

Feature Components
├── LMS-Specific (CourseCard, ProgressStats)
├── Navigation (TabBar, Header)
└── Business Logic (AuthForm, CourseList)

Screen Components (/app/screens/)
├── Authentication (Login, Register)
├── Main App (Home, Courses, Profile)
└── Onboarding (Welcome, Tutorial)
```

### Naming Conventions
- **PascalCase** for component files: `Button.tsx`, `CourseCard.tsx`
- **Descriptive names** indicating purpose: `EmptyState.tsx`, `LoadingSpinner.tsx`
- **Avoid generic names** like `Component.tsx` or `Item.tsx`
- **Consistent suffixes**: `Screen.tsx`, `Modal.tsx`, `Card.tsx`

## 2. Props Interface Patterns

### Standard Props Structure
```typescript
interface ComponentProps {
  // Required props first
  title: string
  onPress: () => void

  // Optional props with defaults
  disabled?: boolean
  variant?: "primary" | "secondary"
  size?: "small" | "medium" | "large"

  // Style overrides
  style?: StyleProp<ViewStyle>
  textStyle?: StyleProp<TextStyle>
  containerStyle?: StyleProp<ViewStyle>

  // Content and children
  children?: ReactNode
  icon?: IconTypes
  description?: string

  // Accessibility
  accessibilityLabel?: string
  accessibilityHint?: string
  testID?: string

  // Internationalization
  tx?: TxKeyPath
  txOptions?: TOptions
}
```

### Default Props Pattern
```typescript
export const MyComponent = (props: MyComponentProps) => {
  const {
    disabled = false,
    variant = "primary",
    size = "medium",
    style: $styleOverride,
    textStyle: $textStyleOverride,
    ...rest
  } = props

  // Component implementation
}
```

## 3. Styling Patterns

### Style Object Naming Convention
```typescript
// Use descriptive $ prefixed names
const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.background,
  padding: spacing.md,
})

const $header: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
  alignItems: "center",
})

const $title: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  color: colors.text,
  fontFamily: typography.primary.bold,
  fontSize: 18,
})

const $description: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  color: colors.textDim,
  fontFamily: typography.primary.light, // Font weight 300 - MANDATORY
  fontSize: 14,
})
```

### Conditional Styling
```typescript
const $button: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.tint,
  borderRadius: 16,
  height: 44,
})

const $disabledButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.tintInactive,
  opacity: 0.6,
})

// Usage with conditional styles
<View style={themed([
  $button,
  disabled && $disabledButton,
  $styleOverride
])} />
```

### Responsive Styling
```typescript
const $responsiveContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  padding: spacing.md,
  flexDirection: Platform.select({
    web: "row",
    default: "column",
  }),
  gap: Platform.select({
    web: spacing.lg,
    default: spacing.md,
  }),
})
```

## 4. Theming Integration

### Standard Theming Pattern
```typescript
import { useAppTheme } from "@/utils/useAppTheme"

export const ThemedComponent = (props: ComponentProps) => {
  const { themed, theme } = useAppTheme()
  const { colors, spacing, typography } = theme

  return (
    <View style={themed($container)}>
      <Text style={themed($title)}>Title</Text>
      <Text style={themed($description)}>Description</Text>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.background, // Always white - MANDATORY
  borderRadius: 12,
  padding: spacing.md,
  shadowColor: colors.shadow,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
})
```

### Theme-Aware Colors
```typescript
// Use theme colors, not hardcoded values
const $themedStyles: ThemedStyle<ViewStyle> = ({ colors }) => ({
  // ✅ Correct - uses theme colors
  backgroundColor: colors.background,
  borderColor: colors.border,
  
  // ❌ Avoid - hardcoded colors
  // backgroundColor: "#FFFFFF",
  // borderColor: "#E5E5E5",
})
```

## 5. Performance Optimization

### Memoization Patterns
```typescript
import { memo, useMemo, useCallback } from "react"

export const OptimizedComponent = memo((props: ComponentProps) => {
  const { data, onItemPress, filterText } = props

  // Memoize expensive calculations
  const filteredData = useMemo(() => {
    return data.filter(item => 
      item.title.toLowerCase().includes(filterText.toLowerCase())
    )
  }, [data, filterText])

  // Memoize callbacks to prevent child re-renders
  const handlePress = useCallback((id: string) => {
    onItemPress(id)
  }, [onItemPress])

  return (
    <View>
      {filteredData.map(item => (
        <Item 
          key={item.id} 
          data={item} 
          onPress={handlePress} 
        />
      ))}
    </View>
  )
})

// Display name for debugging
OptimizedComponent.displayName = "OptimizedComponent"
```

### List Performance
```typescript
import { FlashList } from "@shopify/flash-list"

export const OptimizedList = ({ data }: { data: Item[] }) => {
  const renderItem = useCallback(({ item }: { item: Item }) => (
    <ListItem data={item} />
  ), [])

  const keyExtractor = useCallback((item: Item) => item.id, [])

  return (
    <FlashList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      estimatedItemSize={80}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
    />
  )
}
```

## 6. Error Handling

### Error Boundary Pattern
```typescript
interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export class ComponentErrorBoundary extends Component<
  PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: PropsWithChildren<{}>) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Component error:", error, errorInfo)
    // Log to crash reporting service
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />
    }

    return this.props.children
  }
}
```

### Safe Component Pattern
```typescript
export const SafeComponent = (props: ComponentProps) => {
  try {
    return <ActualComponent {...props} />
  } catch (error) {
    console.error("Component render error:", error)
    return <ErrorFallback error={error} />
  }
}
```

## 7. Testing Patterns

### Component Testing Structure
```typescript
// MyComponent.test.tsx
import { render, fireEvent } from "@testing-library/react-native"
import { MyComponent } from "./MyComponent"

describe("MyComponent", () => {
  const defaultProps = {
    title: "Test Title",
    onPress: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it("renders correctly", () => {
    const { getByText } = render(<MyComponent {...defaultProps} />)
    expect(getByText("Test Title")).toBeTruthy()
  })

  it("handles press events", () => {
    const onPress = jest.fn()
    const { getByRole } = render(
      <MyComponent {...defaultProps} onPress={onPress} />
    )

    fireEvent.press(getByRole("button"))
    expect(onPress).toHaveBeenCalledTimes(1)
  })

  it("applies disabled state correctly", () => {
    const { getByRole } = render(
      <MyComponent {...defaultProps} disabled />
    )

    expect(getByRole("button")).toHaveAccessibilityState({ disabled: true })
  })
})
```

## 8. Quality Assurance

### Component Development Checklist
- [ ] TypeScript interfaces properly defined
- [ ] Theming support implemented
- [ ] Accessibility props included
- [ ] Error handling implemented
- [ ] Performance optimizations applied
- [ ] Tests written and passing
- [ ] Documentation complete
- [ ] Code review completed

### Design System Compliance
- [ ] Uses white backgrounds (MANDATORY)
- [ ] Font weight 300 for descriptions (MANDATORY)
- [ ] Proper spacing (16px standard)
- [ ] Consistent border radius (16px main, 12px social)
- [ ] Appropriate shadows and elevation
- [ ] Color contrast meets WCAG standards
- [ ] Touch targets minimum 44px

This pattern guide ensures all components are built consistently and maintain high quality standards across the eb-lms-app.
