---
description: Base component specifications and interfaces for eb-lms-app
globs:
alwaysApply: true
---

# Base Components

## Overview

This document defines the core base components used throughout eb-lms-app. These components provide the foundation for all UI elements and ensure consistency across the application.

## 1. Text Component

**Purpose:** Typography with theming support and internationalization

```typescript
interface TextProps {
  text?: string
  tx?: TxKeyPath  // Translation key
  txOptions?: TOptions
  preset?: "default" | "bold" | "heading" | "subheading" | "formLabel" | "formHelper"
  weight?: keyof typeof typography.primary
  size?: keyof typeof $sizeStyles
  style?: StyleProp<TextStyle>
  children?: ReactNode
}
```

**Design Specifications:**
- **Font Family:** Nunito Sans (primary typography)
- **Font Weight 500:** MANDATORY for all descriptions and body text
- **Font Weight 600:** For headings and emphasis
- **Font Weight 700:** For page titles only
- **Color:** Uses theme colors (colors.text, colors.textDim)
- **Internationalization:** Built-in translation support

**Usage Examples:**
```typescript
<Text tx="common.welcome" preset="heading" />
<Text text="Description text" weight="light" />
<Text preset="formLabel">Form Label</Text>
```

## 2. Button Component

**Purpose:** Interactive buttons with consistent styling and accessibility

```typescript
interface ButtonProps {
  text?: string
  tx?: TxKeyPath
  preset?: "default" | "filled" | "reversed" | "secondary" | "play"
  style?: StyleProp<ViewStyle>
  textStyle?: StyleProp<TextStyle>
  onPress?: () => void
  disabled?: boolean
  loading?: boolean
  LeftAccessory?: ComponentType<ButtonAccessoryProps>
  RightAccessory?: ComponentType<ButtonAccessoryProps>
}
```

**Design Specifications:**
- **Height:** 44px (minHeight: 44px) for accessibility
- **Border Radius:** 16px for main actions, 12px for social, 8px default
- **Typography:** Font weight 300 (light) - MANDATORY
- **Padding:** 12px horizontal, 12px vertical
- **Shadow:** Consistent elevation based on preset
- **Accessibility:** Full WCAG compliance

**Preset Specifications:**
- **filled:** Royal blue background, white text
- **default:** White background, gray border
- **secondary:** Sky blue background
- **reversed:** Dark background for light themes
- **play:** Orange circular button (40px diameter)

## 3. Screen Component

**Purpose:** Base wrapper for all screens with safe area and theming

```typescript
interface ScreenProps {
  children?: ReactNode
  style?: StyleProp<ViewStyle>
  contentContainerStyle?: StyleProp<ViewStyle>
  safeAreaEdges?: Edge[]
  backgroundColor?: string
  statusBarStyle?: "light" | "dark"
  keyboardOffset?: number
  KeyboardAvoidingViewProps?: KeyboardAvoidingViewProps
  preset?: "fixed" | "scroll" | "auto"
}
```

**Design Specifications:**
- **Background:** Always white (#FFFFFF) - MANDATORY
- **Safe Area:** Automatic safe area handling
- **Keyboard Avoidance:** Built-in keyboard handling
- **Scroll Support:** Multiple preset options
- **Status Bar:** Automatic status bar styling

**Usage Patterns:**
```typescript
<Screen preset="scroll" safeAreaEdges={["top"]}>
  {/* Screen content */}
</Screen>
```

## 4. TextField Component

**Purpose:** Text input with validation, theming, and accessibility

```typescript
interface TextFieldProps {
  value?: string
  onChangeText?: (text: string) => void
  placeholder?: string
  placeholderTx?: TxKeyPath
  label?: string
  labelTx?: TxKeyPath
  helper?: string
  helperTx?: TxKeyPath
  status?: "error" | "disabled"
  multiline?: boolean
  secureTextEntry?: boolean
  style?: StyleProp<ViewStyle>
  inputWrapperStyle?: StyleProp<ViewStyle>
  containerStyle?: StyleProp<ViewStyle>
  LeftAccessory?: ComponentType<TextFieldAccessoryProps>
  RightAccessory?: ComponentType<TextFieldAccessoryProps>
}
```

**Design Specifications:**
- **Height:** 48px minimum for touch accessibility
- **Border Radius:** 8px for consistency
- **Typography:** Font weight 300 for input text
- **Colors:** Light, bright colors per guidelines
- **Validation:** Built-in error state styling
- **Accessibility:** Full label and hint support

## 5. Card Component

**Purpose:** Container with elevation and consistent spacing

```typescript
interface CardProps {
  preset?: "default" | "reversed"
  verticalAlignment?: "top" | "center" | "bottom" | "space-between"
  style?: StyleProp<ViewStyle>
  contentStyle?: StyleProp<ViewStyle>
  HeadingComponent?: ReactElement
  ContentComponent?: ReactElement
  FooterComponent?: ReactElement
  children?: ReactNode
}
```

**Design Specifications:**
- **Background:** Always white (#FFFFFF) - MANDATORY
- **Border Radius:** 12px for modern appearance
- **Padding:** 16px internal spacing
- **Shadow:** Subtle elevation (shadowOffset: {0, 2}, opacity: 0.1)
- **Spacing:** 16px margin between cards
- **Layout:** Flexible content arrangement

## 6. Icon Component

**Purpose:** Consistent icon rendering with theming support

```typescript
interface IconProps {
  icon: IconTypes
  color?: string
  size?: number
  style?: StyleProp<ImageStyle>
  containerStyle?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Size:** 24px standard, 20px small, 32px large
- **Color:** Theme-aware (colors.text, colors.tint)
- **Touch Targets:** 44px minimum for interactive icons
- **Accessibility:** Proper accessibility labels

## 7. Implementation Guidelines

### Theming Integration
All components must support theming:

```typescript
import { useAppTheme } from "@/utils/useAppTheme"

export const MyComponent = (props: MyComponentProps) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <Text style={themed($text)}>Content</Text>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.background,
  padding: spacing.md,
})
```

### Accessibility Requirements
- **Touch Targets:** Minimum 44px for interactive elements
- **Labels:** Proper accessibility labels and hints
- **Contrast:** WCAG 2.1 AA compliance
- **Screen Readers:** Full screen reader support

### Performance Considerations
- **Memoization:** Use React.memo for expensive components
- **Lazy Loading:** Implement for heavy components
- **Optimization:** Minimize re-renders with proper dependencies

## 8. Quality Assurance

### Component Checklist
- [ ] TypeScript interfaces defined
- [ ] Theming support implemented
- [ ] Accessibility props included
- [ ] Error handling implemented
- [ ] Tests written and passing
- [ ] Documentation complete
- [ ] Design system compliance verified

### Design Compliance
- [ ] White backgrounds used (MANDATORY)
- [ ] Font weight 300 for descriptions (MANDATORY)
- [ ] Proper spacing (16px standard)
- [ ] Consistent border radius
- [ ] Appropriate shadows and elevation
- [ ] Color contrast meets standards

This foundation ensures all base components maintain consistency, accessibility, and performance across the eb-lms-app.
