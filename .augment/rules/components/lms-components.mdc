---
description: LMS-specific UI components for eb-lms-app learning management features
globs:
alwaysApply: true
---

# LMS-Specific Components

## Overview

This document defines specialized components for learning management system features in eb-lms-app. These components are designed specifically for educational content, course management, and student progress tracking.

## 1. Course Card Component

**Purpose:** Display course information with progress and statistics

```typescript
interface CourseCardProps {
  title: string
  description?: string
  progress?: number // 0-100 percentage
  completedLessons?: number
  totalLessons?: number
  hoursSpent?: number
  instructor?: string
  thumbnail?: string
  onPress?: () => void
  variant?: "default" | "featured" | "compact"
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** Always white (#FFFFFF) with subtle shadow
- **Border Radius:** 12px for modern, friendly appearance
- **Padding:** 16px internal spacing
- **Progress Bar:** Orange/amber accent color (#FFBB50) - MANDATORY
- **Text Hierarchy:** Title (semiBold), description (light weight 300)
- **Statistics:** Small cards with colored backgrounds
- **Thumbnail:** 60px square with rounded corners
- **Shadow:** Elevation 3 for card prominence

**Implementation Example:**
```typescript
export const CourseCard = (props: CourseCardProps) => {
  const { themed } = useAppTheme()
  const { title, description, progress = 0, onPress } = props

  return (
    <Pressable style={themed($container)} onPress={onPress}>
      <View style={themed($header)}>
        <Text style={themed($title)}>{title}</Text>
        <Text style={themed($description)}>{description}</Text>
      </View>
      
      <View style={themed($progressContainer)}>
        <View style={themed($progressBar)}>
          <View style={themed([$progressFill, { width: `${progress}%` }])} />
        </View>
        <Text style={themed($progressText)}>{progress}% Complete</Text>
      </View>
    </Pressable>
  )
}
```

## 2. Progress Statistics Card

**Purpose:** Display numerical progress with visual indicators

```typescript
interface ProgressStatsProps {
  label: string
  value: number | string
  backgroundColor?: string
  textColor?: string
  size?: "small" | "medium" | "large"
  icon?: ReactElement
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background Colors:**
  - Course: Dark blue (#132339)
  - Progress: Orange (#FFBB50) - MANDATORY accent
  - Completed: Light gray (#F4F2F1)
- **Text Color:** White on dark backgrounds, dark on light backgrounds
- **Border Radius:** 8px for consistency
- **Typography:** Bold numbers, light labels (weight 300)
- **Padding:** 12px for compact appearance
- **Size Variants:** Small (60px), Medium (80px), Large (100px)

## 3. User Profile Header

**Purpose:** Display user information with avatar and greeting

```typescript
interface ProfileHeaderProps {
  name: string
  username?: string
  avatar?: string
  greeting?: string
  onAvatarPress?: () => void
  onSearchPress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Avatar:** Circular, 48px diameter with subtle border
- **Greeting:** Light weight (300) for "Let's Learning to smart"
- **Name:** SemiBold weight for prominence
- **Background:** White with subtle spacing
- **Layout:** Horizontal with space-between alignment
- **Touch Targets:** 44px minimum for interactive elements

## 4. Video Lesson Item

**Purpose:** Display individual video lessons with play functionality

```typescript
interface VideoLessonProps {
  title: string
  duration: string
  isCompleted?: boolean
  isLocked?: boolean
  onPlay?: () => void
  onPress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Play Button:** Orange circular button (#FFBB50) - MANDATORY
- **Duration:** Light gray text (weight 300)
- **Title:** Regular weight for readability
- **Lock Icon:** Gray for locked lessons
- **Spacing:** 16px vertical between items
- **Completion Indicator:** Green checkmark for completed lessons

## 5. Student Avatar Group

**Purpose:** Display multiple student avatars in a row

```typescript
interface StudentAvatarGroupProps {
  students: Array<{
    id: string
    name: string
    avatar?: string
  }>
  maxVisible?: number
  size?: number
  onPress?: (studentId: string) => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Avatar Size:** 32px diameter for group display
- **Overlap:** -8px margin for overlapping effect
- **Border:** 2px white border for separation
- **Max Display:** Show 4-5 avatars, then "+X more"
- **Fallback:** Initials on colored background

## 6. Course Progress Bar

**Purpose:** Visual progress indicator for course completion

```typescript
interface CourseProgressBarProps {
  progress: number // 0-100
  height?: number
  backgroundColor?: string
  progressColor?: string
  showPercentage?: boolean
  animated?: boolean
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Height:** 6px standard, 8px for emphasis
- **Background:** Light gray (#F4F2F1)
- **Progress Color:** Orange (#FFBB50) - MANDATORY
- **Border Radius:** 3px for smooth appearance
- **Animation:** Smooth progress transitions
- **Percentage:** Optional text display

## 7. Lesson Status Badge

**Purpose:** Indicate lesson completion status

```typescript
interface LessonStatusBadgeProps {
  status: "completed" | "current" | "locked" | "available"
  size?: "small" | "medium"
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Completed:** Green background with checkmark
- **Current:** Orange background with play icon
- **Locked:** Gray background with lock icon
- **Available:** Transparent with border
- **Size:** 24px small, 32px medium
- **Icons:** 16px for small, 20px for medium

## 8. Course Category Tag

**Purpose:** Display course categories and subjects

```typescript
interface CourseCategoryTagProps {
  category: string
  color?: string
  size?: "small" | "medium"
  onPress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** Light colored backgrounds
- **Text:** Dark text for contrast
- **Border Radius:** 16px for pill shape
- **Padding:** 8px horizontal, 4px vertical
- **Typography:** Light weight (300), 12px font size

## 9. Implementation Guidelines

### LMS Color System
```typescript
const lmsColors = {
  // Progress and completion
  progress: "#FFBB50",      // Orange - MANDATORY for all progress
  completed: "#10B981",     // Green for completed states
  current: "#3B82F6",       // Blue for current/active states
  locked: "#9CA3AF",        // Gray for locked/unavailable
  
  // Course categories
  math: "#8B5CF6",          // Purple
  science: "#06B6D4",       // Cyan
  language: "#F59E0B",      // Amber
  history: "#EF4444",       // Red
}
```

### Spacing Standards
```typescript
const lmsSpacing = {
  cardPadding: 16,          // Internal card padding
  itemSpacing: 16,          // Between list items
  sectionSpacing: 24,       // Between sections
  avatarOverlap: -8,        // Avatar group overlap
  progressHeight: 6,        // Progress bar height
}
```

## 10. Quality Assurance

### LMS Component Checklist
- [ ] Orange accent color (#FFBB50) used for progress elements
- [ ] White backgrounds for all cards and containers
- [ ] Font weight 300 for descriptions and labels
- [ ] Proper touch targets (44px minimum)
- [ ] Accessibility labels for all interactive elements
- [ ] Loading and error states implemented
- [ ] Responsive design considerations
- [ ] Performance optimizations applied

### Educational UX Standards
- [ ] Clear progress indicators
- [ ] Intuitive lesson navigation
- [ ] Accessible content for all learners
- [ ] Consistent interaction patterns
- [ ] Motivational visual feedback
- [ ] Clear completion states
- [ ] Helpful error messages
- [ ] Offline capability considerations

These LMS-specific components ensure a cohesive and engaging learning experience throughout the eb-lms-app.
