---
description: Display components specifications for eb-lms-app (Avatar, Card, List, Progress)
globs:
alwaysApply: true
---

# Display Components

## Overview

This document defines display components for eb-lms-app including avatars, cards, lists, and progress indicators with consistent styling and behavior.

## 1. Avatar Components

### Single Avatar
**Purpose:** Display user profile images with consistent styling

```typescript
interface AvatarProps {
  source?: string
  size?: "small" | "medium" | "large"
  borderColor?: string
  borderWidth?: number
  fallbackIcon?: string
  onPress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Small:** 32px diameter (for groups, lists)
- **Medium:** 48px diameter (for headers, cards)
- **Large:** 64px diameter (for profiles)
- **Border:** 2px white border for separation
- **Fallback:** User icon with background color
- **Touch Target:** 44px minimum for interactive avatars

**Implementation Example:**
```typescript
export const Avatar = (props: AvatarProps) => {
  const { themed } = useAppTheme()
  const { source, size = "medium", onPress } = props

  const avatarSize = {
    small: 32,
    medium: 48,
    large: 64,
  }[size]

  return (
    <Pressable 
      style={themed([$avatar, { width: avatarSize, height: avatarSize }])}
      onPress={onPress}
    >
      {source ? (
        <AutoImage source={{ uri: source }} style={themed($avatarImage)} />
      ) : (
        <Icon icon="user" size={avatarSize * 0.6} color="#978F8A" />
      )}
    </Pressable>
  )
}
```

### Avatar Group (Student Avatars)
**Purpose:** Display multiple overlapping avatars with count indicator

```typescript
interface AvatarGroupProps {
  avatars: Array<{
    id: string
    source?: string
    name: string
  }>
  maxVisible?: number
  size?: number
  overlapOffset?: number
  showCount?: boolean
  onPress?: (avatarId: string) => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Overlap:** -8px offset for stacked effect
- **Max Visible:** 4-5 avatars before showing "+X"
- **Count Indicator:** "+X" in circular background
- **Border:** White border for each avatar
- **Size:** 32px standard for groups
- **Background Colors:** Varied colors for initials fallback

## 2. Card Components

### Course Card
**Purpose:** Display course information with thumbnail and metadata

```typescript
interface CourseCardProps {
  title: string
  instructor?: string
  thumbnail?: string
  participants?: number
  lessons?: number
  duration?: string
  progress?: number
  onPress?: () => void
  variant?: "grid" | "list"
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** White (#FFFFFF) with shadow - MANDATORY
- **Border Radius:** 12px for modern appearance
- **Padding:** 16px internal spacing
- **Thumbnail:** Top section with overlay text
- **Progress Bar:** Orange (#FFBB50) at bottom - MANDATORY
- **Typography:** SemiBold title (600), light description (300) - MANDATORY
- **Shadow:** Elevation 3 for prominence

### Statistics Card
**Purpose:** Display numerical data with colored backgrounds

```typescript
interface StatCardProps {
  label: string
  value: number | string
  backgroundColor: string
  textColor?: string
  icon?: string
  size?: "small" | "medium" | "large"
  onPress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background Colors:**
  - Course: Dark blue (#132339)
  - Progress: Orange (#FFBB50) - MANDATORY
  - Completed: Light gray (#F4F2F1)
  - Active: Royal blue (#3B82F6)
- **Typography:** Bold numbers (700), light labels (300) - MANDATORY
- **Border Radius:** 8px for consistency
- **Padding:** 12px for small, 16px for medium
- **Text Color:** White on dark, dark on light backgrounds

### Information Card
**Purpose:** Structured information display with metadata

```typescript
interface InfoCardProps {
  title: string
  description: string
  metadata?: Array<{
    icon: string
    label: string
    value: string
  }>
  actions?: ReactElement[]
  variant?: "default" | "compact"
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Background:** White (#FFFFFF) - MANDATORY
- **Border Radius:** 12px
- **Title:** SemiBold weight (600), 16-18px
- **Description:** Light weight (300), 14px - MANDATORY
- **Metadata:** Horizontal layout with icons
- **Actions:** Right-aligned buttons
- **Padding:** 16px internal spacing

## 3. List Components

### Video Lesson Item
**Purpose:** Display lesson with play functionality and status

```typescript
interface LessonItemProps {
  title: string
  duration: string
  isCompleted?: boolean
  isLocked?: boolean
  isCurrent?: boolean
  thumbnail?: string
  onPlay?: () => void
  onPress?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Layout:** Horizontal with play button left
- **Play Button:** 40px circular, orange (#FFBB50) - MANDATORY
- **Title:** Regular weight (400), 16px
- **Duration:** Light gray, 14px, light weight (300) - MANDATORY
- **Status Icons:** Check (green), Lock (gray), Current (blue)
- **Spacing:** 16px vertical between items
- **Touch Target:** Full row pressable

### Course List Item
**Purpose:** Horizontal course display with progress

```typescript
interface CourseListItemProps {
  title: string
  instructor: string
  progress: number
  thumbnail?: string
  isBookmarked?: boolean
  onPress?: () => void
  onBookmark?: () => void
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Thumbnail:** 60px square with rounded corners (8px)
- **Content:** Vertical layout with title and instructor
- **Progress:** Thin orange bar at bottom (#FFBB50) - MANDATORY
- **Bookmark:** Heart icon, top-right
- **Spacing:** 12px padding, 16px between items
- **Typography:** Light weight (300) for instructor - MANDATORY

## 4. Progress Components

### Progress Bar
**Purpose:** Linear progress indicator for courses and tasks

```typescript
interface ProgressBarProps {
  progress: number // 0-100
  height?: number
  backgroundColor?: string
  progressColor?: string
  showPercentage?: boolean
  animated?: boolean
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Height:** 6px for course progress, 4px for small indicators
- **Background:** Light gray (#F4F2F1)
- **Progress:** Orange (#FFBB50) - MANDATORY
- **Border Radius:** 3px (half height) for smooth appearance
- **Animation:** 300ms smooth transition
- **Percentage:** Optional text with light weight (300)

### Circular Progress
**Purpose:** Circular progress indicator for completion status

```typescript
interface CircularProgressProps {
  progress: number // 0-100
  size?: number
  strokeWidth?: number
  backgroundColor?: string
  progressColor?: string
  showPercentage?: boolean
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Size:** 60px standard, 40px small, 80px large
- **Stroke Width:** 4px standard
- **Colors:** Orange (#FFBB50) for progress - MANDATORY
- **Background:** Light gray stroke (#F4F2F1)
- **Text:** Percentage in center, medium weight (500)
- **Animation:** Smooth progress transitions

## 5. Status Components

### Badge Component
**Purpose:** Status indicators and notifications

```typescript
interface BadgeProps {
  count?: number
  status?: "success" | "warning" | "error" | "info"
  size?: "small" | "medium"
  showZero?: boolean
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Colors:** Red (error), Orange (warning), Green (success), Blue (info)
- **Size:** 20px small, 24px medium
- **Typography:** Bold numbers, 12px font size
- **Position:** Top-right of parent component
- **Border:** 2px white border for separation

### Status Indicator
**Purpose:** Visual status representation

```typescript
interface StatusIndicatorProps {
  status: "online" | "offline" | "busy" | "away"
  size?: number
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Size:** 12px standard
- **Colors:** Green (online), Gray (offline), Red (busy), Yellow (away)
- **Border:** 2px white border
- **Position:** Bottom-right of avatar

## 6. Quality Assurance

### Display Component Checklist
- [ ] White backgrounds for cards and containers (MANDATORY)
- [ ] Orange accent color (#FFBB50) for progress elements (MANDATORY)
- [ ] Light font weight (300) for descriptions (MANDATORY)
- [ ] Proper border radius (12px cards, 8px small elements)
- [ ] Consistent shadow elevation
- [ ] Touch targets minimum 44px
- [ ] Accessibility labels for all interactive elements
- [ ] Loading and error states implemented

### Design Compliance
- [ ] Typography hierarchy maintained
- [ ] Color contrast meets WCAG standards
- [ ] Consistent spacing (16px standard)
- [ ] Proper animation timing (300ms)
- [ ] Icon sizes standardized (24px default)
- [ ] Responsive design considerations
- [ ] Performance optimizations applied

These display components ensure consistent visual presentation and user interaction patterns throughout eb-lms-app.
