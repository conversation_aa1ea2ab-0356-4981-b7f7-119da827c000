---
description: Component testing patterns and performance optimization for eb-lms-app
globs:
alwaysApply: true
---

# Component Testing & Performance

## Overview

This document outlines testing strategies, performance optimization techniques, and quality assurance practices for components in eb-lms-app.

## 1. Testing Patterns

### Component Testing Structure
```typescript
// MyComponent.test.tsx
import { render, fireEvent, waitFor } from "@testing-library/react-native"
import { MyComponent } from "./MyComponent"
import { ThemeProvider } from "@/theme"

// Test wrapper with theme
const renderWithTheme = (component: ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  )
}

describe("MyComponent", () => {
  const defaultProps = {
    title: "Test Title",
    onPress: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it("renders correctly", () => {
    const { getByText } = renderWithTheme(
      <MyComponent {...defaultProps} />
    )
    expect(getByText("Test Title")).toBeTruthy()
  })

  it("handles press events", () => {
    const onPress = jest.fn()
    const { getByRole } = renderWithTheme(
      <MyComponent {...defaultProps} onPress={onPress} />
    )

    fireEvent.press(getByRole("button"))
    expect(onPress).toHaveBeenCalledTimes(1)
  })

  it("applies disabled state correctly", () => {
    const { getByRole } = renderWithTheme(
      <MyComponent {...defaultProps} disabled />
    )

    expect(getByRole("button")).toHaveAccessibilityState({ disabled: true })
  })
})
```

### Accessibility Testing
```typescript
describe("Accessibility", () => {
  it("has proper accessibility labels", () => {
    const { getByLabelText } = renderWithTheme(
      <MyComponent 
        {...defaultProps} 
        accessibilityLabel="Custom button label" 
      />
    )
    
    expect(getByLabelText("Custom button label")).toBeTruthy()
  })

  it("supports screen readers", () => {
    const { getByRole } = renderWithTheme(
      <MyComponent {...defaultProps} />
    )
    
    const button = getByRole("button")
    expect(button).toHaveAccessibilityRole("button")
    expect(button).toBeAccessible()
  })

  it("has minimum touch target size", () => {
    const { getByRole } = renderWithTheme(
      <MyComponent {...defaultProps} />
    )
    
    const button = getByRole("button")
    const style = button.props.style
    expect(style.minHeight).toBeGreaterThanOrEqual(44)
  })
})
```

### Snapshot Testing
```typescript
describe("Snapshots", () => {
  it("matches snapshot for default state", () => {
    const tree = renderWithTheme(
      <MyComponent {...defaultProps} />
    ).toJSON()
    
    expect(tree).toMatchSnapshot()
  })

  it("matches snapshot for disabled state", () => {
    const tree = renderWithTheme(
      <MyComponent {...defaultProps} disabled />
    ).toJSON()
    
    expect(tree).toMatchSnapshot()
  })
})
```

## 2. Performance Optimization

### Memoization Strategies
```typescript
import { memo, useMemo, useCallback } from "react"

// Component memoization
export const OptimizedComponent = memo((props: ComponentProps) => {
  const { data, onItemPress, filterText } = props

  // Expensive calculations
  const processedData = useMemo(() => {
    return data
      .filter(item => item.title.toLowerCase().includes(filterText.toLowerCase()))
      .sort((a, b) => a.priority - b.priority)
  }, [data, filterText])

  // Callback memoization
  const handlePress = useCallback((id: string) => {
    onItemPress(id)
  }, [onItemPress])

  // Style memoization
  const containerStyle = useMemo(() => [
    $container,
    props.style,
  ], [props.style])

  return (
    <View style={containerStyle}>
      {processedData.map(item => (
        <Item 
          key={item.id} 
          data={item} 
          onPress={handlePress} 
        />
      ))}
    </View>
  )
})

// Display name for debugging
OptimizedComponent.displayName = "OptimizedComponent"
```

### List Performance
```typescript
import { FlashList } from "@shopify/flash-list"

export const OptimizedList = ({ data }: { data: Item[] }) => {
  // Memoized render function
  const renderItem = useCallback(({ item }: { item: Item }) => (
    <ListItem data={item} />
  ), [])

  // Memoized key extractor
  const keyExtractor = useCallback((item: Item) => item.id, [])

  // Memoized item layout
  const getItemLayout = useCallback((data: any, index: number) => ({
    length: 80,
    offset: 80 * index,
    index,
  }), [])

  return (
    <FlashList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      estimatedItemSize={80}
      removeClippedSubviews={true}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={10}
      getItemLayout={getItemLayout}
    />
  )
}
```

### Image Optimization
```typescript
import { AutoImage } from "@/components"

export const OptimizedImageComponent = ({ imageUrl }: { imageUrl: string }) => {
  return (
    <AutoImage
      source={{ uri: imageUrl }}
      style={$image}
      resizeMode="cover"
      // Performance optimizations
      loadingIndicatorSource={require("@/assets/images/placeholder.png")}
      defaultSource={require("@/assets/images/default.png")}
      // Caching
      cache="force-cache"
      // Lazy loading
      lazy={true}
    />
  )
}
```

## 3. Error Handling

### Error Boundary Implementation
```typescript
interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ComponentErrorBoundary extends Component<
  PropsWithChildren<{ fallback?: ComponentType<{ error?: Error }> }>,
  ErrorBoundaryState
> {
  constructor(props: PropsWithChildren<{}>) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Component error:", error, errorInfo)
    
    // Log to crash reporting service
    if (__DEV__) {
      console.group("🚨 Component Error Boundary")
      console.error("Error:", error)
      console.error("Error Info:", errorInfo)
      console.groupEnd()
    }
    
    this.setState({ errorInfo })
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback
      return <FallbackComponent error={this.state.error} />
    }

    return this.props.children
  }
}

// Default error fallback
const DefaultErrorFallback = ({ error }: { error?: Error }) => (
  <View style={$errorContainer}>
    <Text style={$errorTitle}>Something went wrong</Text>
    <Text style={$errorMessage}>
      {__DEV__ ? error?.message : "Please try again later"}
    </Text>
  </View>
)
```

### Safe Component Pattern
```typescript
export const SafeComponent = (props: ComponentProps) => {
  const [error, setError] = useState<Error | null>(null)

  if (error) {
    return <ErrorFallback error={error} onRetry={() => setError(null)} />
  }

  try {
    return <ActualComponent {...props} />
  } catch (error) {
    setError(error as Error)
    return null
  }
}
```

## 4. Performance Monitoring

### Performance Metrics
```typescript
import { performance } from "perf_hooks"

export const withPerformanceMonitoring = <P extends object>(
  Component: ComponentType<P>
) => {
  return (props: P) => {
    useEffect(() => {
      const startTime = performance.now()
      
      return () => {
        const endTime = performance.now()
        const renderTime = endTime - startTime
        
        if (__DEV__ && renderTime > 16) { // 60fps threshold
          console.warn(
            `Slow render detected: ${Component.displayName || Component.name} took ${renderTime.toFixed(2)}ms`
          )
        }
      }
    })

    return <Component {...props} />
  }
}
```

### Memory Leak Detection
```typescript
export const useMemoryLeakDetection = (componentName: string) => {
  useEffect(() => {
    if (__DEV__) {
      const startMemory = performance.memory?.usedJSHeapSize || 0
      
      return () => {
        const endMemory = performance.memory?.usedJSHeapSize || 0
        const memoryDiff = endMemory - startMemory
        
        if (memoryDiff > 1024 * 1024) { // 1MB threshold
          console.warn(
            `Potential memory leak in ${componentName}: ${(memoryDiff / 1024 / 1024).toFixed(2)}MB`
          )
        }
      }
    }
  }, [componentName])
}
```

## 5. Quality Assurance

### Component Quality Checklist
```typescript
// ComponentQuality.test.tsx
describe("Component Quality Assurance", () => {
  describe("Performance", () => {
    it("renders within performance budget", async () => {
      const startTime = performance.now()
      renderWithTheme(<MyComponent {...defaultProps} />)
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(16) // 60fps
    })

    it("handles large datasets efficiently", () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: i.toString(),
        title: `Item ${i}`,
      }))
      
      const { getByTestId } = renderWithTheme(
        <MyComponent data={largeDataset} />
      )
      
      expect(getByTestId("component-container")).toBeTruthy()
    })
  })

  describe("Accessibility", () => {
    it("meets WCAG 2.1 AA standards", () => {
      const { getByRole } = renderWithTheme(<MyComponent {...defaultProps} />)
      const element = getByRole("button")
      
      expect(element).toBeAccessible()
      expect(element).toHaveAccessibilityRole("button")
    })
  })

  describe("Error Handling", () => {
    it("gracefully handles errors", () => {
      const ThrowError = () => {
        throw new Error("Test error")
      }
      
      const { getByText } = render(
        <ComponentErrorBoundary>
          <ThrowError />
        </ComponentErrorBoundary>
      )
      
      expect(getByText("Something went wrong")).toBeTruthy()
    })
  })
})
```

### Automated Quality Gates
```typescript
// jest.config.js
module.exports = {
  // Performance thresholds
  testTimeout: 10000,
  
  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  
  // Setup files
  setupFilesAfterEnv: ["<rootDir>/test/setup.ts"],
}
```

This comprehensive testing and performance guide ensures all components maintain high quality, performance, and reliability standards in eb-lms-app.
