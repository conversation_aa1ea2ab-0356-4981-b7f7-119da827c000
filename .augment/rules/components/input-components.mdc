---
description: Input and form component specifications for eb-lms-app
globs:
alwaysApply: true
---

# Input & Form Components

## Overview

This document defines input components, form elements, and interactive controls for eb-lms-app with focus on accessibility, usability, and consistent styling.

## 1. TextField Component

**Purpose:** Text input with validation, theming, and accessibility support

```typescript
interface TextFieldProps {
  value?: string
  onChangeText?: (text: string) => void
  placeholder?: string
  placeholderTx?: TxKeyPath
  label?: string
  labelTx?: TxKeyPath
  helper?: string
  helperTx?: TxKeyPath
  status?: "error" | "disabled"
  multiline?: boolean
  secureTextEntry?: boolean
  keyboardType?: KeyboardTypeOptions
  autoCapitalize?: "none" | "sentences" | "words" | "characters"
  autoComplete?: string
  autoCorrect?: boolean
  style?: StyleProp<ViewStyle>
  inputWrapperStyle?: StyleProp<ViewStyle>
  containerStyle?: StyleProp<ViewStyle>
  LeftAccessory?: ComponentType<TextFieldAccessoryProps>
  RightAccessory?: ComponentType<TextFieldAccessoryProps>
}
```

**Design Specifications:**
- **Height:** 48px minimum for touch accessibility
- **Border Radius:** 8px for consistency with design system
- **Typography:** Light weight (300) for input text - MANDATORY
- **Colors:** Light, bright colors per established guidelines
- **Padding:** 16px horizontal, 12px vertical
- **Border:** 1px solid light gray (#D1D5DB)
- **Focus State:** Blue border (#3B82F6) with subtle shadow
- **Error State:** Red border (#EF4444) with error message
- **Disabled State:** Gray background with reduced opacity

**Implementation Example:**
```typescript
const $textField: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  borderWidth: 1,
  borderColor: colors.border,
  borderRadius: 8,
  paddingHorizontal: spacing.md, // 16px
  paddingVertical: spacing.sm, // 12px
  minHeight: 48,
  backgroundColor: colors.background, // White
})

const $textFieldFocused: ThemedStyle<ViewStyle> = ({ colors }) => ({
  borderColor: colors.tint, // Blue focus
  shadowColor: colors.tint,
  shadowOffset: { width: 0, height: 0 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 2,
})

const $textFieldError: ThemedStyle<ViewStyle> = ({ colors }) => ({
  borderColor: colors.error, // Red error
})
```

## 2. Switch Component

**Purpose:** Toggle control with enhanced visual feedback

```typescript
interface SwitchProps {
  value?: boolean
  onValueChange?: (value: boolean) => void
  disabled?: boolean
  size?: "small" | "medium" | "large"
  trackColor?: { false?: string; true?: string }
  thumbColor?: string
  style?: StyleProp<ViewStyle>
  accessibilityLabel?: string
}
```

**Design Specifications:**
- **Track Colors:** Light gray (off), blue (on)
- **Thumb Color:** White with subtle shadow
- **Size:** 51x31px standard (iOS style)
- **Animation:** Smooth 200ms transitions
- **Blue Circle Indicator:** Blue circle inside when in off state for better UX
- **Touch Target:** 44px minimum for accessibility
- **Disabled State:** Reduced opacity and gray colors

**Enhanced Switch Features:**
```typescript
const $switch: ThemedStyle<ViewStyle> = ({ colors }) => ({
  transform: [{ scaleX: 1.1 }, { scaleY: 1.1 }], // Slightly larger
})

const $switchTrackOff: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral300, // Light gray
  // Blue circle indicator when off for better UX recognition
})

const $switchTrackOn: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.tint, // Blue when on
})
```

## 3. Checkbox Component

**Purpose:** Multi-selection control with clear visual states

```typescript
interface CheckboxProps {
  value?: boolean
  onValueChange?: (value: boolean) => void
  disabled?: boolean
  size?: number
  color?: string
  label?: string
  labelTx?: TxKeyPath
  style?: StyleProp<ViewStyle>
  labelStyle?: StyleProp<TextStyle>
}
```

**Design Specifications:**
- **Size:** 24px standard checkbox
- **Border:** 2px solid gray (unchecked), blue (checked)
- **Background:** White (unchecked), blue (checked)
- **Checkmark:** White checkmark icon
- **Border Radius:** 4px for subtle rounding
- **Touch Target:** 44px minimum area
- **Animation:** Smooth check/uncheck transitions

## 4. Radio Button Component

**Purpose:** Single selection from multiple options

```typescript
interface RadioButtonProps {
  value?: boolean
  onValueChange?: (value: boolean) => void
  disabled?: boolean
  size?: number
  color?: string
  label?: string
  labelTx?: TxKeyPath
  style?: StyleProp<ViewStyle>
  labelStyle?: StyleProp<TextStyle>
}

interface RadioGroupProps {
  value?: string
  onValueChange?: (value: string) => void
  options: Array<{
    value: string
    label: string
    labelTx?: TxKeyPath
  }>
  disabled?: boolean
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Size:** 24px circular radio button
- **Border:** 2px solid gray (unselected), blue (selected)
- **Inner Circle:** Blue dot when selected
- **Touch Target:** 44px minimum area
- **Spacing:** 16px between radio options
- **Label Typography:** Light weight (300) - MANDATORY

## 5. Slider Component

**Purpose:** Range selection with visual feedback

```typescript
interface SliderProps {
  value?: number
  onValueChange?: (value: number) => void
  minimumValue?: number
  maximumValue?: number
  step?: number
  disabled?: boolean
  minimumTrackTintColor?: string
  maximumTrackTintColor?: string
  thumbStyle?: StyleProp<ViewStyle>
  trackStyle?: StyleProp<ViewStyle>
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Track Height:** 4px
- **Thumb Size:** 24px diameter
- **Track Colors:** Blue (active), light gray (inactive)
- **Thumb Color:** White with blue border
- **Touch Target:** 44px minimum area
- **Animation:** Smooth value transitions

## 6. Picker/Select Component

**Purpose:** Dropdown selection with platform-appropriate styling

```typescript
interface PickerProps {
  value?: string
  onValueChange?: (value: string) => void
  items: Array<{
    label: string
    value: string
    labelTx?: TxKeyPath
  }>
  placeholder?: string
  placeholderTx?: TxKeyPath
  disabled?: boolean
  style?: StyleProp<ViewStyle>
  pickerStyle?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Height:** 48px to match TextField
- **Border:** Same styling as TextField
- **Typography:** Light weight (300) for options - MANDATORY
- **Dropdown Icon:** Chevron down icon
- **Platform Behavior:** Native picker on mobile, custom on web

## 7. Search Input Component

**Purpose:** Search functionality with enhanced UX

```typescript
interface SearchInputProps {
  value?: string
  onChangeText?: (text: string) => void
  onSubmit?: (text: string) => void
  onClear?: () => void
  placeholder?: string
  placeholderTx?: TxKeyPath
  showClearButton?: boolean
  autoFocus?: boolean
  style?: StyleProp<ViewStyle>
}
```

**Design Specifications:**
- **Search Icon:** Left-aligned search icon
- **Clear Button:** X icon on right when text present
- **Border Radius:** 24px for pill shape
- **Background:** Light gray (#F8F9FA)
- **No Border:** Clean, modern appearance
- **Typography:** Light weight (300) - MANDATORY

## 8. Form Validation

### Validation States
```typescript
interface ValidationState {
  isValid: boolean
  errors: string[]
  warnings?: string[]
}

interface FormFieldProps {
  validation?: ValidationState
  showValidation?: boolean
  validationTrigger?: "onChange" | "onBlur" | "onSubmit"
}
```

### Error Display
```typescript
const $errorText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  color: colors.error,
  marginTop: 4,
})

const $helperText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  color: colors.textDim,
  marginTop: 4,
})
```

## 9. Form Layout Patterns

### Form Container
```typescript
const $formContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.md, // 16px gap between form fields
  paddingHorizontal: spacing.sm, // 12px horizontal padding - MANDATORY
})

const $formField: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md, // 16px below each field
})

const $formSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg, // 24px between form sections
})
```

### Label Styling
```typescript
const $fieldLabel: ThemedStyle<TextStyle> = ({ colors, typography, spacing }) => ({
  fontFamily: typography.primary.medium, // 500 weight for labels
  fontSize: 14,
  color: colors.text,
  marginBottom: spacing.xs, // 8px below label
})

const $requiredIndicator: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.error, // Red asterisk for required fields
})
```

## 10. Accessibility Features

### Required Accessibility Props
```typescript
// Text input accessibility
<TextInput
  accessibilityLabel="Email address"
  accessibilityHint="Enter your email to sign in"
  accessibilityRole="text"
  autoComplete="email"
/>

// Switch accessibility
<Switch
  accessibilityLabel="Enable notifications"
  accessibilityHint="Toggle to receive push notifications"
  accessibilityRole="switch"
/>

// Button accessibility
<Pressable
  accessibilityRole="button"
  accessibilityLabel="Submit form"
  accessibilityHint="Submits the registration form"
  accessibilityState={{ disabled: isDisabled }}
/>
```

### Focus Management
```typescript
const inputRef = useRef<TextInput>(null)

// Focus next field on submit
const focusNextField = () => {
  nextInputRef.current?.focus()
}

// Clear focus on submit
const handleSubmit = () => {
  Keyboard.dismiss()
  // Handle form submission
}
```

## 11. Quality Assurance

### Input Component Checklist
- [ ] 48px minimum height for touch accessibility
- [ ] Font weight 300 for input text and labels (MANDATORY)
- [ ] Light, bright colors for better visibility
- [ ] Proper focus states with blue accent
- [ ] Error states with red indicators
- [ ] Disabled states with reduced opacity
- [ ] Accessibility labels and hints
- [ ] Keyboard type appropriate for input
- [ ] Auto-complete and auto-correct configured

### Form Design Compliance
- [ ] 16px gap between form fields
- [ ] 12px horizontal padding from screen edges (MANDATORY)
- [ ] Consistent border radius (8px for inputs)
- [ ] Proper validation error display
- [ ] Clear visual hierarchy
- [ ] Touch targets minimum 44px
- [ ] Platform-appropriate behavior
- [ ] Smooth animations and transitions

These input component specifications ensure consistent, accessible, and user-friendly form interactions throughout eb-lms-app.
