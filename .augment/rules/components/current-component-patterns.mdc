---
description: Component patterns and styling guidelines based on current eb-lms-app implementation
globs:
alwaysApply: true
---

# Current Component Patterns

## Overview

This document defines component patterns and styling guidelines based on the actual implementation in eb-lms-app, ensuring documentation matches current UI patterns.

## 1. Card Components

### Standard Card Pattern (Student Screens)
```typescript
// Card pattern used throughout student screens
const StandardCard = ({ children, style }) => {
  const { themed } = useAppTheme()
  
  return (
    <View style={[themed($cardContainer), style]}>
      {children}
    </View>
  )
}

const $cardContainer: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#FFFFFF", // White background - CONFIRMED
  borderRadius: 16, // Rounded corners - CONFIRMED
  padding: 16, // Internal padding
  marginBottom: 16, // Spacing between cards
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.08,
  shadowRadius: 8,
  elevation: 4,
})
```

### Course Card Pattern
```typescript
// Course card with thumbnail and info
const CourseCard = ({ course, onPress }) => {
  const { themed } = useAppTheme()
  
  return (
    <TouchableOpacity style={themed($courseCard)} onPress={onPress}>
      <View style={themed($courseThumbnail)} />
      <View style={themed($courseInfo)}>
        <Text style={themed($courseTitle)} numberOfLines={1}>
          {course.title}
        </Text>
        <Text style={themed($courseSubject)} numberOfLines={1}>
          {course.subject}
        </Text>
      </View>
    </TouchableOpacity>
  )
}

const $courseCard: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  padding: 16,
  marginBottom: 16,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.08,
  shadowRadius: 8,
  elevation: 4,
})

const $courseThumbnail: ThemedStyle<ViewStyle> = () => ({
  width: 80,
  height: 80,
  backgroundColor: "#000000",
  borderRadius: 12,
  marginRight: 16,
})

const $courseTitle: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Lexend Deca",
  fontWeight: "600", // SemiBold for titles
  fontSize: 16,
  lineHeight: 22,
  color: "#202244",
  marginBottom: 8,
})

const $courseSubject: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Nunito Sans",
  fontWeight: "300", // Light weight for descriptions - CONFIRMED
  fontSize: 14,
  lineHeight: 20,
  color: "#666666",
})
```

## 2. Bottom Sheet Components

### Schedule Detail Bottom Sheet Pattern
```typescript
// Bottom sheet pattern from schedule screen
const DetailBottomSheet = ({ visible, item, onClose, children }) => {
  const { themed } = useAppTheme()
  
  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={themed($backdrop)}>
        <TouchableOpacity 
          style={themed($backdropTouchable)} 
          activeOpacity={1}
          onPress={onClose}
        />
        
        <View style={themed($bottomSheet)}>
          <View style={themed($handleBar)} />
          
          <View style={themed($header)}>
            <Text style={themed($headerTitle)}>Details</Text>
            <TouchableOpacity onPress={onClose} style={themed($closeButton)}>
              <Icon icon="x" size={24} color="#666666" />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={themed($scrollContainer)}>
            <View style={themed($content)}>
              {children}
            </View>
          </ScrollView>
        </View>
      </View>
    </Modal>
  )
}

const $backdrop: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "flex-end",
})

const $bottomSheet: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#FFFFFF",
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  paddingTop: 8,
  minHeight: "85%",
  maxHeight: "95%",
})

const $handleBar: ThemedStyle<ViewStyle> = () => ({
  width: 40,
  height: 4,
  backgroundColor: "#E0E0E0",
  borderRadius: 2,
  alignSelf: "center",
  marginBottom: 16,
})

const $header: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingHorizontal: 24,
  paddingBottom: 16,
  borderBottomWidth: 1,
  borderBottomColor: "#F0F0F0",
})

const $headerTitle: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Lexend Deca",
  fontWeight: "600", // SemiBold for headers
  fontSize: 18,
  lineHeight: 24,
  color: "#202244",
})
```

## 3. Button Components

### Primary Button Pattern (Auth Screens)
```typescript
// Primary button pattern from auth screens
const PrimaryButton = ({ title, onPress, disabled, loading }) => {
  const { themed } = useAppTheme()
  
  return (
    <TouchableOpacity
      style={themed($primaryButton)}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      <Text style={themed($primaryButtonText)}>
        {loading ? "Loading..." : title}
      </Text>
      <View style={themed($buttonIconContainer)}>
        <Icon icon="caretRight" size={21} color="#0961F5" />
      </View>
    </TouchableOpacity>
  )
}

const $primaryButton: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: "#0961F5", // Exact color from current implementation
  borderRadius: 30, // Exact border radius from auth screens
  height: 60, // Exact height from auth screens
  width: 350, // Exact width from auth screens
  alignSelf: "center",
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 4,
  marginBottom: 25,
})

const $primaryButtonText: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Lexend Deca",
  fontWeight: "300", // Light weight for button text - CONFIRMED
  fontSize: 16,
  lineHeight: 20,
  color: "#FFFFFF",
  flex: 1,
  textAlign: "center",
})
```

### Secondary Button Pattern (Bottom Sheets)
```typescript
// Secondary button pattern from bottom sheets
const SecondaryButton = ({ title, onPress }) => {
  const { themed } = useAppTheme()
  
  return (
    <TouchableOpacity style={themed($secondaryButton)} onPress={onPress}>
      <Text style={themed($secondaryButtonText)}>{title}</Text>
    </TouchableOpacity>
  )
}

const $secondaryButton: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  backgroundColor: "#E8F1FF",
  borderRadius: 30,
  paddingVertical: 16,
  paddingHorizontal: 24,
  alignItems: "center",
  borderWidth: 2,
  borderColor: "rgba(180, 189, 196, 0.2)",
})

const $secondaryButtonText: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Nunito Sans",
  fontWeight: "300", // Light weight for button text - CONFIRMED
  fontSize: 16,
  lineHeight: 22,
  color: "#202244",
})
```

## 4. Input Field Components

### Auth Screen Input Pattern
```typescript
// Input field pattern from auth screens
const AuthInputField = ({ icon, placeholder, value, onChangeText, secureTextEntry }) => {
  const { themed } = useAppTheme()
  
  return (
    <View style={themed($textField)}>
      <View style={themed($inputContainer)}>
        <Icon
          icon={icon}
          size={19}
          color="#545454"
          style={themed($inputIcon)}
        />
        <TextInput
          value={value}
          onChangeText={onChangeText}
          style={themed($input)}
          placeholder={placeholder}
          placeholderTextColor="#505050" // Exact color from current implementation
          secureTextEntry={secureTextEntry}
          autoCapitalize="none"
          autoCorrect={false}
        />
      </View>
    </View>
  )
}

const $textField: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  marginBottom: 16,
})

const $inputContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF",
  borderRadius: 12,
  paddingHorizontal: 16,
  paddingVertical: 12,
  borderWidth: 1,
  borderColor: "#E0E0E0",
})

const $input: ThemedStyle<TextStyle> = () => ({
  flex: 1,
  fontFamily: "Nunito Sans",
  fontWeight: "300", // Light weight - CONFIRMED
  fontSize: 16,
  lineHeight: 22,
  color: "#202244",
})
```

## 5. Typography Patterns

### Text Component Usage
```typescript
// Standard text patterns used throughout app
const AppText = ({ children, variant = "body", style, ...props }) => {
  const { themed } = useAppTheme()
  
  const textStyle = {
    title: themed($titleText),
    subtitle: themed($subtitleText),
    body: themed($bodyText),
    caption: themed($captionText),
  }[variant]
  
  return (
    <Text style={[textStyle, style]} {...props}>
      {children}
    </Text>
  )
}

const $titleText: ThemedStyle<TextStyle> = ({ colors }) => ({
  fontFamily: "Nunito Sans",
  fontWeight: "600", // SemiBold for titles
  fontSize: 18,
  lineHeight: 24,
  color: colors.text, // Primary text color from updated design guidelines
})

const $subtitleText: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Nunito Sans",
  fontWeight: "600", // SemiBold for subtitles
  fontSize: 16,
  lineHeight: 22,
  color: "#202244",
})

const $bodyText: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Nunito Sans",
  fontWeight: "300", // Light weight for body text - CONFIRMED
  fontSize: 14,
  lineHeight: 20,
  color: "#666666",
})

const $captionText: ThemedStyle<TextStyle> = () => ({
  fontFamily: "Nunito Sans",
  fontWeight: "300", // Light weight for captions - CONFIRMED
  fontSize: 12,
  lineHeight: 16,
  color: "#666666",
})
```

## 6. Implementation Guidelines

### Component Best Practices
- ✅ Use white backgrounds for cards and containers
- ✅ Apply consistent border radius (16px for cards, 30px for auth buttons)
- ✅ Use font weight 300 for all descriptions and body text
- ✅ Use font weight 600 for titles and headers
- ✅ Maintain consistent shadow specifications
- ✅ Follow established color patterns per screen type
- ✅ Apply proper spacing and padding patterns

### Typography Guidelines
- ✅ **Titles/Headers:** Font weight 600, appropriate font size
- ✅ **Body Text/Descriptions:** Font weight 300 (MANDATORY)
- ✅ **Button Text:** Font weight 300 (MANDATORY)
- ✅ **Font Family:** Nunito Sans throughout

This component pattern guide ensures consistent implementation across all components in eb-lms-app.
