# EarnBase LMS API Integration Guide

## 📁 Files Created

1. **`earnbase-lms-full-api.json`** - Complete OpenAPI specification (raw JSON)
2. **`api-endpoints-summary.md`** - Quick reference guide with all endpoints
3. **`api-types.ts`** - TypeScript interfaces for React Native integration
4. **`integration-guide.md`** - This integration guide

## 🚀 Quick Start Integration

### 1. Update API Service

Replace your existing API service with the real endpoints:

```typescript
// app/services/api/api.ts
import { ApiResponse, LoginRequest, TokenResponse, UserInfo } from '../../../api-documentation/api-types'

const API_BASE_URL = 'https://lms-dev.ebill.vn/api/v1'

class ApiService {
  private baseURL = API_BASE_URL
  private token: string | null = null

  setAuthToken(token: string) {
    this.token = token
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
      ...options.headers,
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`)
    }

    return response.json()
  }

  // Authentication
  async login(credentials: LoginRequest): Promise<TokenResponse> {
    return this.request<TokenResponse>('/auth/sign-in', {
      method: 'POST',
      body: JSON.stringify(credentials),
    })
  }

  async getUserInfo(): Promise<ApiResponse<UserInfo>> {
    return this.request<UserInfo>('/auth/me')
  }

  async logout(): Promise<ApiResponse> {
    return this.request('/auth/sign-out', { method: 'POST' })
  }

  // Student APIs
  async getStudentProfile(): Promise<ApiResponse> {
    return this.request('/students/profile')
  }

  async getStudentCourses(): Promise<ApiResponse> {
    return this.request('/students/courses')
  }

  async getStudentSchedule(): Promise<ApiResponse> {
    return this.request('/students/schedule')
  }

  // Instructor APIs
  async getInstructorProfile(): Promise<ApiResponse> {
    return this.request('/instructors/profile')
  }

  async getInstructorCourses(): Promise<ApiResponse> {
    return this.request('/instructors/courses')
  }

  async getInstructorSchedule(): Promise<ApiResponse> {
    return this.request('/instructors/schedule')
  }

  // Public APIs
  async getPublicCourses(): Promise<ApiResponse> {
    return this.request('/public/courses')
  }
}

export const apiService = new ApiService()
```

### 2. Update Authentication Store

```typescript
// app/models/AuthenticationStore.ts
import { Instance, SnapshotOut, types } from "mobx-state-tree"
import { apiService } from "../services/api/api"
import { LoginRequest, UserInfo } from "../../api-documentation/api-types"

export const AuthenticationStoreModel = types
  .model("AuthenticationStore")
  .props({
    authToken: types.maybe(types.string),
    userInfo: types.maybe(types.frozen<UserInfo>()),
    isAuthenticated: types.optional(types.boolean, false),
  })
  .actions((self) => ({
    setAuthToken(value?: string) {
      self.authToken = value
      self.isAuthenticated = !!value
      if (value) {
        apiService.setAuthToken(value)
      }
    },
    setUserInfo(userInfo: UserInfo) {
      self.userInfo = userInfo
    },
    async login(credentials: LoginRequest) {
      try {
        const response = await apiService.login(credentials)
        if (response.success && response.data) {
          this.setAuthToken(response.data.access_token)
          this.setUserInfo(response.data.user_info)
          return { success: true, user: response.data.user_info }
        }
        return { success: false, error: response.message }
      } catch (error) {
        return { success: false, error: error.message }
      }
    },
    async logout() {
      try {
        await apiService.logout()
      } catch (error) {
        console.warn("Logout API call failed:", error)
      } finally {
        this.setAuthToken(undefined)
        self.userInfo = undefined
      }
    },
  }))

export interface AuthenticationStore extends Instance<typeof AuthenticationStoreModel> {}
export interface AuthenticationStoreSnapshot extends SnapshotOut<typeof AuthenticationStoreModel> {}
```

### 3. Update Login Screen

```typescript
// app/screens/LoginScreen.tsx
import React, { useState } from "react"
import { View, Alert } from "react-native"
import { observer } from "mobx-react-lite"
import { useStores } from "../models"
import { TEST_ACCOUNTS } from "../../api-documentation/api-types"

export const LoginScreen = observer(() => {
  const { authenticationStore } = useStores()
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const handleLogin = async () => {
    if (!username || !password) {
      Alert.alert("Error", "Please enter username and password")
      return
    }

    setIsLoading(true)
    try {
      const result = await authenticationStore.login({
        username,
        password,
        device_info: {
          device_type: "mobile",
          device_name: "React Native App",
          os_version: Platform.OS + " " + Platform.Version,
        },
        remember_me: true,
      })

      if (result.success) {
        // Navigation will be handled by the navigator based on authentication state
      } else {
        Alert.alert("Login Failed", result.error || "Invalid credentials")
      }
    } catch (error) {
      Alert.alert("Error", "Network error. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestLogin = (accountType: 'STUDENT' | 'INSTRUCTOR') => {
    const account = TEST_ACCOUNTS[accountType]
    setUsername(account.username)
    setPassword(account.password)
  }

  // ... rest of your UI code
})
```

### 4. Create Data Stores for Student/Instructor

```typescript
// app/models/StudentStore.ts
import { Instance, SnapshotOut, types } from "mobx-state-tree"
import { apiService } from "../services/api/api"
import { StudentProfile } from "../../api-documentation/api-types"

export const StudentStoreModel = types
  .model("StudentStore")
  .props({
    profile: types.maybe(types.frozen<StudentProfile>()),
    courses: types.optional(types.array(types.frozen()), []),
    schedule: types.maybe(types.frozen()),
    isLoading: types.optional(types.boolean, false),
  })
  .actions((self) => ({
    setLoading(loading: boolean) {
      self.isLoading = loading
    },
    async loadProfile() {
      this.setLoading(true)
      try {
        const response = await apiService.getStudentProfile()
        if (response.success && response.data) {
          self.profile = response.data
        }
      } catch (error) {
        console.error("Failed to load student profile:", error)
      } finally {
        this.setLoading(false)
      }
    },
    async loadCourses() {
      try {
        const response = await apiService.getStudentCourses()
        if (response.success && response.data) {
          self.courses = response.data
        }
      } catch (error) {
        console.error("Failed to load student courses:", error)
      }
    },
    async loadSchedule() {
      try {
        const response = await apiService.getStudentSchedule()
        if (response.success && response.data) {
          self.schedule = response.data
        }
      } catch (error) {
        console.error("Failed to load student schedule:", error)
      }
    },
  }))

export interface StudentStore extends Instance<typeof StudentStoreModel> {}
export interface StudentStoreSnapshot extends SnapshotOut<typeof StudentStoreModel> {}
```

### 5. Update Navigation Based on User Role

```typescript
// app/navigators/AppNavigator.tsx
import React from "react"
import { observer } from "mobx-react-lite"
import { useStores } from "../models"

export const AppNavigator = observer(() => {
  const { authenticationStore } = useStores()

  if (!authenticationStore.isAuthenticated) {
    return <AuthNavigator />
  }

  // Check user role from userInfo
  const userGroups = authenticationStore.userInfo?.groups || []
  const isStudent = userGroups.includes('student')
  const isInstructor = userGroups.includes('instructor') || userGroups.includes('teacher')

  if (isStudent) {
    return <StudentNavigator />
  } else if (isInstructor) {
    return <InstructorNavigator />
  } else {
    // Default or admin navigation
    return <StudentNavigator />
  }
})
```

## 🔧 Key Integration Points

### Authentication Flow
1. Use `/auth/sign-in` with test accounts
2. Store the access token securely
3. Include `Authorization: Bearer <token>` in all authenticated requests
4. Handle token refresh with `/auth/refresh`

### User Role Detection
- Check `user_info.groups` array from login response
- Route to appropriate navigator based on role

### Error Handling
- All API responses follow the same format: `{ success, message, data, meta }`
- Handle network errors and API errors consistently

### File Uploads
- Use `/files/` endpoint for uploading materials
- Get file URLs from the response for display

## 📱 Testing

Use the provided test accounts:
- **Student**: `username: "student"`, `password: "student@X2025"`
- **Instructor**: `username: "teacher"`, `password: "teacher@X2025"`

## 🔍 API Documentation

- **Full API Spec**: `earnbase-lms-full-api.json`
- **Quick Reference**: `api-endpoints-summary.md`
- **TypeScript Types**: `api-types.ts`

## 🚨 Important Notes

1. **Base URL**: `https://lms-dev.ebill.vn/api/v1`
2. **Authentication**: Bearer token required for most endpoints
3. **Language**: Include `Accept-Language: vi-VN,vi;q=0.9,en;q=0.8` header
4. **Content Type**: `application/json` for all requests
5. **Error Handling**: Check `success` field in all responses
