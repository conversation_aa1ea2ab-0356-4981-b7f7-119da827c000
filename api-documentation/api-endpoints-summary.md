# EarnBase LMS API Documentation

## Base URL
```
https://lms-dev.ebill.vn/api/v1
```

## Authentication
- **Type**: Bearer Token
- **Header**: `Authorization: Bearer <token>`

## Core Endpoints

### 🔐 Authentication
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/auth/sign-in` | Đăng nhập (username/email) | ❌ |
| GET | `/auth/me` | Lấy thông tin user hiện tại | ✅ |
| POST | `/auth/refresh` | Làm mới access token | ❌ |
| GET | `/auth/verify` | Xác thực token hiện tại | ✅ |
| POST | `/auth/sign-out` | Đăng xuất | ✅ |
| GET | `/auth/tokens` | Liệt kê tất cả token | ✅ |
| POST | `/auth/revoke` | Thu hồi token | ✅ |
| POST | `/auth/device/register` | Đăng ký thiết bị | ✅ |

### 👥 Public APIs
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/public/courses` | Danh sách khóa học công khai | ❌ |
| GET | `/public/courses/categories` | Danh mục khóa học | ❌ |
| GET | `/public/courses/search` | Tìm kiếm khóa học | ❌ |
| GET | `/public/events` | Danh sách sự kiện công khai | ❌ |
| GET | `/public/events/{event_id}` | Chi tiết sự kiện | ❌ |
| POST | `/public/events/{event_id}/registrations` | Đăng ký sự kiện | ❌ |
| GET | `/public/registrations` | Danh sách đăng ký theo email | ❌ |
| GET | `/public/workshop-types` | Danh sách loại workshop | ❌ |

### 🎓 Student APIs
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/students/profile` | Lấy profile học viên | ✅ |
| PUT | `/students/profile` | Cập nhật profile học viên | ✅ |
| GET | `/students/courses` | Danh sách khóa học của học viên | ✅ |
| GET | `/students/courses/{course_id}` | Chi tiết khóa học | ✅ |
| GET | `/students/schedule` | Lịch học của học viên | ✅ |
| GET | `/students/schedule/week` | Lịch học theo tuần | ✅ |
| GET | `/students/progress` | Báo cáo tiến độ học tập | ✅ |
| GET | `/students/lessons/{lesson_id}` | Chi tiết bài học | ✅ |
| POST | `/students/lessons/{lesson_id}/attendance` | Điểm danh bài học | ✅ |

### 👨‍🏫 Instructor APIs
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/instructors/profile` | Profile giảng viên | ✅ |
| PUT | `/instructors/profile` | Cập nhật profile giảng viên | ✅ |
| GET | `/instructors/courses` | Danh sách khóa học giảng dạy | ✅ |
| GET | `/instructors/courses/{course_id}` | Chi tiết khóa học giảng dạy | ✅ |
| GET | `/instructors/schedule` | Lịch giảng dạy | ✅ |
| GET | `/instructors/schedule/week` | Lịch giảng dạy theo tuần | ✅ |
| GET | `/instructors/classes/{class_id}/students` | Danh sách học viên trong lớp | ✅ |
| GET | `/instructors/lessons/{lesson_id}` | Chi tiết bài học giảng dạy | ✅ |
| PUT | `/instructors/lessons/{lesson_id}` | Cập nhật bài học | ✅ |
| POST | `/instructors/lessons/{lesson_id}/attendance` | Quản lý điểm danh | ✅ |

### 📚 Course Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| GET | `/courses` | Danh sách tất cả khóa học | ✅ |
| GET | `/courses/{course_id}` | Chi tiết khóa học | ✅ |
| GET | `/courses/{course_id}/lessons` | Danh sách bài học trong khóa học | ✅ |
| GET | `/courses/{course_id}/students` | Danh sách học viên trong khóa học | ✅ |
| GET | `/lessons/{lesson_id}` | Chi tiết bài học | ✅ |
| GET | `/lessons/{lesson_id}/materials` | Tài liệu bài học | ✅ |

### 📁 File Management
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/files/` | Upload file | ❌ |
| GET | `/files/{access_token}` | Lấy file theo access token | ❌ |

### 💬 Chatbot APIs
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/public/chatbot/sessions` | Tạo phiên chat mới | ❌ |
| GET | `/public/chatbot/sessions/{session_id}` | Lấy thông tin phiên chat | ❌ |
| DELETE | `/public/chatbot/sessions/{session_id}` | Kết thúc phiên chat | ❌ |
| POST | `/public/chatbot/sessions/{session_id}/messages` | Gửi tin nhắn | ❌ |
| GET | `/public/chatbot/sessions/{session_id}/messages` | Lấy lịch sử tin nhắn | ❌ |
| POST | `/public/chatbot/messages/{message_id}/feedback` | Gửi feedback | ❌ |
| GET | `/public/chatbot/config` | Lấy cấu hình chatbot | ❌ |

## Request/Response Examples

### Login Request
```json
{
  "username": "student",
  "password": "student@X2025",
  "device_info": {
    "device_type": "mobile",
    "device_name": "iPhone 15 Pro",
    "os_version": "iOS 17.0"
  },
  "remember_me": true
}
```

### Login Response
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user_info": {
      "id": 1,
      "name": "Student User",
      "email": "<EMAIL>",
      "login": "student",
      "is_admin": false,
      "groups": ["student"]
    }
  }
}
```

### User Info Response
```json
{
  "success": true,
  "message": "User info retrieved successfully",
  "data": {
    "id": 1,
    "name": "Student User",
    "email": "<EMAIL>",
    "login": "student",
    "is_admin": false,
    "is_system": false,
    "groups": ["student"],
    "partner_id": 123
  }
}
```

## Error Responses

### 401 Unauthorized
```json
{
  "success": false,
  "message": "Invalid credentials",
  "error_code": "INVALID_CREDENTIALS"
}
```

### 422 Validation Error
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "password",
      "message": "Password is required"
    }
  ]
}
```

## Common Headers
```
Accept-Language: vi-VN,vi;q=0.9,en;q=0.8
Content-Type: application/json
Authorization: Bearer <token>
```

## Test Accounts
- **Student**: username: `student`, password: `student@X2025`
- **Instructor**: username: `teacher`, password: `teacher@X2025`
