{"openapi": "3.1.0", "info": {"title": "EarnBase API", "description": "EarnBase API Framework for Odoo", "version": "1.0"}, "servers": [{"url": "/api/v1"}], "paths": {"/": {"get": {"tags": ["core"], "summary": "Get Api Info", "description": "Trả về thông tin tổng quan về API.", "operationId": "get_api_info__get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiStatus"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"tags": ["core"], "summary": "Health Check", "description": "Endpoint kiểm tra sức khỏe hệ thống API.", "operationId": "health_check_health_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/modules": {"get": {"tags": ["core"], "summary": "<PERSON> <PERSON><PERSON>", "description": "Trả về danh sách tất cả API modules đã đăng ký.", "operationId": "get_api_modules_modules_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Response Get <PERSON>pi <PERSON>les <PERSON>les Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/sessions": {"post": {"tags": ["public"], "summary": "Create Chat Session", "description": "<PERSON><PERSON>o phiên chat mới", "operationId": "create_chat_session_public_chatbot_sessions_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSessionCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/sessions/{session_id}": {"get": {"tags": ["public"], "summary": "Get Chat Session", "description": "<PERSON><PERSON><PERSON> thông tin phiên chat", "operationId": "get_chat_session_public_chatbot_sessions__session_id__get", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["public"], "summary": "End Chat Session", "description": "<PERSON><PERSON><PERSON> th<PERSON> p<PERSON>n chat", "operationId": "end_chat_session_public_chatbot_sessions__session_id__delete", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/sessions/{session_id}/messages": {"post": {"tags": ["public"], "summary": "Send Message", "description": "<PERSON><PERSON><PERSON> tin nhắn mới trong phiên chat", "operationId": "send_message_public_chatbot_sessions__session_id__messages_post", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["public"], "summary": "Get Messages", "description": "<PERSON><PERSON><PERSON> lịch sử tin nhắn trong phiên chat", "operationId": "get_messages_public_chatbot_sessions__session_id__messages_get", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "S<PERSON> lượng tin nhắn tối đa", "default": 50, "title": "Limit"}, "description": "S<PERSON> lượng tin nhắn tối đa"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/messages/{message_id}/feedback": {"post": {"tags": ["public"], "summary": "Create <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>i feedback cho tin nhắn", "operationId": "create_feedback_public_chatbot_messages__message_id__feedback_post", "parameters": [{"name": "message_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Message Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatFeedbackCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatFeedbackResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/config": {"get": {"tags": ["public"], "summary": "<PERSON> <PERSON><PERSON>bot Config", "description": "<PERSON><PERSON><PERSON> c<PERSON>u hình chatbot cho website", "operationId": "get_chatbot_config_public_chatbot_config_get", "parameters": [{"name": "website_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Mã của website", "title": "Website Code"}, "description": "Mã của website"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatbotConfigResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/sign-in": {"post": {"tags": ["auth"], "summary": "Sign In", "description": "Enhanced sign-in supporting both username/email login with optional device tracking", "operationId": "sign_in_auth_sign_in_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnifiedLoginRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhancedTokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/me": {"get": {"tags": ["auth", "auth"], "summary": "Get current user info", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của người dùng đã xác thực", "operationId": "get_user_info_auth_me_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/refresh": {"post": {"tags": ["auth"], "summary": "Refresh <PERSON>", "description": "<PERSON><PERSON><PERSON> mới access token bằng refresh token", "operationId": "refresh_token_auth_refresh_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_refresh_token_auth_refresh_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/verify": {"get": {"tags": ["auth"], "summary": "Verify <PERSON>", "description": "<PERSON><PERSON><PERSON> thực token hiện tại", "operationId": "verify_token_auth_verify_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/sign-out": {"post": {"tags": ["auth"], "summary": "Sign Out", "description": "<PERSON><PERSON><PERSON> xuất người dùng và thu hồi token hiện tại.", "operationId": "sign_out_auth_sign_out_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}}