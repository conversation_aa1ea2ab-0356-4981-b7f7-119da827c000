{"openapi": "3.1.0", "info": {"title": "EarnBase API", "description": "EarnBase API Framework for Odoo", "version": "1.0"}, "servers": [{"url": "/api/v1"}], "paths": {"/": {"get": {"tags": ["core"], "summary": "Get Api Info", "description": "Trả về thông tin tổng quan về API.", "operationId": "get_api_info__get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiStatus"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"tags": ["core"], "summary": "Health Check", "description": "Endpoint kiểm tra sức khỏe hệ thống API.", "operationId": "health_check_health_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/modules": {"get": {"tags": ["core"], "summary": "<PERSON> <PERSON><PERSON>", "description": "Trả về danh sách tất cả API modules đã đăng ký.", "operationId": "get_api_modules_modules_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Response Get <PERSON>pi <PERSON>les <PERSON>les Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/sign-in": {"post": {"tags": ["auth"], "summary": "Sign In", "description": "Enhanced sign-in supporting both username/email login with optional device tracking", "operationId": "sign_in_auth_sign_in_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnifiedLoginRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhancedTokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/me": {"get": {"tags": ["auth"], "summary": "Get current user info", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của người dùng đã xác thực", "operationId": "get_user_info_auth_me_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}}