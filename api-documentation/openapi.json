{"openapi": "3.1.0", "info": {"title": "EarnBase API", "description": "EarnBase API Framework for Odoo", "version": "1.0"}, "servers": [{"url": "/api/v1"}], "paths": {"/": {"get": {"tags": ["core"], "summary": "Get Api Info", "description": "Trả về thông tin tổng quan về API.", "operationId": "get_api_info__get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiStatus"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/health": {"get": {"tags": ["core"], "summary": "Health Check", "description": "Endpoint kiểm tra sức khỏe hệ thống API.", "operationId": "health_check_health_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/modules": {"get": {"tags": ["core"], "summary": "<PERSON> <PERSON><PERSON>", "description": "Trả về danh sách tất cả API modules đã đăng ký.", "operationId": "get_api_modules_modules_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Response Get <PERSON>pi <PERSON>les <PERSON>les Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/sessions": {"post": {"tags": ["public"], "summary": "Create Chat Session", "description": "<PERSON><PERSON>o phiên chat mới", "operationId": "create_chat_session_public_chatbot_sessions_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSessionCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/sessions/{session_id}": {"get": {"tags": ["public"], "summary": "Get Chat Session", "description": "<PERSON><PERSON><PERSON> thông tin phiên chat", "operationId": "get_chat_session_public_chatbot_sessions__session_id__get", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["public"], "summary": "End Chat Session", "description": "<PERSON><PERSON><PERSON> th<PERSON> p<PERSON>n chat", "operationId": "end_chat_session_public_chatbot_sessions__session_id__delete", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/sessions/{session_id}/messages": {"post": {"tags": ["public"], "summary": "Send Message", "description": "<PERSON><PERSON><PERSON> tin nhắn mới trong phiên chat", "operationId": "send_message_public_chatbot_sessions__session_id__messages_post", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["public"], "summary": "Get Messages", "description": "<PERSON><PERSON><PERSON> lịch sử tin nhắn trong phiên chat", "operationId": "get_messages_public_chatbot_sessions__session_id__messages_get", "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "S<PERSON> lượng tin nhắn tối đa", "default": 50, "title": "Limit"}, "description": "S<PERSON> lượng tin nhắn tối đa"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/messages/{message_id}/feedback": {"post": {"tags": ["public"], "summary": "Create <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>i feedback cho tin nhắn", "operationId": "create_feedback_public_chatbot_messages__message_id__feedback_post", "parameters": [{"name": "message_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Message Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatFeedbackCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatFeedbackResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/chatbot/config": {"get": {"tags": ["public"], "summary": "<PERSON> <PERSON><PERSON>bot Config", "description": "<PERSON><PERSON><PERSON> c<PERSON>u hình chatbot cho website", "operationId": "get_chatbot_config_public_chatbot_config_get", "parameters": [{"name": "website_code", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Mã của website", "title": "Website Code"}, "description": "Mã của website"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatbotConfigResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/sign-in": {"post": {"tags": ["auth"], "summary": "Sign In", "description": "Enhanced sign-in supporting both username/email login with optional device tracking", "operationId": "sign_in_auth_sign_in_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnifiedLoginRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnhancedTokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/me": {"get": {"tags": ["auth", "auth"], "summary": "Get current user info", "description": "<PERSON><PERSON><PERSON> thông tin chi tiết của người dùng đã xác thực", "operationId": "get_user_info_auth_me_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserInfoResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/refresh": {"post": {"tags": ["auth"], "summary": "Refresh <PERSON>", "description": "<PERSON><PERSON><PERSON> mới access token bằng refresh token", "operationId": "refresh_token_auth_refresh_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Body_refresh_token_auth_refresh_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/verify": {"get": {"tags": ["auth"], "summary": "Verify <PERSON>", "description": "<PERSON><PERSON><PERSON> thực token hiện tại", "operationId": "verify_token_auth_verify_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/sign-out": {"post": {"tags": ["auth"], "summary": "Sign Out", "description": "<PERSON><PERSON><PERSON> xuất người dùng và thu hồi token hiện tại.", "operationId": "sign_out_auth_sign_out_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/tokens": {"get": {"tags": ["auth"], "summary": "List Tokens", "description": "Liệt kê tất cả token của người dùng hiện tại.", "operationId": "list_tokens_auth_tokens_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/revoke": {"post": {"tags": ["auth"], "summary": "Revoke Token", "description": "<PERSON>hu hồi một token cụ thể (phải là token của user hiệ<PERSON> tại).", "operationId": "revoke_token_auth_revoke_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Token Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/device/register": {"post": {"tags": ["auth"], "summary": "Register Device", "description": "Register a new device for the current user", "operationId": "register_device_auth_device_register_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeviceRegistrationRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeviceRegistrationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/files/{access_token}": {"get": {"tags": ["files"], "summary": "Get File By Token", "description": "Get file by access token\n\nArgs:\n    access_token: The file's access token\n    is_download: Whether to download the file\n    is_preview: Whether to preview the file (applies to images and PDFs)\n    request: FastAPI request object\n\nReturns:\n    File information or the file itself for download/preview", "operationId": "get_file_by_token_files__access_token__get", "parameters": [{"name": "access_token", "in": "path", "required": true, "schema": {"type": "string", "title": "Access Token"}}, {"name": "is_download", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Is Download"}}, {"name": "is_preview", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Is Preview"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/files/": {"post": {"tags": ["files"], "summary": "Upload File Endpoint", "description": "Upload a file\n\nArgs:\n    file: The file to upload\n    request: FastAPI request object\n\nReturns:\n    Details of the uploaded attachment including URLs", "operationId": "upload_file_endpoint_files__post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_file_endpoint_files__post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/events": {"get": {"tags": ["public"], "summary": "Get Public Events", "description": "<PERSON><PERSON><PERSON> <PERSON>n bộ danh sách sự kiện công khai", "operationId": "get_public_events_public_events_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicEventListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/events/{event_id}": {"get": {"tags": ["public"], "summary": "Get Public Event Detail", "description": "<PERSON><PERSON><PERSON> chi tiết sự kiện công khai theo ID", "operationId": "get_public_event_detail_public_events__event_id__get", "parameters": [{"name": "event_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID sự kiện", "title": "Event Id"}, "description": "ID sự kiện"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicEventDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/workshop-types": {"get": {"tags": ["public"], "summary": "Get Public Workshop Types", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON>n bộ danh sách loại workshop công khai", "operationId": "get_public_workshop_types_public_workshop_types_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicWorkshopTypeListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/events/{event_id}/registrations": {"post": {"tags": ["public"], "summary": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký sự kiện mới", "description": "API tạo đăng ký sự kiện mới từ thông tin người dùng website cho một sự kiện cụ thể.", "operationId": "create_registration_public_events__event_id__registrations_post", "parameters": [{"name": "event_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID sự kiện", "title": "Event Id"}, "description": "ID sự kiện"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicEventRegistrationCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicEventRegistrationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/registrations": {"get": {"tags": ["public"], "summary": "<PERSON><PERSON><PERSON> danh sách đăng ký sự kiện theo email", "description": "API lấy danh sách đăng ký sự kiện của người dùng dựa trên email.", "operationId": "get_registrations_by_email_public_registrations_get", "parameters": [{"name": "email", "in": "query", "required": true, "schema": {"type": "string", "description": "<PERSON><PERSON> d<PERSON>ng", "format": "email", "title": "Email"}, "description": "<PERSON><PERSON> d<PERSON>ng"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicEventRegistrationListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/courses/categories": {"get": {"summary": "Get Course Categories", "description": "Get course categories.", "operationId": "get_course_categories_public_courses_categories_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/courses/search": {"get": {"summary": "Search Courses", "description": "Search courses from database.", "operationId": "search_courses_public_courses_search_get", "parameters": [{"name": "query", "in": "query", "required": false, "schema": {"type": "string", "default": "", "title": "Query"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "<PERSON>"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/courses": {"get": {"tags": ["Public APIs"], "summary": "Get Public Courses", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch kh<PERSON><PERSON> học công khai cho landing page", "operationId": "get_public_courses_public_courses_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicCourseListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/courses/{course_id}": {"get": {"summary": "Get Course Detail", "description": "Get course detail by ID.", "operationId": "get_course_detail_public_courses__course_id__get", "parameters": [{"name": "course_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Course Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/classes": {"get": {"tags": ["Public APIs"], "summary": "Get Public Classes", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch lớp học đang tuyển sinh cho landing page", "operationId": "get_public_classes_public_classes_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Số trang", "default": 1, "title": "Page"}, "description": "Số trang"}, {"name": "per_page", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "description": "<PERSON><PERSON> lượng mỗi trang", "default": 10, "title": "Per Page"}, "description": "<PERSON><PERSON> lượng mỗi trang"}, {"name": "course_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo kh<PERSON>a học", "title": "Course Id"}, "description": "<PERSON><PERSON><PERSON> theo kh<PERSON>a học"}, {"name": "start_date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo ng<PERSON>y b<PERSON><PERSON> đ<PERSON>u từ", "title": "Start Date From"}, "description": "<PERSON><PERSON><PERSON> theo ng<PERSON>y b<PERSON><PERSON> đ<PERSON>u từ"}, {"name": "learning_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo lo<PERSON> hình học tập", "title": "Learning Type"}, "description": "<PERSON><PERSON><PERSON> theo lo<PERSON> hình học tập"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicClassListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/classes/{class_id}": {"get": {"tags": ["Public APIs"], "summary": "Get Public Class Detail", "description": "<PERSON><PERSON><PERSON> chi tiết lớp học công khai theo ID", "operationId": "get_public_class_detail_public_classes__class_id__get", "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "<PERSON> lớp h<PERSON>c", "title": "Class Id"}, "description": "<PERSON> lớp h<PERSON>c"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicClassDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/locations": {"get": {"tags": ["Public APIs"], "summary": "Get Public Locations", "description": "<PERSON><PERSON><PERSON> danh sách cơ sở đào tạo công khai", "operationId": "get_public_locations_public_locations_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Số trang", "default": 1, "title": "Page"}, "description": "Số trang"}, {"name": "per_page", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "description": "<PERSON><PERSON> lượng mỗi trang", "default": 10, "title": "Per Page"}, "description": "<PERSON><PERSON> lượng mỗi trang"}, {"name": "city", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo thành phố", "title": "City"}, "description": "<PERSON><PERSON><PERSON> theo thành phố"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>", "default": "name", "title": "Sort By"}, "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON><PERSON> tự sắp xếp (asc, desc)", "default": "asc", "title": "Sort Order"}, "description": "<PERSON><PERSON><PERSON> tự sắp xếp (asc, desc)"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicLocationListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/public/locations/{location_id}": {"get": {"tags": ["Public APIs"], "summary": "Get Public Location Detail", "description": "<PERSON><PERSON><PERSON> chi tiết cơ sở đào tạo công khai theo ID", "operationId": "get_public_location_detail_public_public_locations__location_id__get", "parameters": [{"name": "location_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID cơ sở đào tạo", "title": "Location Id"}, "description": "ID cơ sở đào tạo"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicLocationDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/rooms": {"get": {"tags": ["Public APIs"], "summary": "Get Public Rooms", "description": "<PERSON><PERSON><PERSON> danh sách phòng học công khai", "operationId": "get_public_rooms_public_rooms_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Số trang", "default": 1, "title": "Page"}, "description": "Số trang"}, {"name": "per_page", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "description": "<PERSON><PERSON> lượng mỗi trang", "default": 10, "title": "Per Page"}, "description": "<PERSON><PERSON> lượng mỗi trang"}, {"name": "location_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo cơ sở đào tạo", "title": "Location Id"}, "description": "<PERSON><PERSON><PERSON> theo cơ sở đào tạo"}, {"name": "room_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo lo<PERSON>i phòng", "title": "Room Type"}, "description": "<PERSON><PERSON><PERSON> theo lo<PERSON>i phòng"}, {"name": "min_capacity", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> ch<PERSON>a tối thiểu", "title": "Min Capacity"}, "description": "<PERSON><PERSON><PERSON> ch<PERSON>a tối thiểu"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>", "default": "name", "title": "Sort By"}, "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON><PERSON> tự sắp xếp (asc, desc)", "default": "asc", "title": "Sort Order"}, "description": "<PERSON><PERSON><PERSON> tự sắp xếp (asc, desc)"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicRoomListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/courses/{course_id}/enrollments": {"post": {"tags": ["Public APIs"], "summary": "Tạo đăng ký khóa học mới kèm thông tin thanh toán cho một khóa học cụ thể", "description": "API tạo đăng ký khóa học mới và thông tin thanh toán từ thông tin người dùng website cho một khóa học cụ thể.", "operationId": "create_enrollment_public_courses__course_id__enrollments_post", "parameters": [{"name": "course_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Course Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicEnrollmentCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicEnrollmentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/payments/status": {"get": {"tags": ["Public APIs"], "summary": "<PERSON><PERSON><PERSON> tra trạng thái thanh toán", "description": "API kiểm tra trạng thái thanh toán cho đăng ký khóa học bằng mã tham chiếu đơn hàng", "operationId": "get_payment_status_public_payments_status_get", "parameters": [{"name": "order_reference", "in": "query", "required": true, "schema": {"type": "string", "description": "<PERSON><PERSON> tham chi<PERSON>u đ<PERSON> hà<PERSON> (ví dụ: ENR-XX12345)", "title": "Order Reference"}, "description": "<PERSON><PERSON> tham chi<PERSON>u đ<PERSON> hà<PERSON> (ví dụ: ENR-XX12345)"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicPaymentStatusResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/instructors": {"get": {"summary": "Get Instructors", "description": "Get instructors list.", "operationId": "get_instructors_public_instructors_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "<PERSON>"}}, {"name": "active_only", "in": "query", "required": false, "schema": {"type": "boolean", "default": true, "title": "Active Only"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/instructors/{instructor_id}": {"get": {"tags": ["Public APIs"], "summary": "Get Public Instructor", "description": "<PERSON><PERSON><PERSON> chi tiết gi<PERSON>ng viên công khai theo ID", "operationId": "get_public_instructor_public_instructors__instructor_id__get", "parameters": [{"name": "instructor_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID giảng viên", "title": "Inst<PERSON>ctor Id"}, "description": "ID giảng viên"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicInstructorDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/certificates/verify/{code}": {"get": {"tags": ["Public APIs"], "summary": "<PERSON><PERSON><PERSON> thực chứng chỉ bằng mã xác minh", "description": "API xác thực t<PERSON>h hợp lệ của chứng chỉ thông qua mã xác minh", "operationId": "verify_certificate_public_certificates_verify__code__get", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "description": "<PERSON><PERSON> xác minh chứng chỉ", "title": "Code"}, "description": "<PERSON><PERSON> xác minh chứng chỉ"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicCertificateVerifyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Public APIs"], "summary": "<PERSON><PERSON><PERSON> thực chứng chỉ và lưu thông tin người xác minh", "description": "API xác thực t<PERSON>h hợp lệ của chứng chỉ và lưu thông tin người xác minh", "operationId": "verify_certificate_with_info_public_certificates_verify__code__post", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "description": "<PERSON><PERSON> xác minh chứng chỉ", "title": "Code"}, "description": "<PERSON><PERSON> xác minh chứng chỉ"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicCertificateVerifyRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicCertificateVerifyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/certificates/{code}": {"get": {"tags": ["Public APIs"], "summary": "<PERSON><PERSON> thông tin chi tiết chứng chỉ", "description": "API xem thông tin chi tiết chứng chỉ thông qua mã xác minh", "operationId": "view_certificate_public_certificates__code__get", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "description": "<PERSON><PERSON> xác minh chứng chỉ", "title": "Code"}, "description": "<PERSON><PERSON> xác minh chứng chỉ"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicCertificateViewResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/certificates/{code}/download": {"get": {"tags": ["Public APIs"], "summary": "<PERSON><PERSON><PERSON> xu<PERSON>ng chứng chỉ", "description": "API tải xuống chứng chỉ dưới dạng PDF", "operationId": "download_certificate_public_certificates__code__download_get", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "description": "<PERSON><PERSON> xác minh chứng chỉ", "title": "Code"}, "description": "<PERSON><PERSON> xác minh chứng chỉ"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/certificates/{code}/qr-code": {"get": {"tags": ["Public APIs"], "summary": "<PERSON><PERSON><PERSON> mã QR của chứng chỉ", "description": "API lấy mã QR của chứng chỉ dưới dạng hình ảnh PNG", "operationId": "get_certificate_qr_code_public_certificates__code__qr_code_get", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "description": "<PERSON><PERSON> xác minh chứng chỉ", "title": "Code"}, "description": "<PERSON><PERSON> xác minh chứng chỉ"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/certificates/recent": {"get": {"tags": ["Public APIs"], "summary": "<PERSON><PERSON><PERSON> danh sách chứng chỉ đã xác minh gần đây", "description": "API lấy danh sách chứng chỉ đã xác minh gần đây", "operationId": "get_recent_certificates_public_certificates_recent_get", "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "description": "<PERSON><PERSON> lượng chứng chỉ tối đa", "default": 10, "title": "Limit"}, "description": "<PERSON><PERSON> lượng chứng chỉ tối đa"}, {"name": "days", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 365, "minimum": 1, "description": "<PERSON><PERSON> ngày gần đây", "default": 30, "title": "Days"}, "description": "<PERSON><PERSON> ngày gần đây"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicCertificateListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/leads": {"post": {"tags": ["Public APIs"], "summary": "<PERSON><PERSON><PERSON><PERSON> nhận đăng ký tư vấn khóa học", "description": "API tiếp nhận thông tin đăng ký tư vấn khóa học từ website hoặc ứng dụng di động.", "operationId": "create_lead_public_leads_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicLeadCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PublicLeadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/subjects": {"get": {"summary": "Get Subjects", "description": "Get subjects from database.", "operationId": "get_subjects_public_subjects_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/classes": {"get": {"tags": ["Course Management"], "summary": "Get Classes", "description": "<PERSON><PERSON><PERSON> danh sách lớp học với phân trang và lọc", "operationId": "get_classes_classes_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Số trang", "default": 1, "title": "Page"}, "description": "Số trang"}, {"name": "per_page", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "<PERSON><PERSON> lượng mỗi trang", "default": 20, "title": "Per Page"}, "description": "<PERSON><PERSON> lượng mỗi trang"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "T<PERSON>m kiếm theo tên hoặc mã", "title": "Search"}, "description": "T<PERSON>m kiếm theo tên hoặc mã"}, {"name": "course_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo kh<PERSON>a học", "title": "Course Id"}, "description": "<PERSON><PERSON><PERSON> theo kh<PERSON>a học"}, {"name": "stage_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái", "title": "Stage Id"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái"}, {"name": "active", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái hoạt động", "title": "Active"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái hoạt động"}, {"name": "start_date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo ng<PERSON>y b<PERSON><PERSON> đ<PERSON>u từ", "title": "Start Date From"}, "description": "<PERSON><PERSON><PERSON> theo ng<PERSON>y b<PERSON><PERSON> đ<PERSON>u từ"}, {"name": "start_date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo ngày b<PERSON><PERSON> đ<PERSON>u đến", "title": "Start Date To"}, "description": "<PERSON><PERSON><PERSON> theo ngày b<PERSON><PERSON> đ<PERSON>u đến"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Course Management"], "summary": "Create Class", "description": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> học mới", "operationId": "create_class_classes_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassCreateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/classes/{class_id}": {"get": {"tags": ["Course Management"], "summary": "Get Class", "description": "<PERSON><PERSON><PERSON> chi tiết lớp học theo ID", "operationId": "get_class_classes__class_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "<PERSON> lớp h<PERSON>c", "title": "Class Id"}, "description": "<PERSON> lớp h<PERSON>c"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Course Management"], "summary": "Update Class", "description": "<PERSON><PERSON><PERSON> nh<PERSON>t lớp học", "operationId": "update_class_classes__class_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "<PERSON> lớp h<PERSON>c", "title": "Class Id"}, "description": "<PERSON> lớp h<PERSON>c"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Course Management"], "summary": "Delete Class", "description": "<PERSON><PERSON><PERSON> (đ<PERSON><PERSON> d<PERSON>u không hoạt động)", "operationId": "delete_class_classes__class_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "<PERSON> lớp h<PERSON>c", "title": "Class Id"}, "description": "<PERSON> lớp h<PERSON>c"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassDeleteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/classes/{class_id}/stage": {"post": {"tags": ["Course Management"], "summary": "Update Class Stage", "description": "<PERSON><PERSON><PERSON> nhật trạng thái lớp học", "operationId": "update_class_stage_classes__class_id__stage_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "<PERSON> lớp h<PERSON>c", "title": "Class Id"}, "description": "<PERSON> lớp h<PERSON>c"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassStageUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassStageUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/courses": {"get": {"tags": ["Course Management"], "summary": "Get Courses", "description": "<PERSON><PERSON><PERSON> danh sách khóa học với phân trang và lọc", "operationId": "get_courses_courses_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Số trang", "default": 1, "title": "Page"}, "description": "Số trang"}, {"name": "per_page", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "<PERSON><PERSON> lượng mỗi trang", "default": 20, "title": "Per Page"}, "description": "<PERSON><PERSON> lượng mỗi trang"}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "T<PERSON>m kiếm theo tên hoặc mã", "title": "Search"}, "description": "T<PERSON>m kiếm theo tên hoặc mã"}, {"name": "category_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo danh mục", "title": "Category Id"}, "description": "<PERSON><PERSON><PERSON> theo danh mục"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseBase"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/courses/{course_id}": {"get": {"tags": ["Course Management"], "summary": "Get Course", "description": "<PERSON><PERSON><PERSON> chi tiết kh<PERSON>a học theo ID", "operationId": "get_course_courses__course_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "course_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID khóa học", "title": "Course Id"}, "description": "ID khóa học"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CourseDetail"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/refunds": {"get": {"tags": ["Student Management"], "summary": "Get Refunds", "description": "<PERSON><PERSON><PERSON> danh sách yêu cầu hoàn phí của học viên hiện tại", "operationId": "get_refunds_refunds_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Số trang", "default": 1, "title": "Page"}, "description": "Số trang"}, {"name": "per_page", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "<PERSON><PERSON> lượng mỗi trang", "default": 20, "title": "Per Page"}, "description": "<PERSON><PERSON> lượng mỗi trang"}, {"name": "course_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo kh<PERSON>a học", "title": "Course Id"}, "description": "<PERSON><PERSON><PERSON> theo kh<PERSON>a học"}, {"name": "state", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái", "title": "State"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> t<PERSON> ng<PERSON> (YYYY-MM-DD)", "title": "Date From"}, "description": "<PERSON><PERSON><PERSON> t<PERSON> ng<PERSON> (YYYY-MM-DD)"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> (YYYY-MM-DD)", "title": "Date To"}, "description": "<PERSON><PERSON><PERSON> (YYYY-MM-DD)"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequestListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Student Management"], "summary": "Create Refund", "description": "<PERSON><PERSON><PERSON> yêu cầu hoàn phí mới cho học viên", "operationId": "create_refund_refunds_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequestCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequestCreateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/refunds/{id}": {"get": {"tags": ["Student Management"], "summary": "Get Refund", "description": "<PERSON><PERSON><PERSON> chi tiết yêu cầu hoàn phí của học viên", "operationId": "get_refund_refunds__id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID của yêu cầu hoàn phí", "title": "Id"}, "description": "ID của yêu cầu hoàn phí"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequestDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Student Management"], "summary": "Update Refund", "description": "<PERSON><PERSON><PERSON> nhật yêu cầu hoàn phí của học viên (chỉ ở trạng thái nháp)", "operationId": "update_refund_refunds__id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID của yêu cầu hoàn phí", "title": "Id"}, "description": "ID của yêu cầu hoàn phí"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequestUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequestUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/refunds/{id}/submit": {"post": {"tags": ["Student Management"], "summary": "Submit Refund", "description": "<PERSON><PERSON><PERSON> yêu cầu hoàn phí của học viên", "operationId": "submit_refund_refunds__id__submit_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID của yêu cầu hoàn phí", "title": "Id"}, "description": "ID của yêu cầu hoàn phí"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequestActionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/refunds/{id}/cancel": {"post": {"tags": ["Student Management"], "summary": "Cancel Refund", "description": "<PERSON><PERSON><PERSON> yêu cầu hoàn phí của học viên (chỉ ở trạng thái nháp)", "operationId": "cancel_refund_refunds__id__cancel_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID của yêu cầu hoàn phí", "title": "Id"}, "description": "ID của yêu cầu hoàn phí"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequestActionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/issues": {"get": {"tags": ["Authentication"], "summary": "Get Issues", "description": "<PERSON><PERSON><PERSON> danh sách sự cố\n\nArgs:\n    env: Odoo environment từ request\n    current_user: Ngư<PERSON>i dùng hiện tại đã xác thực\n    request_id: ID request hiện tại\n    page: Số trang\n    per_page: <PERSON><PERSON> lượng mỗi trang\n    related_model: <PERSON>ọ<PERSON> theo model liên quan\n    related_id: <PERSON>ọ<PERSON> theo ID đối tượng liên quan\n    issue_type: <PERSON>ọ<PERSON> theo loại sự cố\n    status: Lọc theo trạng thái\n    sort_by: Sắ<PERSON> xếp theo trường\n    sort_order: Thứ tự sắp xếp (asc, desc)\n\nReturns:\n    <PERSON><PERSON> sách sự cố phân trang\n\nRaises:\n    HTTPException: Khi gặp lỗi trong quá trình xử lý", "operationId": "get_issues_issues_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Số trang", "default": 1, "title": "Page"}, "description": "Số trang"}, {"name": "per_page", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "description": "<PERSON><PERSON> lượng mỗi trang", "default": 10, "title": "Per Page"}, "description": "<PERSON><PERSON> lượng mỗi trang"}, {"name": "related_model", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo model li<PERSON>n quan", "title": "Related Model"}, "description": "<PERSON><PERSON><PERSON> theo model li<PERSON>n quan"}, {"name": "related_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo ID đối tượng liên quan", "title": "Related Id"}, "description": "<PERSON><PERSON><PERSON> theo ID đối tượng liên quan"}, {"name": "issue_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo lo<PERSON>i sự cố", "title": "Issue Type"}, "description": "<PERSON><PERSON><PERSON> theo lo<PERSON>i sự cố"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái", "title": "Status"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái"}, {"name": "sort_by", "in": "query", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>", "default": "create_date", "title": "Sort By"}, "description": "<PERSON><PERSON><PERSON> xế<PERSON> theo tr<PERSON>"}, {"name": "sort_order", "in": "query", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON><PERSON> tự sắp xếp (asc, desc)", "default": "desc", "title": "Sort Order"}, "description": "<PERSON><PERSON><PERSON> tự sắp xếp (asc, desc)"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Authentication"], "summary": "Create Issue", "description": "Tạo sự cố mới\n\nArgs:\n    issue_data: <PERSON><PERSON> liệu sự cố mới\n    env: Odoo environment từ request\n    current_user: Người dùng hiện tại đã xác thực\n    request_id: ID request hiện tại\n\nReturns:\n    Thông tin sự cố đã tạo\n\nRaises:\n    HTTPException: Khi gặp lỗi trong quá trình xử lý", "operationId": "create_issue_issues_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueCreateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/issues/{issue_id}": {"get": {"tags": ["Authentication"], "summary": "Get Issue Detail", "description": "<PERSON><PERSON><PERSON> chi tiết sự cố\n\nArgs:\n    issue_id: ID sự cố\n    env: Odoo environment từ request\n    current_user: <PERSON>ư<PERSON>i dùng hiện tại đã xác thực\n    request_id: ID request hiện tại\n\nReturns:\n    <PERSON> tiết sự cố\n\nRaises:\n    HTTPException: Khi gặp lỗi trong quá trình xử lý", "operationId": "get_issue_detail_issues__issue_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "issue_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID sự cố", "title": "Issue Id"}, "description": "ID sự cố"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Authentication"], "summary": "Update Issue", "description": "Cậ<PERSON> nhật sự cố\n\nArgs:\n    issue_id: ID sự cố\n    issue_data: <PERSON><PERSON> liệu cập nhật\n    env: Odoo environment từ request\n    current_user: Người dùng hiện tại đã xác thực\n    request_id: ID request hiện tại\n\nReturns:\n    Thông tin sự cố đã cập nhật\n\nRaises:\n    HTTPException: Khi gặp lỗi trong quá trình xử lý", "operationId": "update_issue_issues__issue_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "issue_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID sự cố", "title": "Issue Id"}, "description": "ID sự cố"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/issues/{issue_id}/stage": {"post": {"tags": ["Authentication"], "summary": "Update Issue Stage", "description": "Cập nhật trạng thái sự cố\n\nArgs:\n    issue_id: ID sự cố\n    stage_data: D<PERSON> liệu trạng thái mới\n    env: Odoo environment từ request\n    current_user: Người dùng hiện tại đã xác thực\n    request_id: ID request hiện tại\n\nReturns:\n    Thông tin sự cố đã cập nhật trạng thái\n\nRaises:\n    HTTPException: Khi gặp lỗi trong quá trình xử lý", "operationId": "update_issue_stage_issues__issue_id__stage_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "issue_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID sự cố", "title": "Issue Id"}, "description": "ID sự cố"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueStageUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueStageUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/issues/{issue_id}/feedback": {"post": {"tags": ["Authentication"], "summary": "Submit Issue Feedback", "description": "G<PERSON>i phản hồi về cách gi<PERSON>i quyết sự cố\n\nArgs:\n    issue_id: ID sự cố\n    feedback_data: <PERSON><PERSON> liệu phản hồi\n    env: Odoo environment từ request\n    current_user: Người dùng hiện tại đã xác thực\n    request_id: ID request hiện tại\n\nReturns:\n    Thông tin sự cố đã cập nhật phản hồi\n\nRaises:\n    HTTPException: Khi gặp lỗi trong quá trình xử lý", "operationId": "submit_issue_feedback_issues__issue_id__feedback_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "issue_id", "in": "path", "required": true, "schema": {"type": "integer", "description": "ID sự cố", "title": "Issue Id"}, "description": "ID sự cố"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueFeedback"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IssueFeedbackResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/profile/": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Student Profile", "description": "Get complete student profile information", "operationId": "get_student_profile_students_profile__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentProfileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Student Management", "Student Management"], "summary": "Update Student Profile", "description": "Update student profile information", "operationId": "update_student_profile_students_profile__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentProfileUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentProfileUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/profile/preferences": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Learning Preferences", "description": "Get student learning preferences", "operationId": "get_learning_preferences_students_profile_preferences_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentPreferencesResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/profile/documents": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Student Documents", "description": "Get list of student documents", "operationId": "get_student_documents_students_profile_documents_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo danh mục", "title": "Category Id"}, "description": "<PERSON><PERSON><PERSON> theo danh mục"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái", "title": "Status"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "<PERSON>"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/SortOrder", "default": "asc"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentDocumentListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Student Management", "Student Management"], "summary": "Upload Student Document", "description": "Upload a new document for student", "operationId": "upload_student_document_students_profile_documents_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentUploadRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentUploadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/courses/": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Enrolled Classes", "description": "Get list of classes student is enrolled in", "operationId": "get_enrolled_classes_students_courses__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái đăng ký", "title": "Status"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái đăng ký"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "<PERSON>"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/SortOrder", "default": "asc"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentClassListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/courses/{class_id}": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Class Details", "description": "Get detailed information about a specific class", "operationId": "get_class_details_students_courses__class_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentClassDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/courses/dashboard": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Student Dashboard", "description": "Get student dashboard statistics and overview", "operationId": "get_student_dashboard_students_courses_dashboard_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentDashboardResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/lessons/": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Accessible Lessons", "description": "Get list of lessons student can access", "operationId": "get_accessible_lessons_students_lessons__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo l<PERSON> h<PERSON>c", "title": "Class Id"}, "description": "<PERSON><PERSON><PERSON> theo l<PERSON> h<PERSON>c"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái", "title": "Status"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON> ngày", "title": "Date From"}, "description": "<PERSON><PERSON> ngày"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON>", "title": "Date To"}, "description": "<PERSON><PERSON><PERSON>"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "<PERSON>"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/SortOrder", "default": "asc"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentLessonListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/lessons/{lesson_id}": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Lesson Details", "description": "Get detailed information about a specific lesson", "operationId": "get_lesson_details_students_lessons__lesson_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lesson_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lesson Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentLessonDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/lessons/{lesson_id}/checkin": {"post": {"tags": ["Student Management", "Student Management"], "summary": "Check-in to Lesson", "description": "Check-in to a lesson", "operationId": "checkin_lesson_students_lessons__lesson_id__checkin_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lesson_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lesson Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckInRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckInResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/lessons/{lesson_id}/materials": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Lesson Materials", "description": "Get list of materials for a specific lesson", "operationId": "get_lesson_materials_students_lessons__lesson_id__materials_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lesson_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lesson Id"}}, {"name": "material_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo loại tài li<PERSON>u", "title": "Material Type"}, "description": "<PERSON><PERSON><PERSON> theo loại tài li<PERSON>u"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LessonMaterialListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/progress/report": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Progress Report", "description": "Get comprehensive student progress report", "operationId": "get_progress_report_students_progress_report_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON> báo cáo", "default": "current_month", "title": "Period"}, "description": "<PERSON><PERSON> báo cáo"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentProgressReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/progress/goals": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Learning Goals", "description": "Get student learning goals and progress", "operationId": "get_learning_goals_students_progress_goals_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái mục tiêu", "title": "Status"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái mục tiêu"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoalProgressResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/payments/": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Payment History", "description": "Get student payment history with pagination", "operationId": "get_payment_history_students_payments__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái thanh toán", "title": "Status"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái thanh toán"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON> ngày", "title": "Date From"}, "description": "<PERSON><PERSON> ngày"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON>", "title": "Date To"}, "description": "<PERSON><PERSON><PERSON>"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "<PERSON>"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/SortOrder", "default": "asc"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentInvoiceListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/payments/{payment_id}": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Payment Details", "description": "Get detailed information about a specific payment", "operationId": "get_payment_details_students_payments__payment_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "payment_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Payment Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentSubmissionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/payments/invoices": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Invoice List", "description": "Get list of student invoices", "operationId": "get_invoice_list_students_payments_invoices_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái hóa đơn", "title": "Status"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái hóa đơn"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "<PERSON>"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/SortOrder", "default": "asc"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentInvoiceListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/payments/invoices/{invoice_id}": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Invoice Details", "description": "Get detailed information about a specific invoice", "operationId": "get_invoice_details_students_payments_invoices__invoice_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "invoice_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Invoice Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentInvoiceDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/payments/request": {"post": {"tags": ["Student Management", "Student Management"], "summary": "Submit Payment Information", "description": "Submit payment information for verification", "operationId": "submit_payment_request_students_payments_request_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentSubmissionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/payments/summary": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Payment Summary", "description": "Get overall payment summary for student", "operationId": "get_payment_summary_students_payments_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentSummaryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/certificates/": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Certificate List", "description": "Get list of student certificates with pagination and filtering", "operationId": "get_certificate_list_students_certificates__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái", "title": "Status"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái"}, {"name": "certificate_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo lo<PERSON>i chứng chỉ", "title": "Certificate Type"}, "description": "<PERSON><PERSON><PERSON> theo lo<PERSON>i chứng chỉ"}, {"name": "course_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo kh<PERSON>a học", "title": "Course Id"}, "description": "<PERSON><PERSON><PERSON> theo kh<PERSON>a học"}, {"name": "year", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo n<PERSON>m cấp", "title": "Year"}, "description": "<PERSON><PERSON><PERSON> theo n<PERSON>m cấp"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "<PERSON>"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/SortOrder", "default": "asc"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentCertificateListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/certificates/{certificate_id}": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Certificate Details", "description": "Get detailed information about a specific certificate", "operationId": "get_certificate_details_students_certificates__certificate_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "certificate_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Certificate Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudentCertificateDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/certificates/{certificate_id}/download": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Download Certificate", "description": "Get download link for certificate file", "operationId": "download_certificate_students_certificates__certificate_id__download_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "certificate_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Certificate Id"}}, {"name": "format", "in": "query", "required": false, "schema": {"type": "string", "description": "Định dạng file (pdf, png, jpg)", "default": "pdf", "title": "Format"}, "description": "Định dạng file (pdf, png, jpg)"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificateDownloadResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/certificates/summary": {"get": {"tags": ["Student Management", "Student Management"], "summary": "Get Certificate Summary", "description": "Get overall certificate summary for student", "operationId": "get_certificate_summary_students_certificates_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificateSummaryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/assessments/classes/{class_id}": {"get": {"tags": ["Student Management", "Student Assessments"], "summary": "<PERSON><PERSON><PERSON> danh sách đ<PERSON>h giá của học viên trong lớp", "description": "<PERSON><PERSON><PERSON> danh sách tất cả đánh giá và điểm số của học viên trong một lớp học cụ thể", "operationId": "get_student_assessments_by_class_students_assessments_classes__class_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase_StudentAssessmentsResponse_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/assessments/overview": {"get": {"tags": ["Student Management", "Student Assessments"], "summary": "<PERSON><PERSON><PERSON> quan điểm số của học viên", "description": "<PERSON><PERSON><PERSON> tổng quan điểm số của học viên trên tất cả các lớp học", "operationId": "get_student_grade_overview_students_assessments_overview_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase_StudentGradeOverviewResponse_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/": {"get": {"tags": ["Student Management"], "summary": "Student API Status", "description": "Check student API status", "operationId": "student_api_status_students__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/achievements": {"get": {"tags": ["Student Management"], "summary": "Get Student Achievements", "description": "Get student achievements and badges", "operationId": "get_student_achievements_students_achievements_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/students/feedback": {"post": {"tags": ["Student Management"], "summary": "Submit Student <PERSON>back", "description": "Submit feedback about courses, instructors, or system", "operationId": "submit_student_feedback_students_feedback_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Feedback Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/profile/": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Instructor Profile", "description": "Get complete instructor profile information", "operationId": "get_instructor_profile_instructors_profile__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorProfileResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Update Instructor Profile", "description": "Update instructor profile information", "operationId": "update_instructor_profile_instructors_profile__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorProfileUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorProfileUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/profile/skills": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Instructor Skills", "description": "Get all skills of the instructor", "operationId": "get_instructor_skills_instructors_profile_skills_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorSkillsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Add <PERSON><PERSON><PERSON><PERSON>", "description": "Add a skill to instructor", "operationId": "add_instructor_skill_instructors_profile_skills_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillAddRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorSkillsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/profile/qualifications": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Instructor Qualifications", "description": "Get all qualifications of the instructor", "operationId": "get_instructor_qualifications_instructors_profile_qualifications_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorQualificationsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Add Instructor Qualification", "description": "Add a qualification to instructor", "operationId": "add_instructor_qualification_instructors_profile_qualifications_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QualificationAddRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorQualificationsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/profile/skills/{skill_id}": {"put": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Update <PERSON><PERSON><PERSON><PERSON>", "description": "Update an existing instructor skill", "operationId": "update_instructor_skill_instructors_profile_skills__skill_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skill_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Skill Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SkillAddRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorSkillsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Remove In<PERSON><PERSON>", "description": "Remove an instructor skill", "operationId": "remove_instructor_skill_instructors_profile_skills__skill_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skill_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Skill Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorSkillsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/courses/": {"get": {"tags": ["Instructor Management", "Course Management"], "summary": "Get Assigned Classes", "description": "Get list of classes assigned to instructor", "operationId": "get_assigned_classes_instructors_courses__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "state", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái lớp", "title": "State"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái lớp"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "<PERSON>"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/SortOrder", "default": "asc"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorClassListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/courses/{class_id}": {"get": {"tags": ["Instructor Management", "Course Management"], "summary": "Get Class Details", "description": "Get detailed information about a specific class", "operationId": "get_class_details_instructors_courses__class_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorClassDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/courses/dashboard": {"get": {"tags": ["Instructor Management", "Course Management"], "summary": "Get Instructor Dashboard", "description": "Get instructor dashboard statistics and overview", "operationId": "get_instructor_dashboard_instructors_courses_dashboard_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstructorDashboardResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/lessons/": {"get": {"tags": ["Instructor Management", "Course Management"], "summary": "Get In<PERSON><PERSON><PERSON>ons", "description": "Get list of lessons for instructor with filtering and pagination", "operationId": "get_instructor_lessons_instructors_lessons__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "state", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo trạng thái", "title": "State"}, "description": "<PERSON><PERSON><PERSON> theo trạng thái"}, {"name": "class_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> theo l<PERSON> h<PERSON>c", "title": "Class Id"}, "description": "<PERSON><PERSON><PERSON> theo l<PERSON> h<PERSON>c"}, {"name": "date_from", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON> ngày", "title": "Date From"}, "description": "<PERSON><PERSON> ngày"}, {"name": "date_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON>", "title": "Date To"}, "description": "<PERSON><PERSON><PERSON>"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "page_size", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 20, "title": "<PERSON>"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sort By"}}, {"name": "sort_order", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/SortOrder", "default": "asc"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LessonListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Instructor Management", "Course Management"], "summary": "Create New Lesson", "description": "Create a new lesson for instructor", "operationId": "create_lesson_instructors_lessons__post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LessonCreateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LessonCreateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/lessons/{lesson_id}": {"get": {"tags": ["Instructor Management", "Course Management"], "summary": "Get Lesson Details", "description": "Get detailed information about a specific lesson", "operationId": "get_lesson_details_instructors_lessons__lesson_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lesson_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lesson Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LessonDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Instructor Management", "Course Management"], "summary": "Update Lesson", "description": "Update lesson information", "operationId": "update_lesson_instructors_lessons__lesson_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lesson_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lesson Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LessonUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LessonUpdateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/lessons/{lesson_id}/start": {"post": {"tags": ["Instructor Management", "Course Management"], "summary": "Start Lesson", "description": "Start a scheduled lesson", "operationId": "start_lesson_instructors_lessons__lesson_id__start_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lesson_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lesson Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LessonActionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/schedule/day": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Daily Schedule", "description": "Get instructor schedule for a specific day", "operationId": "get_daily_schedule_instructors_schedule_day_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "target_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> c<PERSON>n xem lịch (mặc định là hôm nay)", "title": "Target Date"}, "description": "<PERSON><PERSON><PERSON> c<PERSON>n xem lịch (mặc định là hôm nay)"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DayScheduleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/schedule/week": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Weekly Schedule", "description": "Get instructor schedule for a specific week", "operationId": "get_weekly_schedule_instructors_schedule_week_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "week_start", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> tuần (n<PERSON><PERSON> không có sẽ lấy tuần hiện tại)", "title": "Week Start"}, "description": "<PERSON><PERSON><PERSON> tuần (n<PERSON><PERSON> không có sẽ lấy tuần hiện tại)"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WeekScheduleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/schedule/month": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Monthly Schedule", "description": "Get instructor schedule for a specific month", "operationId": "get_monthly_schedule_instructors_schedule_month_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "month", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "maximum": 12, "minimum": 1}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON><PERSON> (1-12)", "title": "Month"}, "description": "<PERSON><PERSON><PERSON><PERSON> (1-12)"}, {"name": "year", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer", "maximum": 2030, "minimum": 2020}, {"type": "null"}], "description": "Năm", "title": "Year"}, "description": "Năm"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MonthScheduleResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/schedule/availability": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Instructor Availability", "description": "Get instructor availability settings", "operationId": "get_instructor_availability_instructors_schedule_availability_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AvailabilityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/attendance/lessons/{lesson_id}": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Lesson Attendance", "description": "Get attendance records for a specific lesson", "operationId": "get_lesson_attendance_instructors_attendance_lessons__lesson_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lesson_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lesson Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/attendance/lessons/{lesson_id}/bulk": {"put": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Bulk Update Attendance", "description": "Update multiple attendance records for a lesson", "operationId": "bulk_update_attendance_instructors_attendance_lessons__lesson_id__bulk_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "lesson_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Lesson Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkAttendanceUpdateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttendanceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/attendance/classes/{class_id}": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Class Attendance Summary", "description": "Get attendance summary for a class", "operationId": "get_class_attendance_summary_instructors_attendance_classes__class_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "class_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Class Id"}}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LMSResponseBase_AttendanceSummary_"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/analytics/performance": {"get": {"tags": ["Instructor Management", "Instructor Management"], "summary": "Get Performance Analytics", "description": "Get comprehensive instructor performance analytics", "operationId": "get_performance_analytics_instructors_analytics_performance_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"type": "string", "description": "<PERSON><PERSON> phân tích", "default": "current_semester", "title": "Period"}, "description": "<PERSON><PERSON> phân tích"}, {"name": "include_comparison", "in": "query", "required": false, "schema": {"type": "boolean", "description": "<PERSON><PERSON> g<PERSON> so s<PERSON>h với đồng nghi<PERSON>p", "default": true, "title": "Include Comparison"}, "description": "<PERSON><PERSON> g<PERSON> so s<PERSON>h với đồng nghi<PERSON>p"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PerformanceAnalyticsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/instructors/": {"get": {"tags": ["Instructor Management"], "summary": "Instructor API Status", "description": "Check instructor API status and available endpoints", "operationId": "instructor_api_status_instructors__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/ws/status": {"get": {"tags": ["WebSocket APIs"], "summary": "WebSocket API Status", "description": "Check WebSocket API status and connection count", "operationId": "websocket_status_ws_status_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/test": {"get": {"summary": "Test Endpoint", "operationId": "test_endpoint_test_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/test-subjects": {"get": {"summary": "Test Subjects", "description": "Minimal test endpoint for subjects.", "operationId": "test_subjects_public_test_subjects_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/test-courses": {"get": {"summary": "Test Courses", "description": "Minimal test endpoint for courses.", "operationId": "test_courses_public_test_courses_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/login": {"post": {"summary": "<PERSON><PERSON>", "description": "Simple login endpoint.", "operationId": "login_auth_login_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/auth/password-reset": {"post": {"summary": "Password Reset", "description": "Simple password reset endpoint.", "operationId": "password_reset_auth_password_reset_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/enrollments": {"post": {"summary": "Create Enrollment", "description": "Create enrollment request.", "operationId": "create_enrollment_public_enrollments_post", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EnrollmentRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/stats": {"get": {"summary": "Get Public Stats", "description": "Get public statistics.", "operationId": "get_public_stats_public_stats_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/website/content/{code}": {"get": {"tags": ["public"], "summary": "Get Public Content By Code", "description": "Lấy nội dung website công khai theo code", "operationId": "get_public_content_by_code_public_website_content__code__get", "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "description": "<PERSON>ã nội dung", "title": "Code"}, "description": "<PERSON>ã nội dung"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase_Dict_str__Any__"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/website/content/type/{content_type}": {"get": {"tags": ["public"], "summary": "Get Public Content By Type", "description": "Lấy nội dung website công khai theo loại", "operationId": "get_public_content_by_type_public_website_content_type__content_type__get", "parameters": [{"name": "content_type", "in": "path", "required": true, "schema": {"type": "string", "description": "<PERSON><PERSON><PERSON> nội dung", "title": "Content Type"}, "description": "<PERSON><PERSON><PERSON> nội dung"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase_List_Dict_str__Any___"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/website/faqs": {"get": {"tags": ["public"], "summary": "Get Public Faqs", "description": "<PERSON><PERSON><PERSON> tất cả FAQ công khai theo danh mục", "operationId": "get_public_faqs_public_website_faqs_get", "parameters": [{"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase_List_Dict_str__Any___"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/public/website/testimonials": {"get": {"tags": ["public"], "summary": "Get Public Testimonials", "description": "<PERSON><PERSON><PERSON> danh sách testimonials c<PERSON><PERSON> khai", "operationId": "get_public_testimonials_public_website_testimonials_get", "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "S<PERSON> lượng testimonials tố<PERSON> đa", "default": 10, "title": "Limit"}, "description": "S<PERSON> lượng testimonials tố<PERSON> đa"}, {"name": "featured_only", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Chỉ lấy testimonials n<PERSON><PERSON> bật", "default": false, "title": "Featured Only"}, "description": "Chỉ lấy testimonials n<PERSON><PERSON> bật"}, {"name": "Accept-Language", "in": "header", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language.", "title": "Accept-Language"}, "description": "The Accept-Language header is used to specify the language of the content to be returned. If a language is not available, the server will return the content in the default language."}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResponseBase_List_Dict_str__Any___"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AcademicProgress": {"properties": {"total_assignments": {"type": "integer", "title": "Total Assignments", "description": "<PERSON><PERSON><PERSON> bài tập", "default": 0}, "completed_assignments": {"type": "integer", "title": "Completed Assignments", "description": "<PERSON><PERSON><PERSON> tập đã hoàn thành", "default": 0}, "pending_assignments": {"type": "integer", "title": "Pending Assignments", "description": "<PERSON><PERSON><PERSON> tập ch<PERSON>a nộp", "default": 0}, "overdue_assignments": {"type": "integer", "title": "Overdue Assignments", "description": "<PERSON><PERSON><PERSON> tập quá hạn", "default": 0}, "average_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Score", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình"}, "highest_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Highest Score", "description": "<PERSON><PERSON><PERSON><PERSON> cao nh<PERSON>t"}, "lowest_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Lowest Score", "description": "<PERSON><PERSON><PERSON><PERSON> thấp nh<PERSON>t"}, "grade_distribution": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Grade Distribution", "description": "<PERSON><PERSON> bố điểm"}, "score_trend": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Score Trend", "description": "<PERSON> h<PERSON>ng điểm số"}, "monthly_performance": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Monthly Performance", "description": "<PERSON><PERSON><PERSON> su<PERSON>t theo tháng"}, "academic_status": {"type": "string", "title": "Academic Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái h<PERSON>c tập"}, "improvement_areas": {"items": {"type": "string"}, "type": "array", "title": "Improvement Areas", "description": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> c<PERSON><PERSON> cải thi<PERSON>n"}}, "type": "object", "required": ["academic_status"], "title": "AcademicProgress", "description": "Academic progress schema."}, "ApiInfo": {"properties": {"version": {"type": "string", "title": "Version"}, "name": {"type": "string", "title": "Name"}, "description": {"type": "string", "title": "Description"}, "documentation_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Documentation Url"}}, "type": "object", "required": ["version", "name", "description"], "title": "ApiInfo", "description": "Thông tin API"}, "ApiStatus": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}, "info": {"$ref": "#/components/schemas/ApiInfo"}, "server_time": {"type": "string", "format": "date-time", "title": "Server Time"}, "status": {"type": "string", "title": "Status", "default": "operational"}, "modules": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["info", "server_time"], "title": "ApiStatus", "description": "API status response"}, "AssessmentDetailSchema": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID kết quả đánh giá"}, "assessment_id": {"type": "integer", "title": "Assessment Id", "description": "ID đánh giá"}, "assessment_name": {"type": "string", "title": "Assessment Name", "description": "<PERSON><PERSON><PERSON> đ<PERSON> giá"}, "assessment_type": {"type": "string", "title": "Assessment Type", "description": "Loại đ<PERSON>h giá"}, "assessment_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assessment Date", "description": "<PERSON><PERSON><PERSON> giá"}, "score": {"type": "number", "title": "Score", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "max_score": {"type": "number", "title": "Max Score", "description": "<PERSON><PERSON><PERSON><PERSON> tối đa"}, "final_score": {"type": "number", "title": "Final Score", "description": "<PERSON><PERSON><PERSON><PERSON> cuối cùng"}, "normalized_score": {"type": "number", "title": "Normalized Score", "description": "<PERSON><PERSON><PERSON><PERSON> trên thang 10"}, "bonus_score": {"type": "number", "title": "Bonus Score", "description": "<PERSON><PERSON><PERSON><PERSON> thưởng"}, "score_percentage": {"type": "number", "title": "Score Percentage", "description": "<PERSON><PERSON><PERSON> tr<PERSON>m điểm"}, "is_passed": {"type": "boolean", "title": "Is Passed", "description": "<PERSON><PERSON> đạt hay chưa"}, "weight": {"type": "number", "title": "Weight", "description": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>"}, "weighted_score": {"type": "number", "title": "Weighted Score", "description": "<PERSON><PERSON><PERSON><PERSON> có trọng số"}, "status": {"type": "string", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "submission_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Submission Date", "description": "<PERSON><PERSON><PERSON> b<PERSON>i"}, "graded_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Graded Date", "description": "<PERSON><PERSON><PERSON> chấm điểm"}, "feedback": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>t"}, "attempt_number": {"type": "integer", "title": "Attempt Number", "description": "<PERSON><PERSON> lần thử"}}, "type": "object", "required": ["id", "assessment_id", "assessment_name", "assessment_type", "score", "max_score", "final_score", "normalized_score", "bonus_score", "score_percentage", "is_passed", "weight", "weighted_score", "status", "attempt_number"], "title": "AssessmentDetailSchema", "description": "<PERSON><PERSON>a cho chi tiết đ<PERSON>h giá của học viên"}, "AssessmentTypeStatsSchema": {"properties": {"assessment_type": {"type": "string", "title": "Assessment Type", "description": "Loại đ<PERSON>h giá"}, "type_display_name": {"type": "string", "title": "Type Display Name", "description": "<PERSON>ên hiển thị loại đánh giá"}, "total_count": {"type": "integer", "title": "Total Count", "description": "Tổng số đánh giá"}, "completed_count": {"type": "integer", "title": "Completed Count", "description": "S<PERSON> đánh giá đã hoàn thành"}, "passed_count": {"type": "integer", "title": "Passed Count", "description": "Số đánh giá đã đạt"}, "average_score": {"type": "number", "title": "Average Score", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình"}, "pass_rate": {"type": "number", "title": "Pass Rate", "description": "Tỷ lệ đạt (%)"}, "completion_rate": {"type": "number", "title": "Completion Rate", "description": "Tỷ lệ hoàn thành (%)"}}, "type": "object", "required": ["assessment_type", "type_display_name", "total_count", "completed_count", "passed_count", "average_score", "pass_rate", "completion_rate"], "title": "AssessmentTypeStatsSchema", "description": "<PERSON><PERSON>a cho thống kê theo loại đánh giá"}, "AttendanceProgress": {"properties": {"total_lessons": {"type": "integer", "title": "Total Lessons", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> h<PERSON>c", "default": 0}, "attended_lessons": {"type": "integer", "title": "Attended Lessons", "description": "<PERSON><PERSON><PERSON> học đã tham gia", "default": 0}, "absent_lessons": {"type": "integer", "title": "Absent Lessons", "description": "<PERSON><PERSON><PERSON> h<PERSON> vắng mặt", "default": 0}, "late_lessons": {"type": "integer", "title": "Late Lessons", "description": "<PERSON><PERSON><PERSON> học đi muộn", "default": 0}, "excused_lessons": {"type": "integer", "title": "Excused <PERSON>ons", "description": "<PERSON><PERSON><PERSON> h<PERSON> có phép", "default": 0}, "attendance_rate": {"type": "number", "title": "Attendance Rate", "description": "Tỷ lệ điểm danh (%)", "default": 0.0}, "punctuality_rate": {"type": "number", "title": "Punctuality Rate", "description": "Tỷ lệ đúng giờ (%)", "default": 0.0}, "monthly_attendance": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Monthly Attendance", "description": "<PERSON><PERSON><PERSON><PERSON> danh theo tháng"}, "weekly_attendance": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Weekly Attendance", "description": "<PERSON><PERSON><PERSON><PERSON> danh theo tu<PERSON>n"}, "attendance_status": {"type": "string", "title": "Attendance Status", "description": "<PERSON>r<PERSON><PERSON> thái điểm danh"}, "needs_improvement": {"type": "boolean", "title": "Needs Improvement", "description": "<PERSON><PERSON><PERSON> c<PERSON> thi<PERSON>n", "default": false}}, "type": "object", "required": ["attendance_status"], "title": "AttendanceProgress", "description": "Attendance progress schema."}, "AttendanceRecord": {"properties": {"student_id": {"type": "integer", "title": "Student Id", "description": "<PERSON> học viên"}, "student_name": {"type": "string", "title": "Student Name", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên"}, "student_code": {"type": "string", "title": "Student Code", "description": "<PERSON><PERSON> học viên"}, "status": {"type": "string", "title": "Status", "description": "<PERSON>r<PERSON><PERSON> thái điểm danh"}, "check_in_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Check In Time", "description": "Thời gian check-in"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}, "is_late": {"type": "boolean", "title": "Is Late", "description": "<PERSON><PERSON>", "default": false}, "late_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Late Minutes", "description": "S<PERSON> ph<PERSON>t muộn"}}, "type": "object", "required": ["student_id", "student_name", "student_code", "status"], "title": "AttendanceRecord", "description": "Attendance record schema."}, "AttendanceResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/AttendanceRecord"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "AttendanceResponse", "description": "Attendance response schema."}, "AttendanceSummary": {"properties": {"class_id": {"type": "integer", "title": "Class Id", "description": "<PERSON> lớp h<PERSON>c"}, "class_name": {"type": "string", "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> h<PERSON>c", "default": 0}, "completed_lessons": {"type": "integer", "title": "Completed Lessons", "description": "<PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "total_students": {"type": "integer", "title": "Total Students", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên", "default": 0}, "average_attendance_rate": {"type": "number", "title": "Average Attendance Rate", "description": "Tỷ lệ điểm danh trung bình", "default": 0.0}, "students_with_low_attendance": {"type": "integer", "title": "Students With Low Attendance", "description": "<PERSON><PERSON><PERSON> viên điểm danh thấp", "default": 0}}, "type": "object", "required": ["class_id", "class_name"], "title": "AttendanceSummary", "description": "Attendance summary schema."}, "AttendanceUpdateRequest": {"properties": {"student_id": {"type": "integer", "title": "Student Id", "description": "<PERSON> học viên"}, "status": {"type": "string", "pattern": "^(present|absent|late|excused)$", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "check_in_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Check In Time", "description": "Thời gian check-in"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["student_id", "status"], "title": "AttendanceUpdateRequest", "description": "Attendance update request schema."}, "AvailabilityResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/InstructorAvailability"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "AvailabilityResponse", "description": "Availability response schema."}, "AvailabilitySlot": {"properties": {"day_of_week": {"type": "string", "title": "Day Of Week", "description": "<PERSON><PERSON><PERSON> trong tuần"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "<PERSON><PERSON><PERSON> b<PERSON>t đầu có thể dạy"}, "end_time": {"type": "string", "format": "time", "title": "End Time", "description": "<PERSON><PERSON><PERSON> kết thúc có thể dạy"}, "is_preferred": {"type": "boolean", "title": "Is Preferred", "description": "<PERSON><PERSON>g gi<PERSON> <PERSON><PERSON> th<PERSON>ch", "default": false}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["day_of_week", "start_time", "end_time"], "title": "AvailabilitySlot", "description": "Availability time slot schema."}, "Body_refresh_token_auth_refresh_post": {"properties": {"refresh_token": {"type": "string", "title": "Refresh <PERSON>"}}, "type": "object", "required": ["refresh_token"], "title": "Body_refresh_token_auth_refresh_post"}, "Body_upload_file_endpoint_files__post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_file_endpoint_files__post"}, "BulkAttendanceUpdateRequest": {"properties": {"attendance_records": {"items": {"$ref": "#/components/schemas/AttendanceUpdateRequest"}, "type": "array", "title": "Attendance Records", "description": "<PERSON><PERSON> s<PERSON>ch điểm danh"}}, "type": "object", "required": ["attendance_records"], "title": "BulkAttendanceUpdateRequest", "description": "Bulk attendance update request schema."}, "CertificateBasicInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID chứng chỉ"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> ch<PERSON>ng chỉ"}, "certificate_number": {"type": "string", "title": "Certificate Number", "description": "<PERSON><PERSON> chứng chỉ"}, "certificate_type": {"type": "string", "title": "Certificate Type", "description": "<PERSON><PERSON><PERSON> chứng chỉ"}, "course_id": {"type": "integer", "title": "Course Id", "description": "ID khóa học"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "course_code": {"type": "string", "title": "Course Code", "description": "Mã khóa học"}, "status": {"type": "string", "title": "Status", "description": "Tr<PERSON>ng thái chứng chỉ"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "<PERSON><PERSON> ho<PERSON>t động", "default": true}, "issue_date": {"type": "string", "format": "date", "title": "Issue Date", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}, "expiry_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Expiry Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> hạn"}, "grade": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Grade", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "grade_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Grade Text", "description": "<PERSON><PERSON><PERSON>"}, "completion_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Completion Percentage", "description": "Tỷ lệ hoàn thành"}}, "type": "object", "required": ["id", "name", "certificate_number", "certificate_type", "course_id", "course_name", "course_code", "status", "issue_date"], "title": "CertificateBasicInfo", "description": "Basic certificate information schema."}, "CertificateDetail": {"properties": {"created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t"}, "id": {"type": "integer", "title": "Id", "description": "ID chứng chỉ"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> ch<PERSON>ng chỉ"}, "certificate_number": {"type": "string", "title": "Certificate Number", "description": "<PERSON><PERSON> chứng chỉ"}, "certificate_type": {"type": "string", "title": "Certificate Type", "description": "<PERSON><PERSON><PERSON> chứng chỉ"}, "course_id": {"type": "integer", "title": "Course Id", "description": "ID khóa học"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "course_code": {"type": "string", "title": "Course Code", "description": "Mã khóa học"}, "status": {"type": "string", "title": "Status", "description": "Tr<PERSON>ng thái chứng chỉ"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "<PERSON><PERSON> ho<PERSON>t động", "default": true}, "issue_date": {"type": "string", "format": "date", "title": "Issue Date", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}, "expiry_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Expiry Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> hạn"}, "grade": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Grade", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "grade_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Grade Text", "description": "<PERSON><PERSON><PERSON>"}, "completion_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Completion Percentage", "description": "Tỷ lệ hoàn thành"}, "subject_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Subject Name", "description": "<PERSON><PERSON><PERSON> môn h<PERSON>c"}, "instructor_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Instructor Name", "description": "<PERSON><PERSON><PERSON> gi<PERSON>ng viên"}, "class_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "total_lessons": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total Lessons", "description": "Tổng số bài học"}, "attended_lessons": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attended Lessons", "description": "Số bài đã tham gia"}, "attendance_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Attendance Rate", "description": "Tỷ lệ tham gia"}, "total_assessments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total Assessments", "description": "Tổng số bài kiểm tra"}, "passed_assessments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Passed Assessments", "description": "Số bài đã qua"}, "average_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Score", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình"}, "qualification": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qualification", "description": "<PERSON><PERSON><PERSON><PERSON>/<PERSON>y<PERSON><PERSON> môn"}, "skills_acquired": {"items": {"type": "string"}, "type": "array", "title": "Skills Acquired", "description": "<PERSON><PERSON> năng đạt đ<PERSON>"}, "competencies": {"items": {"type": "string"}, "type": "array", "title": "Competencies", "description": "<PERSON><PERSON><PERSON> lực đ<PERSON> đ<PERSON>"}, "issued_by_name": {"type": "string", "title": "Issued By Name", "description": "<PERSON><PERSON><PERSON><PERSON> cấp"}, "issued_by_title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Issued By Title", "description": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> cấp"}, "organization_name": {"type": "string", "title": "Organization Name", "description": "<PERSON><PERSON><PERSON> tổ chức"}, "organization_logo_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization Logo Url", "description": "Logo tổ chức"}, "verification_code": {"type": "string", "title": "Verification Code", "description": "<PERSON><PERSON> x<PERSON>c <PERSON>h"}, "verification_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verification Url", "description": "URL xác minh"}, "qr_code_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qr Code Url", "description": "URL mã QR"}, "view_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "View Url", "description": "URL xem chứng chỉ"}, "download_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Download Url", "description": "URL tải xuống"}, "share_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Share Url", "description": "URL chia sẻ"}, "verification_count": {"type": "integer", "title": "Verification Count", "description": "Số lần x<PERSON>c <PERSON>h", "default": 0}, "download_count": {"type": "integer", "title": "Download Count", "description": "Số lần tải xuống", "default": 0}, "last_verified_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Verified At", "description": "<PERSON><PERSON><PERSON> x<PERSON><PERSON>h cu<PERSON>i"}, "last_downloaded_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Downloaded At", "description": "<PERSON><PERSON><PERSON> t<PERSON> cu<PERSON>i"}, "template_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Template Name", "description": "Tên template"}, "language": {"type": "string", "title": "Language", "description": "<PERSON><PERSON><PERSON>", "default": "vi"}, "file_format": {"type": "string", "title": "File Format", "description": "<PERSON><PERSON><PERSON> dạng file", "default": "pdf"}, "file_size": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "File Size", "description": "<PERSON><PERSON><PERSON> th<PERSON> file (bytes)"}}, "type": "object", "required": ["id", "name", "certificate_number", "certificate_type", "course_id", "course_name", "course_code", "status", "issue_date", "issued_by_name", "organization_name", "verification_code"], "title": "CertificateDetail", "description": "Detailed certificate information schema."}, "CertificateDownloadInfo": {"properties": {"certificate_id": {"type": "integer", "title": "Certificate Id", "description": "ID chứng chỉ"}, "download_url": {"type": "string", "title": "Download Url", "description": "URL tải xuống"}, "file_name": {"type": "string", "title": "File Name", "description": "Tên file"}, "file_format": {"type": "string", "title": "File Format", "description": "<PERSON><PERSON><PERSON> dạng file"}, "file_size": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "File Size", "description": "<PERSON><PERSON><PERSON> file"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "<PERSON>h<PERSON><PERSON> gian hết hạn link"}, "download_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Download Token", "description": "To<PERSON> tải xu<PERSON>ng"}}, "type": "object", "required": ["certificate_id", "download_url", "file_name", "file_format"], "title": "CertificateDownloadInfo", "description": "Certificate download information schema."}, "CertificateDownloadResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/CertificateDownloadInfo"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "CertificateDownloadResponse", "description": "Certificate download response schema."}, "CertificateStatistics": {"properties": {"total_certificates": {"type": "integer", "title": "Total Certificates", "description": "Tổng số chứng chỉ", "default": 0}, "active_certificates": {"type": "integer", "title": "Active Certificates", "description": "<PERSON>ứng chỉ đang hoạt động", "default": 0}, "expired_certificates": {"type": "integer", "title": "Expired Certificates", "description": "<PERSON><PERSON>ng chỉ đã hết hạn", "default": 0}, "pending_certificates": {"type": "integer", "title": "Pending Certificates", "description": "<PERSON>ứng chỉ chờ cấp", "default": 0}, "completion_certificates": {"type": "integer", "title": "Completion Certificates", "description": "<PERSON><PERSON><PERSON> chỉ hoàn thành", "default": 0}, "achievement_certificates": {"type": "integer", "title": "Achievement Certificates", "description": "<PERSON><PERSON>ng chỉ thành tích", "default": 0}, "participation_certificates": {"type": "integer", "title": "Participation Certificates", "description": "<PERSON><PERSON><PERSON> chỉ tham gia", "default": 0}, "certificates_this_month": {"type": "integer", "title": "Certificates This Month", "description": "<PERSON><PERSON><PERSON> chỉ tháng này", "default": 0}, "certificates_this_year": {"type": "integer", "title": "Certificates This Year", "description": "<PERSON><PERSON>ng chỉ năm này", "default": 0}, "total_downloads": {"type": "integer", "title": "Total Downloads", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> t<PERSON>", "default": 0}, "total_verifications": {"type": "integer", "title": "Total Verifications", "description": "<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> x<PERSON>h", "default": 0}, "total_shares": {"type": "integer", "title": "Total Shares", "description": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> chia sẻ", "default": 0}}, "type": "object", "title": "CertificateStatistics", "description": "Certificate statistics schema."}, "CertificateSummaryResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentCertificateSummary"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "CertificateSummaryResponse", "description": "Certificate summary response schema."}, "ChatFeedbackCreate": {"properties": {"message_id": {"type": "string", "title": "Message Id", "description": "ID tin nhắn cần feedback"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": 1.0, "title": "Rating", "description": "<PERSON><PERSON><PERSON> (1-5)"}, "comment": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Comment", "description": "<PERSON><PERSON><PERSON> lu<PERSON>"}}, "type": "object", "required": ["message_id", "rating"], "title": "ChatFeedbackCreate", "description": "<PERSON><PERSON><PERSON> cho vi<PERSON><PERSON> g<PERSON>i feedback"}, "ChatFeedbackData": {"properties": {"feedback_id": {"type": "string", "title": "Feed<PERSON> Id"}, "message_id": {"type": "string", "title": "Message Id"}, "rating": {"type": "integer", "title": "Rating"}, "comment": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Comment"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["feedback_id", "message_id", "rating", "created_at"], "title": "ChatFeedbackData", "description": "<PERSON><PERSON>a cho dữ liệu feedback"}, "ChatFeedbackResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/ChatFeedbackData"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ChatFeedbackResponse", "description": "Schema cho response khi gửi feedback"}, "ChatMessageCreate": {"properties": {"content": {"type": "string", "title": "Content", "description": "<PERSON><PERSON><PERSON> dung tin nhắn"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "Metadata cho tin nhắn"}}, "type": "object", "required": ["content"], "title": "ChatMessageCreate", "description": "<PERSON><PERSON>a cho vi<PERSON>c tạo tin nhắn mới"}, "ChatMessageData": {"properties": {"message_id": {"type": "string", "title": "Message Id"}, "session_id": {"type": "string", "title": "Session Id"}, "content": {"type": "string", "title": "Content"}, "role": {"type": "string", "title": "Role"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["message_id", "session_id", "content", "role", "created_at"], "title": "ChatMessageData", "description": "<PERSON><PERSON>a cho dữ liệu tin nh<PERSON>n"}, "ChatMessageListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/ChatMessageData"}, "type": "array", "title": "Data", "default": []}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ChatMessageListResponse", "description": "<PERSON><PERSON>a cho response khi lấy danh sách tin nhắn"}, "ChatMessageResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/ChatMessageData"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ChatMessageResponse", "description": "<PERSON><PERSON>a cho response khi gửi tin nhắn"}, "ChatSessionCreate": {"properties": {"agent_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Agent Code", "description": "Mã của agent (t<PERSON><PERSON>, sẽ lấy từ cấu hình website nếu không có)"}, "website_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Website Code", "description": "Mã của website (dùng để lấy cấu hình)"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id", "description": "ID phiên chat (t<PERSON><PERSON>, sẽ tự động tạo nếu không có)"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "<PERSON><PERSON>n phiên chat"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> cho phiên chat"}}, "type": "object", "title": "ChatSessionCreate", "description": "<PERSON><PERSON>a cho việc tạo phiên chat mới"}, "ChatSessionData": {"properties": {"session_id": {"type": "string", "title": "Session Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "status": {"type": "string", "title": "Status"}, "metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["session_id", "created_at", "status"], "title": "ChatSessionData", "description": "<PERSON><PERSON>a cho dữ liệu phiên chat"}, "ChatSessionResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/ChatSessionData"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ChatSessionResponse", "description": "<PERSON><PERSON><PERSON> cho response khi tạo phiên chat"}, "ChatbotConfigData": {"properties": {"is_enabled": {"type": "boolean", "title": "Is Enabled"}, "agent_code": {"type": "string", "title": "Agent Code"}, "auto_popup": {"type": "boolean", "title": "Auto Popup"}, "popup_delay": {"type": "integer", "title": "<PERSON><PERSON>"}, "welcome_message": {"type": "string", "title": "Welcome Message"}, "placeholder_text": {"type": "string", "title": "Placeholder Text"}, "button_text": {"type": "string", "title": "Button Text"}, "header_text": {"type": "string", "title": "Header Text"}, "theme_color": {"type": "string", "title": "Theme Color"}}, "type": "object", "required": ["is_enabled", "agent_code", "auto_popup", "popup_delay", "welcome_message", "placeholder_text", "button_text", "header_text", "theme_color"], "title": "ChatbotConfigData", "description": "<PERSON><PERSON>a cho dữ liệu cấu hình chatbot"}, "ChatbotConfigResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/ChatbotConfigData"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ChatbotConfigResponse", "description": "<PERSON><PERSON><PERSON> cho response khi lấy cấu hình chatbot"}, "CheckInRequest": {"properties": {"lesson_id": {"type": "integer", "title": "Lesson Id", "description": "<PERSON> b<PERSON><PERSON> h<PERSON>c"}, "location_lat": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Location Lat", "description": "<PERSON><PERSON>"}, "location_lng": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Location Lng", "description": "<PERSON>nh đ<PERSON>"}, "device_info": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Device Info", "description": "<PERSON><PERSON><PERSON><PERSON> tin thiết bị"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["lesson_id"], "title": "CheckInRequest", "description": "Student check-in request schema."}, "CheckInResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "CheckInResponse", "description": "Check-in response schema."}, "ClassCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "course_id": {"type": "integer", "title": "Course Id", "description": "ID khóa học"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả chi tiết"}, "short_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Short Description", "description": "<PERSON><PERSON> t<PERSON> ng<PERSON>n gọn"}, "start_date": {"type": "string", "format": "date", "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"type": "string", "format": "date", "title": "End Date", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "learning_type": {"type": "string", "title": "Learning Type", "description": "<PERSON><PERSON><PERSON> hình học tập: offline, online, hybrid"}, "expected_location_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Expected Location Id", "description": "ID cơ sở học dự kiến"}, "main_location_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Main Location Id", "description": "ID cơ sở học ch<PERSON>h thức"}, "capacity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Capacity", "description": "<PERSON><PERSON><PERSON> ch<PERSON>a tối đa"}, "min_students": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Min Students", "description": "<PERSON><PERSON> học viên tối thiểu"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "default": true}, "stage_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Stage Id", "description": "ID trạng thái"}}, "type": "object", "required": ["name", "course_id", "start_date", "end_date", "learning_type"], "title": "ClassCreate", "description": "<PERSON><PERSON><PERSON> cho vi<PERSON><PERSON> tạo lớp học mới"}, "ClassCreateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/ClassDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "ClassCreateResponse", "description": "Response cho tạo lớp học mới"}, "ClassDeleteResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ClassDeleteResponse", "description": "Response cho x<PERSON>a lớp h<PERSON>c"}, "ClassDetail": {"properties": {"name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "course_id": {"type": "integer", "title": "Course Id", "description": "ID khóa học"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả chi tiết"}, "short_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Short Description", "description": "<PERSON><PERSON> t<PERSON> ng<PERSON>n gọn"}, "start_date": {"type": "string", "format": "date", "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"type": "string", "format": "date", "title": "End Date", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "learning_type": {"type": "string", "title": "Learning Type", "description": "<PERSON><PERSON><PERSON> hình học tập: offline, online, hybrid"}, "expected_location_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Expected Location Id", "description": "ID cơ sở học dự kiến"}, "main_location_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Main Location Id", "description": "ID cơ sở học ch<PERSON>h thức"}, "capacity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Capacity", "description": "<PERSON><PERSON><PERSON> ch<PERSON>a tối đa"}, "min_students": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Min Students", "description": "<PERSON><PERSON> học viên tối thiểu"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "default": true}, "id": {"type": "integer", "title": "Id", "description": "<PERSON> lớp h<PERSON>c"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "stage_id": {"type": "integer", "title": "Stage Id", "description": "ID trạng thái"}, "stage_name": {"type": "string", "title": "Stage Name", "description": "<PERSON>ên trạng thái"}, "is_draft": {"type": "boolean", "title": "Is Draft", "description": "<PERSON>r<PERSON><PERSON> thái n<PERSON>p"}, "is_recruiting": {"type": "boolean", "title": "Is Recruiting", "description": "<PERSON>r<PERSON><PERSON> thái tuyển sinh"}, "is_ongoing": {"type": "boolean", "title": "Is Ongoing", "description": "<PERSON>r<PERSON><PERSON> thái đang diễn ra"}, "is_completed": {"type": "boolean", "title": "Is Completed", "description": "Tr<PERSON>ng thái đã hoàn thành"}, "is_cancelled": {"type": "boolean", "title": "Is Cancelled", "description": "<PERSON>r<PERSON><PERSON> thái đã hủy"}, "is_closed": {"type": "boolean", "title": "Is Closed", "description": "Tr<PERSON>ng thái đã đóng"}, "current_students": {"type": "integer", "title": "Current Students", "description": "<PERSON><PERSON> học viên hiện tại"}, "subject_count": {"type": "integer", "title": "Subject Count", "description": "Số môn học"}, "expected_location_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Expected Location Name", "description": "<PERSON><PERSON><PERSON> c<PERSON> sở học dự kiến"}, "main_location_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Main Location Name", "description": "<PERSON><PERSON><PERSON> c<PERSON> sở học ch<PERSON>h thức"}, "has_location_changed": {"type": "boolean", "title": "Has Location Changed", "description": "<PERSON><PERSON> thay đổi địa điểm học"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t"}}, "type": "object", "required": ["name", "course_id", "start_date", "end_date", "learning_type", "id", "course_name", "stage_id", "stage_name", "is_draft", "is_recruiting", "is_ongoing", "is_completed", "is_cancelled", "is_closed", "current_students", "subject_count", "has_location_changed"], "title": "ClassDetail", "description": "<PERSON><PERSON><PERSON> chi tiết cho lớp học"}, "ClassDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/ClassDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "ClassDetailResponse", "description": "Response cho chi ti<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "ClassList": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> lớp h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "start_date": {"type": "string", "format": "date", "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"type": "string", "format": "date", "title": "End Date", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "stage_name": {"type": "string", "title": "Stage Name", "description": "<PERSON>ên trạng thái"}, "current_students": {"type": "integer", "title": "Current Students", "description": "<PERSON><PERSON> học viên hiện tại"}}, "type": "object", "required": ["id", "name", "code", "course_name", "start_date", "end_date", "stage_name", "current_students"], "title": "ClassList", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch lớ<PERSON> h<PERSON>c"}, "ClassListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/ClassList"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "ClassListResponse", "description": "Response cho danh s<PERSON>ch lớp h<PERSON>c"}, "ClassStageUpdate": {"properties": {"action": {"type": "string", "title": "Action", "description": "<PERSON><PERSON><PERSON> động: draft, recruiting, ongoing, completed, cancelled"}}, "type": "object", "required": ["action"], "title": "ClassStageUpdate", "description": "<PERSON><PERSON><PERSON> cho vi<PERSON><PERSON> cập nhật trạng thái lớp học"}, "ClassStageUpdateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/ClassDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "ClassStageUpdateResponse", "description": "Response cho cập nhật trạng thái lớp học"}, "ClassStatistics": {"properties": {"total_students": {"type": "integer", "title": "Total Students", "description": "Tổng số học viên", "default": 0}, "active_students": {"type": "integer", "title": "Active Students", "description": "<PERSON><PERSON><PERSON> viên đang học", "default": 0}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "Tổng số bài học", "default": 0}, "completed_lessons": {"type": "integer", "title": "Completed Lessons", "description": "<PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "upcoming_lessons": {"type": "integer", "title": "Upcoming Lessons", "description": "<PERSON><PERSON><PERSON> h<PERSON> sắp tới", "default": 0}, "average_attendance": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Attendance", "description": "<PERSON><PERSON><PERSON><PERSON> danh trung bình (%)"}, "average_progress": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Progress", "description": "Tiến độ trung bình (%)"}, "completion_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Completion Rate", "description": "Tỷ lệ hoàn thành (%)"}}, "type": "object", "title": "ClassStatistics", "description": "Class statistics schema."}, "ClassUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả chi tiết"}, "short_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Short Description", "description": "<PERSON><PERSON> t<PERSON> ng<PERSON>n gọn"}, "start_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "End Date", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "learning_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Learning Type", "description": "<PERSON><PERSON><PERSON> hình học tập: offline, online, hybrid"}, "expected_location_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Expected Location Id", "description": "ID cơ sở học dự kiến"}, "main_location_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Main Location Id", "description": "ID cơ sở học ch<PERSON>h thức"}, "capacity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Capacity", "description": "<PERSON><PERSON><PERSON> ch<PERSON>a tối đa"}, "min_students": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Min Students", "description": "<PERSON><PERSON> học viên tối thiểu"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động"}, "stage_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Stage Id", "description": "ID trạng thái"}}, "type": "object", "title": "ClassUpdate", "description": "<PERSON><PERSON><PERSON> cho vi<PERSON><PERSON> cập nh<PERSON>t lớp học"}, "ClassUpdateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/ClassDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "ClassUpdateResponse", "description": "Response cho cập nh<PERSON>t lớp học"}, "ComparisonData": {"properties": {"metric_name": {"type": "string", "title": "Metric Name", "description": "Tên chỉ số"}, "student_value": {"type": "number", "title": "Student Value", "description": "<PERSON><PERSON><PERSON> tr<PERSON> của học viên"}, "class_average": {"type": "number", "title": "Class Average", "description": "<PERSON>rung bình lớp"}, "course_average": {"type": "number", "title": "Course Average", "description": "Trung bình kh<PERSON>a học"}, "percentile_rank": {"type": "number", "title": "Percentile Rank", "description": "<PERSON><PERSON><PERSON> hạng phần trăm"}, "above_average": {"type": "boolean", "title": "Above Average", "description": "<PERSON>r<PERSON><PERSON> trung bình", "default": false}, "performance_level": {"type": "string", "title": "Performance Level", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON> hiệu su<PERSON>"}, "improvement_suggestion": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Improvement Suggestion", "description": "<PERSON><PERSON> xuất cải thi<PERSON>n"}}, "type": "object", "required": ["metric_name", "student_value", "class_average", "course_average", "percentile_rank", "performance_level"], "title": "ComparisonData", "description": "Comparison data schema."}, "ContactInfo": {"properties": {"phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "Địa chỉ"}, "emergency_contact": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergency Contact", "description": "<PERSON><PERSON><PERSON> h<PERSON> khẩn cấp"}, "emergency_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergency Phone", "description": "SĐT k<PERSON><PERSON>n cấp"}}, "type": "object", "title": "ContactInfo", "description": "Contact information schema."}, "CourseBase": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "CourseBase", "description": "<PERSON><PERSON><PERSON> c<PERSON> bản cho danh sách kh<PERSON>a học"}, "CourseBasicInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID khóa học"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "code": {"type": "string", "title": "Code", "description": "Mã khóa học"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> t<PERSON> kh<PERSON>a học"}, "level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Level", "description": "<PERSON><PERSON><PERSON>"}, "duration_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> (giờ)"}, "subject_name": {"type": "string", "title": "Subject Name", "description": "<PERSON><PERSON><PERSON> môn h<PERSON>c"}}, "type": "object", "required": ["id", "name", "code", "subject_name"], "title": "CourseBasicInfo", "description": "Basic course information schema."}, "CourseDetail": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "CourseDetail", "description": "<PERSON><PERSON><PERSON> chi tiết cho kh<PERSON>a học"}, "DaySchedule": {"properties": {"date": {"type": "string", "format": "date", "title": "Date", "description": "<PERSON><PERSON><PERSON>"}, "day_of_week": {"type": "string", "title": "Day Of Week", "description": "<PERSON><PERSON><PERSON> trong tuần"}, "is_working_day": {"type": "boolean", "title": "Is Working Day", "description": "<PERSON><PERSON><PERSON> l<PERSON> vi<PERSON>c", "default": true}, "items": {"items": {"$ref": "#/components/schemas/ScheduleItem"}, "type": "array", "title": "Items", "description": "<PERSON><PERSON><PERSON> l<PERSON>ch"}, "total_hours": {"type": "number", "title": "Total Hours", "description": "Tổng giờ làm việc", "default": 0.0}, "teaching_hours": {"type": "number", "title": "Teaching Hours", "description": "G<PERSON>ờ giảng dạy", "default": 0.0}, "break_hours": {"type": "number", "title": "Break Hours", "description": "Giờ nghỉ", "default": 0.0}, "is_overloaded": {"type": "boolean", "title": "Is Overloaded", "description": "<PERSON><PERSON><PERSON>", "default": false}, "has_conflicts": {"type": "boolean", "title": "Has Conflicts", "description": "<PERSON><PERSON>ung đột", "default": false}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["date", "day_of_week"], "title": "DaySchedule", "description": "Daily schedule schema."}, "DayScheduleResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/DaySchedule"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "DayScheduleResponse", "description": "Daily schedule response schema."}, "DeviceInfo": {"properties": {"device_id": {"type": "string", "title": "<PERSON>ce Id", "description": "Unique device identifier"}, "device_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Device Name", "description": "Device name"}, "device_type": {"type": "string", "title": "Device Type", "description": "Device type (mobile, tablet, desktop)"}, "os_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Os Name", "description": "Operating system name"}, "os_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Os Version", "description": "Operating system version"}, "app_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "App Version", "description": "Application version"}, "browser_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Browser Name", "description": "Browser name"}, "browser_version": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Browser Version", "description": "Browser version"}, "user_agent": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Agent", "description": "User agent string"}, "ip_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ip Address", "description": "IP address"}, "location": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Location", "description": "Location data"}}, "type": "object", "required": ["device_id", "device_type"], "title": "DeviceInfo", "description": "Device information schema."}, "DeviceRegistrationRequest": {"properties": {"device_info": {"$ref": "#/components/schemas/DeviceInfo", "description": "Device information"}, "push_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "Push notification token"}}, "type": "object", "required": ["device_info"], "title": "DeviceRegistrationRequest", "description": "Device registration request."}, "DeviceRegistrationResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "DeviceRegistrationResponse", "description": "Device registration response."}, "DocumentBasicInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID tài liệu"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> tài li<PERSON>u"}, "file_name": {"type": "string", "title": "File Name", "description": "Tên file"}, "file_type": {"type": "string", "title": "File Type", "description": "Loại file"}, "file_size": {"type": "integer", "title": "File Size", "description": "<PERSON><PERSON><PERSON> th<PERSON> file (bytes)"}, "category_id": {"type": "integer", "title": "Category Id", "description": "<PERSON> danh mục"}, "category_name": {"type": "string", "title": "Category Name", "description": "<PERSON><PERSON><PERSON> da<PERSON> mục"}, "status": {"type": "string", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "is_verified": {"type": "boolean", "title": "Is Verified", "description": "Đ<PERSON> x<PERSON>c <PERSON>h", "default": false}, "is_public": {"type": "boolean", "title": "Is Public", "description": "<PERSON><PERSON><PERSON> khai", "default": false}, "upload_date": {"type": "string", "format": "date-time", "title": "Upload Date", "description": "<PERSON><PERSON><PERSON> l<PERSON>n"}, "verified_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Verified Date", "description": "<PERSON><PERSON><PERSON>h"}, "download_count": {"type": "integer", "title": "Download Count", "description": "<PERSON><PERSON> l<PERSON> t<PERSON>", "default": 0}, "view_count": {"type": "integer", "title": "View Count", "description": "S<PERSON> l<PERSON> xem", "default": 0}}, "type": "object", "required": ["id", "name", "file_name", "file_type", "file_size", "category_id", "category_name", "status", "upload_date"], "title": "DocumentBasicInfo", "description": "Basic document information schema."}, "DocumentDetail": {"properties": {"created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t"}, "id": {"type": "integer", "title": "Id", "description": "ID tài liệu"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> tài li<PERSON>u"}, "file_name": {"type": "string", "title": "File Name", "description": "Tên file"}, "file_type": {"type": "string", "title": "File Type", "description": "Loại file"}, "file_size": {"type": "integer", "title": "File Size", "description": "<PERSON><PERSON><PERSON> th<PERSON> file (bytes)"}, "category_id": {"type": "integer", "title": "Category Id", "description": "<PERSON> danh mục"}, "category_name": {"type": "string", "title": "Category Name", "description": "<PERSON><PERSON><PERSON> da<PERSON> mục"}, "status": {"type": "string", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "is_verified": {"type": "boolean", "title": "Is Verified", "description": "Đ<PERSON> x<PERSON>c <PERSON>h", "default": false}, "is_public": {"type": "boolean", "title": "Is Public", "description": "<PERSON><PERSON><PERSON> khai", "default": false}, "upload_date": {"type": "string", "format": "date-time", "title": "Upload Date", "description": "<PERSON><PERSON><PERSON> l<PERSON>n"}, "verified_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Verified Date", "description": "<PERSON><PERSON><PERSON>h"}, "download_count": {"type": "integer", "title": "Download Count", "description": "<PERSON><PERSON> l<PERSON> t<PERSON>", "default": 0}, "view_count": {"type": "integer", "title": "View Count", "description": "S<PERSON> l<PERSON> xem", "default": 0}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả tài liệu"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "Thẻ tag"}, "version": {"type": "string", "title": "Version", "description": "<PERSON><PERSON><PERSON>", "default": "1.0"}, "mime_type": {"type": "string", "title": "Mime Type", "description": "MIME type"}, "file_hash": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "File Hash", "description": "Hash của file"}, "thumbnail_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "URL thumbnail"}, "verified_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verified By", "description": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>h"}, "verification_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verification Notes", "description": "<PERSON><PERSON> chú xác minh"}, "rejection_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Rejection Reason", "description": "<PERSON>ý do từ chối"}, "view_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "View Url", "description": "URL xem"}, "download_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Download Url", "description": "URL tải xuống"}, "share_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Share Url", "description": "URL chia sẻ"}, "can_edit": {"type": "boolean", "title": "Can Edit", "description": "<PERSON><PERSON> thể chỉnh sửa", "default": true}, "can_delete": {"type": "boolean", "title": "Can Delete", "description": "<PERSON><PERSON> thể xóa", "default": true}, "can_share": {"type": "boolean", "title": "Can Share", "description": "<PERSON><PERSON> thể chia sẻ", "default": true}, "course_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Course Id", "description": "ID khóa học liên quan"}, "course_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Course Name", "description": "<PERSON><PERSON><PERSON> kh<PERSON><PERSON> học liên quan"}, "assignment_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Assignment Id", "description": "<PERSON> bài tập liên quan"}, "assignment_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assignment Name", "description": "<PERSON><PERSON><PERSON> bài tập liên quan"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn"}, "is_expired": {"type": "boolean", "title": "Is Expired", "description": "<PERSON><PERSON> hết hạn", "default": false}}, "type": "object", "required": ["id", "name", "file_name", "file_type", "file_size", "category_id", "category_name", "status", "upload_date", "mime_type"], "title": "DocumentDetail", "description": "Detailed document information schema."}, "DocumentUploadRequest": {"properties": {"name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> tài li<PERSON>u"}, "category_id": {"type": "integer", "title": "Category Id", "description": "<PERSON> danh mục"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON>"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "Thẻ tag"}, "is_public": {"type": "boolean", "title": "Is Public", "description": "<PERSON><PERSON><PERSON> khai", "default": false}, "course_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Course Id", "description": "ID khóa học liên quan"}, "assignment_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Assignment Id", "description": "<PERSON> bài tập liên quan"}, "expires_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expires At", "description": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn"}}, "type": "object", "required": ["name", "category_id"], "title": "DocumentUploadRequest", "description": "Document upload request schema."}, "DocumentUploadResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/DocumentDetail"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "DocumentUploadResponse", "description": "Document upload response schema."}, "EmploymentInfo": {"properties": {"employment_type": {"type": "string", "title": "Employment Type", "description": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng"}, "start_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "contract_end_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Contract End Date", "description": "<PERSON><PERSON><PERSON> kết thúc hợp đồng"}, "salary_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Salary Level", "description": "<PERSON><PERSON><PERSON>"}, "department": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Department", "description": "Phòng ban"}, "position": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Position", "description": "<PERSON><PERSON><PERSON> v<PERSON>"}}, "type": "object", "required": ["employment_type"], "title": "EmploymentInfo", "description": "Employment information schema."}, "EnhancedTokenResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "EnhancedTokenResponse", "description": "Enhanced token response with additional information."}, "EnrollmentData": {"properties": {"enrollment_id": {"type": "integer", "title": "Enrollment Id", "description": "ID đăng ký khóa học"}, "enrollment_number": {"type": "string", "title": "Enrollment Number", "description": "<PERSON>ã đăng ký"}, "course_info": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__CourseInfo", "description": "Thông tin khóa học"}, "student_info": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__StudentInfo", "description": "<PERSON>h<PERSON><PERSON> tin học viên"}, "order_info": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__OrderInfo", "description": "<PERSON><PERSON><PERSON><PERSON> tin đơn hàng"}, "invoice_info": {"anyOf": [{"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__InvoiceInfo"}, {"type": "null"}], "description": "<PERSON>h<PERSON><PERSON> tin hóa đơn"}, "payment_info": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__PaymentInfo", "description": "Thông tin thanh toán"}}, "type": "object", "required": ["enrollment_id", "enrollment_number", "course_info", "student_info", "order_info", "payment_info"], "title": "EnrollmentData"}, "EnrollmentInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID đăng ký"}, "class_id": {"type": "integer", "title": "Class Id", "description": "<PERSON> lớp h<PERSON>c"}, "class_name": {"type": "string", "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "class_code": {"type": "string", "title": "Class Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "enrollment_date": {"type": "string", "format": "date", "title": "Enrollment Date", "description": "<PERSON><PERSON><PERSON> ký"}, "start_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "End Date", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "status": {"type": "string", "title": "Status", "description": "<PERSON>r<PERSON>ng thái đăng ký"}, "payment_status": {"type": "string", "title": "Payment Status", "description": "<PERSON>r<PERSON><PERSON> thái thanh toán"}, "progress_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Progress Percentage", "description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> (%)"}}, "type": "object", "required": ["id", "class_id", "class_name", "class_code", "course_name", "enrollment_date", "status", "payment_status"], "title": "EnrollmentInfo", "description": "Enrollment information schema."}, "EnrollmentRequest": {"properties": {"class_id": {"type": "integer", "title": "Class Id", "description": "<PERSON> lớp h<PERSON>c"}, "student_name": {"type": "string", "title": "Student Name", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên"}, "student_phone": {"type": "string", "title": "Student Phone", "description": "<PERSON><PERSON> điện tho<PERSON><PERSON> học viên"}, "student_email": {"type": "string", "title": "Student Email", "description": "<PERSON><PERSON> vi<PERSON>n", "default": ""}, "notes": {"type": "string", "title": "Notes", "description": "<PERSON><PERSON><PERSON>", "default": ""}}, "type": "object", "required": ["class_id", "student_name", "student_phone"], "title": "EnrollmentRequest", "description": "Enrollment request model.", "example": {"class_id": 1, "notes": "Đăng ký từ website", "student_email": "<EMAIL>", "student_name": "<PERSON><PERSON><PERSON><PERSON>", "student_phone": "0123456789"}}, "FileModel": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "access_token": {"type": "string", "title": "Access Token"}, "type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}, "size": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Size"}, "url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Url", "description": "Direct URL to access the file info/preview."}, "download_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Download Url", "description": "URL to force download the file."}, "preview_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preview Url", "description": "URL to attempt previewing the file (if supported)."}}, "type": "object", "required": ["id", "name", "access_token"], "title": "FileModel", "description": "Schema representing a file attachment"}, "FileResponseSchema": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/FileModel"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "FileResponseSchema", "description": "Response schema for file operations"}, "GoalProgress": {"properties": {"goal_id": {"type": "integer", "title": "Goal Id", "description": "<PERSON> mục tiêu"}, "goal_title": {"type": "string", "title": "Goal Title", "description": "Tiê<PERSON> đề mục tiêu"}, "goal_type": {"type": "string", "title": "Goal Type", "description": "<PERSON><PERSON><PERSON> mục tiêu"}, "target_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Target Date", "description": "<PERSON><PERSON><PERSON> ti<PERSON>u"}, "progress_percentage": {"type": "number", "title": "Progress Percentage", "description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> (%)", "default": 0.0}, "current_value": {"type": "number", "title": "Current Value", "description": "<PERSON><PERSON><PERSON> trị hiện tại", "default": 0.0}, "target_value": {"type": "number", "title": "Target Value", "description": "<PERSON><PERSON><PERSON> trị mục tiêu"}, "status": {"type": "string", "title": "Status", "description": "<PERSON>r<PERSON><PERSON> thái mục tiêu"}, "is_achievable": {"type": "boolean", "title": "Is Achievable", "description": "<PERSON><PERSON> thể đạt đ<PERSON>", "default": true}, "days_remaining": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Days Remaining", "description": "<PERSON><PERSON> ngày còn lại"}, "milestones": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Milestones", "description": "<PERSON><PERSON><PERSON> c<PERSON> mốc"}, "completed_milestones": {"type": "integer", "title": "Completed Milestones", "description": "<PERSON><PERSON><PERSON> mốc đã hoàn thành", "default": 0}, "total_milestones": {"type": "integer", "title": "Total Milestones", "description": "<PERSON><PERSON><PERSON> c<PERSON>t mốc", "default": 0}}, "type": "object", "required": ["goal_id", "goal_title", "goal_type", "target_value", "status"], "title": "GoalProgress", "description": "Goal progress schema."}, "GoalProgressResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/GoalProgress"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "GoalProgressResponse", "description": "Goal progress response schema."}, "GradeBreakdownSchema": {"properties": {"type": {"type": "string", "title": "Type", "description": "Loại đ<PERSON>h giá"}, "count": {"type": "integer", "title": "Count", "description": "Số lượng đ<PERSON>h giá"}, "total_score": {"type": "number", "title": "Total Score", "description": "<PERSON><PERSON><PERSON> điểm"}, "average_score": {"type": "number", "title": "Average Score", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình"}, "passed_count": {"type": "integer", "title": "Passed Count", "description": "Số đánh giá đạt"}, "pass_rate": {"type": "number", "title": "Pass Rate", "description": "Tỷ lệ đạt (%)"}}, "type": "object", "required": ["type", "count", "total_score", "average_score", "passed_count", "pass_rate"], "title": "GradeBreakdownSchema", "description": "<PERSON><PERSON>a cho phân tích điểm theo loại đánh giá"}, "GradeSummarySchema": {"properties": {"total_assessments": {"type": "integer", "title": "Total Assessments", "description": "Tổng số đánh giá"}, "completed_assessments": {"type": "integer", "title": "Completed Assessments", "description": "S<PERSON> đánh giá đã hoàn thành"}, "passed_assessments": {"type": "integer", "title": "Passed Assessments", "description": "Số đánh giá đã đạt"}, "current_grade": {"type": "number", "title": "Current Grade", "description": "<PERSON><PERSON><PERSON><PERSON> hiện tại"}, "average_score": {"type": "number", "title": "Average Score", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình"}, "pass_rate": {"type": "number", "title": "Pass Rate", "description": "Tỷ lệ đạt (%)"}, "grade_breakdown": {"items": {"$ref": "#/components/schemas/GradeBreakdownSchema"}, "type": "array", "title": "Grade Breakdown", "description": "<PERSON><PERSON> tích theo loại đ<PERSON>h giá"}}, "type": "object", "required": ["total_assessments", "completed_assessments", "passed_assessments", "current_grade", "average_score", "pass_rate", "grade_breakdown"], "title": "GradeSummarySchema", "description": "<PERSON><PERSON><PERSON> cho tóm tắt điểm số của học viên"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "InstructorAvailability": {"properties": {"instructor_id": {"type": "integer", "title": "Inst<PERSON>ctor Id", "description": "ID giảng viên"}, "weekly_slots": {"items": {"$ref": "#/components/schemas/AvailabilitySlot"}, "type": "array", "title": "Weekly Slots", "description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> hàng tu<PERSON>n"}, "max_hours_per_day": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Hours Per Day", "description": "Tối đa giờ/ngày"}, "max_hours_per_week": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Hours Per Week", "description": "T<PERSON>i đa giờ/tuần"}, "min_break_between_lessons": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Min Break Between Lessons", "description": "Nghỉ tối thiểu gi<PERSON> bài (phút)"}, "preferred_rooms": {"items": {"type": "string"}, "type": "array", "title": "Preferred Rooms", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON> th<PERSON>ch"}, "avoid_back_to_back": {"type": "boolean", "title": "Avoid Back To Back", "description": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>y liên ti<PERSON>", "default": false}, "unavailable_dates": {"items": {"type": "string", "format": "date"}, "type": "array", "title": "Unavailable Dates", "description": "<PERSON><PERSON><PERSON> không có mặt"}, "special_availability": {"additionalProperties": {"items": {"$ref": "#/components/schemas/TimeSlot"}, "type": "array"}, "type": "object", "title": "Special Availability", "description": "<PERSON><PERSON><PERSON> đặc biệt"}}, "type": "object", "required": ["instructor_id", "weekly_slots"], "title": "InstructorAvailability", "description": "Instructor availability schema."}, "InstructorBasicInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID giảng viên"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> gi<PERSON>ng viên"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "<PERSON><PERSON><PERSON> danh"}, "bio": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bio", "description": "<PERSON><PERSON><PERSON><PERSON> sử"}, "experience_years": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Experience Years", "description": "<PERSON><PERSON> năm kinh nghiệm"}, "specializations": {"items": {"type": "string"}, "type": "array", "title": "Specializations", "description": "<PERSON><PERSON><PERSON><PERSON> môn"}}, "type": "object", "required": ["id", "name"], "title": "InstructorBasicInfo", "description": "Basic instructor information for students."}, "InstructorClass": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> lớp h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "state": {"type": "string", "title": "State", "description": "<PERSON><PERSON><PERSON><PERSON> thái lớ<PERSON> h<PERSON>c"}, "active": {"type": "boolean", "title": "Active", "description": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "course": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__instructors__course_schemas__CourseInfo", "description": "Thông tin khóa học"}, "subject": {"$ref": "#/components/schemas/SubjectInfo", "description": "Thông tin môn học"}, "start_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "End Date", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "schedules": {"items": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__instructors__course_schemas__ClassScheduleInfo"}, "type": "array", "title": "Schedules", "description": "<PERSON><PERSON><PERSON>"}, "is_primary_instructor": {"type": "boolean", "title": "Is Primary Instructor", "description": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON><PERSON>"}, "role": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Role", "description": "<PERSON>ai trò trong lớp"}, "students": {"items": {"$ref": "#/components/schemas/StudentSummary"}, "type": "array", "title": "Students", "description": "<PERSON><PERSON> s<PERSON>ch họ<PERSON> viên"}, "statistics": {"$ref": "#/components/schemas/ClassStatistics", "description": "<PERSON><PERSON><PERSON><PERSON> kê lớp học"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "<PERSON><PERSON><PERSON>"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}}, "type": "object", "required": ["id", "name", "code", "state", "active", "course", "subject", "is_primary_instructor", "statistics"], "title": "InstructorClass", "description": "Instructor class schema with full details."}, "InstructorClassDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/InstructorClass"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "InstructorClassDetailResponse", "description": "Instructor class detail response schema."}, "InstructorClassListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "<PERSON>r<PERSON><PERSON> thái thành công", "default": true}, "message": {"type": "string", "title": "Message", "description": "<PERSON><PERSON><PERSON><PERSON> báo", "default": "<PERSON><PERSON> liệu đ<PERSON><PERSON><PERSON> tải thành công"}, "data": {"items": {"$ref": "#/components/schemas/InstructorClassSummary"}, "type": "array", "title": "Data", "description": "<PERSON><PERSON> liệu"}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata", "description": "Thông tin phân trang"}}, "type": "object", "required": ["data", "pagination"], "title": "InstructorClassListResponse", "description": "Instructor class list response schema."}, "InstructorClassSummary": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> lớp h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "state": {"type": "string", "title": "State", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "subject_name": {"type": "string", "title": "Subject Name", "description": "<PERSON><PERSON><PERSON> môn h<PERSON>c"}, "total_students": {"type": "integer", "title": "Total Students", "description": "<PERSON><PERSON> học viên", "default": 0}, "completed_lessons": {"type": "integer", "title": "Completed Lessons", "description": "<PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> h<PERSON>c", "default": 0}, "next_lesson_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Lesson Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> ti<PERSON> theo"}, "schedule_summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Schedule Summary", "description": "<PERSON><PERSON><PERSON> t<PERSON> lị<PERSON> h<PERSON>c"}, "is_primary_instructor": {"type": "boolean", "title": "Is Primary Instructor", "description": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON><PERSON>"}, "needs_attention": {"type": "boolean", "title": "Needs Attention", "description": "<PERSON><PERSON><PERSON> ch<PERSON>", "default": false}, "attention_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Attention Reason", "description": "Lý do cần chú <PERSON>"}}, "type": "object", "required": ["id", "name", "code", "state", "course_name", "subject_name", "is_primary_instructor"], "title": "InstructorClassSummary", "description": "Instructor class summary for list view."}, "InstructorDashboardResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/InstructorDashboardStats"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "InstructorDashboardResponse", "description": "Instructor dashboard response schema."}, "InstructorDashboardStats": {"properties": {"total_classes": {"type": "integer", "title": "Total Classes", "description": "Tổng số lớp", "default": 0}, "active_classes": {"type": "integer", "title": "Active Classes", "description": "<PERSON><PERSON><PERSON> đang d<PERSON>", "default": 0}, "completed_classes": {"type": "integer", "title": "Completed Classes", "description": "<PERSON><PERSON><PERSON> đã hoàn thành", "default": 0}, "total_students": {"type": "integer", "title": "Total Students", "description": "Tổng số học viên", "default": 0}, "active_students": {"type": "integer", "title": "Active Students", "description": "<PERSON><PERSON><PERSON> viên đang học", "default": 0}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> h<PERSON>c", "default": 0}, "completed_lessons": {"type": "integer", "title": "Completed Lessons", "description": "<PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "upcoming_lessons": {"type": "integer", "title": "Upcoming Lessons", "description": "<PERSON><PERSON><PERSON> h<PERSON> sắp tới", "default": 0}, "lessons_today": {"type": "integer", "title": "Lessons Today", "description": "<PERSON><PERSON><PERSON> h<PERSON> hôm nay", "default": 0}, "average_attendance": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Attendance", "description": "<PERSON><PERSON><PERSON><PERSON> danh trung bình (%)"}, "average_rating": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Rating", "description": "<PERSON><PERSON><PERSON> giá trung bình"}, "completion_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Completion Rate", "description": "Tỷ lệ hoàn thành (%)"}, "total_teaching_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Teaching Hours", "description": "Tổng giờ dạy"}, "this_month_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "This Month Hours", "description": "G<PERSON><PERSON> dạy tháng này"}, "this_week_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "This Week Hours", "description": "G<PERSON>ờ dạy tu<PERSON>n này"}}, "type": "object", "title": "InstructorDashboardStats", "description": "Instructor dashboard statistics schema."}, "InstructorLesson": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> b<PERSON><PERSON> h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "lesson_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lesson Number", "description": "<PERSON><PERSON> thứ tự"}, "state": {"type": "string", "title": "State", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "active": {"type": "boolean", "title": "Active", "description": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "class_id": {"type": "integer", "title": "Class Id", "description": "<PERSON> lớp h<PERSON>c"}, "class_name": {"type": "string", "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "class_code": {"type": "string", "title": "Class Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "start_datetime": {"type": "string", "format": "date-time", "title": "Start Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "end_datetime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc"}, "duration_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "room": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}, "content": {"$ref": "#/components/schemas/LessonContent", "description": "<PERSON><PERSON><PERSON> dung bài học"}, "attendance_records": {"items": {"$ref": "#/components/schemas/AttendanceRecord"}, "type": "array", "title": "Attendance Records", "description": "<PERSON><PERSON><PERSON><PERSON>nh"}, "attendance_statistics": {"$ref": "#/components/schemas/LessonStatistics", "description": "<PERSON><PERSON><PERSON><PERSON> kê điểm danh"}, "materials": {"items": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__instructors__lesson_schemas__LessonMaterial"}, "type": "array", "title": "Materials", "description": "<PERSON><PERSON><PERSON> l<PERSON>"}, "can_start": {"type": "boolean", "title": "Can Start", "description": "<PERSON><PERSON> thể bắt đầu", "default": false}, "can_complete": {"type": "boolean", "title": "Can Complete", "description": "<PERSON><PERSON> thể hoàn thành", "default": false}, "is_overdue": {"type": "boolean", "title": "Is Overdue", "description": "<PERSON><PERSON><PERSON> h<PERSON>n", "default": false}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "<PERSON><PERSON><PERSON>"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}}, "type": "object", "required": ["id", "name", "state", "active", "class_id", "class_name", "class_code", "start_datetime", "content", "attendance_statistics"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Complete instructor lesson schema."}, "InstructorPerformanceAnalytics": {"properties": {"instructor_id": {"type": "integer", "title": "Inst<PERSON>ctor Id", "description": "ID giảng viên"}, "instructor_name": {"type": "string", "title": "Instructor Name", "description": "<PERSON><PERSON><PERSON> gi<PERSON>ng viên"}, "analysis_period": {"type": "string", "title": "Analysis Period", "description": "<PERSON><PERSON> phân tích"}, "generated_at": {"type": "string", "format": "date-time", "title": "Generated At", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo"}, "total_classes_taught": {"type": "integer", "title": "Total Classes Taught", "description": "Tổng số lớp đã dạy", "default": 0}, "total_students_taught": {"type": "integer", "title": "Total Students Taught", "description": "Tổng số học viên đã dạy", "default": 0}, "total_teaching_hours": {"type": "number", "title": "Total Teaching Hours", "description": "Tổng số giờ dạy", "default": 0.0}, "average_class_size": {"type": "number", "title": "Average Class Size", "description": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> lớp trung bình", "default": 0.0}, "average_student_grade": {"type": "number", "title": "Average Student Grade", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình học viên", "default": 0.0}, "student_pass_rate": {"type": "number", "title": "Student Pass Rate", "description": "Tỷ lệ đậu của học viên", "default": 0.0}, "student_attendance_rate": {"type": "number", "title": "Student Attendance Rate", "description": "Tỷ lệ điểm danh học viên", "default": 0.0}, "student_completion_rate": {"type": "number", "title": "Student Completion Rate", "description": "Tỷ lệ hoàn thành khóa học", "default": 0.0}, "average_rating": {"type": "number", "title": "Average Rating", "description": "<PERSON><PERSON><PERSON> giá trung bình", "default": 0.0}, "total_feedback_count": {"type": "integer", "title": "Total Feedback Count", "description": "Tổng số phản hồi", "default": 0}, "positive_feedback_rate": {"type": "number", "title": "Positive Feedback Rate", "description": "Tỷ lệ phản hồi tích cực", "default": 0.0}, "response_rate": {"type": "number", "title": "Response Rate", "description": "Tỷ lệ phản hồi", "default": 0.0}, "lesson_completion_rate": {"type": "number", "title": "Lesson Completion Rate", "description": "Tỷ lệ hoàn thành bài học", "default": 0.0}, "assignment_submission_rate": {"type": "number", "title": "Assignment Submission Rate", "description": "Tỷ lệ nộp bài tập", "default": 0.0}, "student_participation_score": {"type": "number", "title": "Student Participation Score", "description": "<PERSON><PERSON><PERSON><PERSON> tham gia học viên", "default": 0.0}, "performance_vs_average": {"additionalProperties": {"type": "number"}, "type": "object", "title": "Performance Vs Average", "description": "So sánh với trung bình"}, "ranking_among_peers": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "Ranking Among Peers", "description": "<PERSON><PERSON><PERSON> hạng trong đồng nghi<PERSON>"}, "monthly_trends": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Monthly Trends", "description": "xu h<PERSON><PERSON>ng hàng tháng"}, "improvement_areas": {"items": {"type": "string"}, "type": "array", "title": "Improvement Areas", "description": "<PERSON><PERSON> v<PERSON><PERSON> c<PERSON>n c<PERSON>i thi<PERSON>n"}, "strengths": {"items": {"type": "string"}, "type": "array", "title": "Strengths", "description": "<PERSON><PERSON><PERSON><PERSON> mạnh"}, "recommendations": {"items": {"type": "string"}, "type": "array", "title": "Recommendations", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị"}, "action_items": {"items": {"type": "string"}, "type": "array", "title": "Action Items", "description": "<PERSON><PERSON><PERSON> động cần thực hiện"}}, "type": "object", "required": ["instructor_id", "instructor_name", "analysis_period"], "title": "InstructorPerformanceAnalytics", "description": "Instructor performance analytics data model."}, "InstructorProfile": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID giảng viên"}, "code": {"type": "string", "title": "Code", "description": "Mã giảng viên"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> gi<PERSON>ng viên"}, "status": {"type": "string", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "active": {"type": "boolean", "title": "Active", "description": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "user_id": {"type": "integer", "title": "User Id", "description": "ID người dùng"}, "user_name": {"type": "string", "title": "User Name", "description": "<PERSON><PERSON><PERSON> đ<PERSON>p"}, "birth_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birth Date", "description": "<PERSON><PERSON><PERSON>"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gender", "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h"}, "id_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id Number", "description": "Số CMND/CCCD"}, "tax_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tax Code", "description": "<PERSON><PERSON> số thuế"}, "contact_info": {"$ref": "#/components/schemas/ContactInfo", "description": "<PERSON>h<PERSON>ng tin liên hệ"}, "employment_info": {"$ref": "#/components/schemas/EmploymentInfo", "description": "Thông tin công việc"}, "skills": {"items": {"$ref": "#/components/schemas/SkillInfo"}, "type": "array", "title": "Skills", "description": "<PERSON><PERSON> n<PERSON>ng"}, "qualifications": {"items": {"$ref": "#/components/schemas/QualificationInfo"}, "type": "array", "title": "Qualifications", "description": "Bằng cấp"}, "statistics": {"$ref": "#/components/schemas/InstructorStatistics", "description": "<PERSON><PERSON><PERSON><PERSON> kê"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "<PERSON><PERSON><PERSON>"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}}, "type": "object", "required": ["id", "code", "name", "status", "active", "user_id", "user_name", "contact_info", "employment_info", "statistics"], "title": "InstructorProfile", "description": "Complete instructor profile schema."}, "InstructorProfileResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/InstructorProfile"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "InstructorProfileResponse", "description": "Instructor profile response schema."}, "InstructorProfileUpdateRequest": {"properties": {"birth_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birth Date", "description": "<PERSON><PERSON><PERSON>"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gender", "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "Địa chỉ"}, "emergency_contact": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergency Contact", "description": "<PERSON><PERSON><PERSON> h<PERSON> khẩn cấp"}, "emergency_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergency Phone", "description": "SĐT k<PERSON><PERSON>n cấp"}, "bio": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Bio", "description": "<PERSON><PERSON><PERSON><PERSON> sử nghề nghiệp"}, "teaching_philosophy": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Teaching Philosophy", "description": "<PERSON><PERSON><PERSON> lý g<PERSON> d<PERSON>"}}, "type": "object", "title": "InstructorProfileUpdateRequest", "description": "Instructor profile update request schema."}, "InstructorProfileUpdateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/InstructorProfile"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "InstructorProfileUpdateResponse", "description": "Instructor profile update response schema."}, "InstructorQualificationsResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/QualificationInfo"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "InstructorQualificationsResponse", "description": "Instructor qualifications response schema."}, "InstructorSkillsResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/SkillInfo"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "InstructorSkillsResponse", "description": "Instructor skills response schema."}, "InstructorStatistics": {"properties": {"total_courses": {"type": "integer", "title": "Total Courses", "description": "Tổng số khóa học", "default": 0}, "active_courses": {"type": "integer", "title": "Active Courses", "description": "<PERSON><PERSON><PERSON><PERSON> học đang dạy", "default": 0}, "total_students": {"type": "integer", "title": "Total Students", "description": "Tổng số học viên", "default": 0}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "Tổng số bài học", "default": 0}, "completed_lessons": {"type": "integer", "title": "Completed Lessons", "description": "<PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "average_rating": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Rating", "description": "<PERSON><PERSON><PERSON> giá trung bình"}, "total_teaching_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Teaching Hours", "description": "Tổng giờ dạy"}, "this_month_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "This Month Hours", "description": "G<PERSON><PERSON> dạy tháng này"}}, "type": "object", "title": "InstructorStatistics", "description": "Instructor statistics schema."}, "InvoiceItem": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> mục hóa đơn"}, "description": {"type": "string", "title": "Description", "description": "<PERSON><PERSON>"}, "quantity": {"type": "integer", "title": "Quantity", "description": "Số lượng", "default": 1}, "unit_price": {"type": "string", "title": "Unit Price", "description": "Đơn giá"}, "total_price": {"type": "string", "title": "Total Price", "description": "<PERSON><PERSON><PERSON><PERSON> tiền"}, "discount_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Discount Amount", "description": "Số tiền giảm giá"}, "tax_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tax Amount", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["id", "description", "unit_price", "total_price"], "title": "InvoiceItem", "description": "Invoice item schema."}, "IssueCreate": {"properties": {"title": {"type": "string", "title": "Title", "description": "<PERSON><PERSON><PERSON><PERSON> đề sự cố"}, "description": {"type": "string", "title": "Description", "description": "<PERSON><PERSON> tả chi tiết về sự cố"}, "issue_type": {"type": "string", "pattern": "^(technical|content|payment|account|feedback|complaint|suggestion|other)$", "title": "Issue Type", "description": "Loại sự cố: technical (kỹ thuật), content (nội dung), payment (thanh toán), account (tài k<PERSON>), feedback (ph<PERSON><PERSON> hồi), complaint (khi<PERSON>u nại), suggestion (đề xuất), other (khác)"}, "priority": {"type": "string", "pattern": "^[0-3]$", "title": "Priority", "description": "<PERSON><PERSON><PERSON> <PERSON>u tiên: 0 (thấp), 1 (b<PERSON><PERSON> thư<PERSON>), 2 (cao), 3 (<PERSON>h<PERSON><PERSON> cấp)", "default": "1"}, "related_model": {"type": "string", "title": "Related Model", "description": "Model li<PERSON>n quan, mặc định là eb.class.class", "default": "eb.class.class"}, "related_id": {"type": "integer", "title": "Related Id", "description": "<PERSON> của lớp học liên quan"}, "reporter_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reporter Phone", "description": "<PERSON><PERSON> điện thoại người báo cáo"}, "attachments": {"anyOf": [{"items": {"additionalProperties": {"type": "string"}, "type": "object"}, "type": "array"}, {"type": "null"}], "title": "Attachments", "description": "<PERSON><PERSON> s<PERSON>ch tệp đ<PERSON>, mỗi tệp gồm name (tên file) và data (dữ liệu dạng base64)"}}, "type": "object", "required": ["title", "description", "issue_type", "related_id"], "title": "IssueCreate", "description": "Schema cho tạo sự cố mới"}, "IssueCreateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/IssueData"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "IssueCreateResponse", "description": "Response cho API tạo sự cố mới"}, "IssueData": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID sự cố"}, "code": {"type": "string", "title": "Code", "description": "Mã sự cố"}, "title": {"type": "string", "title": "Title", "description": "<PERSON><PERSON><PERSON><PERSON> đề sự cố"}, "status": {"type": "string", "title": "Status", "description": "<PERSON>r<PERSON><PERSON> thái sự cố"}, "create_date": {"type": "string", "format": "date-time", "title": "Create Date", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["id", "code", "title", "status", "create_date"], "title": "IssueData"}, "IssueDetail": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID sự cố"}, "code": {"type": "string", "title": "Code", "description": "Mã sự cố"}, "title": {"type": "string", "title": "Title", "description": "<PERSON><PERSON><PERSON><PERSON> đề sự cố"}, "issue_type": {"type": "string", "title": "Issue Type", "description": "Loại sự cố"}, "priority": {"type": "string", "title": "Priority", "description": "<PERSON><PERSON><PERSON> đ<PERSON>u tiên"}, "status": {"type": "string", "title": "Status", "description": "<PERSON>r<PERSON><PERSON> thái sự cố"}, "create_date": {"type": "string", "format": "date-time", "title": "Create Date", "description": "<PERSON><PERSON><PERSON>"}, "related_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Related Name", "description": "<PERSON><PERSON><PERSON> đ<PERSON>i tượng liên quan"}, "description": {"type": "string", "title": "Description", "description": "<PERSON><PERSON> tả chi tiết về sự cố"}, "reporter_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reporter Name", "description": "<PERSON><PERSON><PERSON><PERSON> báo c<PERSON>o"}, "reporter_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reporter Email", "description": "<PERSON><PERSON> báo c<PERSON>o"}, "reporter_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reporter Phone", "description": "<PERSON><PERSON> điện thoại người báo cáo"}, "open_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Open Date", "description": "Ngày mở sự cố"}, "processing_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Processing Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u xử lý"}, "resolution_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Resolution Date", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "close_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Close Date", "description": "<PERSON><PERSON><PERSON>"}, "resolution": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Resolution", "description": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>"}, "satisfaction_rating": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Satisfaction Rating", "description": "<PERSON><PERSON><PERSON> gi<PERSON> mức độ hài lòng"}, "feedback": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hồi của ng<PERSON><PERSON>i báo cáo"}, "is_late": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Late", "description": "<PERSON>r<PERSON><PERSON> thái tr<PERSON> hạn"}, "sla_deadline": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON><PERSON> Deadline", "description": "Hạn chót SLA"}, "time_to_resolve": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Time To Resolve", "description": "Thời gian g<PERSON><PERSON><PERSON> (giờ)"}, "message_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Message Count", "description": "Số l<PERSON>ng tin nh<PERSON>n"}, "attachment_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Attachment Count", "description": "<PERSON><PERSON> l<PERSON><PERSON> t<PERSON><PERSON> đ<PERSON>"}}, "type": "object", "required": ["id", "code", "title", "issue_type", "priority", "status", "create_date", "description"], "title": "IssueDetail", "description": "<PERSON><PERSON><PERSON> chi tiết cho sự cố"}, "IssueDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/IssueDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "IssueDetailResponse", "description": "Response cho chi tiết sự cố"}, "IssueFeedback": {"properties": {"satisfaction_rating": {"type": "string", "pattern": "^[0-4]$", "title": "Satisfaction Rating", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> mức độ hài lòng: 0 (kh<PERSON><PERSON> hài lòng), 1 (<PERSON><PERSON> hài lòng), 2 (b<PERSON><PERSON> thường), 3 (hà<PERSON> lòng), 4 (rất hài lòng)"}, "feedback": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hồi chi tiết"}}, "type": "object", "required": ["satisfaction_rating"], "title": "IssueFeedback", "description": "<PERSON><PERSON><PERSON> cho ph<PERSON>n hồi về cách gi<PERSON>i quyết sự cố"}, "IssueFeedbackResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/IssueDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "IssueFeedbackResponse", "description": "Response cho ph<PERSON>n hồi về cách gi<PERSON>i quyết sự cố"}, "IssueList": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID sự cố"}, "code": {"type": "string", "title": "Code", "description": "Mã sự cố"}, "title": {"type": "string", "title": "Title", "description": "<PERSON><PERSON><PERSON><PERSON> đề sự cố"}, "issue_type": {"type": "string", "title": "Issue Type", "description": "Loại sự cố"}, "priority": {"type": "string", "title": "Priority", "description": "<PERSON><PERSON><PERSON> đ<PERSON>u tiên"}, "status": {"type": "string", "title": "Status", "description": "<PERSON>r<PERSON><PERSON> thái sự cố"}, "create_date": {"type": "string", "format": "date-time", "title": "Create Date", "description": "<PERSON><PERSON><PERSON>"}, "related_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Related Name", "description": "<PERSON><PERSON><PERSON> đ<PERSON>i tượng liên quan"}, "is_late": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Late", "description": "<PERSON>r<PERSON><PERSON> thái tr<PERSON> hạn"}}, "type": "object", "required": ["id", "code", "title", "issue_type", "priority", "status", "create_date"], "title": "IssueList", "description": "<PERSON><PERSON><PERSON> danh s<PERSON>ch sự cố"}, "IssueListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/IssueList"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "IssueListResponse", "description": "Response cho danh sách sự cố"}, "IssueStageUpdate": {"properties": {"stage_id": {"type": "integer", "title": "Stage Id", "description": "ID trạng thái mới"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON>hi chú khi chuyển trạng thái"}}, "type": "object", "required": ["stage_id"], "title": "IssueStageUpdate", "description": "<PERSON><PERSON><PERSON> cho cập nhật trạng thái sự cố"}, "IssueStageUpdateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/IssueDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "IssueStageUpdateResponse", "description": "Response cho cập nhật trạng thái sự cố"}, "IssueUpdate": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "<PERSON><PERSON><PERSON><PERSON> đề sự cố"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả chi tiết về sự cố"}, "issue_type": {"anyOf": [{"type": "string", "pattern": "^(technical|content|payment|account|feedback|complaint|suggestion|other)$"}, {"type": "null"}], "title": "Issue Type", "description": "Loại sự cố: technical (kỹ thuật), content (nội dung), payment (thanh toán), account (tài k<PERSON>), feedback (ph<PERSON><PERSON> hồi), complaint (khi<PERSON>u nại), suggestion (đề xuất), other (khác)"}, "priority": {"anyOf": [{"type": "string", "pattern": "^[0-3]$"}, {"type": "null"}], "title": "Priority", "description": "<PERSON><PERSON><PERSON> <PERSON>u tiên: 0 (thấp), 1 (b<PERSON><PERSON> thư<PERSON>), 2 (cao), 3 (<PERSON>h<PERSON><PERSON> cấp)"}, "reporter_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reporter Phone", "description": "<PERSON><PERSON> điện thoại người báo cáo"}}, "type": "object", "title": "IssueUpdate", "description": "<PERSON><PERSON><PERSON> cho cập nhật sự cố"}, "IssueUpdateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/IssueDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "IssueUpdateResponse", "description": "Response cho API cập nhật sự cố"}, "LMSResponseBase_AttendanceSummary_": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/AttendanceSummary"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "LMSResponseBase[AttendanceSummary]"}, "LeadData": {"properties": {"name": {"type": "string", "title": "Name", "description": "<PERSON>ã đăng ký tư vấn"}, "email": {"type": "string", "title": "Email", "description": "<PERSON><PERSON> h<PERSON>c viên tiềm năng"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> điện thoại đã định dạng"}, "created_date": {"type": "string", "format": "date-time", "title": "Created Date", "description": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký tư vấn"}}, "type": "object", "required": ["name", "email", "created_date"], "title": "LeadData"}, "LearningPreferences": {"properties": {"preferred_learning_style": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferred Learning Style", "description": "<PERSON><PERSON> c<PERSON>ch học tập"}, "preferred_time_slots": {"items": {"type": "string"}, "type": "array", "title": "Preferred Time Slots", "description": "<PERSON><PERSON>g gi<PERSON> <PERSON><PERSON> th<PERSON>ch"}, "learning_goals": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Learning Goals", "description": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> học tập"}, "special_needs": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Special Needs", "description": "<PERSON><PERSON> cầu đặc biệt"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "title": "LearningPreferences", "description": "Learning preferences schema."}, "LearningProgress": {"properties": {"total_study_hours": {"type": "number", "title": "Total Study Hours", "description": "Tổng g<PERSON> học", "default": 0.0}, "effective_study_hours": {"type": "number", "title": "Effective Study Hours", "description": "<PERSON><PERSON><PERSON> h<PERSON>c hiệu quả", "default": 0.0}, "this_month_hours": {"type": "number", "title": "This Month Hours", "description": "<PERSON><PERSON><PERSON> h<PERSON> tháng này", "default": 0.0}, "this_week_hours": {"type": "number", "title": "This Week Hours", "description": "<PERSON><PERSON><PERSON> h<PERSON> tu<PERSON> này", "default": 0.0}, "courses_enrolled": {"type": "integer", "title": "Courses Enrolled", "description": "<PERSON><PERSON><PERSON><PERSON> học đã đăng ký", "default": 0}, "courses_in_progress": {"type": "integer", "title": "Courses In Progress", "description": "<PERSON><PERSON><PERSON><PERSON> học đang học", "default": 0}, "courses_completed": {"type": "integer", "title": "Courses Completed", "description": "<PERSON><PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "overall_completion_rate": {"type": "number", "title": "Overall Completion Rate", "description": "Tỷ lệ hoàn thành tổng thể (%)", "default": 0.0}, "lessons_per_week": {"type": "number", "title": "Lessons Per Week", "description": "<PERSON><PERSON><PERSON> h<PERSON>/tu<PERSON>n", "default": 0.0}, "hours_per_week": {"type": "number", "title": "Hours Per Week", "description": "<PERSON><PERSON><PERSON> học/tuần", "default": 0.0}, "milestones_achieved": {"type": "integer", "title": "Milestones Achieved", "description": "<PERSON><PERSON><PERSON> mốc đã đạt", "default": 0}, "next_milestone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Next Milestone", "description": "<PERSON><PERSON><PERSON> mốc ti<PERSON> theo"}, "learning_pace": {"type": "string", "title": "Learning Pace", "description": "<PERSON><PERSON><PERSON> độ học tập"}, "is_on_track": {"type": "boolean", "title": "Is On Track", "description": "<PERSON><PERSON><PERSON> tiến độ", "default": true}}, "type": "object", "required": ["learning_pace"], "title": "LearningProgress", "description": "Learning progress schema."}, "LessonActionResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "LessonActionResponse", "description": "Lesson action response schema."}, "LessonBasicInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> b<PERSON><PERSON> h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "lesson_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lesson Number", "description": "<PERSON><PERSON> thứ tự bài học"}, "state": {"type": "string", "title": "State", "description": "<PERSON>r<PERSON><PERSON> thái bài học"}, "start_datetime": {"type": "string", "format": "date-time", "title": "Start Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "end_datetime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc"}, "duration_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> (giờ)"}, "room": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["id", "name", "state", "start_datetime"], "title": "LessonBasicInfo", "description": "Basic lesson information schema."}, "LessonContent": {"properties": {"objectives": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Objectives", "description": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> b<PERSON><PERSON> học"}, "content_outline": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content Outline", "description": "<PERSON><PERSON><PERSON> dung ch<PERSON>h"}, "teaching_method": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Teaching Method", "description": "Phương ph<PERSON>p gi<PERSON>ng d<PERSON>y"}, "materials_needed": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Materials Needed", "description": "<PERSON><PERSON><PERSON> li<PERSON><PERSON> c<PERSON><PERSON> thi<PERSON>"}, "homework_assignment": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Homework Assignment", "description": "<PERSON><PERSON><PERSON> tập về nhà"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Lesson content schema."}, "LessonCreateRequest": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "class_id": {"type": "integer", "title": "Class Id", "description": "<PERSON> lớp h<PERSON>c"}, "lesson_number": {"anyOf": [{"type": "integer", "minimum": 1.0}, {"type": "null"}], "title": "Lesson Number", "description": "<PERSON><PERSON> thứ tự bài học"}, "start_datetime": {"type": "string", "format": "date-time", "title": "Start Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "duration_hours": {"anyOf": [{"type": "number", "maximum": 8.0, "minimum": 0.5}, {"type": "null"}], "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> (0.5-8 giờ)"}, "room": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}, "objectives": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Objectives", "description": "<PERSON><PERSON><PERSON> ti<PERSON>"}, "content_outline": {"anyOf": [{"type": "string", "maxLength": 2000}, {"type": "null"}], "title": "Content Outline", "description": "<PERSON><PERSON>i dung"}, "teaching_method": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Teaching Method", "description": "Phương ph<PERSON>p"}, "materials_needed": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Materials Needed", "description": "<PERSON><PERSON><PERSON> l<PERSON>n"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["name", "class_id", "start_datetime"], "title": "LessonCreateRequest", "description": "Lesson creation request schema."}, "LessonCreateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/LessonBasicInfo"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "LessonCreateResponse", "description": "Lesson creation response schema."}, "LessonDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/InstructorLesson"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "LessonDetailResponse", "description": "Lesson detail response schema."}, "LessonListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "<PERSON>r<PERSON><PERSON> thái thành công", "default": true}, "message": {"type": "string", "title": "Message", "description": "<PERSON><PERSON><PERSON><PERSON> báo", "default": "<PERSON><PERSON> liệu đ<PERSON><PERSON><PERSON> tải thành công"}, "data": {"items": {"$ref": "#/components/schemas/LessonSummary"}, "type": "array", "title": "Data", "description": "<PERSON><PERSON> liệu"}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata", "description": "Thông tin phân trang"}}, "type": "object", "required": ["data", "pagination"], "title": "LessonListResponse", "description": "Lesson list response schema."}, "LessonMaterialListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__students__lesson_schemas__LessonMaterial"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "LessonMaterialListResponse", "description": "Lesson material list response schema."}, "LessonStatistics": {"properties": {"total_students": {"type": "integer", "title": "Total Students", "description": "Tổng số học viên", "default": 0}, "present_count": {"type": "integer", "title": "Present Count", "description": "<PERSON><PERSON> học viên có mặt", "default": 0}, "absent_count": {"type": "integer", "title": "Absent Count", "description": "<PERSON><PERSON> học viên vắng mặt", "default": 0}, "late_count": {"type": "integer", "title": "Late Count", "description": "<PERSON><PERSON> học viên đi muộn", "default": 0}, "attendance_rate": {"type": "number", "title": "Attendance Rate", "description": "Tỷ lệ điểm danh (%)", "default": 0.0}, "average_check_in_time": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Average Check In Time", "description": "Thời gian check-in trung bình"}}, "type": "object", "title": "LessonStatistics", "description": "Lesson statistics schema."}, "LessonSummary": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> b<PERSON><PERSON> h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "lesson_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lesson Number", "description": "<PERSON><PERSON> thứ tự"}, "state": {"type": "string", "title": "State", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "class_name": {"type": "string", "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "class_code": {"type": "string", "title": "Class Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "start_datetime": {"type": "string", "format": "date-time", "title": "Start Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "duration_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "room": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}, "total_students": {"type": "integer", "title": "Total Students", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên", "default": 0}, "present_count": {"type": "integer", "title": "Present Count", "description": "<PERSON><PERSON> mặt", "default": 0}, "attendance_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Attendance Rate", "description": "Tỷ lệ điểm danh"}, "is_today": {"type": "boolean", "title": "Is Today", "description": "<PERSON><PERSON><PERSON> h<PERSON> hôm nay", "default": false}, "is_upcoming": {"type": "boolean", "title": "Is Upcoming", "description": "<PERSON><PERSON><PERSON> ra", "default": false}, "is_overdue": {"type": "boolean", "title": "Is Overdue", "description": "<PERSON><PERSON><PERSON> h<PERSON>n", "default": false}, "needs_attention": {"type": "boolean", "title": "Needs Attention", "description": "<PERSON><PERSON><PERSON> ch<PERSON>", "default": false}}, "type": "object", "required": ["id", "name", "state", "class_name", "class_code", "start_datetime"], "title": "LessonSummary", "description": "Lesson summary for list view."}, "LessonUpdateRequest": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "start_datetime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Start Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "duration_hours": {"anyOf": [{"type": "number", "maximum": 8.0, "minimum": 0.5}, {"type": "null"}], "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "room": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}, "objectives": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Objectives", "description": "<PERSON><PERSON><PERSON> ti<PERSON>"}, "content_outline": {"anyOf": [{"type": "string", "maxLength": 2000}, {"type": "null"}], "title": "Content Outline", "description": "<PERSON><PERSON>i dung"}, "teaching_method": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Teaching Method", "description": "Phương ph<PERSON>p"}, "materials_needed": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Materials Needed", "description": "<PERSON><PERSON><PERSON> l<PERSON>n"}, "homework_assignment": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Homework Assignment", "description": "<PERSON><PERSON><PERSON> t<PERSON>p"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "title": "LessonUpdateRequest", "description": "Lesson update request schema."}, "LessonUpdateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/LessonBasicInfo"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "LessonUpdateResponse", "description": "Lesson update response schema."}, "LoginRequest": {"properties": {"username": {"type": "string", "title": "Username", "description": "<PERSON><PERSON><PERSON> đ<PERSON>p"}, "password": {"type": "string", "title": "Password", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["username", "password"], "title": "LoginRequest", "description": "Login request model.", "example": {"password": "admin", "username": "admin"}}, "MonthSchedule": {"properties": {"month": {"type": "integer", "maximum": 12.0, "minimum": 1.0, "title": "Month", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "year": {"type": "integer", "title": "Year", "description": "Năm"}, "month_name": {"type": "string", "title": "Month Name", "description": "<PERSON><PERSON><PERSON>g"}, "weeks": {"items": {"$ref": "#/components/schemas/WeekSchedule"}, "type": "array", "title": "Weeks", "description": "<PERSON><PERSON><PERSON> c<PERSON> tu<PERSON>n trong tháng"}, "total_hours": {"type": "number", "title": "Total Hours", "description": "Tổng gi<PERSON> tháng", "default": 0.0}, "teaching_hours": {"type": "number", "title": "Teaching Hours", "description": "G<PERSON>ờ giảng dạy", "default": 0.0}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> h<PERSON>c", "default": 0}, "working_days": {"type": "integer", "title": "Working Days", "description": "<PERSON><PERSON><PERSON> l<PERSON> vi<PERSON>c", "default": 0}, "average_daily_hours": {"type": "number", "title": "Average Daily Hours", "description": "Giờ trung bình/ngày", "default": 0.0}, "peak_day": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Peak Day", "description": "<PERSON><PERSON><PERSON> b<PERSON> nh<PERSON>t"}, "peak_day_hours": {"type": "number", "title": "Peak Day Hours", "description": "<PERSON><PERSON><PERSON> ng<PERSON> b<PERSON>n nh<PERSON>t", "default": 0.0}}, "type": "object", "required": ["month", "year", "month_name", "weeks"], "title": "MonthSchedule", "description": "Monthly schedule schema."}, "MonthScheduleResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/MonthSchedule"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "MonthScheduleResponse", "description": "Monthly schedule response schema."}, "OAuth2Token": {"properties": {"access_token": {"type": "string", "title": "Access Token"}, "token_type": {"type": "string", "title": "Token Type", "default": "Bearer"}, "expires_in": {"type": "integer", "title": "Expires In"}, "refresh_token": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": "api"}, "refresh_token_expires_in": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Refresh <PERSON> Expires In"}, "issued_at": {"type": "integer", "title": "Issued At"}}, "type": "object", "required": ["access_token", "expires_in", "issued_at"], "title": "OAuth2Token", "description": "<PERSON><PERSON> li<PERSON>u token theo <PERSON><PERSON><PERSON><PERSON> OAuth 2.0 (RFC 6749)"}, "PaginationMetadata": {"properties": {"page": {"type": "integer", "title": "Page", "description": "<PERSON><PERSON> hi<PERSON>n tại"}, "page_size": {"type": "integer", "title": "<PERSON>", "description": "Số item mỗi trang"}, "total_count": {"type": "integer", "title": "Total Count", "description": "Tổng số item"}, "total_pages": {"type": "integer", "title": "Total Pages", "description": "Tổng số trang"}, "has_next": {"type": "boolean", "title": "Has Next", "description": "<PERSON><PERSON> trang tiếp theo"}, "has_previous": {"type": "boolean", "title": "Has Previous", "description": "<PERSON><PERSON> trang trư<PERSON>"}}, "type": "object", "required": ["page", "page_size", "total_count", "total_pages", "has_next", "has_previous"], "title": "PaginationMetadata", "description": "Pagination metadata for responses."}, "PasswordResetRequest": {"properties": {"email": {"type": "string", "title": "Email", "description": "<PERSON><PERSON> reset password"}}, "type": "object", "required": ["email"], "title": "PasswordResetRequest", "description": "Password reset request model.", "example": {"email": "<EMAIL>"}}, "PaymentHistoryItem": {"properties": {"date": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Date", "description": "<PERSON><PERSON><PERSON> to<PERSON>"}, "amount": {"type": "number", "title": "Amount", "description": "<PERSON><PERSON> tiền thanh toán"}, "reference": {"type": "string", "title": "Reference", "description": "<PERSON><PERSON> tham chi<PERSON>u thanh toán", "default": ""}}, "type": "object", "required": ["amount"], "title": "PaymentHistoryItem"}, "PaymentMethod": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID phư<PERSON><PERSON> thức thanh toán"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> thức"}, "type": {"type": "string", "title": "Type", "description": "Loại: cash|bank_transfer|credit_card|e_wallet"}, "is_active": {"type": "boolean", "title": "Is Active", "description": "<PERSON><PERSON> ho<PERSON>t động", "default": true}, "processing_fee": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Processing Fee", "description": "<PERSON><PERSON> lý"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON>"}}, "type": "object", "required": ["id", "name", "type"], "title": "PaymentMethod", "description": "Payment method schema."}, "PaymentRecord": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID thanh toán"}, "payment_code": {"type": "string", "title": "Payment Code", "description": "<PERSON><PERSON> thanh toán"}, "amount": {"type": "string", "title": "Amount", "description": "<PERSON><PERSON> tiền"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Đơn vị tiền tệ", "default": "VND"}, "payment_date": {"type": "string", "format": "date-time", "title": "Payment Date", "description": "<PERSON><PERSON><PERSON> to<PERSON>"}, "payment_method": {"$ref": "#/components/schemas/PaymentMethod", "description": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán"}, "status": {"type": "string", "title": "Status", "description": "<PERSON>r<PERSON><PERSON> thái thanh toán"}, "is_verified": {"type": "boolean", "title": "Is Verified", "description": "Đ<PERSON> x<PERSON>c <PERSON>h", "default": false}, "reference_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reference Number", "description": "<PERSON><PERSON> tham chiếu"}, "transaction_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Transaction Id", "description": "ID giao d<PERSON>ch"}, "receipt_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Receipt Url", "description": "<PERSON> lai"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}, "processed_by": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Processed By", "description": "<PERSON>ư<PERSON><PERSON> x<PERSON> lý"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "<PERSON><PERSON><PERSON>"}, "verified_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Verified At", "description": "<PERSON><PERSON><PERSON>h"}}, "type": "object", "required": ["id", "payment_code", "amount", "payment_date", "payment_method", "status", "created_at"], "title": "PaymentRecord", "description": "Payment record schema."}, "PaymentRequest": {"properties": {"invoice_id": {"type": "integer", "title": "Invoice Id", "description": "ID hóa đơn"}, "amount": {"anyOf": [{"type": "number", "exclusiveMinimum": 0.0}, {"type": "string"}], "title": "Amount", "description": "<PERSON><PERSON> tiền thanh toán"}, "payment_method_id": {"type": "integer", "title": "Payment Method Id", "description": "ID phư<PERSON><PERSON> thức thanh toán"}, "reference_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reference Number", "description": "<PERSON><PERSON> tham chiếu"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["invoice_id", "amount", "payment_method_id"], "title": "PaymentRequest", "description": "Payment request schema."}, "PaymentStatusData": {"properties": {"enrollment_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Enrollment Id", "description": "ID đăng ký khóa học"}, "enrollment_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Enrollment Number", "description": "Mã đăng ký khóa học"}, "course_info": {"anyOf": [{"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__CourseInfo"}, {"type": "null"}], "description": "Thông tin khóa học"}, "student_info": {"anyOf": [{"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__StudentInfo"}, {"type": "null"}], "description": "<PERSON>h<PERSON><PERSON> tin học viên"}, "order_info": {"anyOf": [{"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__OrderInfo"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON><PERSON> tin đơn hàng"}, "invoice_info": {"anyOf": [{"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__InvoiceInfo"}, {"type": "null"}], "description": "<PERSON>h<PERSON><PERSON> tin hóa đơn"}, "payment_info": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__PaymentInfo", "description": "Thông tin thanh toán"}, "refund_status": {"anyOf": [{"$ref": "#/components/schemas/RefundStatus"}, {"type": "null"}], "description": "Thông tin hoàn tiền"}}, "type": "object", "required": ["payment_info"], "title": "PaymentStatusData"}, "PaymentSubmissionResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PaymentSubmissionResponse", "description": "Payment submission response schema."}, "PaymentSummaryResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentPaymentSummary"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PaymentSummaryResponse", "description": "Payment summary response schema."}, "PerformanceAnalyticsResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/InstructorPerformanceAnalytics"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PerformanceAnalyticsResponse", "description": "Performance analytics response schema."}, "ProgressMetric": {"properties": {"metric_name": {"type": "string", "title": "Metric Name", "description": "Tên chỉ số"}, "current_value": {"type": "number", "title": "Current Value", "description": "<PERSON><PERSON><PERSON> trị hiện tại"}, "target_value": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Target Value", "description": "<PERSON><PERSON><PERSON> trị mục tiêu"}, "previous_value": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Previous Value", "description": "<PERSON><PERSON><PERSON> trị trước đó"}, "unit": {"type": "string", "title": "Unit", "description": "Đơn vị đo"}, "trend": {"type": "string", "title": "Trend", "description": "<PERSON> hướng: up|down|stable"}, "percentage_change": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Percentage Change", "description": "<PERSON><PERSON><PERSON> tr<PERSON>m thay đổi"}, "is_improving": {"type": "boolean", "title": "Is Improving", "description": "<PERSON><PERSON> c<PERSON> thi<PERSON>n", "default": false}}, "type": "object", "required": ["metric_name", "current_value", "unit", "trend"], "title": "ProgressMetric", "description": "Progress metric schema."}, "PublicCertificateInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID chứng chỉ"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> ch<PERSON>ng chỉ"}, "certificate_number": {"type": "string", "title": "Certificate Number", "description": "<PERSON><PERSON> chứng chỉ"}, "student_name": {"type": "string", "title": "Student Name", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "issue_date": {"type": "string", "format": "date", "title": "Issue Date", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}, "expiry_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Expiry Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> hạn"}, "grade": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Grade", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "grade_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Grade Text", "description": "<PERSON><PERSON><PERSON>"}, "qualification": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qualification", "description": "<PERSON><PERSON><PERSON><PERSON>/<PERSON>y<PERSON><PERSON> môn"}, "status": {"type": "string", "title": "Status", "description": "Tr<PERSON>ng thái chứng chỉ"}, "issued_by_name": {"type": "string", "title": "Issued By Name", "description": "<PERSON><PERSON><PERSON><PERSON> cấp"}, "organization_name": {"type": "string", "title": "Organization Name", "description": "<PERSON><PERSON><PERSON> tổ chức"}, "organization_website": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organization Website", "description": "Website tổ chức"}, "verification_count": {"type": "integer", "title": "Verification Count", "description": "Số lần x<PERSON>c <PERSON>h", "default": 0}, "last_verified_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Verified At", "description": "<PERSON><PERSON><PERSON><PERSON> gian x<PERSON>c minh gần nhất"}, "qr_code_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qr Code Url", "description": "URL mã QR"}, "certificate_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Certificate Url", "description": "URL xem chứng chỉ"}, "certificate_download_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Certificate Download Url", "description": "URL tải chứng chỉ"}}, "type": "object", "required": ["id", "name", "certificate_number", "student_name", "course_name", "issue_date", "status", "issued_by_name", "organization_name"], "title": "PublicCertificateInfo", "description": "<PERSON><PERSON><PERSON> cho thông tin chứng chỉ công khai"}, "PublicCertificateListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/PublicCertificateInfo"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PublicCertificateListResponse", "description": "<PERSON><PERSON><PERSON> cho phản hồi danh sách chứng chỉ", "example": {"data": [{"certificate_download_url": "https://api.earnbase.io/v1/certificates/45/download", "certificate_number": "CERT-2025-045", "certificate_url": "https://api.earnbase.io/v1/certificates/45/view", "course_name": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON> c<PERSON> bản", "expiry_date": "2026-04-03", "grade": 85.5, "grade_text": "<PERSON><PERSON><PERSON>", "id": 45, "issue_date": "2025-04-03", "issued_by_name": "<PERSON>", "last_verified_at": "2025-04-03T15:08:00Z", "name": "<PERSON><PERSON><PERSON> chỉ hoàn thành khóa học lập trình <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "organization_name": "EarnBase Technology", "organization_website": "https://earnbase.io", "qr_code_url": "https://api.earnbase.io/v1/certificates/45/qr-code", "qualification": "<PERSON><PERSON><PERSON> trì<PERSON> viên Python c<PERSON> bản", "status": "issued", "student_name": "<PERSON><PERSON><PERSON><PERSON>", "verification_count": 3}, {"certificate_download_url": "https://api.earnbase.io/v1/certificates/46/download", "certificate_number": "CERT-2025-046", "certificate_url": "https://api.earnbase.io/v1/certificates/46/view", "course_name": "<PERSON><PERSON><PERSON> trình <PERSON> c<PERSON> bản", "expiry_date": "2026-04-02", "grade": 90.0, "grade_text": "<PERSON><PERSON><PERSON>", "id": 46, "issue_date": "2025-04-02", "issued_by_name": "<PERSON>", "last_verified_at": "2025-04-03T14:30:00Z", "name": "<PERSON><PERSON><PERSON> chỉ hoàn thành khóa học lập trình Java - Trần Thị B", "organization_name": "EarnBase Technology", "organization_website": "https://earnbase.io", "qr_code_url": "https://api.earnbase.io/v1/certificates/46/qr-code", "qualification": "<PERSON><PERSON><PERSON> trình viên Java c<PERSON> bản", "status": "issued", "student_name": "Trần <PERSON>hị B", "verification_count": 2}], "message": "<PERSON><PERSON> sách chứng chỉ đã xác minh gần đây", "meta": {"timestamp": "2025-04-03T15:08:00Z", "version": "1.0"}, "success": true}}, "PublicCertificateVerifyRequest": {"properties": {"verifier_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verifier Name", "description": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>h"}, "verifier_email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verifier Email", "description": "<PERSON><PERSON> x<PERSON>h"}, "verifier_company": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verifier Company", "description": "<PERSON><PERSON>ng ty ngư<PERSON>i x<PERSON>c <PERSON>h"}, "verifier_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verifier Notes", "description": "<PERSON><PERSON> chú xác minh"}}, "type": "object", "title": "PublicCertificateVerifyRequest", "description": "<PERSON><PERSON><PERSON> cho yêu cầu xác thực chứng chỉ"}, "PublicCertificateVerifyResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PublicCertificateVerifyResponse", "description": "<PERSON><PERSON><PERSON> cho phản hồi xác thực chứng chỉ", "example": {"data": {"certificate": {"certificate_download_url": "https://api.earnbase.io/v1/certificates/45/download", "certificate_number": "CERT-2025-045", "certificate_url": "https://api.earnbase.io/v1/certificates/45/view", "course_name": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON> c<PERSON> bản", "expiry_date": "2026-04-03", "grade": 85.5, "grade_text": "<PERSON><PERSON><PERSON>", "id": 45, "issue_date": "2025-04-03", "issued_by_name": "<PERSON>", "last_verified_at": "2025-04-03T15:08:00Z", "name": "<PERSON><PERSON><PERSON> chỉ hoàn thành khóa học lập trình <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "organization_name": "EarnBase Technology", "organization_website": "https://earnbase.io", "qr_code_url": "https://api.earnbase.io/v1/certificates/45/qr-code", "qualification": "<PERSON><PERSON><PERSON> trì<PERSON> viên Python c<PERSON> bản", "status": "issued", "student_name": "<PERSON><PERSON><PERSON><PERSON>", "verification_count": 3}, "verified": true}, "message": "<PERSON><PERSON><PERSON> chỉ hợp lệ", "meta": {"timestamp": "2025-04-03T15:08:00Z", "version": "1.0"}, "success": true}}, "PublicCertificateViewResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/PublicCertificateInfo"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PublicCertificateViewResponse", "description": "<PERSON><PERSON><PERSON> cho phản hồi xem chứng chỉ", "example": {"data": {"certificate_download_url": "https://api.earnbase.io/v1/certificates/45/download", "certificate_number": "CERT-2025-045", "certificate_url": "https://api.earnbase.io/v1/certificates/45/view", "course_name": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON> c<PERSON> bản", "expiry_date": "2026-04-03", "grade": 85.5, "grade_text": "<PERSON><PERSON><PERSON>", "id": 45, "issue_date": "2025-04-03", "issued_by_name": "<PERSON>", "last_verified_at": "2025-04-03T15:08:00Z", "name": "<PERSON><PERSON><PERSON> chỉ hoàn thành khóa học lập trình <PERSON> - <PERSON><PERSON><PERSON><PERSON>", "organization_name": "EarnBase Technology", "organization_website": "https://earnbase.io", "qr_code_url": "https://api.earnbase.io/v1/certificates/45/qr-code", "qualification": "<PERSON><PERSON><PERSON> trì<PERSON> viên Python c<PERSON> bản", "status": "issued", "student_name": "<PERSON><PERSON><PERSON><PERSON>", "verification_count": 3}, "message": "Thông tin chứng chỉ", "meta": {"timestamp": "2025-04-03T15:08:00Z", "version": "1.0"}, "success": true}}, "PublicClassBase": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> lớp h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "course_id": {"type": "integer", "title": "Course Id", "description": "ID khóa học"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "start_date": {"type": "string", "format": "date", "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"type": "string", "format": "date", "title": "End Date", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "learning_type": {"type": "string", "title": "Learning Type", "description": "<PERSON><PERSON><PERSON> hình học tập: offline, online, hybrid"}, "status": {"type": "string", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái lớ<PERSON> h<PERSON>c"}, "is_recruiting": {"type": "boolean", "title": "Is Recruiting", "description": "<PERSON><PERSON> t<PERSON> sinh"}, "location_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location Name", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> đi<PERSON> học"}}, "type": "object", "required": ["id", "name", "code", "course_id", "course_name", "start_date", "end_date", "learning_type", "status", "is_recruiting"], "title": "PublicClassBase", "description": "<PERSON><PERSON><PERSON> c<PERSON> bản cho thông tin lớp học công khai"}, "PublicClassDetail": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> lớp h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "course_id": {"type": "integer", "title": "Course Id", "description": "ID khóa học"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "start_date": {"type": "string", "format": "date", "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"type": "string", "format": "date", "title": "End Date", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "learning_type": {"type": "string", "title": "Learning Type", "description": "<PERSON><PERSON><PERSON> hình học tập: offline, online, hybrid"}, "status": {"type": "string", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái lớ<PERSON> h<PERSON>c"}, "is_recruiting": {"type": "boolean", "title": "Is Recruiting", "description": "<PERSON><PERSON> t<PERSON> sinh"}, "location_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location Name", "description": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> đi<PERSON> học"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả chi tiết"}, "schedule_summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Schedule Summary", "description": "<PERSON><PERSON><PERSON> t<PERSON> lị<PERSON> h<PERSON>c"}, "capacity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Capacity", "description": "<PERSON><PERSON><PERSON> ch<PERSON>a tối đa"}, "current_students": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Current Students", "description": "<PERSON><PERSON> học viên hiện tại"}, "instructors": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "title": "Instructors", "description": "Giảng viên"}, "price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Price", "description": "<PERSON><PERSON><PERSON> phí"}, "discount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Discount", "description": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> (%)"}, "registration_deadline": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Registration Deadline", "description": "<PERSON><PERSON><PERSON> đ<PERSON> ký"}}, "type": "object", "required": ["id", "name", "code", "course_id", "course_name", "start_date", "end_date", "learning_type", "status", "is_recruiting"], "title": "PublicClassDetail", "description": "<PERSON><PERSON><PERSON> chi tiết cho lớp học công khai"}, "PublicClassDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/PublicClassDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicClassDetailResponse", "description": "Response cho chi ti<PERSON>t lớp họ<PERSON> công khai"}, "PublicClassListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/PublicClassBase"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicClassListResponse", "description": "Response cho danh s<PERSON>ch lớp học công khai"}, "PublicCourseBase": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID khóa học"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "code": {"type": "string", "title": "Code", "description": "Mã khóa học"}, "short_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Short Description", "description": "<PERSON><PERSON> t<PERSON> ng<PERSON>n gọn"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category", "description": "<PERSON><PERSON> mụ<PERSON> kh<PERSON><PERSON> học"}, "price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Price", "description": "<PERSON><PERSON><PERSON> (giữ lại để tương thích ng<PERSON>)"}, "original_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Original Price", "description": "<PERSON><PERSON><PERSON> g<PERSON> kh<PERSON><PERSON> học"}, "discounted_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Discounted Price", "description": "<PERSON><PERSON><PERSON> đã gi<PERSON>m kh<PERSON> học"}, "has_discount": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Has Discount", "description": "<PERSON><PERSON> gi<PERSON>m giá hay không"}, "discount_percentage": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Discount Percentage", "description": "Phần tr<PERSON>m giảm giá"}, "currency_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currency Code", "description": "<PERSON><PERSON> tiền tệ (VND, USD,...)"}, "location_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location Name", "description": "<PERSON><PERSON><PERSON> c<PERSON> sở học dự kiến"}, "start_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Start Date", "description": "<PERSON><PERSON><PERSON> khai gi<PERSON>ng dự kiến"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url", "description": "URL hình ảnh khóa học"}, "rating": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Rating", "description": "<PERSON><PERSON><PERSON> giá trung bình"}, "duration": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration", "description": "Thời lư<PERSON> kh<PERSON>a học"}, "level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Level", "description": "<PERSON><PERSON><PERSON> đ<PERSON> kh<PERSON>a học"}}, "type": "object", "required": ["id", "name", "code"], "title": "PublicCourseBase", "description": "<PERSON><PERSON><PERSON> c<PERSON> bản cho thông tin khóa học công khai"}, "PublicCourseCategory": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> danh mục"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> da<PERSON> mục"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả danh mục"}, "course_count": {"type": "integer", "title": "Course Count", "description": "Số lượng kh<PERSON>a học"}}, "type": "object", "required": ["id", "name", "course_count"], "title": "PublicCourseCategory", "description": "<PERSON><PERSON><PERSON> cho danh mục kh<PERSON>a học"}, "PublicCourseCategoryListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/PublicCourseCategory"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicCourseCategoryListResponse", "description": "Response cho danh sách danh mục khóa học"}, "PublicCourseDetail": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID khóa học"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "code": {"type": "string", "title": "Code", "description": "Mã khóa học"}, "short_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Short Description", "description": "<PERSON><PERSON> t<PERSON> ng<PERSON>n gọn"}, "category": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category", "description": "<PERSON><PERSON> mụ<PERSON> kh<PERSON><PERSON> học"}, "price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Price", "description": "<PERSON><PERSON><PERSON> (giữ lại để tương thích ng<PERSON>)"}, "original_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Original Price", "description": "<PERSON><PERSON><PERSON> g<PERSON> kh<PERSON><PERSON> học"}, "discounted_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Discounted Price", "description": "<PERSON><PERSON><PERSON> đã gi<PERSON>m kh<PERSON> học"}, "has_discount": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Has Discount", "description": "<PERSON><PERSON> gi<PERSON>m giá hay không"}, "discount_percentage": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Discount Percentage", "description": "Phần tr<PERSON>m giảm giá"}, "currency_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Currency Code", "description": "<PERSON><PERSON> tiền tệ (VND, USD,...)"}, "location_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location Name", "description": "<PERSON><PERSON><PERSON> c<PERSON> sở học dự kiến"}, "start_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Start Date", "description": "<PERSON><PERSON><PERSON> khai gi<PERSON>ng dự kiến"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url", "description": "URL hình ảnh khóa học"}, "rating": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Rating", "description": "<PERSON><PERSON><PERSON> giá trung bình"}, "duration": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration", "description": "Thời lư<PERSON> kh<PERSON>a học"}, "level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Level", "description": "<PERSON><PERSON><PERSON> đ<PERSON> kh<PERSON>a học"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả chi tiết"}, "subjects": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "title": "Subjects", "description": "<PERSON><PERSON> s<PERSON>ch các môn họ<PERSON> (id, name, description, category, tags)"}, "is_enrollment_available": {"type": "boolean", "title": "Is Enrollment Available", "description": "<PERSON><PERSON><PERSON><PERSON> học có thể đăng ký hay không"}, "enrollment_unavailable_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Enrollment Unavailable Reason", "description": "<PERSON>ý do không thể đăng ký (nếu có)"}}, "type": "object", "required": ["id", "name", "code", "is_enrollment_available"], "title": "PublicCourseDetail", "description": "<PERSON><PERSON><PERSON> chi tiết cho kh<PERSON>a học công khai"}, "PublicCourseDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/PublicCourseDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicCourseDetailResponse", "description": "Response cho chi tiết kh<PERSON><PERSON> học công khai"}, "PublicCourseListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/PublicCourseBase"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicCourseListResponse", "description": "Response cho danh sách kh<PERSON>a học công khai"}, "PublicEnrollmentCreate": {"properties": {"full_name": {"type": "string", "title": "Full Name", "description": "<PERSON><PERSON><PERSON> đ<PERSON>y đủ người đăng ký"}, "email": {"type": "string", "title": "Email", "description": "Địa chỉ email người đăng ký"}, "country_code": {"type": "string", "title": "Country Code", "description": "<PERSON><PERSON> quốc gia theo định dạng ISO (VN, US, ...)", "default": "VN"}, "phone_number": {"type": "string", "title": "Phone Number", "description": "<PERSON><PERSON> điện tho<PERSON><PERSON> không bao gồm mã quốc gia"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON> chú đăng ký"}, "payment_method": {"type": "string", "title": "Payment Method", "description": "<PERSON><PERSON> ph<PERSON><PERSON><PERSON> thức thanh to<PERSON> (mặc định: qr_code)", "default": "qr_code"}}, "type": "object", "required": ["full_name", "email", "phone_number"], "title": "PublicEnrollmentCreate", "description": "<PERSON><PERSON>a cho tạo đăng ký khóa học"}, "PublicEnrollmentResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/EnrollmentData"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicEnrollmentResponse", "description": "Response cho API tạo đăng ký khóa học kèm thông tin thanh toán"}, "PublicEventBase": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID sự kiện"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> s<PERSON> kiện"}, "is_workshop": {"type": "boolean", "title": "Is Workshop", "description": "Là workshop hay không"}, "workshop_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workshop Type", "description": "Tên <PERSON> workshop"}, "date_begin": {"type": "string", "format": "date-time", "title": "Date Begin", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "date_end": {"type": "string", "format": "date-time", "title": "Date End", "description": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc"}, "duration_hours": {"type": "number", "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> (giờ)"}, "organizer": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organizer", "description": "<PERSON><PERSON> chức"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "Địa chỉ"}, "seats_available": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Seats Available", "description": "Số chỗ còn trống"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả chi tiết"}}, "type": "object", "required": ["id", "name", "is_workshop", "date_begin", "date_end", "duration_hours"], "title": "PublicEventBase", "description": "<PERSON><PERSON><PERSON> c<PERSON> bản công khai cho sự kiện"}, "PublicEventDetail": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID sự kiện"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> s<PERSON> kiện"}, "is_workshop": {"type": "boolean", "title": "Is Workshop", "description": "Là workshop hay không"}, "workshop_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workshop Type", "description": "Tên <PERSON> workshop"}, "date_begin": {"type": "string", "format": "date-time", "title": "Date Begin", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "date_end": {"type": "string", "format": "date-time", "title": "Date End", "description": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc"}, "duration_hours": {"type": "number", "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> (giờ)"}, "organizer": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Organizer", "description": "<PERSON><PERSON> chức"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "Địa chỉ"}, "seats_available": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Seats Available", "description": "Số chỗ còn trống"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả chi tiết"}, "presenters": {"items": {"$ref": "#/components/schemas/PublicPresenter"}, "type": "array", "title": "Presenters", "description": "<PERSON><PERSON> s<PERSON>ch ng<PERSON><PERSON> trình b<PERSON>y", "default": []}}, "type": "object", "required": ["id", "name", "is_workshop", "date_begin", "date_end", "duration_hours"], "title": "PublicEventDetail", "description": "<PERSON><PERSON><PERSON> chi tiết công khai sự kiện"}, "PublicEventDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/PublicEventDetail"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PublicEventDetailResponse", "description": "Response công khai cho chi tiết sự kiện"}, "PublicEventListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/PublicEventBase"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PublicEventListResponse", "description": "Response công khai cho danh sách sự kiện"}, "PublicEventRegistrationCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> tham dự"}, "email": {"type": "string", "format": "email", "title": "Email", "description": "<PERSON><PERSON> tham dự"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> điện thoại người tham dự"}, "company_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Company Name", "description": "<PERSON><PERSON>n công ty"}, "ticket_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Ticket Id", "description": "ID của vé sự kiện (nếu có)"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON> chú bổ sung"}}, "type": "object", "required": ["name", "email"], "title": "PublicEventRegistrationCreate", "description": "<PERSON><PERSON><PERSON> cho việc tạo đăng ký sự kiện công khai"}, "PublicEventRegistrationData": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID của đăng ký sự kiện"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> tham dự"}, "email": {"type": "string", "title": "Email", "description": "<PERSON><PERSON> tham dự"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> điện thoại người tham dự"}, "company_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Company Name", "description": "<PERSON><PERSON>n công ty"}, "event_name": {"type": "string", "title": "Event Name", "description": "<PERSON><PERSON><PERSON> s<PERSON> kiện"}, "event_date_begin": {"type": "string", "title": "Event Date Begin", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON>t đầu sự kiện"}, "event_date_end": {"type": "string", "title": "Event Date End", "description": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc sự kiện"}, "ticket_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ticket Name", "description": "<PERSON><PERSON><PERSON> vé"}, "state": {"type": "string", "title": "State", "description": "<PERSON>r<PERSON>ng thái đăng ký"}, "barcode": {"type": "string", "title": "Barcode", "description": "Mã vạch đăng ký"}}, "type": "object", "required": ["id", "name", "email", "event_name", "event_date_begin", "event_date_end", "state", "barcode"], "title": "PublicEventRegistrationData", "description": "<PERSON><PERSON>a cho dữ liệu đăng ký sự kiện trả về"}, "PublicEventRegistrationListItem": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID của đăng ký sự kiện"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> tham dự"}, "email": {"type": "string", "title": "Email", "description": "<PERSON><PERSON> tham dự"}, "state": {"type": "string", "title": "State", "description": "<PERSON>r<PERSON>ng thái đăng ký"}, "barcode": {"type": "string", "title": "Barcode", "description": "Mã vạch đăng ký"}}, "type": "object", "required": ["id", "name", "email", "state", "barcode"], "title": "PublicEventRegistrationListItem", "description": "Schema cho item trong danh sách đăng ký sự kiện"}, "PublicEventRegistrationListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/PublicEventRegistrationListItem"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PublicEventRegistrationListResponse", "description": "Response cho API lấy danh sách đăng ký sự kiện của người dùng"}, "PublicEventRegistrationResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/PublicEventRegistrationData"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PublicEventRegistrationResponse", "description": "Response cho API đăng ký sự kiện"}, "PublicInstructorBase": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID giảng viên"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> gi<PERSON>ng viên"}, "code": {"type": "string", "title": "Code", "description": "Mã giảng viên"}, "bio": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bio", "description": "<PERSON><PERSON><PERSON>u sử giảng viên"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url", "description": "URL hình ảnh giảng viên"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gender", "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h"}, "employment_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Employment Type", "description": "<PERSON><PERSON><PERSON> hình công vi<PERSON>c"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "<PERSON><PERSON> s<PERSON>ch các tag của giảng viên"}}, "type": "object", "required": ["id", "name", "code"], "title": "PublicInstructorBase", "description": "<PERSON><PERSON><PERSON> c<PERSON> bản cho thông tin giảng viên công khai"}, "PublicInstructorDetail": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID giảng viên"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> gi<PERSON>ng viên"}, "code": {"type": "string", "title": "Code", "description": "Mã giảng viên"}, "bio": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bio", "description": "<PERSON><PERSON><PERSON>u sử giảng viên"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url", "description": "URL hình ảnh giảng viên"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gender", "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h"}, "employment_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Employment Type", "description": "<PERSON><PERSON><PERSON> hình công vi<PERSON>c"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "tags": {"items": {"type": "string"}, "type": "array", "title": "Tags", "description": "<PERSON><PERSON> s<PERSON>ch các tag của giảng viên"}, "class_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Class Count", "description": "Số lượ<PERSON> lớp học đã dạy"}, "lesson_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lesson Count", "description": "Số lư<PERSON> bu<PERSON><PERSON> học đã dạy"}}, "type": "object", "required": ["id", "name", "code"], "title": "PublicInstructorDetail", "description": "<PERSON><PERSON><PERSON> chi tiết cho thông tin giảng viên công khai"}, "PublicInstructorDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/PublicInstructorDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicInstructorDetailResponse", "description": "Response cho chi tiết gi<PERSON>ng viên công khai"}, "PublicInstructorListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/PublicInstructorBase"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicInstructorListResponse", "description": "Response cho danh sách giảng viên công khai"}, "PublicLeadCreate": {"properties": {"full_name": {"type": "string", "title": "Full Name", "description": "<PERSON>ên đ<PERSON>y đủ học viên tiềm năng"}, "email": {"type": "string", "title": "Email", "description": "Đ<PERSON>a chỉ email học viên tiềm năng"}, "country_code": {"type": "string", "title": "Country Code", "description": "<PERSON><PERSON> quốc gia theo định dạng ISO (VN, US, ...)", "default": "VN"}, "phone_number": {"type": "string", "title": "Phone Number", "description": "<PERSON><PERSON> điện tho<PERSON><PERSON> không bao gồm mã quốc gia"}, "interest_description": {"type": "string", "title": "Interest Description", "description": "<PERSON><PERSON> cầu học tập và mong muốn tư vấn"}}, "type": "object", "required": ["full_name", "email", "phone_number", "interest_description"], "title": "PublicLeadCreate", "description": "<PERSON><PERSON><PERSON> cho tiếp nhận đăng ký tư vấn khóa học"}, "PublicLeadResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/LeadData"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicLeadResponse", "description": "Response cho API tiếp nhận đăng ký tư vấn khóa học"}, "PublicLocationBase": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID cơ sở"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> c<PERSON> sở"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code", "description": "Mã cơ sở"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "Địa chỉ"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City", "description": "<PERSON><PERSON><PERSON><PERSON> phố"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "description": "<PERSON><PERSON> l<PERSON>"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url", "description": "URL hình ảnh cơ sở"}, "room_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Room Count", "description": "Số lượng phòng học"}}, "type": "object", "required": ["id", "name"], "title": "PublicLocationBase", "description": "<PERSON><PERSON><PERSON> cơ bản cho thông tin cơ sở đào tạo công khai"}, "PublicLocationDetail": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID cơ sở"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> c<PERSON> sở"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code", "description": "Mã cơ sở"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "Địa chỉ"}, "city": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "City", "description": "<PERSON><PERSON><PERSON><PERSON> phố"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "description": "<PERSON><PERSON> l<PERSON>"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url", "description": "URL hình ảnh cơ sở"}, "room_count": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Room Count", "description": "Số lượng phòng học"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả chi tiết"}, "facilities": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Facilities", "description": "Cơ sở vật chất"}, "transportation": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Transportation", "description": "Hướng dẫn đi lại"}, "map_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Map Url", "description": "URL bản đồ"}, "working_hours": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Working Hours", "description": "<PERSON><PERSON><PERSON> làm việc"}}, "type": "object", "required": ["id", "name"], "title": "PublicLocationDetail", "description": "<PERSON><PERSON><PERSON> chi tiết cho cơ sở đào tạo công khai"}, "PublicLocationDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/PublicLocationDetail"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicLocationDetailResponse", "description": "Response cho chi tiết cơ sở đào tạo công khai"}, "PublicLocationListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/PublicLocationBase"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicLocationListResponse", "description": "Response cho danh sách cơ sở đào tạo công khai"}, "PublicPaymentStatusResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"$ref": "#/components/schemas/PaymentStatusData"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicPaymentStatusResponse", "description": "Response cho API kiểm tra trạng thái thanh toán"}, "PublicPresenter": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID người trình bày"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> trình b<PERSON>y"}}, "type": "object", "required": ["id", "name"], "title": "PublicPresenter", "description": "<PERSON><PERSON><PERSON> công khai cho người trình bày"}, "PublicRoomBase": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID phòng học"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> phò<PERSON> học"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code", "description": "Mã phòng học"}, "location_id": {"type": "integer", "title": "Location Id", "description": "ID cơ sở"}, "location_name": {"type": "string", "title": "Location Name", "description": "<PERSON><PERSON><PERSON> c<PERSON> sở"}, "capacity": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Capacity", "description": "<PERSON><PERSON><PERSON>"}, "room_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Room Type", "description": "Loại phòng"}, "floor": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Floor", "description": "<PERSON><PERSON><PERSON>"}, "image_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Image Url", "description": "URL hình ảnh phòng học"}}, "type": "object", "required": ["id", "name", "location_id", "location_name"], "title": "PublicRoomBase", "description": "<PERSON><PERSON><PERSON> c<PERSON> bản cho thông tin phòng học công khai"}, "PublicRoomListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/PublicRoomBase"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicRoomListResponse", "description": "Response cho danh sách phòng học công khai"}, "PublicSearchResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"items": {"$ref": "#/components/schemas/PublicCourseBase"}, "type": "array", "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "required": ["data"], "title": "PublicSearchResponse", "description": "Response cho kết quả tìm kiếm"}, "PublicWorkshopType": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID loại workshop"}, "name": {"type": "string", "title": "Name", "description": "Tên <PERSON> workshop"}, "code": {"type": "string", "title": "Code", "description": "Mã loại workshop"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Mô tả loại workshop"}, "color": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Color", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["id", "name", "code"], "title": "PublicWorkshopType", "description": "Schema công khai cho lo<PERSON>i workshop"}, "PublicWorkshopTypeListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"$ref": "#/components/schemas/PublicWorkshopType"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "PublicWorkshopTypeListResponse", "description": "Response công khai cho danh sách loại workshop"}, "QualificationAddRequest": {"properties": {"qualification_id": {"type": "integer", "title": "Qualification Id", "description": "ID bằng cấp"}, "institution": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Institution", "description": "Cơ sở đào tạo"}, "graduation_year": {"anyOf": [{"type": "integer", "maximum": 2030.0, "minimum": 1950.0}, {"type": "null"}], "title": "Graduation Year", "description": "<PERSON><PERSON><PERSON> t<PERSON>"}, "grade": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Grade", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["qualification_id"], "title": "QualificationAddRequest", "description": "Add qualification request schema."}, "QualificationInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID bằng cấp"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> bằng cấp"}, "institution": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Institution", "description": "Cơ sở đào tạo"}, "graduation_year": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Graduation Year", "description": "<PERSON><PERSON><PERSON> t<PERSON>"}, "grade": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Grade", "description": "<PERSON><PERSON><PERSON>"}, "is_verified": {"type": "boolean", "title": "Is Verified", "description": "Đ<PERSON> x<PERSON>c <PERSON>h", "default": false}}, "type": "object", "required": ["id", "name"], "title": "QualificationInfo", "description": "Qualification information schema."}, "RefundRequestActionResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "state": {"type": "string", "title": "State"}, "message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["id", "state", "message"], "title": "RefundRequestActionResponse", "description": "<PERSON><PERSON><PERSON> phản hồi thực hiện hành động trên yêu cầu hoàn phí."}, "RefundRequestCreate": {"properties": {"student_id": {"type": "integer", "title": "Student Id", "description": "<PERSON> c<PERSON>a h<PERSON> viên"}, "enrollment_id": {"type": "integer", "title": "Enrollment Id", "description": "ID của đăng ký khóa học"}, "request_amount": {"type": "number", "title": "Request Amount", "description": "<PERSON><PERSON> tiền yêu cầu hoàn"}, "reason": {"type": "string", "title": "Reason", "description": "<PERSON><PERSON> do yêu cầu hoàn phí"}, "source": {"type": "string", "title": "Source", "description": "<PERSON><PERSON><PERSON><PERSON> yêu cầu (app, web, direct, phone, email, system, other)", "default": "app"}, "refund_method": {"type": "string", "title": "Refund Method", "description": "<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền (original, bank_transfer, cash, credit, other)", "default": "original"}, "refund_method_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refund Method Details", "description": "<PERSON> tiết p<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền"}, "api_request_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Request Id", "description": "ID yêu cầu từ API"}, "supporting_documents": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Supporting Documents", "description": "<PERSON><PERSON> s<PERSON>ch ID của các tài liệu hỗ trợ"}}, "type": "object", "required": ["student_id", "enrollment_id", "request_amount", "reason"], "title": "RefundRequestCreate", "description": "<PERSON><PERSON>a cho việc tạo yêu c<PERSON>u hoàn phí."}, "RefundRequestCreateResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "message": {"type": "string", "title": "Message", "default": "<PERSON><PERSON><PERSON> cầu hoàn phí đã đư<PERSON>c tạo thành công"}}, "type": "object", "required": ["id", "name"], "title": "RefundRequestCreateResponse", "description": "<PERSON><PERSON><PERSON> ph<PERSON>n hồi tạo yêu cầu hoàn phí."}, "RefundRequestDetail": {"properties": {"student_id": {"type": "integer", "title": "Student Id", "description": "<PERSON> c<PERSON>a h<PERSON> viên"}, "enrollment_id": {"type": "integer", "title": "Enrollment Id", "description": "ID của đăng ký khóa học"}, "request_amount": {"type": "number", "title": "Request Amount", "description": "<PERSON><PERSON> tiền yêu cầu hoàn"}, "reason": {"type": "string", "title": "Reason", "description": "<PERSON><PERSON> do yêu cầu hoàn phí"}, "source": {"type": "string", "title": "Source", "description": "<PERSON><PERSON><PERSON><PERSON> yêu cầu (app, web, direct, phone, email, system, other)", "default": "app"}, "refund_method": {"type": "string", "title": "Refund Method", "description": "<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền (original, bank_transfer, cash, credit, other)", "default": "original"}, "refund_method_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refund Method Details", "description": "<PERSON> tiết p<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền"}, "id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "student_name": {"type": "string", "title": "Student Name"}, "course_id": {"type": "integer", "title": "Course Id"}, "course_name": {"type": "string", "title": "Course Name"}, "request_date": {"type": "string", "format": "date-time", "title": "Request Date"}, "state": {"type": "string", "title": "State"}, "approved_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Approved Amount"}, "rejection_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Rejection Reason"}, "review_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Review Date"}, "approval_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Approval Date"}, "refund_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Refund Date"}, "supporting_documents": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "title": "Supporting Documents"}}, "type": "object", "required": ["student_id", "enrollment_id", "request_amount", "reason", "id", "name", "student_name", "course_id", "course_name", "request_date", "state"], "title": "RefundRequestDetail", "description": "<PERSON><PERSON>a chi tiết cho yêu cầu hoàn phí."}, "RefundRequestDetailResponse": {"properties": {"data": {"$ref": "#/components/schemas/RefundRequestDetail"}}, "type": "object", "required": ["data"], "title": "RefundRequestDetailResponse", "description": "<PERSON><PERSON><PERSON> ph<PERSON>n hồi chi tiết yêu cầu hoàn phí."}, "RefundRequestList": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "student_name": {"type": "string", "title": "Student Name"}, "course_name": {"type": "string", "title": "Course Name"}, "request_date": {"type": "string", "format": "date-time", "title": "Request Date"}, "request_amount": {"type": "number", "title": "Request Amount"}, "approved_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Approved Amount"}, "state": {"type": "string", "title": "State"}}, "type": "object", "required": ["id", "name", "student_name", "course_name", "request_date", "request_amount", "state"], "title": "RefundRequestList", "description": "<PERSON><PERSON><PERSON> danh sách yêu c<PERSON>u hoàn phí."}, "RefundRequestListResponse": {"properties": {"total": {"type": "integer", "title": "Total"}, "page": {"type": "integer", "title": "Page"}, "per_page": {"type": "integer", "title": "Per Page"}, "items": {"items": {"$ref": "#/components/schemas/RefundRequestList"}, "type": "array", "title": "Items"}}, "type": "object", "required": ["total", "page", "per_page", "items"], "title": "RefundRequestListResponse", "description": "<PERSON><PERSON><PERSON> ph<PERSON>n hồi danh sách yêu cầu hoàn phí."}, "RefundRequestUpdate": {"properties": {"request_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Request Amount", "description": "<PERSON><PERSON> tiền yêu cầu hoàn"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason", "description": "<PERSON><PERSON> do yêu cầu hoàn phí"}, "refund_method": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refund Method", "description": "<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền"}, "refund_method_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refund Method Details", "description": "<PERSON> tiết p<PERSON><PERSON><PERSON><PERSON> thức hoàn tiền"}, "supporting_documents": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Supporting Documents", "description": "<PERSON><PERSON> s<PERSON>ch ID của các tài liệu hỗ trợ"}}, "type": "object", "title": "RefundRequestUpdate", "description": "<PERSON><PERSON><PERSON> cho vi<PERSON><PERSON> cập nhật yêu cầu hoàn phí."}, "RefundRequestUpdateResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "message": {"type": "string", "title": "Message", "default": "<PERSON><PERSON><PERSON> cầu hoàn phí đã đư<PERSON><PERSON> cập nhật thành công"}}, "type": "object", "required": ["id"], "title": "RefundRequestUpdateResponse", "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>n hồi cập nhật yêu cầu hoàn phí."}, "RefundStatus": {"properties": {"is_refunded": {"type": "boolean", "title": "Is Refunded", "description": "<PERSON><PERSON> hoàn tiền hay chưa", "default": false}, "refund_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Refund Date", "description": "<PERSON><PERSON><PERSON> ti<PERSON>n"}, "refund_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Refund Amount", "description": "<PERSON><PERSON> tiền hoàn"}, "refund_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refund Reason", "description": "Lý do hoàn tiền"}, "refund_state": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refund State", "description": "<PERSON>r<PERSON><PERSON> thái hoàn tiền"}, "refund_reference": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refund Reference", "description": "<PERSON><PERSON> tham chiếu hoàn tiền"}}, "type": "object", "title": "RefundStatus"}, "ResponseBase": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ResponseBase", "description": "Base model cho tất cả API response"}, "ResponseBase_Dict_str__Any__": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ResponseBase[Dict[str, Any]]"}, "ResponseBase_List_Dict_str__Any___": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "title": "Data"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ResponseBase[List[Dict[str, Any]]]"}, "ResponseBase_StudentAssessmentsResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentAssessmentsResponse"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ResponseBase[StudentAssessmentsResponse]"}, "ResponseBase_StudentGradeOverviewResponse_": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentGradeOverviewResponse"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "ResponseBase[StudentGradeOverviewResponse]"}, "ScheduleItem": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> lị<PERSON> h<PERSON>"}, "type": {"type": "string", "title": "Type", "description": "Loại: lesson|meeting|break|other"}, "title": {"type": "string", "title": "Title", "description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON>"}, "start_datetime": {"type": "string", "format": "date-time", "title": "Start Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "end_datetime": {"type": "string", "format": "date-time", "title": "End Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc"}, "duration_hours": {"type": "number", "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> (giờ)"}, "room": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location", "description": "<PERSON><PERSON><PERSON>"}, "class_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Class Id", "description": "<PERSON> lớp h<PERSON>c"}, "class_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "lesson_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lesson Id", "description": "<PERSON> b<PERSON><PERSON> h<PERSON>c"}, "status": {"type": "string", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "is_confirmed": {"type": "boolean", "title": "Is Confirmed", "description": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "default": true}, "is_recurring": {"type": "boolean", "title": "Is Recurring", "description": "Lặp lại", "default": false}, "has_conflict": {"type": "boolean", "title": "Has Conflict", "description": "<PERSON><PERSON>ung đột", "default": false}, "conflict_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Conflict Reason", "description": "Lý do xung đột"}}, "type": "object", "required": ["id", "type", "title", "start_datetime", "end_datetime", "duration_hours", "status"], "title": "ScheduleItem", "description": "Individual schedule item schema."}, "SkillAddRequest": {"properties": {"skill_id": {"type": "integer", "title": "Skill Id", "description": "ID kỹ năng"}, "level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Level", "description": "<PERSON><PERSON><PERSON> độ kỹ năng"}, "certification_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Certification Date", "description": "<PERSON><PERSON><PERSON>n"}, "is_primary": {"type": "boolean", "title": "Is Primary", "description": "<PERSON><PERSON> n<PERSON>ng ch<PERSON>h", "default": false}}, "type": "object", "required": ["skill_id"], "title": "SkillAddRequest", "description": "Add skill request schema."}, "SkillInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID kỹ năng"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> k<PERSON> n<PERSON>ng"}, "level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Level", "description": "<PERSON><PERSON><PERSON> độ kỹ năng"}, "certification_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Certification Date", "description": "<PERSON><PERSON><PERSON>n"}, "is_primary": {"type": "boolean", "title": "Is Primary", "description": "<PERSON><PERSON> n<PERSON>ng ch<PERSON>h", "default": false}}, "type": "object", "required": ["id", "name"], "title": "SkillInfo", "description": "Skill information schema."}, "SkillProgress": {"properties": {"skill_name": {"type": "string", "title": "Skill Name", "description": "<PERSON><PERSON><PERSON> k<PERSON> n<PERSON>ng"}, "current_level": {"type": "string", "title": "Current Level", "description": "<PERSON><PERSON><PERSON> độ hiện tại"}, "target_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Target Level", "description": "<PERSON><PERSON><PERSON> độ mục tiêu"}, "progress_percentage": {"type": "number", "title": "Progress Percentage", "description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> (%)", "default": 0.0}, "total_assessments": {"type": "integer", "title": "Total Assessments", "description": "Tổng đ<PERSON>h giá", "default": 0}, "passed_assessments": {"type": "integer", "title": "Passed Assessments", "description": "<PERSON><PERSON><PERSON> giá đ<PERSON>t", "default": 0}, "average_assessment_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Assessment Score", "description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>h giá trung bình"}, "practice_hours": {"type": "number", "title": "Practice Hours", "description": "<PERSON><PERSON><PERSON> thực hành", "default": 0.0}, "practice_sessions": {"type": "integer", "title": "Practice Sessions", "description": "<PERSON><PERSON><PERSON> th<PERSON> h<PERSON>nh", "default": 0}, "mastery_level": {"type": "string", "title": "Mastery Level", "description": "<PERSON><PERSON><PERSON> độ thành thạo"}, "improvement_rate": {"type": "string", "title": "Improvement Rate", "description": "<PERSON><PERSON><PERSON> độ cải thiện"}}, "type": "object", "required": ["skill_name", "current_level", "mastery_level", "improvement_rate"], "title": "SkillProgress", "description": "Skill progress schema."}, "SortOrder": {"type": "string", "enum": ["asc", "desc"], "title": "SortOrder", "description": "Sort order options."}, "StudentAcademicInfo": {"properties": {"education_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Education Level", "description": "<PERSON><PERSON><PERSON><PERSON> độ học vấn"}, "school_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "School Name", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "graduation_year": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Graduation Year", "description": "<PERSON><PERSON><PERSON> t<PERSON>"}, "major": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Major", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "gpa": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Gpa", "description": "Điểm GPA"}, "english_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "English Level", "description": "<PERSON><PERSON><PERSON><PERSON> độ tiếng <PERSON>h"}}, "type": "object", "title": "StudentAcademicInfo", "description": "Student academic information schema."}, "StudentAssessmentsResponse": {"properties": {"class_id": {"type": "integer", "title": "Class Id", "description": "<PERSON> lớp h<PERSON>c"}, "class_name": {"type": "string", "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "grade_summary": {"$ref": "#/components/schemas/GradeSummarySchema", "description": "<PERSON><PERSON><PERSON> tắt điểm số"}, "assessments": {"items": {"$ref": "#/components/schemas/AssessmentDetailSchema"}, "type": "array", "title": "Assessments", "description": "<PERSON><PERSON> s<PERSON>ch đ<PERSON>h giá chi tiết"}}, "type": "object", "required": ["class_id", "class_name", "course_name", "grade_summary", "assessments"], "title": "StudentAssessmentsResponse", "description": "<PERSON><PERSON>a cho response danh sách đ<PERSON>h giá của học viên"}, "StudentAssignment": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> bài tập"}, "title": {"type": "string", "title": "Title", "description": "<PERSON><PERSON><PERSON><PERSON> đề bài tập"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> tả bài tập"}, "instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Instructions", "description": "Hướng dẫn làm bài"}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date", "description": "<PERSON><PERSON><PERSON>"}, "is_overdue": {"type": "boolean", "title": "Is Overdue", "description": "<PERSON><PERSON><PERSON> h<PERSON>n", "default": false}, "days_until_due": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Days Until Due", "description": "<PERSON><PERSON> ngày còn lại"}, "submission_status": {"type": "string", "title": "Submission Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái n<PERSON> bài"}, "submitted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Submitted At", "description": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON>"}, "submission_file_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Submission File Url", "description": "Link file nộp"}, "submission_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Submission Text", "description": "<PERSON><PERSON><PERSON> dung nộp"}, "max_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Max Score", "description": "<PERSON><PERSON><PERSON><PERSON> tối đa"}, "score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Score", "description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON>"}, "grade": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Grade", "description": "<PERSON><PERSON><PERSON>"}, "feedback": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hồi từ giảng viên"}, "graded_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Graded At", "description": "<PERSON><PERSON><PERSON><PERSON> gian chấm điểm"}, "can_submit": {"type": "boolean", "title": "Can Submit", "description": "<PERSON><PERSON> thể nộp bài", "default": false}, "can_resubmit": {"type": "boolean", "title": "Can Resubmit", "description": "<PERSON><PERSON> thể nộp lại", "default": false}}, "type": "object", "required": ["id", "title", "submission_status"], "title": "StudentAssignment", "description": "Student assignment schema."}, "StudentCertificateDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/CertificateDetail"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "StudentCertificateDetailResponse", "description": "Student certificate detail response schema."}, "StudentCertificateListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "<PERSON>r<PERSON><PERSON> thái thành công", "default": true}, "message": {"type": "string", "title": "Message", "description": "<PERSON><PERSON><PERSON><PERSON> báo", "default": "<PERSON><PERSON> liệu đ<PERSON><PERSON><PERSON> tải thành công"}, "data": {"items": {"$ref": "#/components/schemas/CertificateBasicInfo"}, "type": "array", "title": "Data", "description": "<PERSON><PERSON> liệu"}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata", "description": "Thông tin phân trang"}}, "type": "object", "required": ["data", "pagination"], "title": "StudentCertificateListResponse", "description": "Student certificate list response schema."}, "StudentCertificateSummary": {"properties": {"student_id": {"type": "integer", "title": "Student Id", "description": "<PERSON> học viên"}, "student_name": {"type": "string", "title": "Student Name", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên"}, "statistics": {"$ref": "#/components/schemas/CertificateStatistics", "description": "<PERSON><PERSON><PERSON><PERSON> kê chứng chỉ"}, "recent_certificates": {"items": {"$ref": "#/components/schemas/CertificateBasicInfo"}, "type": "array", "title": "Recent Certificates", "description": "<PERSON><PERSON><PERSON> chỉ gần đây"}, "pending_certificates": {"items": {"$ref": "#/components/schemas/CertificateBasicInfo"}, "type": "array", "title": "Pending Certificates", "description": "<PERSON><PERSON><PERSON> chỉ sắp đ<PERSON><PERSON><PERSON> cấp"}, "highest_grade": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Highest Grade", "description": "<PERSON><PERSON><PERSON><PERSON> cao nh<PERSON>t"}, "best_certificate": {"anyOf": [{"$ref": "#/components/schemas/CertificateBasicInfo"}, {"type": "null"}], "description": "<PERSON><PERSON><PERSON> chỉ xuất sắc nhất"}, "next_certificate_progress": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Next Certificate Progress", "description": "Ti<PERSON><PERSON> độ chứng chỉ tiếp theo"}, "next_certificate_course": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Next Certificate Course", "description": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>c chứng chỉ tiếp theo"}}, "type": "object", "required": ["student_id", "student_name", "statistics"], "title": "StudentCertificateSummary", "description": "Student certificate summary schema."}, "StudentClass": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> lớp h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "status": {"type": "string", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái lớp"}, "course": {"$ref": "#/components/schemas/CourseBasicInfo", "description": "Thông tin khóa học"}, "primary_instructor": {"$ref": "#/components/schemas/InstructorBasicInfo", "description": "<PERSON><PERSON><PERSON><PERSON> vi<PERSON><PERSON>"}, "assistant_instructors": {"items": {"$ref": "#/components/schemas/InstructorBasicInfo"}, "type": "array", "title": "Assistant Instructors", "description": "Giảng viên phụ"}, "start_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u"}, "end_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "End Date", "description": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c"}, "schedules": {"items": {"$ref": "#/components/schemas/odoo__addons__eb_lms__schemas__students__course_schemas__ClassScheduleInfo"}, "type": "array", "title": "Schedules", "description": "<PERSON><PERSON><PERSON>"}, "total_students": {"type": "integer", "title": "Total Students", "description": "Tổng số học viên", "default": 0}, "max_students": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Max Students", "description": "<PERSON><PERSON> học viên tối đa"}, "enrollment": {"$ref": "#/components/schemas/StudentEnrollmentDetail", "description": "Thông tin đăng ký"}, "can_access": {"type": "boolean", "title": "Can Access", "description": "<PERSON><PERSON> thể truy cập", "default": true}, "is_current": {"type": "boolean", "title": "Is Current", "description": "<PERSON><PERSON>", "default": false}, "is_completed": {"type": "boolean", "title": "Is Completed", "description": "<PERSON><PERSON> hoàn thành", "default": false}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "<PERSON><PERSON><PERSON>"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}}, "type": "object", "required": ["id", "name", "code", "status", "course", "primary_instructor", "enrollment"], "title": "StudentClass", "description": "Student class information schema."}, "StudentClassDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentClass"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "StudentClassDetailResponse", "description": "Student class detail response schema."}, "StudentClassListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "<PERSON>r<PERSON><PERSON> thái thành công", "default": true}, "message": {"type": "string", "title": "Message", "description": "<PERSON><PERSON><PERSON><PERSON> báo", "default": "<PERSON><PERSON> liệu đ<PERSON><PERSON><PERSON> tải thành công"}, "data": {"items": {"$ref": "#/components/schemas/StudentClassSummary"}, "type": "array", "title": "Data", "description": "<PERSON><PERSON> liệu"}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata", "description": "Thông tin phân trang"}}, "type": "object", "required": ["data", "pagination"], "title": "StudentClassListResponse", "description": "Student class list response schema."}, "StudentClassSummary": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> lớp h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON>"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> lớ<PERSON> h<PERSON>c"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "instructor_name": {"type": "string", "title": "Instructor Name", "description": "<PERSON><PERSON><PERSON> gi<PERSON>ng viên"}, "progress_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Progress Percentage", "description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> (%)"}, "attendance_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Attendance Rate", "description": "Tỷ lệ điểm danh (%)"}, "current_grade": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Current Grade", "description": "<PERSON><PERSON><PERSON><PERSON> hiện tại"}, "next_lesson_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Lesson Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> ti<PERSON> theo"}, "schedule_summary": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Schedule Summary", "description": "<PERSON><PERSON><PERSON> t<PERSON> lị<PERSON> h<PERSON>c"}, "enrollment_status": {"type": "string", "title": "Enrollment Status", "description": "<PERSON>r<PERSON>ng thái đăng ký"}, "payment_status": {"type": "string", "title": "Payment Status", "description": "<PERSON>r<PERSON><PERSON> thái thanh toán"}, "is_current": {"type": "boolean", "title": "Is Current", "description": "<PERSON><PERSON>", "default": false}, "needs_attention": {"type": "boolean", "title": "Needs Attention", "description": "<PERSON><PERSON><PERSON> ch<PERSON>", "default": false}, "attention_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Attention Reason", "description": "Lý do cần chú <PERSON>"}}, "type": "object", "required": ["id", "name", "code", "course_name", "instructor_name", "enrollment_status", "payment_status"], "title": "StudentClassSummary", "description": "Student class summary for list view."}, "StudentContactInfo": {"properties": {"phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "Địa chỉ"}, "parent_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Name", "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> huynh"}, "parent_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Phone", "description": "SĐT phụ huynh"}, "emergency_contact": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergency Contact", "description": "<PERSON><PERSON><PERSON> h<PERSON> khẩn cấp"}, "emergency_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergency Phone", "description": "SĐT k<PERSON><PERSON>n cấp"}}, "type": "object", "title": "StudentContactInfo", "description": "Student contact information schema."}, "StudentDashboardResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentDashboardStats"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "StudentDashboardResponse", "description": "Student dashboard response schema."}, "StudentDashboardStats": {"properties": {"total_courses": {"type": "integer", "title": "Total Courses", "description": "Tổng kh<PERSON><PERSON> h<PERSON>c", "default": 0}, "active_courses": {"type": "integer", "title": "Active Courses", "description": "<PERSON><PERSON><PERSON><PERSON> học đang học", "default": 0}, "completed_courses": {"type": "integer", "title": "Completed Courses", "description": "<PERSON><PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "overall_progress": {"type": "number", "title": "Overall Progress", "description": "<PERSON><PERSON><PERSON><PERSON> độ tổng thể (%)", "default": 0.0}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> h<PERSON>c", "default": 0}, "completed_lessons": {"type": "integer", "title": "Completed Lessons", "description": "<PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "upcoming_lessons": {"type": "integer", "title": "Upcoming Lessons", "description": "<PERSON><PERSON><PERSON> h<PERSON> sắp tới", "default": 0}, "lessons_today": {"type": "integer", "title": "Lessons Today", "description": "<PERSON><PERSON><PERSON> h<PERSON> hôm nay", "default": 0}, "attendance_rate": {"type": "number", "title": "Attendance Rate", "description": "Tỷ lệ điểm danh (%)", "default": 0.0}, "average_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Score", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình"}, "total_study_hours": {"type": "number", "title": "Total Study Hours", "description": "Tổng g<PERSON> học", "default": 0.0}, "this_month_hours": {"type": "number", "title": "This Month Hours", "description": "<PERSON><PERSON><PERSON> h<PERSON> tháng này", "default": 0.0}, "certificates_earned": {"type": "integer", "title": "Certificates Earned", "description": "Chứng chỉ đã đạt", "default": 0}, "last_lesson_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Last Lesson Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> gầ<PERSON> n<PERSON>t"}, "next_lesson_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Next Lesson Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> ti<PERSON> theo"}, "overdue_assignments": {"type": "integer", "title": "Overdue Assignments", "description": "<PERSON><PERSON><PERSON> tập quá hạn", "default": 0}, "missed_lessons": {"type": "integer", "title": "Missed <PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> h<PERSON>c bỏ lỡ", "default": 0}, "needs_attention": {"type": "boolean", "title": "Needs Attention", "description": "<PERSON><PERSON><PERSON> ch<PERSON>", "default": false}}, "type": "object", "title": "StudentDashboardStats", "description": "Student dashboard statistics schema."}, "StudentDocumentListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "<PERSON>r<PERSON><PERSON> thái thành công", "default": true}, "message": {"type": "string", "title": "Message", "description": "<PERSON><PERSON><PERSON><PERSON> báo", "default": "<PERSON><PERSON> liệu đ<PERSON><PERSON><PERSON> tải thành công"}, "data": {"items": {"$ref": "#/components/schemas/DocumentBasicInfo"}, "type": "array", "title": "Data", "description": "<PERSON><PERSON> liệu"}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata", "description": "Thông tin phân trang"}}, "type": "object", "required": ["data", "pagination"], "title": "StudentDocumentListResponse", "description": "Student document list response schema."}, "StudentEnrollmentDetail": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID đăng ký"}, "enrollment_date": {"type": "string", "format": "date", "title": "Enrollment Date", "description": "<PERSON><PERSON><PERSON> ký"}, "status": {"type": "string", "title": "Status", "description": "<PERSON>r<PERSON>ng thái đăng ký"}, "payment_status": {"type": "string", "title": "Payment Status", "description": "<PERSON>r<PERSON><PERSON> thái thanh toán"}, "progress_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Progress Percentage", "description": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> (%)"}, "lessons_completed": {"type": "integer", "title": "Lessons Completed", "description": "<PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> h<PERSON>c", "default": 0}, "attendance_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Attendance Rate", "description": "Tỷ lệ điểm danh (%)"}, "current_grade": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Current Grade", "description": "<PERSON><PERSON><PERSON><PERSON> hiện tại"}, "average_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Score", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình"}, "start_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Start Date", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON><PERSON> h<PERSON>c"}, "expected_completion_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Expected Completion Date", "description": "<PERSON><PERSON><PERSON> dự kiến hoàn thành"}, "actual_completion_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Actual Completion Date", "description": "<PERSON><PERSON><PERSON> hoàn thành thực tế"}}, "type": "object", "required": ["id", "enrollment_date", "status", "payment_status"], "title": "StudentEnrollmentDetail", "description": "Detailed enrollment information for student."}, "StudentGradeOverviewResponse": {"properties": {"student_id": {"type": "integer", "title": "Student Id", "description": "<PERSON> học viên"}, "student_name": {"type": "string", "title": "Student Name", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên"}, "overall_grade": {"type": "number", "title": "Overall Grade", "description": "<PERSON><PERSON><PERSON><PERSON> tổng kết"}, "overall_average": {"type": "number", "title": "Overall Average", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình tổng"}, "total_assessments": {"type": "integer", "title": "Total Assessments", "description": "Tổng số đánh giá"}, "completed_assessments": {"type": "integer", "title": "Completed Assessments", "description": "S<PERSON> đánh giá đã hoàn thành"}, "passed_assessments": {"type": "integer", "title": "Passed Assessments", "description": "Số đánh giá đã đạt"}, "overall_pass_rate": {"type": "number", "title": "Overall Pass Rate", "description": "Tỷ lệ đạt tổng (%)"}, "type_stats": {"items": {"$ref": "#/components/schemas/AssessmentTypeStatsSchema"}, "type": "array", "title": "Type Stats", "description": "<PERSON><PERSON><PERSON><PERSON> kê theo loại đánh giá"}, "recent_assessments": {"items": {"$ref": "#/components/schemas/AssessmentDetailSchema"}, "type": "array", "title": "Recent Assessments", "description": "<PERSON><PERSON><PERSON> giá gần đây"}}, "type": "object", "required": ["student_id", "student_name", "overall_grade", "overall_average", "total_assessments", "completed_assessments", "passed_assessments", "overall_pass_rate", "type_stats", "recent_assessments"], "title": "StudentGradeOverviewResponse", "description": "<PERSON><PERSON><PERSON> cho tổng quan điểm số của học viên"}, "StudentInvoice": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID hóa đơn"}, "invoice_number": {"type": "string", "title": "Invoice Number", "description": "S<PERSON> hóa đơn"}, "invoice_date": {"type": "string", "format": "date", "title": "Invoice Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>n"}, "due_date": {"type": "string", "format": "date", "title": "Due Date", "description": "<PERSON><PERSON><PERSON>h to<PERSON>"}, "class_id": {"type": "integer", "title": "Class Id", "description": "<PERSON> lớp h<PERSON>c"}, "class_name": {"type": "string", "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "items": {"items": {"$ref": "#/components/schemas/InvoiceItem"}, "type": "array", "title": "Items", "description": "<PERSON><PERSON><PERSON> h<PERSON> đơn"}, "subtotal": {"type": "string", "title": "Subtotal", "description": "Tổng phụ"}, "discount_total": {"type": "string", "title": "Discount Total", "description": "Tổng giảm giá", "default": "0"}, "tax_total": {"type": "string", "title": "Tax Total", "description": "<PERSON><PERSON><PERSON> thuế", "default": "0"}, "total_amount": {"type": "string", "title": "Total Amount", "description": "<PERSON><PERSON><PERSON> tiền"}, "paid_amount": {"type": "string", "title": "<PERSON><PERSON>", "description": "<PERSON><PERSON> thanh toán", "default": "0"}, "outstanding_amount": {"type": "string", "title": "Outstanding Amount", "description": "<PERSON><PERSON><PERSON> nợ"}, "payment_status": {"type": "string", "title": "Payment Status", "description": "<PERSON>r<PERSON><PERSON> thái thanh toán"}, "payments": {"items": {"$ref": "#/components/schemas/PaymentRecord"}, "type": "array", "title": "Payments", "description": "<PERSON><PERSON><PERSON> sử thanh toán"}, "is_overdue": {"type": "boolean", "title": "Is Overdue", "description": "<PERSON><PERSON><PERSON> h<PERSON>n", "default": false}, "days_overdue": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Days Overdue", "description": "<PERSON><PERSON> ngày quá hạn"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "<PERSON><PERSON><PERSON>"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}}, "type": "object", "required": ["id", "invoice_number", "invoice_date", "due_date", "class_id", "class_name", "course_name", "items", "subtotal", "total_amount", "outstanding_amount", "payment_status", "created_at", "updated_at"], "title": "StudentInvoice", "description": "Student invoice schema."}, "StudentInvoiceDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentInvoice"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "StudentInvoiceDetailResponse", "description": "Student invoice detail response schema."}, "StudentInvoiceListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "<PERSON>r<PERSON><PERSON> thái thành công", "default": true}, "message": {"type": "string", "title": "Message", "description": "<PERSON><PERSON><PERSON><PERSON> báo", "default": "<PERSON><PERSON> liệu đ<PERSON><PERSON><PERSON> tải thành công"}, "data": {"items": {"$ref": "#/components/schemas/StudentInvoice"}, "type": "array", "title": "Data", "description": "<PERSON><PERSON> liệu"}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata", "description": "Thông tin phân trang"}}, "type": "object", "required": ["data", "pagination"], "title": "StudentInvoiceListResponse", "description": "Student invoice list response schema."}, "StudentLessonDetail": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> b<PERSON><PERSON> h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "lesson_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lesson Number", "description": "<PERSON><PERSON> thứ tự"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> t<PERSON> b<PERSON><PERSON> h<PERSON>c"}, "class_id": {"type": "integer", "title": "Class Id", "description": "<PERSON> lớp h<PERSON>c"}, "class_name": {"type": "string", "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "instructor_name": {"type": "string", "title": "Instructor Name", "description": "<PERSON><PERSON><PERSON> gi<PERSON>ng viên"}, "instructor_id": {"type": "integer", "title": "Inst<PERSON>ctor Id", "description": "ID giảng viên"}, "start_datetime": {"type": "string", "format": "date-time", "title": "Start Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "end_datetime": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc"}, "duration_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> (giờ)"}, "room": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location", "description": "<PERSON><PERSON><PERSON>"}, "objectives": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Objectives", "description": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> b<PERSON><PERSON> học"}, "content_outline": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content Outline", "description": "<PERSON><PERSON><PERSON> dung ch<PERSON>h"}, "homework_assignment": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Homework Assignment", "description": "<PERSON><PERSON><PERSON> tập về nhà"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}, "materials": {"items": {"$ref": "#/components/schemas/StudentLessonMaterial"}, "type": "array", "title": "Materials", "description": "<PERSON><PERSON><PERSON> li<PERSON> bà<PERSON> h<PERSON>c"}, "assignments": {"items": {"$ref": "#/components/schemas/StudentAssignment"}, "type": "array", "title": "Assignments", "description": "<PERSON><PERSON><PERSON> t<PERSON>p"}, "attendance_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Attendance Status", "description": "<PERSON>r<PERSON><PERSON> thái điểm danh"}, "check_in_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Check In Time", "description": "Thời gian check-in"}, "check_out_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Check Out Time", "description": "Th<PERSON>i gian check-out"}, "participation_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Participation Notes", "description": "<PERSON>hi chú tham gia"}, "lesson_status": {"type": "string", "title": "Lesson Status", "description": "<PERSON>r<PERSON><PERSON> thái bài học"}, "can_access": {"type": "boolean", "title": "Can Access", "description": "<PERSON><PERSON> thể truy cập", "default": false}, "can_check_in": {"type": "boolean", "title": "Can Check In", "description": "<PERSON><PERSON> thể check-in", "default": false}, "is_live": {"type": "boolean", "title": "Is Live", "description": "<PERSON><PERSON> ra", "default": false}, "is_completed": {"type": "boolean", "title": "Is Completed", "description": "<PERSON><PERSON> hoàn thành", "default": false}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "<PERSON><PERSON><PERSON>"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}}, "type": "object", "required": ["id", "name", "class_id", "class_name", "course_name", "instructor_name", "instructor_id", "start_datetime", "lesson_status"], "title": "StudentLessonDetail", "description": "Detailed student lesson information schema."}, "StudentLessonDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentLessonDetail"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "StudentLessonDetailResponse", "description": "Student lesson detail response schema."}, "StudentLessonListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "<PERSON>r<PERSON><PERSON> thái thành công", "default": true}, "message": {"type": "string", "title": "Message", "description": "<PERSON><PERSON><PERSON><PERSON> báo", "default": "<PERSON><PERSON> liệu đ<PERSON><PERSON><PERSON> tải thành công"}, "data": {"items": {"$ref": "#/components/schemas/StudentLessonSummary"}, "type": "array", "title": "Data", "description": "<PERSON><PERSON> liệu"}, "pagination": {"$ref": "#/components/schemas/PaginationMetadata", "description": "Thông tin phân trang"}}, "type": "object", "required": ["data", "pagination"], "title": "StudentLessonListResponse", "description": "Student lesson list response schema."}, "StudentLessonMaterial": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID tài liệu"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> tài li<PERSON>u"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON>"}, "file_type": {"type": "string", "title": "File Type", "description": "Loại file"}, "file_size": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "File Size", "description": "<PERSON><PERSON><PERSON> th<PERSON> file (bytes)"}, "is_downloadable": {"type": "boolean", "title": "Is Downloadable", "description": "<PERSON><PERSON> thể tải về", "default": true}, "is_required": {"type": "boolean", "title": "Is Required", "description": "<PERSON><PERSON><PERSON> l<PERSON> b<PERSON><PERSON> bu<PERSON>", "default": false}, "download_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Download Url", "description": "<PERSON> t<PERSON> về"}, "view_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "View Url", "description": "Link xem online"}, "upload_date": {"type": "string", "format": "date-time", "title": "Upload Date", "description": "Ngày upload"}, "downloaded_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Downloaded At", "description": "<PERSON>h<PERSON>i gian đã tải"}, "view_count": {"type": "integer", "title": "View Count", "description": "Số lần xem", "default": 0}}, "type": "object", "required": ["id", "name", "file_type", "upload_date"], "title": "StudentLessonMaterial", "description": "Student lesson material schema."}, "StudentLessonSummary": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> b<PERSON><PERSON> h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>"}, "lesson_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lesson Number", "description": "<PERSON><PERSON> thứ tự"}, "class_name": {"type": "string", "title": "Class Name", "description": "<PERSON><PERSON><PERSON>"}, "instructor_name": {"type": "string", "title": "Instructor Name", "description": "<PERSON><PERSON><PERSON> gi<PERSON>ng viên"}, "start_datetime": {"type": "string", "format": "date-time", "title": "Start Datetime", "description": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu"}, "duration_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>"}, "room": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}, "attendance_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Attendance Status", "description": "<PERSON>r<PERSON><PERSON> thái điểm danh"}, "has_assignments": {"type": "boolean", "title": "Has Assignments", "description": "<PERSON><PERSON> b<PERSON>i tập", "default": false}, "pending_assignments": {"type": "integer", "title": "Pending Assignments", "description": "<PERSON><PERSON><PERSON> tập ch<PERSON>a nộp", "default": 0}, "lesson_status": {"type": "string", "title": "Lesson Status", "description": "<PERSON>r<PERSON><PERSON> thái bài học"}, "can_access": {"type": "boolean", "title": "Can Access", "description": "<PERSON><PERSON> thể truy cập", "default": false}, "is_today": {"type": "boolean", "title": "Is Today", "description": "<PERSON><PERSON><PERSON> h<PERSON> hôm nay", "default": false}, "is_upcoming": {"type": "boolean", "title": "Is Upcoming", "description": "<PERSON><PERSON><PERSON> ra", "default": false}, "is_live": {"type": "boolean", "title": "Is Live", "description": "<PERSON><PERSON> ra", "default": false}, "needs_attention": {"type": "boolean", "title": "Needs Attention", "description": "<PERSON><PERSON><PERSON> ch<PERSON>", "default": false}, "attention_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Attention Reason", "description": "Lý do cần chú <PERSON>"}}, "type": "object", "required": ["id", "name", "class_name", "instructor_name", "start_datetime", "lesson_status"], "title": "Student<PERSON><PERSON>on<PERSON><PERSON><PERSON><PERSON>", "description": "Student lesson summary for list view."}, "StudentPaymentSummary": {"properties": {"student_id": {"type": "integer", "title": "Student Id", "description": "<PERSON> học viên"}, "student_name": {"type": "string", "title": "Student Name", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên"}, "total_fees": {"type": "string", "title": "Total Fees", "description": "<PERSON><PERSON><PERSON> h<PERSON>c phí"}, "total_paid": {"type": "string", "title": "Total Paid", "description": "Tổng đã thanh toán"}, "total_outstanding": {"type": "string", "title": "Total Outstanding", "description": "<PERSON><PERSON><PERSON> còn nợ"}, "overall_payment_status": {"type": "string", "title": "Overall Payment Status", "description": "Tr<PERSON>ng thái thanh toán tổng thể"}, "has_overdue": {"type": "boolean", "title": "Has Overdue", "description": "<PERSON><PERSON>n quá hạn", "default": false}, "overdue_amount": {"type": "string", "title": "Overdue Amount", "description": "<PERSON><PERSON> tiền quá hạn", "default": "0"}, "total_invoices": {"type": "integer", "title": "Total Invoices", "description": "Tổng số hóa đơn", "default": 0}, "paid_invoices": {"type": "integer", "title": "Paid Invoices", "description": "<PERSON><PERSON><PERSON> đơn đã thanh toán", "default": 0}, "pending_invoices": {"type": "integer", "title": "Pending Invoices", "description": "<PERSON><PERSON><PERSON> đơn chờ thanh toán", "default": 0}, "overdue_invoices": {"type": "integer", "title": "Overdue Invoices", "description": "<PERSON><PERSON><PERSON> đơn quá hạn", "default": 0}, "active_installment_plans": {"type": "integer", "title": "Active Installment Plans", "description": "<PERSON><PERSON> hoạch trả góp đang hoạt động", "default": 0}, "active_financial_aids": {"type": "integer", "title": "Active Financial Aids", "description": "Hỗ trợ tài ch<PERSON>h đang hoạt động", "default": 0}, "next_payment_due": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Next Payment Due", "description": "<PERSON><PERSON><PERSON> đến hạn tiếp theo"}, "next_payment_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Next Payment Amount", "description": "<PERSON><PERSON> tiền đến hạn tiếp theo"}}, "type": "object", "required": ["student_id", "student_name", "total_fees", "total_paid", "total_outstanding", "overall_payment_status"], "title": "StudentPaymentSummary", "description": "Student payment summary schema."}, "StudentPreferencesResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/LearningPreferences"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "StudentPreferencesResponse", "description": "Student preferences response schema."}, "StudentProfile": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> học viên"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> học viên"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên"}, "status": {"type": "string", "title": "Status", "description": "<PERSON><PERSON><PERSON><PERSON> thái"}, "active": {"type": "boolean", "title": "Active", "description": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "user_id": {"type": "integer", "title": "User Id", "description": "ID người dùng"}, "user_name": {"type": "string", "title": "User Name", "description": "<PERSON><PERSON><PERSON> đ<PERSON>p"}, "birth_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birth Date", "description": "<PERSON><PERSON><PERSON>"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gender", "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h"}, "id_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id Number", "description": "Số CMND/CCCD"}, "nationality": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Nationality", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "contact_info": {"$ref": "#/components/schemas/StudentContactInfo", "description": "<PERSON>h<PERSON>ng tin liên hệ"}, "academic_info": {"$ref": "#/components/schemas/StudentAcademicInfo", "description": "<PERSON><PERSON><PERSON><PERSON> tin học vấn"}, "learning_preferences": {"$ref": "#/components/schemas/LearningPreferences", "description": "Sở thích học tập"}, "enrollments": {"items": {"$ref": "#/components/schemas/EnrollmentInfo"}, "type": "array", "title": "Enrollments", "description": "Đ<PERSON>ng ký khóa học"}, "statistics": {"$ref": "#/components/schemas/StudentStatistics", "description": "<PERSON><PERSON><PERSON><PERSON> kê"}, "created_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Created At", "description": "<PERSON><PERSON><PERSON>"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At", "description": "<PERSON><PERSON><PERSON> c<PERSON>"}}, "type": "object", "required": ["id", "code", "name", "status", "active", "user_id", "user_name", "contact_info", "academic_info", "learning_preferences", "statistics"], "title": "StudentProfile", "description": "Complete student profile schema."}, "StudentProfileResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentProfile"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "StudentProfileResponse", "description": "Student profile response schema."}, "StudentProfileUpdateRequest": {"properties": {"birth_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Birth Date", "description": "<PERSON><PERSON><PERSON>"}, "gender": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Gender", "description": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h"}, "nationality": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Nationality", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address", "description": "Địa chỉ"}, "parent_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Name", "description": "<PERSON><PERSON><PERSON> p<PERSON><PERSON> huynh"}, "parent_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Phone", "description": "SĐT phụ huynh"}, "emergency_contact": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergency Contact", "description": "<PERSON><PERSON><PERSON> h<PERSON> khẩn cấp"}, "emergency_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Emergency Phone", "description": "SĐT k<PERSON><PERSON>n cấp"}, "education_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Education Level", "description": "<PERSON><PERSON><PERSON><PERSON> độ học vấn"}, "school_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "School Name", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "graduation_year": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Graduation Year", "description": "<PERSON><PERSON><PERSON> t<PERSON>"}, "major": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Major", "description": "<PERSON><PERSON><PERSON><PERSON>"}, "english_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "English Level", "description": "<PERSON><PERSON><PERSON><PERSON> độ tiếng <PERSON>h"}, "preferred_learning_style": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Preferred Learning Style", "description": "<PERSON><PERSON> c<PERSON>ch học tập"}, "preferred_time_slots": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "title": "Preferred Time Slots", "description": "<PERSON><PERSON>g gi<PERSON> <PERSON><PERSON> th<PERSON>ch"}, "learning_goals": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Learning Goals", "description": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON> học tập"}, "special_needs": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Special Needs", "description": "<PERSON><PERSON> cầu đặc biệt"}, "notes": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "title": "StudentProfileUpdateRequest", "description": "Student profile update request schema."}, "StudentProfileUpdateResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentProfile"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "StudentProfileUpdateResponse", "description": "Student profile update response schema."}, "StudentProgressReport": {"properties": {"student_id": {"type": "integer", "title": "Student Id", "description": "<PERSON> học viên"}, "student_name": {"type": "string", "title": "Student Name", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên"}, "report_period": {"type": "string", "title": "Report Period", "description": "<PERSON><PERSON> báo cáo"}, "generated_at": {"type": "string", "format": "date-time", "title": "Generated At", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo báo cáo"}, "attendance_progress": {"$ref": "#/components/schemas/AttendanceProgress", "description": "<PERSON>i<PERSON><PERSON> độ điểm danh"}, "academic_progress": {"$ref": "#/components/schemas/AcademicProgress", "description": "<PERSON><PERSON><PERSON><PERSON> độ học tập"}, "learning_progress": {"$ref": "#/components/schemas/LearningProgress", "description": "<PERSON><PERSON><PERSON><PERSON> độ học tổng thể"}, "skill_progress": {"items": {"$ref": "#/components/schemas/SkillProgress"}, "type": "array", "title": "Skill Progress", "description": "<PERSON><PERSON><PERSON>n độ kỹ năng"}, "goal_progress": {"items": {"$ref": "#/components/schemas/GoalProgress"}, "type": "array", "title": "Goal Progress", "description": "<PERSON><PERSON><PERSON><PERSON> độ mục tiêu"}, "key_metrics": {"items": {"$ref": "#/components/schemas/ProgressMetric"}, "type": "array", "title": "Key Metrics", "description": "Chỉ số ch<PERSON>h"}, "class_comparison": {"items": {"$ref": "#/components/schemas/ComparisonData"}, "type": "array", "title": "Class Comparison", "description": "So s<PERSON>h với lớp"}, "overall_grade": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Overall Grade", "description": "<PERSON><PERSON><PERSON><PERSON> tổng thể"}, "overall_status": {"type": "string", "title": "Overall Status", "description": "Tr<PERSON><PERSON> thái tổng thể"}, "strengths": {"items": {"type": "string"}, "type": "array", "title": "Strengths", "description": "<PERSON><PERSON><PERSON><PERSON> mạnh"}, "areas_for_improvement": {"items": {"type": "string"}, "type": "array", "title": "Areas For Improvement", "description": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> c<PERSON><PERSON> cải thi<PERSON>n"}, "recommendations": {"items": {"type": "string"}, "type": "array", "title": "Recommendations", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> nghị"}, "next_milestones": {"items": {"type": "string"}, "type": "array", "title": "Next Milestones", "description": "<PERSON><PERSON><PERSON> mốc ti<PERSON> theo"}, "action_items": {"items": {"type": "string"}, "type": "array", "title": "Action Items", "description": "<PERSON><PERSON><PERSON> động cần thực hiện"}}, "type": "object", "required": ["student_id", "student_name", "report_period", "generated_at", "attendance_progress", "academic_progress", "learning_progress", "overall_status"], "title": "StudentProgressReport", "description": "Complete student progress report schema."}, "StudentProgressReportResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/StudentProgressReport"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "StudentProgressReportResponse", "description": "Student progress report response schema."}, "StudentStatistics": {"properties": {"total_courses": {"type": "integer", "title": "Total Courses", "description": "Tổng số khóa học", "default": 0}, "active_courses": {"type": "integer", "title": "Active Courses", "description": "<PERSON><PERSON><PERSON><PERSON> học đang học", "default": 0}, "completed_courses": {"type": "integer", "title": "Completed Courses", "description": "<PERSON><PERSON><PERSON><PERSON> học đã hoàn thành", "default": 0}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "Tổng số bài học", "default": 0}, "attended_lessons": {"type": "integer", "title": "Attended Lessons", "description": "<PERSON><PERSON><PERSON> học đã tham gia", "default": 0}, "attendance_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Attendance Rate", "description": "Tỷ lệ điểm danh (%)"}, "average_score": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Score", "description": "<PERSON><PERSON><PERSON><PERSON> trung bình"}, "total_study_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Study Hours", "description": "Tổng g<PERSON> học"}, "certificates_earned": {"type": "integer", "title": "Certificates Earned", "description": "Chứng chỉ đã đạt", "default": 0}}, "type": "object", "title": "StudentStatistics", "description": "Student statistics schema."}, "StudentSummary": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> học viên"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> học viên"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> h<PERSON> viên"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "description": "Email"}, "enrollment_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Enrollment Date", "description": "<PERSON><PERSON><PERSON> ký"}, "payment_status": {"type": "string", "title": "Payment Status", "description": "<PERSON>r<PERSON><PERSON> thái thanh toán"}, "attendance_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Attendance Rate", "description": "Tỷ lệ điểm danh (%)"}, "progress": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Progress", "description": "<PERSON><PERSON><PERSON><PERSON> độ học tập (%)"}}, "type": "object", "required": ["id", "code", "name", "payment_status"], "title": "StudentSummary", "description": "Student summary for instructor view."}, "SubjectInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> môn h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> môn h<PERSON>c"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> môn học"}, "level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Level", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["id", "name", "code"], "title": "SubjectInfo", "description": "Subject information schema."}, "SubjectListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "<PERSON>r<PERSON><PERSON> thái thành công"}, "message": {"type": "string", "title": "Message", "description": "<PERSON><PERSON><PERSON><PERSON> báo"}, "data": {"items": {"$ref": "#/components/schemas/SubjectPublicSchema"}, "type": "array", "title": "Data", "description": "<PERSON><PERSON> s<PERSON>ch môn h<PERSON>c"}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["success", "message", "data"], "title": "SubjectListResponse", "description": "Response schema for subject list.", "example": {"data": [{"active": true, "category": "academic", "code": "MATH", "color": "#3498db", "course_count": 5, "description": "<PERSON><PERSON><PERSON> học to<PERSON> học cơ bản và nâng cao", "icon": "fa-calculator", "id": 1, "name": "<PERSON><PERSON>"}, {"active": true, "category": "language", "code": "ENG", "color": "#e74c3c", "course_count": 8, "description": "<PERSON><PERSON><PERSON> họ<PERSON> tiếng <PERSON>h giao tiếp và học thuật", "icon": "fa-language", "id": 2, "name": "<PERSON><PERSON><PERSON><PERSON>"}], "message": "<PERSON><PERSON><PERSON> danh sách môn học thành công", "meta": {"active_only": true, "api_version": "v1", "service": "lms", "timestamp": "2025-01-29T10:30:00Z", "total_count": 2}, "success": true}}, "SubjectPublicSchema": {"properties": {"id": {"type": "integer", "title": "Id", "description": "<PERSON> môn h<PERSON>c"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> môn h<PERSON>c"}, "code": {"type": "string", "title": "Code", "description": "<PERSON><PERSON> môn học"}, "description": {"type": "string", "title": "Description", "description": "<PERSON><PERSON> tả môn học", "default": ""}, "category": {"type": "string", "title": "Category", "description": "<PERSON><PERSON> mục môn học", "default": ""}, "icon": {"type": "string", "title": "Icon", "description": "<PERSON><PERSON> môn h<PERSON>c", "default": ""}, "color": {"type": "string", "title": "Color", "description": "<PERSON><PERSON><PERSON> môn học", "default": ""}, "course_count": {"type": "integer", "title": "Course Count", "description": "Số lượng kh<PERSON>a học", "default": 0}, "active": {"type": "boolean", "title": "Active", "description": "<PERSON>r<PERSON><PERSON> thái ho<PERSON>t động", "default": true}}, "type": "object", "required": ["id", "name", "code"], "title": "SubjectPublicSchema", "description": "Schema for public subject information.", "example": {"active": true, "category": "academic", "code": "MATH", "color": "#3498db", "course_count": 5, "description": "<PERSON><PERSON><PERSON> học to<PERSON> học cơ bản và nâng cao", "icon": "fa-calculator", "id": 1, "name": "<PERSON><PERSON>"}}, "TimeSlot": {"properties": {"start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu"}, "end_time": {"type": "string", "format": "time", "title": "End Time", "description": "<PERSON><PERSON><PERSON> kết thúc"}}, "type": "object", "required": ["start_time", "end_time"], "title": "TimeSlot", "description": "Time slot schema."}, "TokenResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/OAuth2Token"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "TokenResponse", "description": "<PERSON><PERSON><PERSON> hồi khi cấp token thành công theo chuẩn API EarnBase và OAuth 2.0 (RFC 6749)"}, "UnifiedLoginRequest": {"properties": {"username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Username", "description": "Username (for standard login)"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email (for enhanced login)"}, "password": {"type": "string", "title": "Password", "description": "User password"}, "device_info": {"anyOf": [{"$ref": "#/components/schemas/DeviceInfo"}, {"type": "null"}], "description": "Device information (optional)"}, "remember_me": {"type": "boolean", "title": "Remember Me", "description": "Remember login", "default": false}, "mfa_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mfa Code", "description": "Multi-factor authentication code"}}, "type": "object", "required": ["password"], "title": "UnifiedLoginRequest", "description": "Unified login request supporting both standard and enhanced login."}, "UserInfo": {"properties": {"id": {"type": "integer", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "login": {"type": "string", "title": "<PERSON><PERSON>"}, "is_admin": {"type": "boolean", "title": "Is Admin", "default": false}, "is_system": {"type": "boolean", "title": "Is System", "default": false}, "groups": {"items": {"type": "string"}, "type": "array", "title": "Groups", "default": []}, "partner_id": {"type": "integer", "title": "Partner Id"}}, "type": "object", "required": ["id", "name", "login", "partner_id"], "title": "UserInfo", "description": "Thông tin người dùng đã xác thực"}, "UserInfoResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/UserInfo"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "UserInfoResponse", "description": "<PERSON>ản hồi thông tin người dùng theo chuẩn API EarnBase"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WeekSchedule": {"properties": {"week_start": {"type": "string", "format": "date", "title": "Week Start", "description": "<PERSON><PERSON><PERSON> t<PERSON>"}, "week_end": {"type": "string", "format": "date", "title": "Week End", "description": "<PERSON><PERSON><PERSON><PERSON> tu<PERSON>n"}, "week_number": {"type": "integer", "title": "Week Number", "description": "<PERSON><PERSON><PERSON>"}, "year": {"type": "integer", "title": "Year", "description": "Năm"}, "days": {"items": {"$ref": "#/components/schemas/DaySchedule"}, "type": "array", "title": "Days", "description": "<PERSON><PERSON><PERSON> c<PERSON>c ngày trong tu<PERSON>n"}, "total_hours": {"type": "number", "title": "Total Hours", "description": "Tổng giờ tuần", "default": 0.0}, "teaching_hours": {"type": "number", "title": "Teaching Hours", "description": "G<PERSON>ờ giảng dạy", "default": 0.0}, "total_lessons": {"type": "integer", "title": "Total Lessons", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> h<PERSON>c", "default": 0}, "total_classes": {"type": "integer", "title": "Total Classes", "description": "Số lớp d<PERSON>y", "default": 0}, "is_busy_week": {"type": "boolean", "title": "Is Busy Week", "description": "<PERSON><PERSON><PERSON> bận rộn", "default": false}, "has_conflicts": {"type": "boolean", "title": "Has Conflicts", "description": "<PERSON><PERSON>ung đột", "default": false}, "workload_percentage": {"type": "number", "title": "Workload Percentage", "description": "Tỷ lệ công việc (%)", "default": 0.0}}, "type": "object", "required": ["week_start", "week_end", "week_number", "year", "days"], "title": "WeekSchedule", "description": "Weekly schedule schema."}, "WeekScheduleResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "message": {"type": "string", "title": "Message", "default": "Operation completed successfully"}, "data": {"anyOf": [{"$ref": "#/components/schemas/WeekSchedule"}, {"type": "null"}]}, "meta": {"additionalProperties": true, "type": "object", "title": "Meta"}}, "type": "object", "title": "WeekScheduleResponse", "description": "Weekly schedule response schema."}, "odoo__addons__eb_lms__schemas__instructors__course_schemas__ClassScheduleInfo": {"properties": {"day_of_week": {"type": "string", "title": "Day Of Week", "description": "<PERSON><PERSON><PERSON> trong tuần"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu"}, "end_time": {"type": "string", "format": "time", "title": "End Time", "description": "<PERSON><PERSON><PERSON> kết thúc"}, "room": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["day_of_week", "start_time", "end_time"], "title": "ClassScheduleInfo", "description": "Class schedule information schema."}, "odoo__addons__eb_lms__schemas__instructors__course_schemas__CourseInfo": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID khóa học"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "code": {"type": "string", "title": "Code", "description": "Mã khóa học"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON> t<PERSON> kh<PERSON>a học"}, "level": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Level", "description": "<PERSON><PERSON><PERSON>"}, "duration_hours": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Duration Hours", "description": "<PERSON><PERSON><PERSON><PERSON> (giờ)"}, "max_students": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Max Students", "description": "<PERSON><PERSON> học viên tối đa"}}, "type": "object", "required": ["id", "name", "code"], "title": "CourseInfo", "description": "Course information schema."}, "odoo__addons__eb_lms__schemas__instructors__lesson_schemas__LessonMaterial": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID tài liệu"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> tài li<PERSON>u"}, "file_type": {"type": "string", "title": "File Type", "description": "Loại file"}, "file_size": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "File Size", "description": "<PERSON><PERSON><PERSON> th<PERSON> file (bytes)"}, "upload_date": {"type": "string", "format": "date-time", "title": "Upload Date", "description": "Ngày upload"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "<PERSON><PERSON><PERSON> l<PERSON> b<PERSON><PERSON> bu<PERSON>", "default": false}, "download_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Download Url", "description": "Link download"}}, "type": "object", "required": ["id", "name", "file_type", "upload_date"], "title": "LessonMaterial", "description": "Lesson material schema."}, "odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__CourseInfo": {"properties": {"course_id": {"type": "integer", "title": "Course Id", "description": "ID khóa học"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "course_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Course Description", "description": "<PERSON><PERSON> t<PERSON> kh<PERSON>a học"}}, "type": "object", "required": ["course_id", "course_name"], "title": "CourseInfo"}, "odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__InvoiceInfo": {"properties": {"invoice_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Invoice Id", "description": "ID hóa đơn"}, "invoice_number": {"type": "string", "title": "Invoice Number", "description": "<PERSON><PERSON> hóa đơn", "default": ""}, "invoice_state": {"type": "string", "title": "Invoice State", "description": "<PERSON><PERSON><PERSON><PERSON> thái hóa đơn", "default": ""}, "invoice_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Invoice Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>n"}, "invoice_amount": {"type": "number", "title": "Invoice Amount", "description": "<PERSON><PERSON> tiền hóa đơn", "default": 0.0}}, "type": "object", "title": "InvoiceInfo"}, "odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__OrderInfo": {"properties": {"order_id": {"type": "integer", "title": "Order Id", "description": "<PERSON> đơn hàng"}, "order_number": {"type": "string", "title": "Order Number", "description": "<PERSON><PERSON> đơn hàng"}, "total_amount": {"type": "number", "title": "Total Amount", "description": "<PERSON><PERSON> tiền cần thanh toán"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> tiền tệ"}, "enrollment_date": {"type": "string", "format": "date-time", "title": "Enrollment Date", "description": "<PERSON><PERSON><PERSON> ký"}, "state": {"type": "string", "title": "State", "description": "<PERSON>r<PERSON>ng thái đăng ký"}}, "type": "object", "required": ["order_id", "order_number", "total_amount", "currency", "enrollment_date", "state"], "title": "OrderInfo"}, "odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__PaymentInfo": {"properties": {"transaction_id": {"type": "integer", "title": "Transaction Id", "description": "ID giao dịch thanh toán"}, "payment_reference": {"type": "string", "title": "Payment Reference", "description": "<PERSON><PERSON> tham chi<PERSON>u thanh toán"}, "qr_code_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qr Code Url", "description": "URL mã QR thanh toán"}, "bank_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bank Name", "description": "<PERSON><PERSON><PERSON> ng<PERSON> hàng"}, "bank_account_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bank Account Number", "description": "Số tài k<PERSON>n ngân hàng"}, "bank_account_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bank Account Name", "description": "<PERSON>ên chủ tài k<PERSON>n"}, "state": {"type": "string", "title": "State", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch"}, "expiration_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expiration Time", "description": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn"}, "payment_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Instructions", "description": "Hướng dẫn thanh toán"}}, "type": "object", "required": ["transaction_id", "payment_reference", "state"], "title": "PaymentInfo"}, "odoo__addons__eb_lms__schemas__public_schema__PublicEnrollmentResponse__StudentInfo": {"properties": {"name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> đăng ký"}, "email": {"type": "string", "title": "Email", "description": "<PERSON>ail ng<PERSON> đ<PERSON>ng ký"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "S<PERSON> điện thoại đã định dạng E164"}, "country_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Country Code", "description": "<PERSON><PERSON> quốc gia theo định dạng ISO"}, "country_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Country Name", "description": "<PERSON><PERSON><PERSON> quốc gia"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON> chú đăng ký"}}, "type": "object", "required": ["name", "email"], "title": "StudentInfo"}, "odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__CourseInfo": {"properties": {"course_id": {"type": "integer", "title": "Course Id", "description": "ID khóa học"}, "course_name": {"type": "string", "title": "Course Name", "description": "<PERSON><PERSON><PERSON> h<PERSON>"}, "course_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Course Description", "description": "<PERSON><PERSON> t<PERSON> kh<PERSON>a học"}, "course_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Course Code", "description": "Mã khóa học"}}, "type": "object", "required": ["course_id", "course_name"], "title": "CourseInfo"}, "odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__InvoiceInfo": {"properties": {"invoice_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Invoice Id", "description": "ID hóa đơn"}, "invoice_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Invoice Number", "description": "<PERSON><PERSON> hóa đơn"}, "invoice_state": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Invoice State", "description": "<PERSON><PERSON><PERSON><PERSON> thái hóa đơn"}, "invoice_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Invoice Date", "description": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>n"}, "invoice_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Invoice Amount", "description": "<PERSON><PERSON> tiền hóa đơn"}}, "type": "object", "title": "InvoiceInfo"}, "odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__OrderInfo": {"properties": {"order_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Id", "description": "<PERSON> đơn hàng"}, "order_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order Number", "description": "<PERSON><PERSON> đơn hàng"}, "order_reference": {"type": "string", "title": "Order Reference", "description": "<PERSON><PERSON> tham chi<PERSON>u đơn hàng"}, "total_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Amount", "description": "<PERSON><PERSON> tiền cần thanh toán"}, "currency": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> tiền tệ"}, "enrollment_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Enrollment Date", "description": "<PERSON><PERSON><PERSON> ký"}, "state": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "State", "description": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng"}}, "type": "object", "required": ["order_reference"], "title": "OrderInfo"}, "odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__PaymentInfo": {"properties": {"transaction_id": {"type": "integer", "title": "Transaction Id", "description": "ID giao dịch thanh toán"}, "payment_reference": {"type": "string", "title": "Payment Reference", "description": "<PERSON><PERSON> tham chi<PERSON>u thanh toán"}, "state": {"type": "string", "title": "State", "description": "<PERSON>r<PERSON><PERSON> thái giao d<PERSON>ch"}, "amount": {"type": "number", "title": "Amount", "description": "<PERSON><PERSON> tiền giao d<PERSON>ch"}, "paid_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "Số tiền đã thanh toán"}, "received_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Received Amount", "description": "<PERSON><PERSON> tiền đã nhận (cho thanh to<PERSON> một phần)"}, "payment_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Payment Date", "description": "<PERSON><PERSON><PERSON><PERSON> gian thanh toán"}, "payment_method": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Method", "description": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán"}, "qr_code_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Qr Code Url", "description": "URL mã QR thanh toán"}, "bank_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bank Name", "description": "<PERSON><PERSON><PERSON> ng<PERSON> hàng"}, "bank_account_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bank Account Number", "description": "Số tài k<PERSON>n ngân hàng"}, "bank_account_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bank Account Name", "description": "<PERSON>ên chủ tài k<PERSON>n"}, "payment_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Description", "description": "<PERSON><PERSON><PERSON> dung thanh toán"}, "expiration_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expiration Time", "description": "<PERSON><PERSON><PERSON><PERSON> gian hế<PERSON> hạn"}, "payment_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Payment Instructions", "description": "Hướng dẫn thanh toán"}, "payment_history": {"anyOf": [{"items": {"$ref": "#/components/schemas/PaymentHistoryItem"}, "type": "array"}, {"type": "null"}], "title": "Payment History", "description": "<PERSON><PERSON><PERSON> sử thanh toán"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message", "description": "<PERSON><PERSON><PERSON><PERSON> báo về trạng thái"}}, "type": "object", "required": ["transaction_id", "payment_reference", "state", "amount"], "title": "PaymentInfo"}, "odoo__addons__eb_lms__schemas__public_schema__PublicPaymentStatusResponse__StudentInfo": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> đăng ký"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email", "description": "<PERSON>ail ng<PERSON> đ<PERSON>ng ký"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone", "description": "<PERSON><PERSON> điện thoại đã định dạng"}, "country_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Country Code", "description": "<PERSON><PERSON> quốc gia theo định dạng ISO"}, "country_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Country Name", "description": "<PERSON><PERSON><PERSON> quốc gia"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "<PERSON><PERSON> chú đăng ký"}}, "type": "object", "title": "StudentInfo"}, "odoo__addons__eb_lms__schemas__students__course_schemas__ClassScheduleInfo": {"properties": {"day_of_week": {"type": "string", "title": "Day Of Week", "description": "<PERSON><PERSON><PERSON> trong tuần"}, "start_time": {"type": "string", "format": "time", "title": "Start Time", "description": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đầu"}, "end_time": {"type": "string", "format": "time", "title": "End Time", "description": "<PERSON><PERSON><PERSON> kết thúc"}, "room": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Room", "description": "<PERSON><PERSON><PERSON>"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location", "description": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["day_of_week", "start_time", "end_time"], "title": "ClassScheduleInfo", "description": "Class schedule information for students."}, "odoo__addons__eb_lms__schemas__students__lesson_schemas__LessonMaterial": {"properties": {"id": {"type": "integer", "title": "Id", "description": "ID tài liệu"}, "name": {"type": "string", "title": "Name", "description": "<PERSON><PERSON><PERSON> tài li<PERSON>u"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "<PERSON><PERSON>"}, "material_type": {"type": "string", "title": "Material Type", "description": "<PERSON><PERSON><PERSON> tài li<PERSON>u"}, "file_name": {"type": "string", "title": "File Name", "description": "Tên file"}, "file_size": {"type": "integer", "title": "File Size", "description": "<PERSON><PERSON><PERSON> file", "default": 0}, "file_type": {"type": "string", "title": "File Type", "description": "Loại file"}, "sequence": {"type": "integer", "title": "Sequence", "description": "<PERSON><PERSON><PERSON> tự", "default": 0}, "is_required": {"type": "boolean", "title": "Is Required", "description": "<PERSON><PERSON><PERSON> b<PERSON>", "default": false}, "is_accessible": {"type": "boolean", "title": "Is Accessible", "description": "<PERSON><PERSON> thể truy cập", "default": true}, "access_restriction": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Access Restriction", "description": "<PERSON><PERSON><PERSON> ch<PERSON> truy cập"}, "view_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "View Url", "description": "URL xem"}, "download_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Download Url", "description": "URL tải xuống"}, "thumbnail_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "URL thumbnail"}, "duration_minutes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Duration Minutes", "description": "<PERSON><PERSON><PERSON><PERSON> (phút)"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "<PERSON><PERSON><PERSON><PERSON> gian tạo"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t"}}, "type": "object", "required": ["id", "name", "material_type", "file_name", "file_type", "created_at", "updated_at"], "title": "LessonMaterial", "description": "Lesson material schema."}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}, "tags": [{"name": "core", "description": "Core API functionality"}, {"name": "eb_website_content", "description": "API for website static content management"}]}