// EarnBase LMS API Types for React Native Integration
// Generated from: https://lms-dev.ebill.vn/api/v1/openapi.json

export const API_BASE_URL = 'https://lms-dev.ebill.vn/api/v1';

// ============================================================================
// Authentication Types
// ============================================================================

export interface LoginRequest {
  username?: string;
  email?: string;
  password: string;
  device_info?: DeviceInfo;
  remember_me?: boolean;
  mfa_code?: string;
}

export interface DeviceInfo {
  device_type?: string;
  device_name?: string;
  os_version?: string;
  app_version?: string;
}

export interface TokenResponse {
  success: boolean;
  message: string;
  data: {
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
    user_info: UserInfo;
  };
  meta?: Record<string, any>;
}

export interface UserInfo {
  id: number;
  name: string;
  email?: string;
  login: string;
  is_admin: boolean;
  is_system: boolean;
  groups: string[];
  partner_id: number;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

// ============================================================================
// Student Types
// ============================================================================

export interface StudentProfile {
  id: number;
  code: string;
  name: string;
  status: string;
  active: boolean;
  user_id: number;
  user_name: string;
  birth_date?: string;
  gender?: string;
  id_number?: string;
  nationality?: string;
  contact_info: StudentContactInfo;
  academic_info: StudentAcademicInfo;
  learning_preferences: LearningPreferences;
  enrollments: EnrollmentInfo[];
  statistics: StudentStatistics;
  created_at?: string;
  updated_at?: string;
}

export interface StudentContactInfo {
  phone?: string;
  address?: string;
  parent_name?: string;
  parent_phone?: string;
  emergency_contact?: string;
  emergency_phone?: string;
}

export interface StudentAcademicInfo {
  education_level?: string;
  school_name?: string;
  graduation_year?: number;
  major?: string;
  english_level?: string;
}

export interface LearningPreferences {
  preferred_learning_style?: string;
  preferred_time_slots?: string[];
  learning_goals?: string;
  special_needs?: string;
  notes?: string;
}

export interface EnrollmentInfo {
  id: number;
  course_id: number;
  course_name: string;
  enrollment_date: string;
  status: string;
  progress?: number;
  completion_date?: string;
}

export interface StudentStatistics {
  total_courses: number;
  active_courses: number;
  completed_courses: number;
  total_lessons: number;
  attended_lessons: number;
  attendance_rate?: number;
  average_score?: number;
  total_study_hours?: number;
  certificates_earned: number;
}

// ============================================================================
// Instructor Types
// ============================================================================

export interface InstructorProfile {
  id: number;
  code: string;
  name: string;
  status: string;
  active: boolean;
  user_id: number;
  user_name: string;
  birth_date?: string;
  gender?: string;
  id_number?: string;
  nationality?: string;
  contact_info: InstructorContactInfo;
  professional_info: InstructorProfessionalInfo;
  teaching_preferences: TeachingPreferences;
  courses: CourseInfo[];
  statistics: InstructorStatistics;
  created_at?: string;
  updated_at?: string;
}

export interface InstructorContactInfo {
  phone?: string;
  address?: string;
  emergency_contact?: string;
  emergency_phone?: string;
}

export interface InstructorProfessionalInfo {
  education_level?: string;
  university?: string;
  graduation_year?: number;
  major?: string;
  certifications?: string[];
  experience_years?: number;
  specializations?: string[];
}

export interface TeachingPreferences {
  preferred_subjects?: string[];
  preferred_levels?: string[];
  preferred_time_slots?: string[];
  max_students_per_class?: number;
  teaching_style?: string;
  notes?: string;
}

export interface InstructorStatistics {
  total_courses: number;
  active_courses: number;
  total_students: number;
  total_lessons: number;
  completed_lessons: number;
  average_rating?: number;
  total_teaching_hours?: number;
}

// ============================================================================
// Course Types
// ============================================================================

export interface CourseInfo {
  id: number;
  name: string;
  code: string;
  description?: string;
  level?: string;
  duration_hours?: number;
  max_students?: number;
  subject_info?: SubjectInfo;
  instructor_info?: InstructorInfo;
  schedule_info?: ClassScheduleInfo[];
  enrollment_info?: EnrollmentSummary;
}

export interface SubjectInfo {
  id: number;
  name: string;
  code: string;
  level?: string;
}

export interface InstructorInfo {
  id: number;
  name: string;
  code: string;
  email?: string;
  phone?: string;
}

export interface ClassScheduleInfo {
  day_of_week: string;
  start_time: string;
  end_time: string;
  room?: string;
  location?: string;
}

export interface EnrollmentSummary {
  total_students: number;
  max_students?: number;
  enrollment_status: string;
  start_date?: string;
  end_date?: string;
}

// ============================================================================
// Lesson Types
// ============================================================================

export interface LessonInfo {
  id: number;
  name: string;
  description?: string;
  lesson_type: string;
  sequence: number;
  duration_minutes?: number;
  scheduled_date?: string;
  scheduled_time?: string;
  status: string;
  course_info: CourseInfo;
  instructor_info: InstructorInfo;
  materials: LessonMaterial[];
  attendance_info?: AttendanceInfo;
}

export interface LessonMaterial {
  id: number;
  name: string;
  description?: string;
  material_type: string;
  file_name: string;
  file_size: number;
  file_type: string;
  sequence: number;
  is_required: boolean;
  is_accessible: boolean;
  access_restriction?: string;
  view_url?: string;
  download_url?: string;
  thumbnail_url?: string;
  duration_minutes?: number;
  created_at: string;
  updated_at: string;
}

export interface AttendanceInfo {
  is_attended: boolean;
  attendance_time?: string;
  attendance_status: string;
  notes?: string;
}

// ============================================================================
// Schedule Types
// ============================================================================

export interface WeekSchedule {
  week_start: string;
  week_end: string;
  week_number: number;
  year: number;
  days: DaySchedule[];
  total_hours: number;
  teaching_hours: number;
  total_lessons: number;
  total_classes: number;
  is_busy_week: boolean;
  has_conflicts: boolean;
  workload_percentage: number;
}

export interface DaySchedule {
  date: string;
  day_of_week: string;
  is_weekend: boolean;
  is_holiday: boolean;
  lessons: LessonSchedule[];
  total_hours: number;
  total_lessons: number;
  is_busy_day: boolean;
  has_conflicts: boolean;
}

export interface LessonSchedule {
  lesson_id: number;
  lesson_name: string;
  course_id: number;
  course_name: string;
  start_time: string;
  end_time: string;
  duration_minutes: number;
  room?: string;
  location?: string;
  lesson_type: string;
  status: string;
  instructor_info?: InstructorInfo;
  student_count?: number;
}

// ============================================================================
// API Response Types
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  meta?: Record<string, any>;
}

export interface ApiError {
  success: false;
  message: string;
  error_code?: string;
  errors?: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
}

// ============================================================================
// File Upload Types
// ============================================================================

export interface FileUploadResponse {
  success: boolean;
  message: string;
  data: {
    id: number;
    name: string;
    file_name: string;
    file_size: number;
    file_type: string;
    access_token: string;
    view_url?: string;
    download_url?: string;
    thumbnail_url?: string;
    created_at: string;
  };
}

// ============================================================================
// Test Accounts
// ============================================================================

export const TEST_ACCOUNTS = {
  STUDENT: {
    username: 'student',
    password: 'student@X2025'
  },
  INSTRUCTOR: {
    username: 'teacher',
    password: 'teacher@X2025'
  }
} as const;
