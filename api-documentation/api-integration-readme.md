# EarnBase LMS API Integration System

## 🎯 Overview

Hệ thống API integration linh hoạt cho eb-lms-app với khả năng chuyển đổi endpoint động và quản lý môi trường dễ dàng.

## 📁 Files Structure

```
app/
├── config/
│   └── api.config.ts           # API configuration với dynamic endpoint
├── services/
│   └── api/
│       ├── earnbase-lms-api.ts # EarnBase LMS API service
│       └── api-manager.ts      # API manager với endpoint switching
├── components/
│   └── ApiEndpointManager.tsx  # UI component để quản lý API endpoints
├── screens/
│   └── DeveloperSettingsScreen.tsx # Developer settings screen
└── models/
    └── AuthenticationStore.ts  # Updated với real API integration
```

## 🚀 Key Features

### 1. **Dynamic Endpoint Switching**
- Chuyển đổi API endpoint bất kỳ lúc nào
- Không cần restart app
- Tất cả API calls sẽ tự động sử dụng endpoint mới

### 2. **Environment Management**
- Development: `https://lms-dev.ebill.vn/api/v1`
- Staging: `https://lms-staging.ebill.vn/api/v1`
- Production: `https://lms.ebill.vn/api/v1`

### 3. **Real-time Configuration**
- Update URL qua UI hoặc code
- Test connection ngay lập tức
- Global access trong development mode

## 🔧 Usage Examples

### Basic Usage

```typescript
import { api, apiManager } from '@/services/api/api-manager'

// Sử dụng API service
const result = await api.signIn({
  username: 'student',
  password: 'student@X2025'
})

// Chuyển đổi environment
apiManager.switchEnvironment('staging')

// Update custom URL
apiManager.updateBaseUrl('https://your-custom-api.com/api/v1')
```

### Authentication Integration

```typescript
// Trong AuthenticationStore
async loginWithApi(credentials) {
  const result = await api.signIn(credentials)
  if (result.kind === "ok") {
    this.setAuthToken(result.data.access_token)
    setAuthToken(result.data.access_token) // Set cho API manager
    return { success: true, user: result.data.user_info }
  }
  return { success: false, error: "Login failed" }
}
```

### Development Console Commands

```javascript
// Trong development mode, có thể sử dụng global commands:
global.apiManager.updateBaseUrl('https://localhost:3000/api/v1')
global.apiManager.switchEnvironment('development')
global.switchToCommonEndpoints.local()
global.switchToCommonEndpoints.custom('https://your-api.com/api/v1')
```

## 🎨 UI Components

### ApiEndpointManager Component
- Switch environments với buttons
- Input custom URL
- Test connection
- Reset to default
- Real-time status display

### DeveloperSettingsScreen
- Complete developer tools
- API status monitoring
- Test authentication
- Quick actions
- Console commands reference

## 📱 How to Access

### 1. **Via Developer Settings Screen**
```typescript
// Add to your navigation
import { DeveloperSettingsScreen } from '@/screens/DeveloperSettingsScreen'

// Navigate to developer settings
navigation.navigate('DeveloperSettings')
```

### 2. **Via Modal Component**
```typescript
import { ApiEndpointManager } from '@/components/ApiEndpointManager'

const [showApiManager, setShowApiManager] = useState(false)

<Modal visible={showApiManager}>
  <ApiEndpointManager onClose={() => setShowApiManager(false)} />
</Modal>
```

### 3. **Programmatically**
```typescript
import { apiManager, switchToCommonEndpoints } from '@/services/api/api-manager'

// Quick switches
switchToCommonEndpoints.development()
switchToCommonEndpoints.staging()
switchToCommonEndpoints.production()
switchToCommonEndpoints.local()
switchToCommonEndpoints.custom('https://your-api.com/api/v1')
```

## 🔐 Authentication Flow

### 1. **Login Process**
```typescript
// User login với real API
const result = await authenticationStore.loginWithApi({
  username: 'student',
  password: 'student@X2025'
})

// API manager tự động set auth token cho tất cả subsequent calls
```

### 2. **Token Management**
```typescript
// Set token
apiManager.setAuthToken(token)

// Clear token
apiManager.clearAuthToken()

// Auto logout khi token expires
```

### 3. **Test Accounts**
```typescript
const TEST_ACCOUNTS = {
  STUDENT: { username: 'student', password: 'student@X2025' },
  INSTRUCTOR: { username: 'teacher', password: 'teacher@X2025' }
}
```

## 🛠️ Configuration Options

### API Endpoints
```typescript
const API_ENDPOINTS = {
  development: 'https://lms-dev.ebill.vn/api/v1',
  staging: 'https://lms-staging.ebill.vn/api/v1', 
  production: 'https://lms.ebill.vn/api/v1',
}
```

### Request Configuration
```typescript
const DEFAULT_HEADERS = {
  'Accept': 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
}

const DEFAULT_TIMEOUT = 30000 // 30 seconds
```

## 🔍 Debugging & Monitoring

### API Status Monitoring
```typescript
const status = apiManager.getApiStatus()
// Returns: { baseUrl, environment, hasAuthToken }
```

### Connection Testing
```typescript
const isConnected = await apiManager.testConnection()
console.log('API Connection:', isConnected ? 'OK' : 'Failed')
```

### Development Logging
```typescript
// All API operations are logged in development mode
// Check console for detailed API call information
```

## 🚨 Important Notes

### 1. **Environment Detection**
- Development mode: `__DEV__ === true`
- Production builds: Defaults to production environment
- Can be overridden via configuration

### 2. **Token Persistence**
- Tokens are managed by AuthenticationStore
- API manager receives token updates automatically
- Logout clears tokens from both stores

### 3. **Error Handling**
- All API calls return standardized response format
- Network errors are handled gracefully
- Connection failures don't crash the app

### 4. **Security**
- No API secrets in client code
- Tokens are handled securely
- Environment switching only in development builds

## 🎯 Next Steps

1. **Integrate with existing screens**: Update các screen hiện tại để sử dụng real API
2. **Add more endpoints**: Implement thêm API endpoints theo nhu cầu
3. **Error handling**: Enhance error handling và user feedback
4. **Offline support**: Add offline capabilities và data caching
5. **Performance**: Optimize API calls và response handling

## 📞 Support

Nếu có vấn đề với API integration:
1. Check console logs trong development mode
2. Use Developer Settings screen để test connection
3. Verify API endpoint và authentication
4. Check network connectivity

---

**Happy Coding! 🚀**
