import React from "react"
import { View, TouchableOpacity, StyleSheet } from "react-native"
import { Text, Icon } from "@/components"
import { spacing, colors } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ScheduleSectionProps {
  data: {
    upcomingClasses: any[]
    scheduleData: any[]
  }
  handlers: {
    handleViewFullSchedule: () => void
    handleScheduleItemPress: (item: any) => void
    handleCreateClass: () => void
  }
}

export const ScheduleSection: React.FC<ScheduleSectionProps> = ({
  data,
  handlers,
}) => {
  const { themed } = useAppTheme()

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "lecture":
        return "components"
      case "workshop":
        return "settings"
      case "lab":
        return "view"
      default:
        return "calendar"
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "lecture":
        return colors.palette.primary500
      case "workshop":
        return colors.palette.warning500
      case "lab":
        return colors.palette.success500
      default:
        return colors.textDim
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.sectionHeader}>
        <Text style={themed(styles.sectionTitle)}>Schedule</Text>
        <TouchableOpacity onPress={handlers.handleViewFullSchedule}>
          <Text style={themed(styles.viewAllText)}>View All</Text>
        </TouchableOpacity>
      </View>

      {data.upcomingClasses.slice(0, 3).map((classItem) => (
        <TouchableOpacity
          key={classItem.id}
          style={themed(styles.scheduleItem)}
          onPress={() => handlers.handleScheduleItemPress(classItem)}
          activeOpacity={0.7}
        >
          <View style={[styles.typeIndicator, { backgroundColor: getTypeColor(classItem.type) }]}>
            <Icon 
              icon={getTypeIcon(classItem.type)} 
              size={16} 
              color={colors.palette.neutral100} 
            />
          </View>
          <View style={styles.scheduleInfo}>
            <Text style={themed(styles.classTitle)} numberOfLines={1}>
              {classItem.title}
            </Text>
            <Text style={themed(styles.classTime)}>
              {classItem.time} • {classItem.date}
            </Text>
            <Text style={themed(styles.classLocation)}>
              {classItem.room} • {classItem.students} students
            </Text>
          </View>
          <View style={styles.scheduleActions}>
            <Text style={themed(styles.classType)}>{classItem.type}</Text>
            <Icon icon="caretRight" size={16} color={themed(styles.actionArrow).color} />
          </View>
        </TouchableOpacity>
      ))}

      <TouchableOpacity
        style={themed(styles.createButton)}
        onPress={handlers.handleCreateClass}
        activeOpacity={0.8}
      >
        <Icon icon="plus" size={20} color={colors.palette.primary500} />
        <Text style={themed(styles.createButtonText)}>Schedule New Class</Text>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.md,
    marginBottom: spacing.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.palette.primary500,
  },
  scheduleItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
  },
  typeIndicator: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.sm,
  },
  scheduleInfo: {
    flex: 1,
  },
  classTitle: {
    fontSize: 14,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
    marginBottom: 2,
  },
  classTime: {
    fontSize: 12,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.palette.primary500,
    marginBottom: 2,
  },
  classLocation: {
    fontSize: 11,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
  },
  scheduleActions: {
    alignItems: "flex-end",
  },
  classType: {
    fontSize: 10,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
    textTransform: "uppercase",
    marginBottom: 4,
  },
  actionArrow: {
    color: colors.textDim,
  },
  createButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.palette.primary100,
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.palette.primary500,
    borderStyle: "dashed",
  },
  createButtonText: {
    fontSize: 14,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.palette.primary500,
    marginLeft: spacing.xs,
  },
})
