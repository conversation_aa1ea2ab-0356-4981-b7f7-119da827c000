import React from "react"
import { View, StyleSheet } from "react-native"
import { spacing } from "app/theme"
import {
  UpcomingAssignments,
  UpcomingSchedule,
} from "."

interface AssignmentsSectionProps {
  /**
   * Assignments and schedule data
   */
  data: {
    upcomingAssignmentsData: any[]
    upcomingScheduleData: any[]
  }

  /**
   * Assignments interaction handlers
   */
  handlers: {
    handleViewAllAssignments: () => void
    handleViewFullSchedule: () => void
    handleScheduleItemPress: (item: any) => void
  }
}

/**
 * AssignmentsSection - Assignments and schedule management
 *
 * Contains:
 * - Upcoming Assignments
 * - Upcoming Schedule Section
 */
export const AssignmentsSection: React.FC<AssignmentsSectionProps> = ({
  data,
  handlers,
}) => {
  return (
    <View style={styles.container}>
      {/* Upcoming Assignments Section */}
      <UpcomingAssignments
        assignments={data.upcomingAssignmentsData}
        title="Upcoming Assignments"
        showViewAll={true}
        onViewAllPress={handlers.handleViewAllAssignments}
      />

      {/* Upcoming Schedule Section */}
      <UpcomingSchedule
        scheduleItems={data.upcomingScheduleData}
        title="Upcoming Schedule"
        showViewAll={true}
        onViewAllPress={handlers.handleViewFullSchedule}
        onScheduleItemPress={handlers.handleScheduleItemPress}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
    paddingVertical: spacing.md, // 16px vertical spacing
    marginBottom: spacing.lg, // 24px spacing between sections
  },
})
