import React from "react"
import { View, StyleSheet } from "react-native"
import { spacing } from "app/theme"
import {
  LastWatchedCourse,
  ContinueLearning,
  RecentCourses,
} from "."

interface LearningSectionProps {
  /**
   * Learning-related data
   */
  data: {
    lastCourseData: any
    recentCoursesData: any[]
  }

  /**
   * Learning interaction handlers
   */
  handlers: {
    handleContinueCourse: (courseTitle?: string) => void
    handleViewAllCourses: () => void
    handleLastCoursePress: () => void
  }
}

/**
 * LearningSection - Learning progress and course management
 *
 * Contains:
 * - Last Watched Course
 * - Continue Learning
 * - Recent Courses
 */
export const LearningSection: React.FC<LearningSectionProps> = ({
  data,
  handlers,
}) => {
  return (
    <View style={styles.container}>
      {/* Last Watched Course Section */}
      <LastWatchedCourse
        course={data.lastCourseData}
        showEmpty={true}
        onPress={handlers.handleLastCoursePress}
        onContinue={() => handlers.handleContinueCourse(data.lastCourseData.title)}
      />

      {/* Continue Learning Section */}
      <ContinueLearning
        lastCourse={data.lastCourseData}
        overallProgress={45}
        onContinueCourse={() => handlers.handleContinueCourse(data.lastCourseData.title)}
        onViewAllCourses={handlers.handleViewAllCourses}
        show={true}
      />

      {/* Recent Courses Section */}
      <RecentCourses
        courses={data.recentCoursesData}
        title="Recent Courses"
        showViewAll={true}
        onViewAllPress={handlers.handleViewAllCourses}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
    paddingVertical: spacing.md, // 16px vertical spacing
    marginBottom: spacing.lg, // 24px spacing between sections
  },
})
