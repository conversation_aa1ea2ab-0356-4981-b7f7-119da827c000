import React from "react"
import { View, StyleSheet } from "react-native"
import { colors, spacing } from "app/theme"
import {
  PersonalizedGreeting,
  DailyMotivationQuote,
  DailyTarget,
  ProgressIndicator,
  ProgressRing,
} from "../../../components"

interface PersonalSectionProps {
  /**
   * User and personal data
   */
  data: {
    userData: any
    dailyTargetData: any
  }

  /**
   * Personal interaction handlers
   */
  handlers: {
    handleGreetingPress: () => void
    handleQuotePress: () => void
    handleDailyTargetPress: () => void
    handleEditDailyTarget: () => void
  }
}

/**
 * PersonalSection - Personal greeting, motivation, and progress components
 *
 * Contains:
 * - Personalized Greeting
 * - Daily Motivation Quote
 * - Daily Target
 * - Progress Ring
 * - Progress Indicator
 */
export const PersonalSection: React.FC<PersonalSectionProps> = ({
  data,
  handlers,
}) => {
  console.log("🔧 PersonalSection rendering with data:", data)

  return (
    <View style={styles.container}>
      {/* Personalized Greeting Section */}
      <PersonalizedGreeting
        userName={data.userData.displayName}
        showTimeBasedGreeting={true}
        onPress={handlers.handleGreetingPress}
      />

      {/* Daily Motivation Quote Section */}
      <DailyMotivationQuote
        showIcon={true}
        onPress={handlers.handleQuotePress}
      />

      {/* Daily Target Section */}
      <DailyTarget
        target={data.dailyTargetData}
        onPress={handlers.handleDailyTargetPress}
        showEditButton={true}
        onEdit={handlers.handleEditDailyTarget}
      />

      {/* Progress Ring Section */}
      <View style={styles.progressRingContainer}>
        <ProgressRing
          progress={75}
          size={120}
          showCenter={true}
        />
      </View>

      {/* Progress Indicator Section */}
      <View style={styles.progressIndicatorContainer}>
        <ProgressIndicator
          progress={65}
          label="Overall Learning Progress"
          showPercentage={true}
          color={colors.palette.primary500}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
    paddingVertical: spacing.md, // 16px vertical spacing
    marginBottom: spacing.lg, // 24px spacing between sections
  },
  progressRingContainer: {
    alignItems: "center",
    marginVertical: spacing.md,
  },
  progressIndicatorContainer: {
    paddingHorizontal: spacing.xs,
    marginVertical: spacing.sm,
  },
})
