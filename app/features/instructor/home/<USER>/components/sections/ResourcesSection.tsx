import React from "react"
import { View, TouchableOpacity, StyleSheet } from "react-native"
import { Text, Icon } from "@/components"
import { spacing, colors } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ResourcesSectionProps {
  data: {
    teachingResources: any[]
  }
  handlers: {
    handleViewAllResources: () => void
    handleResourcePress: (resource: any) => void
  }
}

export const ResourcesSection: React.FC<ResourcesSectionProps> = ({
  data,
  handlers,
}) => {
  const { themed } = useAppTheme()

  const getResourceIcon = (type: string) => {
    switch (type) {
      case "documentation":
        return "view"
      case "guide":
        return "components"
      case "presentation":
        return "settings"
      default:
        return "view"
    }
  }

  const getResourceColor = (category: string) => {
    switch (category) {
      case "Reference":
        return colors.palette.primary500
      case "Teaching Material":
        return colors.palette.success500
      case "Slides":
        return colors.palette.warning500
      default:
        return colors.textDim
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.sectionHeader}>
        <Text style={themed(styles.sectionTitle)}>Teaching Resources</Text>
        <TouchableOpacity onPress={handlers.handleViewAllResources}>
          <Text style={themed(styles.viewAllText)}>View All</Text>
        </TouchableOpacity>
      </View>

      {data.teachingResources.slice(0, 3).map((resource) => (
        <TouchableOpacity
          key={resource.id}
          style={themed(styles.resourceItem)}
          onPress={() => handlers.handleResourcePress(resource)}
          activeOpacity={0.7}
        >
          <View style={[styles.resourceIcon, { backgroundColor: getResourceColor(resource.category) }]}>
            <Icon 
              icon={getResourceIcon(resource.type)} 
              size={16} 
              color={colors.palette.neutral100} 
            />
          </View>
          <View style={styles.resourceInfo}>
            <Text style={themed(styles.resourceTitle)} numberOfLines={1}>
              {resource.title}
            </Text>
            <Text style={themed(styles.resourceDescription)} numberOfLines={2}>
              {resource.description}
            </Text>
            <View style={styles.resourceMeta}>
              <Text style={themed(styles.resourceCategory)}>{resource.category}</Text>
              <Text style={themed(styles.resourceUpdated)}>Updated {resource.lastUpdated}</Text>
            </View>
          </View>
          <Icon icon="caretRight" size={16} color={themed(styles.actionArrow).color} />
        </TouchableOpacity>
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.md,
    marginBottom: spacing.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.palette.primary500,
  },
  resourceItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
  },
  resourceIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: spacing.sm,
  },
  resourceInfo: {
    flex: 1,
  },
  resourceTitle: {
    fontSize: 14,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
    marginBottom: 4,
  },
  resourceDescription: {
    fontSize: 12,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
    marginBottom: spacing.xs,
  },
  resourceMeta: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  resourceCategory: {
    fontSize: 11,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.palette.primary500,
  },
  resourceUpdated: {
    fontSize: 11,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
  },
  actionArrow: {
    color: colors.textDim,
  },
})
