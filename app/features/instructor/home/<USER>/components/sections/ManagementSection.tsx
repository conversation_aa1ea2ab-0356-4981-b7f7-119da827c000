import React from "react"
import { View, TouchableOpacity, StyleSheet, ScrollView } from "react-native"
import { Text, Icon } from "@/components"
import { spacing, colors } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ManagementSectionProps {
  data: {
    pendingGrading: any[]
    quickActions: any[]
  }
  handlers: {
    handleCreateCourse: () => void
    handleViewPendingGrading: () => void
    handleGradeAssignment: (assignmentId: string) => void
    handleQuickActionPress: (action: any) => void
  }
}

/**
 * ManagementSection - Course and student management tools
 *
 * Contains:
 * - Quick Actions for instructors
 * - Pending Grading Queue
 * - Management Tools
 */
export const ManagementSection: React.FC<ManagementSectionProps> = ({
  data,
  handlers,
}) => {
  const { themed } = useAppTheme()

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return colors.palette.angry500
      case "medium":
        return colors.palette.warning500
      case "low":
        return colors.palette.success500
      default:
        return colors.palette.neutral400
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "assignment":
        return "check"
      case "quiz":
        return "help"
      case "project":
        return "components"
      default:
        return "view"
    }
  }

  return (
    <View style={styles.container}>
      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={themed(styles.sectionTitle)}>Quick Actions</Text>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.actionsScroll}
          contentContainerStyle={styles.actionsContainer}
        >
          {data.quickActions.map((action, index) => (
            <TouchableOpacity
              key={action.id}
              style={[
                themed(styles.actionCard),
                { backgroundColor: action.color + "15" }, // 15% opacity
                index === 0 && styles.firstAction,
                index === data.quickActions.length - 1 && styles.lastAction,
              ]}
              onPress={() => handlers.handleQuickActionPress(action)}
              activeOpacity={0.8}
            >
              <View style={[styles.actionIcon, { backgroundColor: action.color }]}>
                <Icon icon={action.icon} size={20} color={colors.palette.neutral100} />
              </View>
              <Text style={themed(styles.actionTitle)}>{action.title}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Pending Grading */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={themed(styles.sectionTitle)}>Pending Grading</Text>
          <TouchableOpacity onPress={handlers.handleViewPendingGrading}>
            <Text style={themed(styles.viewAllText)}>View All ({data.pendingGrading.length})</Text>
          </TouchableOpacity>
        </View>

        {data.pendingGrading.slice(0, 3).map((item) => (
          <TouchableOpacity
            key={item.id}
            style={themed(styles.gradingItem)}
            onPress={() => handlers.handleGradeAssignment(item.id)}
            activeOpacity={0.7}
          >
            <View style={styles.gradingInfo}>
              <View style={styles.gradingHeader}>
                <Text style={themed(styles.studentName)}>{item.studentName}</Text>
                <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}>
                  <Text style={styles.priorityText}>{item.priority.toUpperCase()}</Text>
                </View>
              </View>
              <Text style={themed(styles.assignmentTitle)} numberOfLines={1}>
                {item.assignmentTitle}
              </Text>
              <Text style={themed(styles.courseText)}>{item.course}</Text>
              <View style={styles.gradingMeta}>
                <Text style={themed(styles.submittedText)}>
                  Submitted {item.submittedAt}
                </Text>
                <Text style={themed(styles.dueText)}>
                  Due {item.dueDate}
                </Text>
              </View>
            </View>
            <View style={styles.gradingActions}>
              <Icon 
                icon={getTypeIcon(item.type)} 
                size={16} 
                color={themed(styles.typeIcon).color} 
              />
              <Icon 
                icon="caretRight" 
                size={16} 
                color={themed(styles.actionArrow).color} 
              />
            </View>
          </TouchableOpacity>
        ))}

        {data.pendingGrading.length === 0 && (
          <View style={themed(styles.emptyState)}>
            <Icon icon="check" size={32} color={themed(styles.emptyIcon).color} />
            <Text style={themed(styles.emptyText)}>All caught up!</Text>
            <Text style={themed(styles.emptySubtext)}>No pending grading at the moment.</Text>
          </View>
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
    paddingVertical: spacing.md, // 16px vertical spacing
    marginBottom: spacing.lg, // 24px spacing between sections
  },
  section: {
    marginBottom: spacing.lg, // 24px
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md, // 16px
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    fontFamily: "Montserrat",
    color: colors.text,
    marginBottom: spacing.md, // 16px
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: "500",
    fontFamily: "Montserrat",
    color: colors.palette.primary500,
  },
  actionsScroll: {
    marginBottom: spacing.md, // 16px
  },
  actionsContainer: {
    paddingRight: spacing.sm, // 12px
  },
  actionCard: {
    width: 120,
    height: 100,
    borderRadius: 16,
    padding: spacing.md, // 16px
    marginRight: spacing.md, // 16px
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.palette.neutral300,
  },
  firstAction: {
    marginLeft: 0,
  },
  lastAction: {
    marginRight: 0,
  },
  actionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: spacing.xs, // 8px
  },
  actionTitle: {
    fontSize: 12,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.text,
    textAlign: "center",
  },
  gradingItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    padding: spacing.md, // 16px
    marginBottom: spacing.sm, // 12px
  },
  gradingInfo: {
    flex: 1,
  },
  gradingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  studentName: {
    fontSize: 14,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
  },
  priorityText: {
    fontSize: 10,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.palette.neutral100,
  },
  assignmentTitle: {
    fontSize: 13,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.text,
    marginBottom: 2,
  },
  courseText: {
    fontSize: 12,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
    marginBottom: spacing.xs, // 8px
  },
  gradingMeta: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  submittedText: {
    fontSize: 11,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
  },
  dueText: {
    fontSize: 11,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.palette.warning500,
  },
  gradingActions: {
    flexDirection: "row",
    alignItems: "center",
  },
  typeIcon: {
    color: colors.palette.primary500,
    marginRight: spacing.xs, // 8px
  },
  actionArrow: {
    color: colors.textDim,
  },
  emptyState: {
    alignItems: "center",
    padding: spacing.xl, // 32px
    backgroundColor: colors.palette.neutral200,
    borderRadius: 16,
  },
  emptyIcon: {
    color: colors.palette.success500,
    marginBottom: spacing.sm, // 12px
  },
  emptyText: {
    fontSize: 16,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 12,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
    textAlign: "center",
  },
})
