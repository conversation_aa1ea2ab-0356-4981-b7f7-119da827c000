import React from "react"
import { View, TouchableOpacity, StyleSheet, Image, ScrollView } from "react-native"
import { Text, Icon } from "@/components"
import { spacing, colors } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface TeachingSectionProps {
  data: {
    activeCourses: any[]
    teachingStats: any
    recentStudentActivity: any[]
  }
  handlers: {
    handleViewAllCourses: () => void
    handleCoursePress: (course: any) => void
    handleViewAllStudents: () => void
    handleStudentPress: (student: any) => void
  }
}

/**
 * TeachingSection - Teaching overview and active courses
 *
 * Contains:
 * - Active Courses Overview
 * - Recent Student Activity
 * - Quick Course Actions
 */
export const TeachingSection: React.FC<TeachingSectionProps> = ({
  data,
  handlers,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={styles.container}>
      {/* Section Header */}
      <View style={styles.sectionHeader}>
        <Text style={themed(styles.sectionTitle)}>Your Teaching</Text>
        <TouchableOpacity onPress={handlers.handleViewAllCourses}>
          <Text style={themed(styles.viewAllText)}>View All</Text>
        </TouchableOpacity>
      </View>

      {/* Active Courses */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.coursesScroll}
        contentContainerStyle={styles.coursesContainer}
      >
        {data.activeCourses.map((course, index) => (
          <TouchableOpacity
            key={course.id}
            style={[
              themed(styles.courseCard),
              index === 0 && styles.firstCard,
              index === data.activeCourses.length - 1 && styles.lastCard,
            ]}
            onPress={() => handlers.handleCoursePress(course)}
            activeOpacity={0.8}
          >
            <Image source={{ uri: course.thumbnail }} style={styles.courseThumbnail} />
            <View style={styles.courseInfo}>
              <Text style={themed(styles.courseTitle)} numberOfLines={2}>
                {course.title}
              </Text>
              <Text style={themed(styles.courseCode)}>
                {course.code}
              </Text>
              <View style={styles.courseStats}>
                <View style={styles.courseStat}>
                  <Icon icon="community" size={12} color={themed(styles.statIcon).color} />
                  <Text style={themed(styles.statText)}>{course.students}</Text>
                </View>
                <View style={styles.courseStat}>
                  <Icon icon="clock" size={12} color={themed(styles.statIcon).color} />
                  <Text style={themed(styles.statText)}>{course.nextClass}</Text>
                </View>
              </View>
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View 
                    style={[
                      styles.progressFill, 
                      { width: `${course.progress}%` }
                    ]} 
                  />
                </View>
                <Text style={themed(styles.progressText)}>{course.progress}%</Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Recent Student Activity */}
      <View style={styles.activitySection}>
        <View style={styles.activityHeader}>
          <Text style={themed(styles.activityTitle)}>Recent Activity</Text>
          <TouchableOpacity onPress={handlers.handleViewAllStudents}>
            <Text style={themed(styles.viewAllText)}>View All</Text>
          </TouchableOpacity>
        </View>

        {data.recentStudentActivity.slice(0, 3).map((activity) => (
          <TouchableOpacity
            key={activity.id}
            style={themed(styles.activityItem)}
            onPress={() => handlers.handleStudentPress(activity)}
            activeOpacity={0.7}
          >
            <Image source={{ uri: activity.studentAvatar }} style={styles.studentAvatar} />
            <View style={styles.activityInfo}>
              <Text style={themed(styles.studentName)}>{activity.studentName}</Text>
              <Text style={themed(styles.activityAction)}>{activity.action}</Text>
              <Text style={themed(styles.activityCourse)}>{activity.course}</Text>
            </View>
            <View style={styles.activityMeta}>
              <Text style={themed(styles.activityTime)}>{activity.timestamp}</Text>
              <Icon 
                icon={activity.type === "assignment" ? "check" : activity.type === "question" ? "help" : "view"} 
                size={16} 
                color={themed(styles.activityIcon).color} 
              />
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
    paddingVertical: spacing.md, // 16px vertical spacing
    marginBottom: spacing.lg, // 24px spacing between sections
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md, // 16px
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.palette.primary500,
  },
  coursesScroll: {
    marginBottom: spacing.lg, // 24px
  },
  coursesContainer: {
    paddingRight: spacing.sm, // 12px
  },
  courseCard: {
    width: 280,
    backgroundColor: colors.palette.neutral100,
    borderRadius: 16,
    padding: spacing.md, // 16px
    marginRight: spacing.md, // 16px
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  firstCard: {
    marginLeft: 0,
  },
  lastCard: {
    marginRight: 0,
  },
  courseThumbnail: {
    width: "100%",
    height: 120,
    borderRadius: 12,
    marginBottom: spacing.sm, // 12px
  },
  courseInfo: {
    flex: 1,
  },
  courseTitle: {
    fontSize: 16,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
    marginBottom: 4,
  },
  courseCode: {
    fontSize: 12,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
    marginBottom: spacing.sm, // 12px
  },
  courseStats: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.sm, // 12px
  },
  courseStat: {
    flexDirection: "row",
    alignItems: "center",
  },
  statIcon: {
    color: colors.textDim,
  },
  statText: {
    fontSize: 12,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
    marginLeft: 4,
  },
  progressContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  progressBar: {
    flex: 1,
    height: 6,
    backgroundColor: colors.palette.neutral300,
    borderRadius: 3,
    marginRight: spacing.xs, // 8px
  },
  progressFill: {
    height: "100%",
    backgroundColor: colors.palette.primary500,
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.text,
  },
  activitySection: {
    marginTop: spacing.md, // 16px
  },
  activityHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md, // 16px
  },
  activityTitle: {
    fontSize: 16,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
  },
  activityItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: colors.palette.neutral200,
    borderRadius: 12,
    padding: spacing.sm, // 12px
    marginBottom: spacing.xs, // 8px
  },
  studentAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: spacing.sm, // 12px
  },
  activityInfo: {
    flex: 1,
  },
  studentName: {
    fontSize: 14,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.text,
    marginBottom: 2,
  },
  activityAction: {
    fontSize: 12,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
    marginBottom: 2,
  },
  activityCourse: {
    fontSize: 11,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
  },
  activityMeta: {
    alignItems: "flex-end",
  },
  activityTime: {
    fontSize: 11,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
    marginBottom: 4,
  },
  activityIcon: {
    color: colors.palette.primary500,
  },
})
