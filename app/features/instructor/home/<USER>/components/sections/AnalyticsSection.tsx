import React from "react"
import { View, TouchableOpacity, StyleSheet } from "react-native"
import { Text, Icon } from "@/components"
import { spacing, colors } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface AnalyticsSectionProps {
  data: {
    courseAnalytics: any[]
    teachingStats: any
  }
  handlers: {
    handleViewAnalytics: () => void
    handleViewCourseAnalytics: (courseId: string) => void
  }
}

export const AnalyticsSection: React.FC<AnalyticsSectionProps> = ({
  data,
  handlers,
}) => {
  const { themed } = useAppTheme()

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return "caretUp"
      case "down":
        return "caretDown"
      default:
        return "minus"
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return colors.palette.success500
      case "down":
        return colors.palette.angry500
      default:
        return colors.textDim
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.sectionHeader}>
        <Text style={themed(styles.sectionTitle)}>Teaching Analytics</Text>
        <TouchableOpacity onPress={handlers.handleViewAnalytics}>
          <Text style={themed(styles.viewAllText)}>View All</Text>
        </TouchableOpacity>
      </View>

      {data.courseAnalytics.slice(0, 2).map((course) => (
        <TouchableOpacity
          key={course.id}
          style={themed(styles.analyticsCard)}
          onPress={() => handlers.handleViewCourseAnalytics(course.id)}
          activeOpacity={0.8}
        >
          <View style={styles.courseHeader}>
            <Text style={themed(styles.courseTitle)} numberOfLines={1}>
              {course.courseTitle}
            </Text>
            <Icon 
              icon={getTrendIcon(course.trend)} 
              size={16} 
              color={getTrendColor(course.trend)} 
            />
          </View>
          
          <View style={styles.metricsGrid}>
            <View style={styles.metric}>
              <Text style={themed(styles.metricValue)}>{course.totalStudents}</Text>
              <Text style={themed(styles.metricLabel)}>Students</Text>
            </View>
            <View style={styles.metric}>
              <Text style={themed(styles.metricValue)}>{course.completionRate}%</Text>
              <Text style={themed(styles.metricLabel)}>Completion</Text>
            </View>
            <View style={styles.metric}>
              <Text style={themed(styles.metricValue)}>{course.averageGrade}</Text>
              <Text style={themed(styles.metricLabel)}>Avg Grade</Text>
            </View>
            <View style={styles.metric}>
              <Text style={themed(styles.metricValue)}>{course.engagementScore}%</Text>
              <Text style={themed(styles.metricLabel)}>Engagement</Text>
            </View>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.md,
    marginBottom: spacing.lg,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: "500",
    fontFamily: "Lexend Deca",
    color: colors.palette.primary500,
  },
  analyticsCard: {
    backgroundColor: colors.palette.neutral200,
    borderRadius: 16,
    padding: spacing.md,
    marginBottom: spacing.sm,
  },
  courseHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.md,
  },
  courseTitle: {
    fontSize: 16,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
    flex: 1,
  },
  metricsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  metric: {
    alignItems: "center",
    flex: 1,
  },
  metricValue: {
    fontSize: 18,
    fontWeight: "600",
    fontFamily: "Lexend Deca",
    color: colors.text,
    marginBottom: 2,
  },
  metricLabel: {
    fontSize: 10,
    fontWeight: "300",
    fontFamily: "Lexend Deca",
    color: colors.textDim,
    textAlign: "center",
  },
})
