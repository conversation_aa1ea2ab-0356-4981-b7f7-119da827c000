import React from "react"
import { View, StyleSheet } from "react-native"
import { spacing } from "app/theme"
import {
  QuickStats,
  QuickActions,
  ResumeButton,
} from "."

interface ActionsSectionProps {
  /**
   * Actions-related data
   */
  data: {
    quickStatsData: any[]
    quickActionsData: any[]
  }

  /**
   * Actions interaction handlers
   */
  handlers: {
    handleResumeButtonPress: () => void
  }
}

/**
 * ActionsSection - Quick stats, actions, and resume functionality
 *
 * Contains:
 * - Quick Stats Section
 * - Quick Actions Section
 * - Resume Button
 */
export const ActionsSection: React.FC<ActionsSectionProps> = ({
  data,
  handlers,
}) => {
  return (
    <View style={styles.container}>
      {/* Quick Stats Section */}
      <QuickStats
        stats={data.quickStatsData}
        columns={2}
      />

      {/* Quick Actions Section */}
      <QuickActions
        actions={data.quickActionsData}
        title="Quick Actions"
        columns={4}
      />

      {/* Resume Button Section */}
      <View style={styles.resumeButtonContainer}>
        <ResumeButton
          text="Continue Your Journey"
          variant="primary"
          showIcon={true}
          onPress={handlers.handleResumeButtonPress}
        />
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
    paddingVertical: spacing.md, // 16px vertical spacing
    marginBottom: spacing.lg, // 24px spacing between sections
  },
  resumeButtonContainer: {
    marginVertical: spacing.sm,
  },
})
