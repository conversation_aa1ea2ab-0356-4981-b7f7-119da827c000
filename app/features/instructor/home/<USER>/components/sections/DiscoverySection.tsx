import React from "react"
import { View, StyleSheet } from "react-native"
import { spacing } from "app/theme"
import {
  RecommendedCourses,
  PopularCategories,
  NewReleases,
  TrendingNow,
} from "."

interface DiscoverySectionProps {
  /**
   * Discovery and exploration data
   */
  data: {
    recommendedCoursesData: any[]
    popularCategoriesData: any[]
    newReleasesData: any[]
    trendingCoursesData: any[]
  }

  /**
   * Discovery interaction handlers
   */
  handlers: {
    handleViewAllRecommended: () => void
    handleViewAllCategories: () => void
    handleViewAllNewReleases: () => void
    handleViewAllTrending: () => void
    handleRecommendedCoursePress: (course: any) => void
    handleNewCoursePress: (course: any) => void
    handleTrendingCoursePress: (course: any) => void
    handleCategoryPress: (category: any) => void
  }
}

/**
 * DiscoverySection - Course discovery and exploration
 *
 * Contains:
 * - Recommended Courses
 * - Popular Categories
 * - New Releases
 * - Trending Now
 */
export const DiscoverySection: React.FC<DiscoverySectionProps> = ({
  data,
  handlers,
}) => {
  return (
    <View style={styles.container}>
      {/* Recommended Courses Section */}
      <RecommendedCourses
        courses={data.recommendedCoursesData}
        title="Recommended for You"
        showViewAll={true}
        onViewAllPress={handlers.handleViewAllRecommended}
        onCoursePress={handlers.handleRecommendedCoursePress}
      />

      {/* Popular Categories Section */}
      <PopularCategories
        categories={data.popularCategoriesData}
        title="Popular Categories"
        columns={2}
        showViewAll={true}
        onViewAllPress={handlers.handleViewAllCategories}
        onCategoryPress={handlers.handleCategoryPress}
      />

      {/* New Releases Section */}
      <NewReleases
        courses={data.newReleasesData}
        title="New Releases"
        showViewAll={true}
        onViewAllPress={handlers.handleViewAllNewReleases}
        onCoursePress={handlers.handleNewCoursePress}
      />

      {/* Trending Now Section */}
      <TrendingNow
        courses={data.trendingCoursesData}
        title="Trending Now"
        showViewAll={true}
        onViewAllPress={handlers.handleViewAllTrending}
        onCoursePress={handlers.handleTrendingCoursePress}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
    paddingVertical: spacing.md, // 16px vertical spacing
    marginBottom: spacing.lg, // 24px spacing between sections
  },
})
