import React from "react"
import { View, Text, StyleSheet } from "react-native"
import { spacing } from "app/theme"
import {
  WelcomeBanner,
} from "."

interface WelcomeSectionProps {
  /**
   * Welcome and completion data
   */
  data: {
    userData: any
  }
}

/**
 * WelcomeSection - Welcome banner and completion indicator
 *
 * Contains:
 * - Welcome Banner
 * - End of Content Indicator
 */
export const WelcomeSection: React.FC<WelcomeSectionProps> = ({
  data,
}) => {
  return (
    <View style={styles.container}>
      {/* Welcome Banner Section */}
      <WelcomeBanner
        user={data.userData}
        showQuote={true}
        variant="default"
      />

      {/* End of content */}
      <View style={styles.endOfContentContainer}>
        <Text style={styles.endOfContentText}>
          🎉 COMPLETE HOME SCREEN - All sections loaded!
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
    paddingVertical: spacing.md, // 16px vertical spacing
    marginBottom: spacing.lg, // 24px spacing between sections
  },
  endOfContentContainer: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: spacing.md, // 16px internal padding
    paddingVertical: spacing.sm, // 12px vertical padding
    borderRadius: 8,
    marginTop: spacing.md,
  },
  endOfContentText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '300', // Light font weight per guidelines
    textAlign: 'center',
  },
})
