import React from "react"
import { View, StyleSheet } from "react-native"
import { spacing } from "app/theme"
import {
  PersonalSection,
  LearningSection,
  ActionsSection,
  AssignmentsSection,
  DiscoverySection,
  WelcomeSection,
} from "./sections"

interface InstructorHomeScreenContentProps {
  /**
   * All data needed for the content sections
   */
  data: {
    userData: any
    lastCourseData: any
    dailyTargetData: any
    quickStatsData: any[]
    recentCoursesData: any[]
    upcomingAssignmentsData: any[]
    quickActionsData: any[]
    recommendedCoursesData: any[]
    popularCategoriesData: any[]
    newReleasesData: any[]
    trendingCoursesData: any[]
    upcomingScheduleData: any[]
  }
  /**
   * All handlers for user interactions
   */
  handlers: {
    // Navigation handlers
    handleNavigateToProfile: () => void
    handleNavigateToNotifications: () => void
    handleNavigateToSearch: () => void
    handleViewStreak: () => void

    // Personal section handlers
    handleGreetingPress: () => void
    handleQuotePress: () => void
    handleDailyTargetPress: () => void
    handleEditDailyTarget: () => void

    // Learning section handlers
    handleContinueCourse: (courseTitle: string) => void
    handleViewAllCourses: () => void
    handleLastCoursePress: () => void

    // Actions section handlers
    handleResumeButtonPress: () => void

    // Assignments section handlers
    handleViewAllAssignments: () => void
    handleViewFullSchedule: () => void
    handleScheduleItemPress: (item: any) => void

    // Discovery section handlers
    handleViewAllRecommended: () => void
    handleViewAllCategories: () => void
    handleViewAllNewReleases: () => void
    handleViewAllTrending: () => void
    handleRecommendedCoursePress: (course: any) => void
    handleNewCoursePress: (course: any) => void
    handleTrendingCoursePress: (course: any) => void
    handleCategoryPress: (category: any) => void
  }
}

/**
 * InstructorHomeScreenContent - Refactored into smaller section components
 *
 * This component has been cloned from StudentHomeScreenContent and uses the same structure.
 * It's broken down into 6 logical sections:
 * - PersonalSection: Greeting, motivation, targets, progress
 * - LearningSection: Course management and learning progress
 * - ActionsSection: Quick stats, actions, and resume functionality
 * - AssignmentsSection: Assignments and schedule management
 * - DiscoverySection: Course discovery and exploration
 * - WelcomeSection: Welcome banner and completion indicator
 *
 * Benefits:
 * - Much smaller and focused components
 * - Better separation of concerns
 * - Easier to maintain and test
 * - Cleaner prop passing
 * - Consistent with student app architecture
 */
export const InstructorHomeScreenContent: React.FC<InstructorHomeScreenContentProps> = ({
  data,
  handlers,
}) => {
  console.log("📚 InstructorHomeScreenContent rendering with data:", data)
  console.log("📚 InstructorHomeScreenContent handlers:", handlers)
  console.log("🔄 InstructorHomeScreenContent updated - testing layout")

  return (
    <View style={styles.container}>
      {/* Personal Section - Greeting, motivation, targets, progress */}
      <PersonalSection
        data={{
          userData: data.userData,
          dailyTargetData: data.dailyTargetData,
        }}
        handlers={{
          handleGreetingPress: handlers.handleGreetingPress,
          handleQuotePress: handlers.handleQuotePress,
          handleDailyTargetPress: handlers.handleDailyTargetPress,
          handleEditDailyTarget: handlers.handleEditDailyTarget,
        }}
      />

      {/* Actions Section - Quick stats, actions, resume */}
      <ActionsSection
        data={{
          quickStatsData: data.quickStatsData,
          quickActionsData: data.quickActionsData,
        }}
        handlers={{
          handleResumeButtonPress: handlers.handleResumeButtonPress,
        }}
      />

      {/* Learning Section - Course management and progress */}
      <LearningSection
        data={{
          lastCourseData: data.lastCourseData,
          recentCoursesData: data.recentCoursesData,
        }}
        handlers={{
          handleContinueCourse: handlers.handleContinueCourse,
          handleViewAllCourses: handlers.handleViewAllCourses,
          handleLastCoursePress: handlers.handleLastCoursePress,
        }}
      />

      {/* Assignments Section - Assignments and schedule */}
      <AssignmentsSection
        data={{
          upcomingAssignmentsData: data.upcomingAssignmentsData,
          upcomingScheduleData: data.upcomingScheduleData,
        }}
        handlers={{
          handleViewAllAssignments: handlers.handleViewAllAssignments,
          handleViewFullSchedule: handlers.handleViewFullSchedule,
          handleScheduleItemPress: handlers.handleScheduleItemPress,
        }}
      />

      {/* Discovery Section - Course discovery and exploration */}
      <DiscoverySection
        data={{
          recommendedCoursesData: data.recommendedCoursesData,
          popularCategoriesData: data.popularCategoriesData,
          newReleasesData: data.newReleasesData,
          trendingCoursesData: data.trendingCoursesData,
        }}
        handlers={{
          handleViewAllRecommended: handlers.handleViewAllRecommended,
          handleViewAllCategories: handlers.handleViewAllCategories,
          handleViewAllNewReleases: handlers.handleViewAllNewReleases,
          handleViewAllTrending: handlers.handleViewAllTrending,
          handleRecommendedCoursePress: handlers.handleRecommendedCoursePress,
          handleNewCoursePress: handlers.handleNewCoursePress,
          handleTrendingCoursePress: handlers.handleTrendingCoursePress,
          handleCategoryPress: handlers.handleCategoryPress,
        }}
      />

      {/* Welcome Section - Welcome banner and completion */}
      <WelcomeSection
        data={{
          userData: data.userData,
        }}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // No horizontal padding here - sections handle their own 12px padding
    paddingVertical: spacing.sm,
  },
})
