import React from "react"
import { TouchableOpacity, View, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { NewCourseCardThumbnail } from "./CourseThumbnail"
import { CourseDetails } from "./CourseDetails"
import { CourseRating } from "./CourseRating"
import { CoursePrice } from "./CoursePrice"

interface NewCourse {
  id: string
  title: string
  instructor: string
  thumbnailUrl?: string
  rating?: number
  reviewCount?: number
  price: number
  originalPrice?: number
  duration: string
  level: "beginner" | "intermediate" | "advanced"
  category: string
  releaseDate: string
  isNew: boolean
  isBestseller?: boolean
}

interface CourseCardProps {
  /**
   * Course data
   */
  course: NewCourse
  /**
   * Callback when course is pressed
   */
  onPress?: (course: NewCourse) => void
}

export const CourseCard: React.FC<CourseCardProps> = ({
  course,
  onPress,
}) => {
  const formatPrice = (price: number): string => {
    if (price === 0) return "Free"
    return `$${price.toFixed(0)}`
  }

  return (
    <TouchableOpacity
      style={$container}
      onPress={() => onPress?.(course)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`${course.title} by ${course.instructor}, new course, ${formatPrice(course.price)}`}
      accessibilityRole="button"
    >
      <NewCourseCardThumbnail
        isNew={course.isNew}
        isBestseller={course.isBestseller}
      />

      <View style={$courseInfo}>
        <CourseDetails
          title={course.title}
          instructor={course.instructor}
          releaseDate={course.releaseDate}
        />

        <CourseRating
          rating={course.rating}
          reviewCount={course.reviewCount}
          level={course.level}
        />

        <CoursePrice
          price={course.price}
          originalPrice={course.originalPrice}
          duration={course.duration}
        />
      </View>
    </TouchableOpacity>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  width: 200,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  padding: spacing.sm, // 12px internal padding
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}

const $courseInfo: ViewStyle = {
  gap: spacing.xs / 2, // 4px gap between elements
}
