import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface CourseRatingProps {
  /**
   * Course rating (1-5)
   */
  rating?: number
  /**
   * Number of reviews
   */
  reviewCount?: number
  /**
   * Course level
   */
  level: "beginner" | "intermediate" | "advanced"
}

export const CourseRating: React.FC<CourseRatingProps> = ({
  rating,
  reviewCount,
  level,
}) => {
  const getLevelColor = (level: string): string => {
    switch (level) {
      case "beginner":
        return colors.palette.success500
      case "intermediate":
        return colors.palette.warning500
      case "advanced":
        return colors.palette.angry500
      default:
        return colors.palette.primary500
    }
  }

  if (!rating) {
    return null
  }

  return (
    <View style={$container}>
      <View style={$ratingStars}>
        <Icon
          icon="star"
          size={12}
          color={colors.palette.warning500}
        />
        <Text
          style={$ratingText}
          text={rating.toFixed(1)}
        />
        {reviewCount && (
          <Text
            style={$reviewCount}
            text={`(${reviewCount})`}
          />
        )}
      </View>
      
      <View style={[$levelBadge, { backgroundColor: getLevelColor(level) + "20" }]}>
        <Text
          style={[$levelText, { color: getLevelColor(level) }]}
          text={level.toUpperCase()}
        />
      </View>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
}

const $ratingStars: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs / 2, // 4px gap
}

const $ratingText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary700, // Deep navy
}

const $reviewCount: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  color: colors.palette.neutral500,
}

const $levelBadge: ViewStyle = {
  paddingHorizontal: 8,
  paddingVertical: 2,
  borderRadius: 12, // More rounded background for modern pill shape
}

const $levelText: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - consistent with course cards
  fontWeight: "500", // Explicit medium weight for better visibility
  fontSize: 10,
  lineHeight: 12,
}
