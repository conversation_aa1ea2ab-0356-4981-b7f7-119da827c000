import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface CoursePriceProps {
  /**
   * Current price
   */
  price: number
  /**
   * Original price (for discounts)
   */
  originalPrice?: number
  /**
   * Course duration
   */
  duration: string
}

export const CoursePrice: React.FC<CoursePriceProps> = ({
  price,
  originalPrice,
  duration,
}) => {
  const formatPrice = (price: number): string => {
    if (price === 0) return "Free"
    return `$${price.toFixed(0)}`
  }

  return (
    <View style={$container}>
      <Text
        style={$currentPrice}
        text={formatPrice(price)}
      />
      {originalPrice && originalPrice > price && (
        <Text
          style={$originalPrice}
          text={formatPrice(originalPrice)}
        />
      )}
      <Text
        style={$duration}
        text={duration}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
}

const $currentPrice: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for emphasis
  fontSize: 14,
  lineHeight: 18,
  color: colors.palette.primary700, // Deep navy
}

const $originalPrice: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.neutral500,
  textDecorationLine: "line-through",
}

const $duration: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  color: colors.palette.neutral500,
}
