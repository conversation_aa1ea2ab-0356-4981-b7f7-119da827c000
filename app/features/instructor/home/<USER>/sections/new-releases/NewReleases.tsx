import React from "react"
import { View, ScrollView, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { NewReleasesHeader } from "./NewReleasesHeader"
import { CourseCard } from "./CourseCard"

interface NewCourse {
  id: string
  title: string
  instructor: string
  thumbnailUrl?: string
  rating?: number
  reviewCount?: number
  price: number
  originalPrice?: number
  duration: string
  level: "beginner" | "intermediate" | "advanced"
  category: string
  releaseDate: string
  isNew: boolean
  isBestseller?: boolean
}

interface NewReleasesProps {
  /**
   * Array of new courses
   */
  courses: NewCourse[]
  /**
   * Section title
   */
  title?: string
  /**
   * Show view all button
   */
  showViewAll?: boolean
  /**
   * Callback when view all is pressed
   */
  onViewAllPress?: () => void
  /**
   * Callback when course is pressed
   */
  onCoursePress?: (course: NewCourse) => void
}

export const NewReleases: React.FC<NewReleasesProps> = ({
  courses,
  title = "New Releases",
  showViewAll = true,
  onViewAllPress,
  onCoursePress,
}) => {
  if (courses.length === 0) {
    return null
  }

  return (
    <View style={$container}>
      <NewReleasesHeader
        title={title}
        showViewAll={showViewAll}
        onViewAllPress={onViewAllPress}
      />

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={$scrollContent}
        style={$scrollContainer}
      >
        {courses.map((course) => (
          <CourseCard
            key={course.id}
            course={course}
            onPress={onCoursePress}
          />
        ))}
      </ScrollView>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}

const $scrollContainer: ViewStyle = {
  marginHorizontal: -spacing.xs, // Negative margin for edge-to-edge scroll
}

const $scrollContent: ViewStyle = {
  paddingHorizontal: spacing.xs, // 8px horizontal padding
  gap: spacing.sm, // 12px gap between cards
}
