import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface NewCourseCardThumbnailProps {
  /**
   * Show NEW badge
   */
  isNew?: boolean
  /**
   * Show BESTSELLER badge
   */
  isBestseller?: boolean
}

export const NewCourseCardThumbnail: React.FC<NewCourseCardThumbnailProps> = ({
  isNew = false,
  isBestseller = false,
}) => {
  return (
    <View style={$container}>
      <View style={$thumbnailPlaceholder}>
        <Icon
          icon="play-circle"
          size={32}
          color={colors.palette.primary500}
        />
      </View>

      {isNew && (
        <View style={$newBadge}>
          <Text
            style={$newBadgeText}
            text="NEW"
          />
        </View>
      )}

      {isBestseller && (
        <View style={$bestsellerBadge}>
          <Text
            style={$bestsellerBadgeText}
            text="BESTSELLER"
          />
        </View>
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  position: "relative",
  marginBottom: spacing.sm, // 12px below thumbnail
}

const $thumbnailPlaceholder: ViewStyle = {
  width: "100%",
  height: 100,
  backgroundColor: colors.palette.primary100, // Light blue background
  borderRadius: 8,
  alignItems: "center",
  justifyContent: "center",
}

const $newBadge: ViewStyle = {
  position: "absolute",
  top: 8,
  left: 8,
  backgroundColor: colors.palette.success500, // Green NEW badge
  borderRadius: 8,
  paddingHorizontal: 6,
  paddingVertical: 2,
}

const $newBadgeText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 10,
  lineHeight: 12,
  color: colors.palette.neutral100, // White text
  fontWeight: "600",
}

const $bestsellerBadge: ViewStyle = {
  position: "absolute",
  top: 8,
  right: 8,
  backgroundColor: colors.palette.warning500, // Orange bestseller badge
  borderRadius: 8,
  paddingHorizontal: 6,
  paddingVertical: 2,
}

const $bestsellerBadgeText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 9,
  lineHeight: 11,
  color: colors.palette.neutral100, // White text
  fontWeight: "600",
}
