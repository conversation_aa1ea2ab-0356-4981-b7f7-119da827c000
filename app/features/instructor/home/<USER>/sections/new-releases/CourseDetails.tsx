import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface CourseDetailsProps {
  /**
   * Course title
   */
  title: string
  /**
   * Instructor name
   */
  instructor: string
  /**
   * Release date string
   */
  releaseDate: string
}

export const CourseDetails: React.FC<CourseDetailsProps> = ({
  title,
  instructor,
  releaseDate,
}) => {
  const formatReleaseDate = (dateString: string): string => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return "Today"
    if (diffDays === 1) return "Yesterday"
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
    return `${Math.floor(diffDays / 30)} months ago`
  }

  return (
    <View style={$container}>
      <Text
        style={$courseTitle}
        text={title}
        numberOfLines={2}
      />
      
      <Text
        style={$instructorName}
        text={instructor}
        numberOfLines={1}
      />
      
      <Text
        style={$releaseDate}
        text={`Released ${formatReleaseDate(releaseDate)}`}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  gap: spacing.xs / 2, // 4px gap between elements
}

const $courseTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for card titles
  fontSize: 14,
  lineHeight: 18,
  color: colors.palette.primary700, // Deep navy
}

const $instructorName: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary600, // Dark blue
}

const $releaseDate: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  color: colors.palette.success600, // Green for new release
  fontStyle: "italic",
}
