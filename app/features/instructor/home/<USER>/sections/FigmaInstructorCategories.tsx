import React from "react"
import { View, Text, TouchableOpacity, StyleSheet } from "react-native"
import { Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface FigmaInstructorCategoriesProps {
  /**
   * Categories data
   */
  categories?: string[]
  /**
   * Callbacks
   */
  onSeeAllPress?: () => void
  onCategoryPress?: (category: string) => void
}

/**
 * FigmaInstructorCategories - Categories component based on exact Figma design
 * 
 * Replicates the CATEGORY section from Figma:
 * - "Categories" heading with "See All" link
 * - Arrow icon next to "See All"
 * - Category text items (adapted for instructor)
 */
export const FigmaInstructorCategories: React.FC<FigmaInstructorCategoriesProps> = ({
  categories = ["Teaching Methods", "Course Development", "Student Engagement"],
  onSeeAllPress,
  onCategoryPress,
}) => {
  return (
    <View style={styles.container}>
      {/* Heading section */}
      <View style={styles.headingContainer}>
        {/* Categories title */}
        <Text style={styles.categoriesTitle}>
          Categories
        </Text>
        
        {/* See All section */}
        <TouchableOpacity 
          style={styles.seeAllContainer}
          onPress={onSeeAllPress}
          activeOpacity={0.7}
        >
          <Text style={styles.seeAllText}>
            SEE ALL
          </Text>
          <Icon
            icon="caretRight"
            size={10}
            color="#23408B" // MAIN BRAND COLOR from design guidelines
            style={styles.seeAllIcon}
          />
        </TouchableOpacity>
      </View>
      
      {/* Categories list */}
      <View style={styles.categoriesContainer}>
        {categories.map((category, index) => (
          <TouchableOpacity
            key={index}
            style={styles.categoryItem}
            onPress={() => onCategoryPress?.(category)}
            activeOpacity={0.7}
          >
            <Text style={[
              styles.categoryText,
              index === 0 ? styles.categoryTextActive : styles.categoryTextInactive
            ]}>
              {category}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: 365, // From Figma: width: 365
    height: 60, // From Figma: height: 60
  },
  headingContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: 358, // From Figma: width: 358
    height: 26, // From Figma: height: 26
    marginBottom: 15, // Space before categories
  },
  categoriesTitle: {
    fontFamily: typography.primary.semiBold, // 600 weight - for headings per design guidelines
    fontSize: 18,
    lineHeight: 26,
    color: "#132339", // Deep navy from design guidelines
  },
  seeAllContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  seeAllText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 12,
    lineHeight: 15,
    textTransform: "uppercase",
    textAlign: "right",
    color: "#23408B", // MAIN BRAND COLOR from design guidelines
    marginRight: 10,
  },
  seeAllIcon: {
    // Icon styling handled by Icon component
  },
  categoriesContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  categoryItem: {
    // No additional styling needed - just for touch handling
  },
  categoryText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 15,
    lineHeight: 19,
  },
  categoryTextActive: {
    color: "#A0A4AB", // From Figma: fill_1KRXD1 (first category is active)
  },
  categoryTextInactive: {
    color: "#23408B", // MAIN BRAND COLOR from design guidelines
  },
})
