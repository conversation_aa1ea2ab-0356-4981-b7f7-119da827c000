import React from "react"
import { View, ViewStyle, TextStyle, Text } from "react-native"
import { colors, spacing, typography } from "app/theme"

interface AssignmentInfoProps {
  /**
   * Assignment title
   */
  title: string
  /**
   * Course name
   */
  courseName: string
}

export const AssignmentInfo: React.FC<AssignmentInfoProps> = ({
  title,
  courseName,
}) => {
  return (
    <View style={$container}>
      <Text
        style={$assignmentTitle}
        numberOfLines={1}
      >
        {title}
      </Text>

      <Text
        style={$courseName}
        numberOfLines={1}
      >
        {courseName}
      </Text>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  flex: 1,
}

const $assignmentTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for card titles
  fontSize: 14,
  lineHeight: 18,
  color: colors.palette.primary700, // Deep navy
  marginBottom: spacing.xs / 2, // 4px below title
}

const $courseName: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary600, // Dark blue
}
