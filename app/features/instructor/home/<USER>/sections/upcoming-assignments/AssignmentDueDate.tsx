import React from "react"
import { View, ViewStyle, TextStyle, Text } from "react-native"
import { colors, spacing, typography } from "app/theme"

interface AssignmentDueDateProps {
  /**
   * Due date string
   */
  dueDate: string
  /**
   * Priority level
   */
  priority: "high" | "medium" | "low"
}

export const AssignmentDueDate: React.FC<AssignmentDueDateProps> = ({
  dueDate,
  priority,
}) => {
  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case "high":
        return colors.palette.angry500
      case "medium":
        return colors.palette.warning500
      case "low":
        return colors.palette.success500
      default:
        return colors.palette.primary500
    }
  }

  const formatDueDate = (dueDate: string): string => {
    const date = new Date(dueDate)
    const now = new Date()
    const diffTime = date.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return "Due today"
    if (diffDays === 1) return "Due tomorrow"
    if (diffDays > 1) return `Due in ${diffDays} days`
    return "Overdue"
  }

  return (
    <View style={$container}>
      <Text
        style={[$dueDateText, { color: getPriorityColor(priority) }]}
      >
        {formatDueDate(dueDate)}
      </Text>

      <View style={[$priorityIndicator, { backgroundColor: getPriorityColor(priority) }]} />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  alignItems: "flex-end",
  gap: spacing.xs / 2, // 4px gap
}

const $dueDateText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  textAlign: "right",
}

const $priorityIndicator: ViewStyle = {
  width: 8,
  height: 8,
  borderRadius: 4,
}
