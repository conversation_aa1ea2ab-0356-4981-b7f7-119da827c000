import React from "react"
import { View, ViewStyle } from "react-native"
import { Icon } from "app/components"
import { colors } from "app/theme"

interface AssignmentIconProps {
  /**
   * Assignment type
   */
  type: "quiz" | "assignment" | "project" | "exam"
  /**
   * Priority level
   */
  priority: "high" | "medium" | "low"
}

export const AssignmentIcon: React.FC<AssignmentIconProps> = ({
  type,
  priority,
}) => {
  const getAssignmentIcon = (type: string): string => {
    switch (type) {
      case "quiz":
        return "help-circle"
      case "assignment":
        return "file-text"
      case "project":
        return "folder"
      case "exam":
        return "award"
      default:
        return "file-text"
    }
  }

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case "high":
        return colors.palette.angry500
      case "medium":
        return colors.palette.warning500
      case "low":
        return colors.palette.success500
      default:
        return colors.palette.primary500
    }
  }

  return (
    <View style={[$container, { backgroundColor: getPriorityColor(priority) + "20" }]}>
      <Icon
        icon={getAssignmentIcon(type)}
        size={20}
        color={getPriorityColor(priority)}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 20,
  alignItems: "center",
  justifyContent: "center",
}
