import React from "react"
import { View, ViewStyle, TextStyle, Text } from "react-native"
import { Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface EmptyAssignmentsProps {
  /**
   * Empty state message
   */
  message?: string
}

export const EmptyAssignments: React.FC<EmptyAssignmentsProps> = ({
  message = "No upcoming assignments",
}) => {
  return (
    <View style={$container}>
      <Icon
        icon="calendar"
        size={48}
        color={colors.palette.neutral400}
      />
      <Text style={$emptyText}>
        {message}
      </Text>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  alignItems: "center",
  paddingVertical: spacing.lg, // 24px vertical padding
  gap: spacing.sm, // 12px gap
}

const $emptyText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 14,
  lineHeight: 20,
  color: colors.palette.neutral500,
  textAlign: "center",
}
