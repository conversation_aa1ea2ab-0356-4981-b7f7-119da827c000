import React from "react"
import { TouchableOpacity, View, ViewStyle } from "react-native"
import { Icon } from "app/components"
import { colors, spacing } from "app/theme"
import { AssignmentIcon } from "./AssignmentIcon"
import { AssignmentInfo } from "./AssignmentInfo"
import { AssignmentDueDate } from "./AssignmentDueDate"

interface Assignment {
  id: string
  title: string
  courseName: string
  dueDate: string
  priority: "high" | "medium" | "low"
  type: "quiz" | "assignment" | "project" | "exam"
  isCompleted?: boolean
}

interface AssignmentItemProps {
  /**
   * Assignment data
   */
  assignment: Assignment
  /**
   * Callback when assignment is pressed
   */
  onPress?: (assignment: Assignment) => void
}

export const AssignmentItem: React.FC<AssignmentItemProps> = ({
  assignment,
  onPress,
}) => {
  const formatDueDate = (dueDate: string): string => {
    const date = new Date(dueDate)
    const now = new Date()
    const diffTime = date.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return "Due today"
    if (diffDays === 1) return "Due tomorrow"
    if (diffDays > 1) return `Due in ${diffDays} days`
    return "Overdue"
  }

  return (
    <TouchableOpacity
      style={$container}
      onPress={() => onPress?.(assignment)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`${assignment.title} for ${assignment.courseName}, ${formatDueDate(assignment.dueDate)}`}
      accessibilityRole="button"
    >
      <View style={$assignmentHeader}>
        <AssignmentIcon
          type={assignment.type}
          priority={assignment.priority}
        />
        
        <AssignmentInfo
          title={assignment.title}
          courseName={assignment.courseName}
        />
        
        <AssignmentDueDate
          dueDate={assignment.dueDate}
          priority={assignment.priority}
        />
      </View>
      
      {assignment.isCompleted && (
        <View style={$completedBadge}>
          <Icon
            icon="check"
            size={12}
            color={colors.palette.neutral100}
          />
        </View>
      )}
    </TouchableOpacity>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  padding: spacing.sm, // 12px internal padding
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
  position: "relative",
}

const $assignmentHeader: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.sm, // 12px gap
}

const $completedBadge: ViewStyle = {
  position: "absolute",
  top: 8,
  right: 8,
  width: 20,
  height: 20,
  borderRadius: 10,
  backgroundColor: colors.palette.success500,
  alignItems: "center",
  justifyContent: "center",
}
