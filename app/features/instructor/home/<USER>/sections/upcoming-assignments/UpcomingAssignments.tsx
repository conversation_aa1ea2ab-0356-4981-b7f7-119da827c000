import React from "react"
import { View, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { UpcomingAssignmentsHeader } from "./UpcomingAssignmentsHeader"
import { AssignmentItem } from "./AssignmentItem"
import { EmptyAssignments } from "./EmptyAssignments"

interface Assignment {
  id: string
  title: string
  courseName: string
  dueDate: string
  priority: "high" | "medium" | "low"
  type: "quiz" | "assignment" | "project" | "exam"
  isCompleted?: boolean
}

interface UpcomingAssignmentsProps {
  /**
   * Array of upcoming assignments
   */
  assignments: Assignment[]
  /**
   * Section title
   */
  title?: string
  /**
   * Show view all button
   */
  showViewAll?: boolean
  /**
   * Callback when view all is pressed
   */
  onViewAllPress?: () => void
  /**
   * Callback when assignment is pressed
   */
  onAssignmentPress?: (assignment: Assignment) => void
  /**
   * Maximum number of assignments to show
   */
  maxItems?: number
}

export const UpcomingAssignments: React.FC<UpcomingAssignmentsProps> = ({
  assignments,
  title = "Upcoming Assignments",
  showViewAll = true,
  onViewAllPress,
  onAssignmentPress,
  maxItems = 3,
}) => {
  if (assignments.length === 0) {
    return (
      <View style={$container}>
        <UpcomingAssignmentsHeader
          title={title}
          showViewAll={false}
        />

        <EmptyAssignments />
      </View>
    )
  }

  return (
    <View style={$container}>
      <UpcomingAssignmentsHeader
        title={title}
        showViewAll={showViewAll}
        onViewAllPress={onViewAllPress}
      />

      <View style={$assignmentsList}>
        {assignments.slice(0, maxItems).map((assignment) => (
          <AssignmentItem
            key={assignment.id}
            assignment={assignment}
            onPress={onAssignmentPress}
          />
        ))}
      </View>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}

const $assignmentsList: ViewStyle = {
  gap: spacing.sm, // 12px gap between assignments
}
