import React from "react"
import { View, ViewStyle } from "react-native"
import { Icon } from "app/components"
import { spacing } from "app/theme"
import { ButtonConfig } from "./ButtonConfig"
import type { ResumeButtonVariant } from "./types"

interface ButtonIconProps {
  /**
   * Button variant
   */
  variant: ResumeButtonVariant
  /**
   * Disabled state
   */
  disabled?: boolean
  /**
   * Loading state
   */
  loading?: boolean
}

export const ButtonIcon: React.FC<ButtonIconProps> = ({
  variant,
  disabled = false,
  loading = false,
}) => {
  const config = ButtonConfig[variant]
  const iconName = loading ? "loading" : "caretRight"

  return (
    <View style={$iconContainer}>
      <Icon
        icon={iconName}
        size={16}
        color={config.iconColor}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $iconContainer: ViewStyle = {
  marginRight: spacing.xs, // 8px spacing from text
}
