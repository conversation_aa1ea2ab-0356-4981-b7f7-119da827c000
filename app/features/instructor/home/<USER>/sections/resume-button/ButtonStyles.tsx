import { ViewStyle, TextStyle } from "react-native"
import { colors, spacing } from "app/theme"

/**
 * Button Styles
 * 
 * Shared styles for resume button components
 */

export const ButtonStyles = {
  // Base button styles
  baseButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: spacing.md, // 16px horizontal padding
    paddingVertical: spacing.sm, // 12px vertical padding
    borderRadius: 12, // 12px border radius per guideline
    minHeight: 48,
  } as ViewStyle,

  // Text styles
  baseText: {
    fontWeight: "300", // Light weight - MANDATORY
  } as TextStyle,

  // Icon styles
  iconContainer: {
    marginRight: spacing.xs, // 8px spacing from text
  } as ViewStyle,

  // Shadow styles
  shadow: {
    shadowColor: colors.palette.neutral800,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  } as ViewStyle,

  // Disabled styles
  disabled: {
    opacity: 0.6,
  } as ViewStyle,
}
