import React from "react"
import { TextStyle } from "react-native"
import { Text } from "app/components"
import { ButtonConfig } from "./ButtonConfig"
import type { ResumeButtonVariant } from "./types"

interface ButtonTextProps {
  /**
   * Button text
   */
  text: string
  /**
   * Button variant
   */
  variant: ResumeButtonVariant
  /**
   * Disabled state
   */
  disabled?: boolean
  /**
   * Loading state
   */
  loading?: boolean
}

export const ButtonText: React.FC<ButtonTextProps> = ({
  text,
  variant,
  disabled = false,
  loading = false,
}) => {
  const config = ButtonConfig[variant]
  const displayText = loading ? "Loading..." : text

  return (
    <Text
      text={displayText}
      preset="description" // Light weight (300) - MANDATORY
      size="md"
      style={[config.textStyle]}
    />
  )
}
