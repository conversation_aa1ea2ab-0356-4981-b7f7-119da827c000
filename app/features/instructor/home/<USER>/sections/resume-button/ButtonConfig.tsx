import { ViewStyle, TextStyle } from "react-native"
import { colors } from "app/theme"
import type { ResumeButtonVariant } from "./types"

interface ButtonVariantConfig {
  containerStyle: ViewStyle
  textStyle: TextStyle
  iconColor: string
}

export const ButtonConfig: Record<ResumeButtonVariant, ButtonVariantConfig> = {
  primary: {
    containerStyle: {
      backgroundColor: colors.palette.primary500,
    },
    textStyle: {
      color: colors.palette.neutral100,
      fontWeight: "300", // Light weight - MANDATORY
    },
    iconColor: colors.palette.neutral100,
  },
  secondary: {
    containerStyle: {
      backgroundColor: colors.background,
      borderWidth: 1,
      borderColor: colors.palette.primary500,
    },
    textStyle: {
      color: colors.palette.primary500,
      fontWeight: "300", // Light weight - MANDATORY
    },
    iconColor: colors.palette.primary500,
  },
  outline: {
    containerStyle: {
      backgroundColor: "transparent",
      borderWidth: 1,
      borderColor: colors.palette.neutral400,
    },
    textStyle: {
      color: colors.text,
      fontWeight: "300", // Light weight - MANDATORY
    },
    iconColor: colors.text,
  },
  ghost: {
    containerStyle: {
      backgroundColor: "transparent",
    },
    textStyle: {
      color: colors.palette.primary500,
      fontWeight: "300", // Light weight - MANDATORY
    },
    iconColor: colors.palette.primary500,
  },
}
