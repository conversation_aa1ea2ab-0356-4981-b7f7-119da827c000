import React from "react"
import { Pressable, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { ButtonIcon } from "./ButtonIcon"
import { ButtonText } from "./ButtonText"
import { ButtonConfig } from "./ButtonConfig"
import type { ResumeButtonVariant } from "./types"

interface ResumeButtonProps {
  /**
   * Button text
   */
  text?: string
  /**
   * Button variant
   */
  variant?: ResumeButtonVariant
  /**
   * Show icon
   */
  showIcon?: boolean
  /**
   * Disabled state
   */
  disabled?: boolean
  /**
   * Loading state
   */
  loading?: boolean
  /**
   * Callback when button is pressed
   */
  onPress?: () => void
  /**
   * Custom style
   */
  style?: ViewStyle
}

export const ResumeButton: React.FC<ResumeButtonProps> = ({
  text = "Continue Learning",
  variant = "primary",
  showIcon = true,
  disabled = false,
  loading = false,
  onPress,
  style,
}) => {
  const config = ButtonConfig[variant]
  const isDisabled = disabled || loading

  return (
    <Pressable
      style={[
        $button,
        config.containerStyle,
        isDisabled && $disabledButton,
        style
      ]}
      onPress={isDisabled ? undefined : onPress}
      disabled={isDisabled}
      accessibilityRole="button"
      accessibilityLabel={text}
      accessibilityState={{ disabled: isDisabled }}
    >
      {showIcon && (
        <ButtonIcon 
          variant={variant}
          disabled={isDisabled}
          loading={loading}
        />
      )}
      
      <ButtonText 
        text={text}
        variant={variant}
        disabled={isDisabled}
        loading={loading}
      />
    </Pressable>
  )
}

// Themed styles following design guidelines
const $button: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  paddingHorizontal: spacing.md, // 16px horizontal padding
  paddingVertical: spacing.sm, // 12px vertical padding
  borderRadius: 12, // 12px border radius per guideline
  minHeight: 48,
  // Subtle shadow for elevation
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 2,
}

const $disabledButton: ViewStyle = {
  opacity: 0.6,
}
