/**
 * Resume Button Types
 * 
 * Type definitions for resume button components
 */

export type ResumeButtonVariant = "primary" | "secondary" | "outline" | "ghost"

export interface ResumeButtonProps {
  /**
   * Button text
   */
  text?: string
  /**
   * Button variant
   */
  variant?: ResumeButtonVariant
  /**
   * Show icon
   */
  showIcon?: boolean
  /**
   * Disabled state
   */
  disabled?: boolean
  /**
   * Loading state
   */
  loading?: boolean
  /**
   * Callback when button is pressed
   */
  onPress?: () => void
}
