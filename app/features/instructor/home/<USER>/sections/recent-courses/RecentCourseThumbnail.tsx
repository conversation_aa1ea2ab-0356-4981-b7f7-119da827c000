import React from "react"
import { View, ViewStyle } from "react-native"
import { Icon } from "app/components"
import { colors, spacing } from "app/theme"

interface RecentCourseThumbnailProps {
  /**
   * Show completed badge
   */
  isCompleted?: boolean
}

export const RecentCourseThumbnail: React.FC<RecentCourseThumbnailProps> = ({
  isCompleted = false,
}) => {
  return (
    <View style={$container}>
      <View style={$thumbnailPlaceholder}>
        <Icon
          icon="play-circle"
          size={32}
          color={colors.palette.primary500}
        />
      </View>
      
      {isCompleted && (
        <View style={$completedBadge}>
          <Icon
            icon="check"
            size={12}
            color={colors.palette.neutral100}
          />
        </View>
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  position: "relative",
  marginBottom: spacing.sm, // 12px below thumbnail
}

const $thumbnailPlaceholder: ViewStyle = {
  width: "100%",
  height: 100,
  backgroundColor: colors.palette.primary100, // Light blue background
  borderRadius: 8,
  alignItems: "center",
  justifyContent: "center",
}

const $completedBadge: ViewStyle = {
  position: "absolute",
  top: 8,
  right: 8,
  width: 20,
  height: 20,
  borderRadius: 10,
  backgroundColor: colors.palette.success500,
  alignItems: "center",
  justifyContent: "center",
}
