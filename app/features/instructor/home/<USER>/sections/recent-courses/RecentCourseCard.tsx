import React from "react"
import { TouchableOpacity, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { RecentCourseThumbnail } from "./RecentCourseThumbnail"
import { RecentCourseInfo } from "./RecentCourseInfo"
import { RecentCourseProgress } from "./RecentCourseProgress"

interface Course {
  id: string
  title: string
  instructor: string
  thumbnailUrl?: string
  progress: number
  duration?: string
  rating?: number
  isCompleted?: boolean
}

interface RecentCourseCardProps {
  /**
   * Course data
   */
  course: Course
  /**
   * Callback when course is pressed
   */
  onPress?: (course: Course) => void
}

export const RecentCourseCard: React.FC<RecentCourseCardProps> = ({
  course,
  onPress,
}) => {
  return (
    <TouchableOpacity
      style={$container}
      onPress={() => onPress?.(course)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`${course.title} by ${course.instructor}, ${course.progress}% complete`}
      accessibilityRole="button"
    >
      <RecentCourseThumbnail isCompleted={course.isCompleted} />
      
      <RecentCourseInfo
        title={course.title}
        instructor={course.instructor}
        duration={course.duration}
      />
      
      <RecentCourseProgress progress={course.progress} />
    </TouchableOpacity>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  width: 200,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  padding: spacing.sm, // 12px internal padding
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}
