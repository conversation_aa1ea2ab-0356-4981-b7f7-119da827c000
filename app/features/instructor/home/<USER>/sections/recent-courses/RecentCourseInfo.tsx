import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface RecentCourseInfoProps {
  /**
   * Course title
   */
  title: string
  /**
   * Instructor name
   */
  instructor: string
  /**
   * Course duration
   */
  duration?: string
}

export const RecentCourseInfo: React.FC<RecentCourseInfoProps> = ({
  title,
  instructor,
  duration,
}) => {
  return (
    <View style={$container}>
      <Text
        style={$courseTitle}
        text={title}
        numberOfLines={2}
      />
      
      <Text
        style={$instructorName}
        text={instructor}
        numberOfLines={1}
      />
      
      {duration && (
        <Text
          style={$courseDuration}
          text={duration}
        />
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  marginBottom: spacing.sm, // 12px below info
}

const $courseTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for card titles
  fontSize: 14,
  lineHeight: 18,
  color: colors.palette.primary700, // Deep navy
  marginBottom: spacing.xs / 2, // 4px below title
}

const $instructorName: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary600, // Dark blue
  marginBottom: spacing.xs / 2, // 4px below instructor
}

const $courseDuration: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  color: colors.palette.neutral500,
}
