import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface RecentCourseProgressProps {
  /**
   * Progress percentage (0-100)
   */
  progress: number
}

export const RecentCourseProgress: React.FC<RecentCourseProgressProps> = ({
  progress,
}) => {
  return (
    <View style={$container}>
      <View style={$progressBackground}>
        <View style={[$progressFill, { width: `${progress}%` }]} />
      </View>
      <Text
        style={$progressText}
        text={`${progress}%`}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  gap: spacing.xs / 2, // 4px gap
}

const $progressBackground: ViewStyle = {
  height: 4,
  backgroundColor: colors.palette.neutral200,
  borderRadius: 2,
  overflow: "hidden",
}

const $progressFill: ViewStyle = {
  height: "100%",
  backgroundColor: colors.palette.accent500, // Orange progress - MANDATORY
  borderRadius: 2,
}

const $progressText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 10,
  lineHeight: 12,
  color: colors.palette.primary600, // Dark blue
  textAlign: "right",
}
