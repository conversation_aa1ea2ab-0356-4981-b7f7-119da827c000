import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface ActionBadgeProps {
  /**
   * Badge content (number or text)
   */
  badge: string | number
}

export const ActionBadge: React.FC<ActionBadgeProps> = ({ badge }) => {
  return (
    <View style={$badge}>
      <Text
        style={$badgeText}
        text={badge.toString()}
      />
    </View>
  )
}

const $badge: ViewStyle = {
  position: "absolute",
  top: -4,
  right: -4,
  minWidth: 18,
  height: 18,
  borderRadius: 9,
  backgroundColor: colors.palette.angry500, // Red badge
  alignItems: "center",
  justifyContent: "center",
  paddingHorizontal: 4,
}

const $badgeText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 10,
  lineHeight: 12,
  color: colors.palette.neutral100, // White text
  textAlign: "center",
}
