import React from "react"
import { TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface QuickActionsHeaderProps {
  /**
   * Section title
   */
  title: string
}

export const QuickActionsHeader: React.FC<QuickActionsHeaderProps> = ({ title }) => {
  return (
    <Text
      style={$sectionTitle}
      text={title}
    />
  )
}

const $sectionTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for section headings
  fontSize: 18,
  lineHeight: 24,
  color: colors.palette.primary700, // Deep navy
  marginBottom: spacing.md, // 16px below title
}
