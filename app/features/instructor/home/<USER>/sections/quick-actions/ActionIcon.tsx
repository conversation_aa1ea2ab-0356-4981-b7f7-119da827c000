import React from "react"
import { View, ViewStyle } from "react-native"
import { Icon } from "app/components"
import { colors, spacing } from "app/theme"
import { ActionBadge } from "./ActionBadge"

interface ActionIconProps {
  /**
   * Icon name
   */
  icon: string
  /**
   * Icon color
   */
  color?: string
  /**
   * Background color for icon container
   */
  backgroundColor?: string
  /**
   * Badge content (optional)
   */
  badge?: string | number
}

export const ActionIcon: React.FC<ActionIconProps> = ({
  icon,
  color,
  backgroundColor,
  badge,
}) => {
  return (
    <View style={[$iconContainer, { backgroundColor: backgroundColor || colors.palette.primary100 }]}>
      <Icon
        icon={icon}
        size={24}
        color={color || colors.palette.primary500}
      />
      
      {badge && <ActionBadge badge={badge} />}
    </View>
  )
}

const $iconContainer: ViewStyle = {
  width: 48,
  height: 48,
  borderRadius: 24,
  alignItems: "center",
  justifyContent: "center",
  marginBottom: spacing.xs, // 8px below icon
  position: "relative",
}
