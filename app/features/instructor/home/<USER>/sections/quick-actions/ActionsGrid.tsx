import React from "react"
import { View, ViewStyle } from "react-native"
import { spacing } from "app/theme"
import { ActionItem } from "./ActionItem"
import type { ActionItem as ActionItemType } from "./ActionItem"

interface ActionsGridProps {
  /**
   * Array of action items to display
   */
  actions: ActionItemType[]
  /**
   * Number of columns (2, 3, or 4)
   */
  columns: 2 | 3 | 4
}

export const ActionsGrid: React.FC<ActionsGridProps> = ({ actions, columns }) => {
  const renderActionsGrid = () => {
    const rows = []
    for (let i = 0; i < actions.length; i += columns) {
      const rowActions = actions.slice(i, i + columns)
      rows.push(
        <View key={i} style={$actionsRow}>
          {rowActions.map((action) => (
            <ActionItem
              key={action.id}
              action={action}
              flex={1 / columns}
            />
          ))}
          {/* Fill empty spaces if needed */}
          {rowActions.length < columns && 
            Array.from({ length: columns - rowActions.length }).map((_, index) => (
              <View key={`empty-${index}`} style={{ flex: 1 / columns }} />
            ))
          }
        </View>
      )
    }
    return rows
  }

  return (
    <View style={$actionsContainer}>
      {renderActionsGrid()}
    </View>
  )
}

const $actionsContainer: ViewStyle = {
  gap: spacing.sm, // 12px gap between rows
}

const $actionsRow: ViewStyle = {
  flexDirection: "row",
  gap: spacing.sm, // 12px gap between items
}
