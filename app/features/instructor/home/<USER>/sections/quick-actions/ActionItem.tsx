import React from "react"
import { TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { ActionIcon } from "./ActionIcon"

export interface ActionItem {
  id: string
  title: string
  icon: string
  color?: string
  backgroundColor?: string
  onPress?: () => void
  badge?: string | number
}

interface ActionItemProps {
  /**
   * Action item data
   */
  action: ActionItem
  /**
   * Flex value for grid layout
   */
  flex: number
}

export const ActionItem: React.FC<ActionItemProps> = ({ action, flex }) => {
  return (
    <TouchableOpacity
      style={[$actionItem, { flex }]}
      onPress={action.onPress}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={action.title}
      accessibilityRole="button"
    >
      <ActionIcon
        icon={action.icon}
        color={action.color}
        backgroundColor={action.backgroundColor}
        badge={action.badge}
      />
      
      <Text
        style={$actionTitle}
        text={action.title}
        numberOfLines={2}
      />
    </TouchableOpacity>
  )
}

const $actionItem: ViewStyle = {
  alignItems: "center",
  padding: spacing.sm, // 12px internal padding
  backgroundColor: colors.palette.neutral100, // Light background
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
  minHeight: 80,
  justifyContent: "center",
}

const $actionTitle: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary600, // Dark blue
  textAlign: "center",
}
