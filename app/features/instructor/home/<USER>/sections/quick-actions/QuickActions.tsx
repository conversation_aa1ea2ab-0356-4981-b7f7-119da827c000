import React from "react"
import { View, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { QuickActionsHeader } from "./QuickActionsHeader"
import { ActionsGrid } from "./ActionsGrid"
import type { ActionItem } from "./ActionItem"

interface QuickActionsProps {
  /**
   * Array of action items to display
   */
  actions: ActionItem[]
  /**
   * Section title
   */
  title?: string
  /**
   * Number of columns (2, 3, or 4)
   */
  columns?: 2 | 3 | 4
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  actions,
  title = "Quick Actions",
  columns = 4,
}) => {
  if (actions.length === 0) {
    return null
  }

  return (
    <View style={$container}>
      <QuickActionsHeader title={title} />
      <ActionsGrid actions={actions} columns={columns} />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}
