import React from "react"
import { View } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { ThemedStyle } from "app/utils/themedStyle"

interface TargetContentProps {
  /**
   * Target information
   */
  target: {
    type: "minutes" | "lessons" | "courses" | "points"
    current: number
    goal: number
    unit: string
  }
  /**
   * Status color
   */
  statusColor: string
  /**
   * Is completed
   */
  isCompleted: boolean
  /**
   * Progress percentage
   */
  progressPercentage: number
}

export const TargetContent: React.FC<TargetContentProps> = ({
  target,
  statusColor,
  isCompleted,
  progressPercentage,
}) => {
  const formatTargetText = (): string => {
    return `${target.current} / ${target.goal} ${target.unit}`
  }

  return (
    <View style={$container}>
      <Text
        style={$targetText}
        text={formatTargetText()}
      />

      <Text
        style={[$statusText, { color: statusColor }]}
        text={isCompleted ? "Goal achieved!" : `${Math.round(progressPercentage)}% complete`}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ThemedStyle = ({ spacing }) => ({
  alignItems: "center",
  marginBottom: spacing.sm, // 12px below content
})

const $targetText: ThemedStyle = ({ colors, typography, spacing }) => ({
  fontFamily: typography.primary.semiBold, // 600 weight for card titles
  fontSize: 20,
  lineHeight: 26,
  color: colors.palette.primary700, // Deep navy
  marginBottom: spacing.xs, // 8px below
  textAlign: "center",
})

const $statusText: ThemedStyle = ({ typography }) => ({
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 14,
  lineHeight: 20,
  textAlign: "center",
})
