import React from "react"
import { View } from "react-native"
import { Icon } from "app/components"
import { colors } from "app/theme"
import { ThemedStyle } from "app/utils/themedStyle"

interface TargetCompletedBadgeProps {
  /**
   * Show completed badge
   */
  isCompleted: boolean
}

export const TargetCompletedBadge: React.FC<TargetCompletedBadgeProps> = ({
  isCompleted,
}) => {
  if (!isCompleted) {
    return null
  }

  return (
    <View style={$container}>
      <Icon
        icon="check"
        size={16}
        color={colors.palette.neutral100}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ThemedStyle = ({ colors }) => ({
  position: "absolute",
  top: -8,
  right: -8,
  width: 28,
  height: 28,
  borderRadius: 14,
  backgroundColor: colors.palette.success500,
  alignItems: "center",
  justifyContent: "center",
  borderWidth: 2,
  borderColor: "#FFFFFF",
})
