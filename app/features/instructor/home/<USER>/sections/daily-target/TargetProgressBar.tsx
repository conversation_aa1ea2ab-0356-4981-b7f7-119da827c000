import React from "react"
import { View } from "react-native"
import { colors, spacing } from "app/theme"
import { ThemedStyle } from "app/utils/themedStyle"

interface TargetProgressBarProps {
  /**
   * Progress percentage (0-100)
   */
  progressPercentage: number
  /**
   * Status color for progress fill
   */
  statusColor: string
}

export const TargetProgressBar: React.FC<TargetProgressBarProps> = ({
  progressPercentage,
  statusColor,
}) => {
  return (
    <View style={$container}>
      <View style={$progressBackground}>
        <View style={[$progressFill, { width: `${progressPercentage}%`, backgroundColor: statusColor }]} />
      </View>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ThemedStyle = ({ spacing }) => ({
  marginTop: spacing.sm, // 12px above progress
})

const $progressBackground: ThemedStyle = ({ colors }) => ({
  height: 6,
  backgroundColor: colors.palette.neutral200,
  borderRadius: 3,
  overflow: "hidden",
})

const $progressFill: ThemedStyle = () => ({
  height: "100%",
  borderRadius: 3,
})
