import React from "react"
import { TouchableOpacity } from "react-native"
import { colors, spacing } from "app/theme"
import { ThemedStyle } from "app/utils/themedStyle"
import { TargetHeader } from "./TargetHeader"
import { TargetContent } from "./TargetContent"
import { TargetProgressBar } from "./TargetProgressBar"
import { TargetCompletedBadge } from "./TargetCompletedBadge"

interface DailyTargetProps {
  /**
   * Target information
   */
  target: {
    type: "minutes" | "lessons" | "courses" | "points"
    current: number
    goal: number
    unit: string
  }
  /**
   * Callback when target is pressed
   */
  onPress?: () => void
  /**
   * Show edit button
   */
  showEditButton?: boolean
  /**
   * Callback when edit is pressed
   */
  onEdit?: () => void
}

export const DailyTarget: React.FC<DailyTargetProps> = ({
  target,
  onPress,
  showEditButton = true,
  onEdit,
}) => {
  const progressPercentage = Math.min((target.current / target.goal) * 100, 100)
  const isCompleted = target.current >= target.goal

  const getStatusColor = (): string => {
    if (isCompleted) return colors.palette.success500
    if (progressPercentage >= 50) return colors.palette.primary500
    return colors.palette.warning500
  }

  const formatTargetText = (): string => {
    return `${target.current} / ${target.goal} ${target.unit}`
  }

  const statusColor = getStatusColor()

  return (
    <TouchableOpacity
      style={[$container, isCompleted && $completedContainer]}
      onPress={onPress}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`Daily target: ${formatTargetText()}, ${Math.round(progressPercentage)}% complete`}
      accessibilityRole="button"
    >
      <TargetHeader
        targetType={target.type}
        statusColor={statusColor}
        showEditButton={showEditButton}
        onEdit={onEdit}
      />

      <TargetContent
        target={target}
        statusColor={statusColor}
        isCompleted={isCompleted}
        progressPercentage={progressPercentage}
      />

      <TargetProgressBar
        progressPercentage={progressPercentage}
        statusColor={statusColor}
      />

      <TargetCompletedBadge isCompleted={isCompleted} />
    </TouchableOpacity>
  )
}

// Themed styles following design guidelines
const $container: ThemedStyle = ({ colors, spacing }) => ({
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
  position: "relative",
})

const $completedContainer: ThemedStyle = ({ colors }) => ({
  borderWidth: 2,
  borderColor: colors.palette.success500,
  backgroundColor: colors.palette.success100,
})
