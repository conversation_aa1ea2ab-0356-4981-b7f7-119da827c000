import React from "react"
import { View, TouchableOpacity } from "react-native"
import { Icon } from "app/components"
import { colors, spacing } from "app/theme"
import { ThemedStyle } from "app/utils/themedStyle"

interface TargetHeaderProps {
  /**
   * Target type for icon selection
   */
  targetType: "minutes" | "lessons" | "courses" | "points"
  /**
   * Status color for icon
   */
  statusColor: string
  /**
   * Show edit button
   */
  showEditButton?: boolean
  /**
   * Callback when edit is pressed
   */
  onEdit?: () => void
}

export const TargetHeader: React.FC<TargetHeaderProps> = ({
  targetType,
  statusColor,
  showEditButton = true,
  onEdit,
}) => {
  const getTargetIcon = (type: string): string => {
    switch (type) {
      case "minutes":
        return "clock"
      case "lessons":
        return "book"
      case "courses":
        return "graduation-cap"
      case "points":
        return "star"
      default:
        return "target"
    }
  }

  return (
    <View style={$container}>
      <View style={$iconContainer}>
        <Icon
          icon={getTargetIcon(targetType)}
          size={20}
          color={statusColor}
        />
      </View>

      {showEditButton && (
        <TouchableOpacity
          style={$editButton}
          onPress={onEdit}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Icon
            icon="edit"
            size={16}
            color={colors.palette.neutral400}
          />
        </TouchableOpacity>
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ThemedStyle = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.sm, // 12px below header
})

const $iconContainer: ThemedStyle = ({ colors }) => ({
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: colors.palette.primary100, // Light blue background
  alignItems: "center",
  justifyContent: "center",
})

const $editButton: ThemedStyle = ({ spacing }) => ({
  padding: spacing.xs, // 8px padding
})
