import React from "react"
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native"
import { Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface Mentor {
  id: string
  name: string
  image?: string
}

interface FigmaInstructorTopMentorsProps {
  /**
   * Mentors data
   */
  mentors?: <PERSON><PERSON>[]
  /**
   * Callbacks
   */
  onSeeAllPress?: () => void
  onMentorPress?: (mentor: <PERSON><PERSON>) => void
}

/**
 * FigmaInstructorTopMentors - Top mentors component based on exact Figma design
 * 
 * Replicates the TOP MENTOR section from Figma:
 * - "Top Mentor" heading with "See All" link (adapted to "Top Instructors")
 * - Mentor cards with image and name (adapted for fellow instructors)
 */
export const FigmaInstructorTopMentors: React.FC<FigmaInstructorTopMentorsProps> = ({
  mentors = [
    { id: "1", name: "<PERSON>" },
    { id: "2", name: "<PERSON>" },
    { id: "3", name: "<PERSON><PERSON><PERSON>" },
    { id: "4", name: "<PERSON>" }
  ],
  onSeeAllPress,
  onMentorPress,
}) => {
  return (
    <View style={styles.container}>
      {/* Heading section */}
      <View style={styles.headingContainer}>
        {/* Top Instructors title */}
        <Text style={styles.topMentorsTitle}>
          Top Instructors
        </Text>
        
        {/* See All section */}
        <TouchableOpacity 
          style={styles.seeAllContainer}
          onPress={onSeeAllPress}
          activeOpacity={0.7}
        >
          <Text style={styles.seeAllText}>
            SEE ALL
          </Text>
          <Icon
            icon="caretRight"
            size={10}
            color="#23408B" // MAIN BRAND COLOR from design guidelines
            style={styles.seeAllIcon}
          />
        </TouchableOpacity>
      </View>
      
      {/* Mentors list */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.mentorsContent}
        style={styles.mentorsContainer}
      >
        {mentors.map((mentor, index) => (
          <TouchableOpacity
            key={mentor.id}
            style={[styles.mentorCard, index > 0 && styles.mentorCardMargin]}
            onPress={() => onMentorPress?.(mentor)}
            activeOpacity={0.8}
          >
            {/* Mentor image */}
            <View style={styles.mentorImage} />
            
            {/* Mentor name */}
            <Text style={styles.mentorNameText}>
              {mentor.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: 374, // From Figma: width: 374
    height: 137, // From Figma: height: 137
  },
  headingContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: 358, // From Figma: width: 358
    height: 26, // From Figma: height: 26
    marginBottom: 15, // Space before mentors
  },
  topMentorsTitle: {
    fontFamily: typography.primary.semiBold, // 600 weight - for headings per design guidelines
    fontSize: 18,
    lineHeight: 26,
    color: "#132339", // Deep navy from design guidelines
  },
  seeAllContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  seeAllText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 12,
    lineHeight: 15,
    textTransform: "uppercase",
    textAlign: "right",
    color: "#23408B", // MAIN BRAND COLOR from design guidelines
    marginRight: 10,
  },
  seeAllIcon: {
    // Icon styling handled by Icon component
  },
  mentorsContainer: {
    flex: 1,
  },
  mentorsContent: {
    paddingRight: spacing.md,
  },
  mentorCard: {
    width: 80, // From Figma: width: 80
    height: 96, // From Figma: height: 96
    alignItems: "center",
  },
  mentorCardMargin: {
    marginLeft: 18, // From Figma: spacing between mentor cards (98-80=18)
  },
  mentorImage: {
    width: 80, // From Figma: width: 80
    height: 70, // From Figma: height: 70
    backgroundColor: "#000000", // From Figma: fill_BN34AD (placeholder)
    borderRadius: 20, // From Figma: borderRadius: 20px
    marginBottom: 7, // Space between image and name
  },
  mentorNameText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 13,
    lineHeight: 19,
    textAlign: "center",
    color: "#132339", // Deep navy from design guidelines
  },
})
