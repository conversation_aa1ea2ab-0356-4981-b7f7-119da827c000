import React from "react"
import { TextStyle } from "react-native"
import { Text } from "app/components"
import { colors } from "app/theme"

interface ProgressTextProps {
  /**
   * Progress value (0-100)
   */
  progress: number
  /**
   * Text color
   */
  color?: string
  /**
   * Custom style
   */
  style?: TextStyle
}

export const ProgressText: React.FC<ProgressTextProps> = ({
  progress,
  color = colors.textDim,
  style,
}) => {
  return (
    <Text
      text={`${Math.round(progress)}%`}
      preset="description" // Light weight (300) - MANDATORY
      size="sm"
      style={[$progressText, { color }, style]}
    />
  )
}

// Themed styles following design guidelines
const $progressText: TextStyle = {
  fontWeight: "600", // Semi-bold for numbers
}
