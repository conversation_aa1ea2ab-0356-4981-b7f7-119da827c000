import React from "react"
import { View, ViewStyle } from "react-native"
import { colors } from "app/theme"

interface ProgressBarProps {
  /**
   * Progress value (0-100)
   */
  progress: number
  /**
   * Progress bar color
   */
  color?: string
  /**
   * Progress bar height
   */
  height?: number
  /**
   * Track color
   */
  trackColor?: string
  /**
   * Custom style
   */
  style?: ViewStyle
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  color = colors.palette.primary500,
  height = 8,
  trackColor = colors.palette.neutral300,
  style,
}) => {
  const normalizedProgress = Math.max(0, Math.min(100, progress))

  return (
    <View style={[$track, { height, backgroundColor: trackColor }, style]}>
      <View 
        style={[
          $fill,
          { 
            width: `${normalizedProgress}%`,
            backgroundColor: color,
            height
          }
        ]}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $track: ViewStyle = {
  borderRadius: 4,
  overflow: "hidden",
}

const $fill: ViewStyle = {
  borderRadius: 4,
}
