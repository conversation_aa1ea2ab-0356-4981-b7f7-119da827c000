/**
 * Progress Indicator Utilities
 * 
 * Utility functions for progress calculations and formatting
 */

/**
 * Calculate progress percentage
 */
export const calculateProgress = (current: number, total: number): number => {
  if (total === 0) return 0
  return Math.max(0, Math.min(100, (current / total) * 100))
}

/**
 * Format progress as percentage string
 */
export const formatProgress = (progress: number): string => {
  return `${Math.round(progress)}%`
}

/**
 * Get progress color based on value
 */
export const getProgressColor = (progress: number): string => {
  if (progress >= 80) return "#10B981" // Green for high progress
  if (progress >= 50) return "#F59E0B" // Orange for medium progress
  return "#EF4444" // Red for low progress
}

/**
 * Get progress status text
 */
export const getProgressStatus = (progress: number): string => {
  if (progress >= 100) return "Completed"
  if (progress >= 80) return "Almost done"
  if (progress >= 50) return "In progress"
  if (progress > 0) return "Just started"
  return "Not started"
}
