import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing } from "app/theme"
import { ProgressBar } from "./ProgressBar"
import { ProgressText } from "./ProgressText"

interface ProgressIndicatorProps {
  /**
   * Progress value (0-100)
   */
  progress: number
  /**
   * Progress label
   */
  label?: string
  /**
   * Show percentage text
   */
  showPercentage?: boolean
  /**
   * Progress bar color
   */
  color?: string
  /**
   * Progress bar height
   */
  height?: number
  /**
   * Custom style
   */
  style?: ViewStyle
}

export const ProgressIndicator: React.FC<ProgressIndicatorProps> = ({
  progress,
  label,
  showPercentage = true,
  color = colors.palette.primary500,
  height = 8,
  style,
}) => {
  const normalizedProgress = Math.max(0, Math.min(100, progress))

  return (
    <View style={[$container, style]}>
      {(label || showPercentage) && (
        <View style={$header}>
          {label && (
            <Text
              text={label}
              preset="description" // Light weight (300) - MANDATORY
              size="sm"
              style={$labelText}
            />
          )}
          
          {showPercentage && (
            <ProgressText progress={normalizedProgress} />
          )}
        </View>
      )}
      
      <ProgressBar 
        progress={normalizedProgress}
        color={color}
        height={height}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  marginVertical: spacing.xs, // 8px vertical spacing
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.xs, // 8px spacing from progress bar
}

const $labelText: TextStyle = {
  color: colors.text,
}
