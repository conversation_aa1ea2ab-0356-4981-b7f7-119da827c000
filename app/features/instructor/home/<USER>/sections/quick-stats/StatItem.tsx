import React from "react"
import { TouchableOpacity, View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { StatItem as StatItemType } from "./types"

interface StatItemProps {
  /**
   * Stat item data
   */
  stat: StatItemType
  /**
   * Flex value for grid layout
   */
  flex: number
}

export const StatItem: React.FC<StatItemProps> = ({
  stat,
  flex,
}) => {
  return (
    <TouchableOpacity
      style={[$statItem, { flex }]}
      onPress={stat.onPress}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`${stat.title}: ${stat.value}`}
      accessibilityRole="button"
    >
      <View style={[$iconContainer, { backgroundColor: stat.color || colors.palette.primary100 }]}>
        <Icon
          icon={stat.icon}
          size={24}
          color={stat.color || colors.palette.primary500}
        />
      </View>
      
      <Text
        style={$statValue}
        text={stat.value.toString()}
      />
      
      <Text
        style={$statTitle}
        text={stat.title}
      />
    </TouchableOpacity>
  )
}

// Themed styles following design guidelines
const $statItem: ViewStyle = {
  alignItems: "center",
  padding: spacing.sm, // 12px internal padding
  backgroundColor: colors.palette.neutral100, // Light background
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}

const $iconContainer: ViewStyle = {
  width: 48,
  height: 48,
  borderRadius: 24,
  alignItems: "center",
  justifyContent: "center",
  marginBottom: spacing.xs, // 8px below icon
}

const $statValue: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for emphasis
  fontSize: 20,
  lineHeight: 26,
  color: colors.palette.primary700, // Deep navy
  marginBottom: spacing.xs / 2, // 4px below value
}

const $statTitle: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary600, // Dark blue
  textAlign: "center",
}
