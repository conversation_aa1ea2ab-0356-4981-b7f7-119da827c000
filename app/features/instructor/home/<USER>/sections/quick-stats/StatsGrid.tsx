import React from "react"
import { View, ViewStyle } from "react-native"
import { spacing } from "app/theme"
import { StatItem } from "./StatItem"
import { StatItem as StatItemType } from "./types"

interface StatsGridProps {
  /**
   * Array of stat items to display
   */
  stats: StatItemType[]
  /**
   * Number of columns (2 or 3)
   */
  columns?: 2 | 3
}

export const StatsGrid: React.FC<StatsGridProps> = ({
  stats,
  columns = 2,
}) => {
  const renderStatItem = (stat: StatItemType) => (
    <StatItem
      key={stat.id}
      stat={stat}
      flex={1 / columns}
    />
  )

  const renderStatsGrid = () => {
    const rows = []
    for (let i = 0; i < stats.length; i += columns) {
      const rowStats = stats.slice(i, i + columns)
      rows.push(
        <View key={i} style={$statsRow}>
          {rowStats.map(renderStatItem)}
          {/* Fill empty spaces if needed */}
          {rowStats.length < columns && 
            Array.from({ length: columns - rowStats.length }).map((_, index) => (
              <View key={`empty-${index}`} style={{ flex: 1 / columns }} />
            ))
          }
        </View>
      )
    }
    return rows
  }

  return (
    <View style={$statsContainer}>
      {renderStatsGrid()}
    </View>
  )
}

// Themed styles following design guidelines
const $statsContainer: ViewStyle = {
  gap: spacing.sm, // 12px gap between rows
}

const $statsRow: ViewStyle = {
  flexDirection: "row",
  gap: spacing.sm, // 12px gap between items
}
