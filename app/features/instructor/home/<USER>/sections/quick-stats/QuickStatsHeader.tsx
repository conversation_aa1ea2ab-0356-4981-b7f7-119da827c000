import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface QuickStatsHeaderProps {
  /**
   * Section title
   */
  title?: string
  /**
   * Show header
   */
  show?: boolean
}

export const QuickStatsHeader: React.FC<QuickStatsHeaderProps> = ({
  title = "Quick Stats",
  show = true,
}) => {
  if (!show) {
    return null
  }

  return (
    <View style={$container}>
      <Text
        style={$sectionTitle}
        text={title}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  marginBottom: spacing.md, // 16px below header
}

const $sectionTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for section headings
  fontSize: 18,
  lineHeight: 24,
  color: colors.palette.primary700, // Deep navy
}
