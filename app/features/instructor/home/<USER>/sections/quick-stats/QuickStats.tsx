import React from "react"
import { View, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { QuickStatsHeader } from "./QuickStatsHeader"
import { StatsGrid } from "./StatsGrid"
import { StatItem } from "./types"

interface QuickStatsProps {
  /**
   * Array of stat items to display
   */
  stats: StatItem[]
  /**
   * Number of columns (2 or 3)
   */
  columns?: 2 | 3
  /**
   * Section title
   */
  title?: string
  /**
   * Show quick stats section
   */
  show?: boolean
}

export const QuickStats: React.FC<QuickStatsProps> = ({
  stats,
  columns = 2,
  title = "Quick Stats",
  show = true,
}) => {
  if (!show || !stats || stats.length === 0) {
    return null
  }

  return (
    <View style={$container}>
      <QuickStatsHeader
        title={title}
        show={true}
      />

      <StatsGrid
        stats={stats}
        columns={columns}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}
