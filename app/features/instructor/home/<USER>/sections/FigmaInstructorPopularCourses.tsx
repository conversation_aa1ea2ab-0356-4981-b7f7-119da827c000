import React, { useState } from "react"
import { View, Text, TouchableOpacity, ScrollView, StyleSheet } from "react-native"
import { Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface Course {
  id: string
  title: string
  category: string
  price: string
  rating: string
  students: string
  image?: string
}

interface FigmaInstructorPopularCoursesProps {
  /**
   * Courses data
   */
  courses?: Course[]
  /**
   * Callbacks
   */
  onSeeAllPress?: () => void
  onCoursePress?: (course: Course) => void
  onCategoryFilterPress?: (category: string) => void
}

/**
 * FigmaInstructorPopularCourses - Popular courses component based on exact Figma design
 * 
 * Replicates the POPULAR COURSE section from Figma:
 * - "Popular Courses" heading with "See All" link
 * - Filter tabs (All, Graphic Design, 3D Design, Arts & Humanities) - adapted for instructor
 * - Course cards with image, category, title, price, rating, students
 */
export const FigmaInstructorPopularCourses: React.FC<FigmaInstructorPopularCoursesProps> = ({
  courses = [
    {
      id: "1",
      title: "Teaching Methods Advanced",
      category: "Teaching Methods",
      price: "Free",
      rating: "4.8",
      students: "156 Instructors",
      image: "placeholder"
    },
    {
      id: "2", 
      title: "Course Development",
      category: "Course Development",
      price: "Free",
      rating: "4.9",
      students: "89 Instructors",
      image: "placeholder"
    }
  ],
  onSeeAllPress,
  onCoursePress,
  onCategoryFilterPress,
}) => {
  const [selectedCategory, setSelectedCategory] = useState("All")
  
  const categories = ["All", "Teaching Methods", "Course Development", "Student Engagement"]
  
  const handleCategoryPress = (category: string) => {
    setSelectedCategory(category)
    onCategoryFilterPress?.(category)
  }

  return (
    <View style={styles.container}>
      {/* Heading section */}
      <View style={styles.headingContainer}>
        {/* Popular Courses title */}
        <Text style={styles.popularCoursesTitle}>
          Popular Courses
        </Text>
        
        {/* See All section */}
        <TouchableOpacity 
          style={styles.seeAllContainer}
          onPress={onSeeAllPress}
          activeOpacity={0.7}
        >
          <Text style={styles.seeAllText}>
            SEE ALL
          </Text>
          <Icon
            icon="caretRight"
            size={10}
            color="#23408B" // MAIN BRAND COLOR from design guidelines
            style={styles.seeAllIcon}
          />
        </TouchableOpacity>
      </View>
      
      {/* Category filter tabs */}
      <View style={styles.categoryTabsContainer}>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoryTabsContent}
        >
          {categories.map((category, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.categoryTab,
                selectedCategory === category ? styles.categoryTabActive : styles.categoryTabInactive
              ]}
              onPress={() => handleCategoryPress(category)}
              activeOpacity={0.7}
            >
              <Text style={[
                styles.categoryTabText,
                selectedCategory === category ? styles.categoryTabTextActive : styles.categoryTabTextInactive
              ]}>
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      
      {/* Course cards */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.courseCardsContent}
        style={styles.courseCardsContainer}
      >
        {courses.map((course, index) => (
          <TouchableOpacity
            key={course.id}
            style={[styles.courseCard, index > 0 && styles.courseCardMargin]}
            onPress={() => onCoursePress?.(course)}
            activeOpacity={0.8}
          >
            {/* Course image */}
            <View style={styles.courseImage} />
            
            {/* Course content */}
            <View style={styles.courseContent}>
              {/* Category badge */}
              <Text style={styles.courseCategoryText}>
                {course.category}
              </Text>
              
              {/* Course title */}
              <Text style={styles.courseTitleText}>
                {course.title}
              </Text>
              
              {/* Bookmark icon */}
              <Icon 
                icon="heart" 
                size={14} 
                color="#167F71"
                style={styles.bookmarkIcon}
              />
              
              {/* Course details */}
              <View style={styles.courseDetails}>
                <Text style={styles.coursePriceText}>
                  {course.price}
                </Text>
                <Text style={styles.courseSeparator}>|</Text>
                <Icon icon="star" size={12} color="#FCCB40" />
                <Text style={styles.courseRatingText}>
                  {course.rating}
                </Text>
                <Text style={styles.courseSeparator}>|</Text>
                <Text style={styles.courseStudentsText}>
                  {course.students}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: 580, // From Figma: width: 580 (extends beyond screen for horizontal scroll)
    height: 326, // From Figma: height: 326
  },
  headingContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: 358, // From Figma: width: 358
    height: 26, // From Figma: height: 26
    marginBottom: 10,
  },
  popularCoursesTitle: {
    fontFamily: typography.primary.semiBold, // 600 weight - for headings per design guidelines
    fontSize: 18,
    lineHeight: 26,
    color: "#132339", // Deep navy from design guidelines
  },
  seeAllContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  seeAllText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 12,
    lineHeight: 15,
    textTransform: "uppercase",
    textAlign: "right",
    color: "#23408B", // MAIN BRAND COLOR from design guidelines
    marginRight: 10,
  },
  seeAllIcon: {
    // Icon styling handled by Icon component
  },
  categoryTabsContainer: {
    marginBottom: 20,
  },
  categoryTabsContent: {
    paddingRight: spacing.md,
  },
  categoryTab: {
    paddingHorizontal: 18,
    paddingVertical: 7,
    borderRadius: 15, // From Figma: borderRadius: 15px
    marginRight: 12,
    height: 30, // From Figma: height: 30
    justifyContent: "center",
    alignItems: "center",
  },
  categoryTabActive: {
    backgroundColor: "#167F71", // From Figma: fill_69GPK9 (green for active)
  },
  categoryTabInactive: {
    backgroundColor: "#E8F1FF", // From Figma: fill_IV1JZW (light blue for inactive)
  },
  categoryTabText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 13,
    lineHeight: 16,
    textAlign: "center",
  },
  categoryTabTextActive: {
    color: "#FFFFFF", // From Figma: fill_ORCEJY (white for active)
  },
  categoryTabTextInactive: {
    color: "#202244", // From Figma: fill_2IU6L4 (dark for inactive)
  },
  courseCardsContainer: {
    flex: 1,
  },
  courseCardsContent: {
    paddingRight: spacing.md,
  },
  courseCard: {
    width: 280, // From Figma: width: 280
    height: 240, // From Figma: height: 240
    backgroundColor: "#FFFFFF", // From Figma: fill_ORCEJY
    borderRadius: 20, // From Figma: borderRadius: 20px
    // Shadow from Figma: effect_QQ6KKD
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.08,
    shadowRadius: 10,
    elevation: 5, // For Android
  },
  courseCardMargin: {
    marginLeft: 20, // Space between cards
  },
  courseImage: {
    width: 280, // From Figma: width: 280
    height: 134, // From Figma: height: 134
    backgroundColor: "#000000", // From Figma: fill_BN34AD (placeholder)
    borderRadius: 20, // From Figma: borderRadius: 20px 20px 0px 0px
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  courseContent: {
    padding: 14, // From Figma: content starts at x: 14, y: 144
    flex: 1,
    position: "relative",
  },
  courseCategoryText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 12,
    lineHeight: 15,
    color: "#FF6B00",
    marginBottom: 7,
  },
  courseTitleText: {
    fontFamily: typography.primary.semiBold, // 600 weight - for headings per design guidelines
    fontSize: 16,
    lineHeight: 23,
    color: "#132339", // Deep navy from design guidelines
    marginBottom: 10,
  },
  bookmarkIcon: {
    position: "absolute",
    right: 14,
    top: 14,
  },
  courseDetails: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: "auto", // Push to bottom
  },
  coursePriceText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 15,
    lineHeight: 19,
    color: "#23408B", // MAIN BRAND COLOR from design guidelines
    marginRight: 6,
  },
  courseSeparator: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 14,
    lineHeight: 18,
    color: "#000000",
    marginHorizontal: 6,
  },
  courseRatingText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 11,
    lineHeight: 14,
    color: "#132339", // Deep navy from design guidelines
    marginLeft: 3,
    marginRight: 6,
  },
  courseStudentsText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 11,
    lineHeight: 14,
    color: "#132339", // Deep navy from design guidelines
  },
})
