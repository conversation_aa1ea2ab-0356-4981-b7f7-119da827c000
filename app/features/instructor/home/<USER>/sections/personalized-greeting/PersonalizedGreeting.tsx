import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { PersonalizedGreetingHeader } from "./PersonalizedGreetingHeader"
import { TimeBasedGreeting } from "./TimeBasedGreeting"
import { GreetingText } from "./GreetingText"

interface PersonalizedGreetingProps {
  /**
   * User's display name
   */
  userName?: string
  /**
   * Custom greeting message
   */
  customMessage?: string
  /**
   * Show time-based greeting (Good morning, afternoon, etc.)
   */
  showTimeBasedGreeting?: boolean
  /**
   * Callback when greeting is pressed
   */
  onPress?: () => void
}

export const PersonalizedGreeting: React.FC<PersonalizedGreetingProps> = ({
  userName = "Student",
  customMessage,
  showTimeBasedGreeting = true,
  onPress,
}) => {
  console.log("👋 PersonalizedGreeting rendering for:", userName)

  return (
    <View style={$container}>
      <PersonalizedGreetingHeader />

      {showTimeBasedGreeting && (
        <TimeBasedGreeting userName={userName} />
      )}

      <GreetingText
        userName={userName}
        customMessage={customMessage}
        onPress={onPress}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: colors.background, // White background - MANDATORY
  borderRadius: 12, // 12px border radius per guideline
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 16px padding
  marginVertical: spacing.sm, // 12px vertical spacing
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 2,
}
