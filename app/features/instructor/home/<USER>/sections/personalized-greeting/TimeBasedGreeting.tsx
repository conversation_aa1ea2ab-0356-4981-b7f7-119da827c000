import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing } from "app/theme"

interface TimeBasedGreetingProps {
  /**
   * User's display name
   */
  userName: string
}

export const TimeBasedGreeting: React.FC<TimeBasedGreetingProps> = ({
  userName,
}) => {
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours()
    
    if (hour < 12) {
      return "Good morning"
    } else if (hour < 17) {
      return "Good afternoon"
    } else {
      return "Good evening"
    }
  }

  return (
    <View style={$container}>
      <Text
        text={`${getTimeBasedGreeting()}, ${userName}!`}
        preset="description" // Light weight (300) - MANDATORY
        size="md"
        style={$greetingText}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  marginBottom: spacing.xs, // 8px spacing
}

const $greetingText: TextStyle = {
  color: colors.textDim,
}
