import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"

export const PersonalizedGreetingHeader: React.FC = () => {
  return (
    <View style={$header}>
      <Text
        text="Welcome Back"
        weight="bold" // Bold weight for header
        size="lg"
        style={$headerTitle}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $header: ViewStyle = {
  marginBottom: spacing.sm, // 12px spacing
}

const $headerTitle: TextStyle = {
  color: colors.text,
}
