import React from "react"
import { Pressable, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing } from "app/theme"

interface GreetingTextProps {
  /**
   * User's display name
   */
  userName: string
  /**
   * Custom greeting message
   */
  customMessage?: string
  /**
   * Callback when greeting is pressed
   */
  onPress?: () => void
}

export const GreetingText: React.FC<GreetingTextProps> = ({
  userName,
  customMessage,
  onPress,
}) => {
  const defaultMessage = `Ready to continue your learning journey?`
  const message = customMessage || defaultMessage

  const Component = onPress ? Pressable : React.Fragment

  const content = (
    <Text
      text={message}
      preset="description" // Light weight (300) - MANDATORY
      size="sm"
      style={$messageText}
    />
  )

  if (onPress) {
    return (
      <Pressable
        onPress={onPress}
        style={$pressable}
        accessibilityRole="button"
        accessibilityLabel={`Greeting message: ${message}`}
      >
        {content}
      </Pressable>
    )
  }

  return content
}

// Themed styles following design guidelines
const $pressable: ViewStyle = {
  padding: spacing.xs, // 8px padding for touch area
}

const $messageText: TextStyle = {
  color: colors.textDim,
  lineHeight: 20,
}
