import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface TrendingCourseThumbnailProps {
  /**
   * Trending rank (1, 2, 3, etc.)
   */
  trendingRank: number
  /**
   * Show HOT badge
   */
  isHot?: boolean
}

export const TrendingCourseThumbnail: React.FC<TrendingCourseThumbnailProps> = ({
  trendingRank,
  isHot = false,
}) => {
  const getTrendingIcon = (rank: number): string => {
    if (rank === 1) return "trending-up"
    if (rank <= 3) return "trending-up"
    return "trending-up"
  }

  const getTrendingColor = (rank: number): string => {
    if (rank === 1) return colors.palette.angry500 // Red for #1
    if (rank <= 3) return colors.palette.warning500 // Orange for top 3
    return colors.palette.primary500 // Blue for others
  }

  return (
    <View style={$container}>
      <View style={$thumbnailPlaceholder}>
        <Icon
          icon="play-circle"
          size={32}
          color={colors.palette.primary500}
        />
      </View>
      
      <View style={[$trendingBadge, { backgroundColor: getTrendingColor(trendingRank) }]}>
        <Icon
          icon={getTrendingIcon(trendingRank)}
          size={12}
          color={colors.palette.neutral100}
        />
        <Text
          style={$trendingRankText}
          text={`#${trendingRank}`}
        />
      </View>
      
      {isHot && (
        <View style={$hotBadge}>
          <Icon
            icon="flame"
            size={12}
            color={colors.palette.neutral100}
          />
          <Text
            style={$hotBadgeText}
            text="HOT"
          />
        </View>
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  position: "relative",
  marginBottom: spacing.sm, // 12px below thumbnail
}

const $thumbnailPlaceholder: ViewStyle = {
  width: "100%",
  height: 110,
  backgroundColor: colors.palette.primary100, // Light blue background
  borderRadius: 8,
  alignItems: "center",
  justifyContent: "center",
}

const $trendingBadge: ViewStyle = {
  position: "absolute",
  top: 8,
  left: 8,
  flexDirection: "row",
  alignItems: "center",
  borderRadius: 12,
  paddingHorizontal: 6,
  paddingVertical: 2,
  gap: 2,
}

const $trendingRankText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 10,
  lineHeight: 12,
  color: colors.palette.neutral100, // White text
  fontWeight: "600",
}

const $hotBadge: ViewStyle = {
  position: "absolute",
  top: 8,
  right: 8,
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.angry500, // Red HOT badge
  borderRadius: 12,
  paddingHorizontal: 6,
  paddingVertical: 2,
  gap: 2,
}

const $hotBadgeText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 9,
  lineHeight: 11,
  color: colors.palette.neutral100, // White text
  fontWeight: "600",
}
