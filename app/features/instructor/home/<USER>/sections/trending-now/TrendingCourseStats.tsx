import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface TrendingCourseStatsProps {
  /**
   * View count
   */
  viewCount: number
  /**
   * Weekly growth percentage
   */
  weeklyGrowth: number
}

export const TrendingCourseStats: React.FC<TrendingCourseStatsProps> = ({
  viewCount,
  weeklyGrowth,
}) => {
  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M views`
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k views`
    }
    return `${count} views`
  }

  return (
    <View style={$container}>
      <View style={$viewStats}>
        <Icon
          icon="eye"
          size={12}
          color={colors.palette.primary500}
        />
        <Text
          style={$viewCount}
          text={formatViewCount(viewCount)}
        />
      </View>
      
      <View style={$growthStats}>
        <Icon
          icon="trending-up"
          size={12}
          color={colors.palette.success500}
        />
        <Text
          style={$growthText}
          text={`+${weeklyGrowth}%`}
        />
      </View>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
}

const $viewStats: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs / 2, // 4px gap
}

const $viewCount: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  color: colors.palette.primary600, // Dark blue
}

const $growthStats: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs / 2, // 4px gap
}

const $growthText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  color: colors.palette.success600, // Green for growth
  fontWeight: "600",
}
