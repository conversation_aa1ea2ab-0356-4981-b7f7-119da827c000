import React from "react"
import { TouchableOpacity, View, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { TrendingCourseThumbnail } from "./TrendingCourseThumbnail"
import { TrendingCourseStats } from "./TrendingCourseStats"
import { TrendingCourseInfo } from "./TrendingCourseInfo"

interface TrendingCourse {
  id: string
  title: string
  instructor: string
  thumbnailUrl?: string
  rating: number
  reviewCount: number
  price: number
  originalPrice?: number
  duration: string
  level: "beginner" | "intermediate" | "advanced"
  category: string
  viewCount: number
  trendingRank: number
  weeklyGrowth: number
  isHot?: boolean
}

interface TrendingCourseCardProps {
  /**
   * Course data
   */
  course: TrendingCourse
  /**
   * Callback when course is pressed
   */
  onPress?: (course: TrendingCourse) => void
}

export const TrendingCourseCard: React.FC<TrendingCourseCardProps> = ({
  course,
  onPress,
}) => {
  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M views`
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k views`
    }
    return `${count} views`
  }

  return (
    <TouchableOpacity
      style={$container}
      onPress={() => onPress?.(course)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`${course.title} by ${course.instructor}, trending #${course.trendingRank}, ${formatViewCount(course.viewCount)}`}
      accessibilityRole="button"
    >
      <TrendingCourseThumbnail
        trendingRank={course.trendingRank}
        isHot={course.isHot}
      />

      <View style={$courseInfo}>
        <TrendingCourseInfo
          title={course.title}
          instructor={course.instructor}
          rating={course.rating}
          reviewCount={course.reviewCount}
          level={course.level}
          price={course.price}
          originalPrice={course.originalPrice}
          duration={course.duration}
        />
        
        <TrendingCourseStats
          viewCount={course.viewCount}
          weeklyGrowth={course.weeklyGrowth}
        />
      </View>
    </TouchableOpacity>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  width: 220,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  padding: spacing.sm, // 12px internal padding
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}

const $courseInfo: ViewStyle = {
  gap: spacing.xs / 2, // 4px gap between elements
}
