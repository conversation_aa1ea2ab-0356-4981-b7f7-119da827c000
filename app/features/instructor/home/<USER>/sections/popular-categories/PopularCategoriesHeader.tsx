import React from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface PopularCategoriesHeaderProps {
  /**
   * Section title
   */
  title?: string
  /**
   * Show view all button
   */
  showViewAll?: boolean
  /**
   * Callback when view all is pressed
   */
  onViewAllPress?: () => void
}

export const PopularCategoriesHeader: React.FC<PopularCategoriesHeaderProps> = ({
  title = "Popular Categories",
  showViewAll = true,
  onViewAllPress,
}) => {
  return (
    <View style={$container}>
      <Text
        style={$sectionTitle}
        text={title}
      />
      
      {showViewAll && (
        <TouchableOpacity
          style={$viewAllButton}
          onPress={onViewAllPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Text
            style={$viewAllText}
            text="View All"
          />
          <Icon
            icon="chevron-right"
            size={16}
            color={colors.palette.primary500}
          />
        </TouchableOpacity>
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.md, // 16px below header
}

const $sectionTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for section headings
  fontSize: 18,
  lineHeight: 24,
  color: colors.palette.primary700, // Deep navy
}

const $viewAllButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs / 2, // 4px gap
}

const $viewAllText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 14,
  lineHeight: 20,
  color: colors.palette.primary500, // Royal blue
}
