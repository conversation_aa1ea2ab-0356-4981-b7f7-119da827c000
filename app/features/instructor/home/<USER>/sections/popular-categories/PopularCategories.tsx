import React from "react"
import { View, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { PopularCategoriesHeader } from "./PopularCategoriesHeader"
import { CategoriesGrid } from "./CategoriesGrid"

interface Category {
  id: string
  name: string
  icon: string
  courseCount: number
  color?: string
  backgroundColor?: string
}

interface PopularCategoriesProps {
  /**
   * Array of popular categories
   */
  categories: Category[]
  /**
   * Section title
   */
  title?: string
  /**
   * Number of columns (2 or 3)
   */
  columns?: 2 | 3
  /**
   * Show view all button
   */
  showViewAll?: boolean
  /**
   * Callback when view all is pressed
   */
  onViewAllPress?: () => void
  /**
   * Callback when category is pressed
   */
  onCategoryPress?: (category: Category) => void
}

export const PopularCategories: React.FC<PopularCategoriesProps> = ({
  categories,
  title = "Popular Categories",
  columns = 2,
  showViewAll = true,
  onViewAllPress,
  onCategoryPress,
}) => {
  if (categories.length === 0) {
    return null
  }

  return (
    <View style={$container}>
      <PopularCategoriesHeader
        title={title}
        showViewAll={showViewAll}
        onViewAllPress={onViewAllPress}
      />

      <CategoriesGrid
        categories={categories}
        columns={columns}
        onCategoryPress={onCategoryPress}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}
