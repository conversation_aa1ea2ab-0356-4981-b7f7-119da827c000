import React from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface Category {
  id: string
  name: string
  icon: string
  courseCount: number
  color?: string
  backgroundColor?: string
}

interface CategoryItemProps {
  /**
   * Category data
   */
  category: Category
  /**
   * Number of columns for flex calculation
   */
  columns: number
  /**
   * Callback when category is pressed
   */
  onPress?: (category: Category) => void
}

export const CategoryItem: React.FC<CategoryItemProps> = ({
  category,
  columns,
  onPress,
}) => {
  const formatCourseCount = (count: number): string => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k courses`
    }
    return `${count} courses`
  }

  return (
    <TouchableOpacity
      style={[$container, { flex: 1 / columns }]}
      onPress={() => onPress?.(category)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`${category.name} category with ${category.courseCount} courses`}
      accessibilityRole="button"
    >
      <View style={[$iconContainer, { 
        backgroundColor: category.backgroundColor || colors.palette.primary100 
      }]}>
        <Icon
          icon={category.icon}
          size={32}
          color={category.color || colors.palette.primary500}
        />
      </View>
      
      <View style={$categoryInfo}>
        <Text
          style={$categoryName}
          text={category.name}
          numberOfLines={2}
        />
        
        <Text
          style={$courseCount}
          text={formatCourseCount(category.courseCount)}
        />
      </View>
    </TouchableOpacity>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: colors.palette.neutral100, // Light background
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
  alignItems: "center",
  minHeight: 120,
  justifyContent: "center",
}

const $iconContainer: ViewStyle = {
  width: 56,
  height: 56,
  borderRadius: 28,
  alignItems: "center",
  justifyContent: "center",
  marginBottom: spacing.sm, // 12px below icon
}

const $categoryInfo: ViewStyle = {
  alignItems: "center",
  gap: spacing.xs / 2, // 4px gap
}

const $categoryName: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for card titles
  fontSize: 14,
  lineHeight: 18,
  color: colors.palette.primary700, // Deep navy
  textAlign: "center",
}

const $courseCount: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary600, // Dark blue
  textAlign: "center",
}
