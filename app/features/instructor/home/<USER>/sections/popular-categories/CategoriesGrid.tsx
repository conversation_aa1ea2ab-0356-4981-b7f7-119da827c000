import React from "react"
import { View, ViewStyle } from "react-native"
import { spacing } from "app/theme"
import { CategoryItem } from "./CategoryItem"

interface Category {
  id: string
  name: string
  icon: string
  courseCount: number
  color?: string
  backgroundColor?: string
}

interface CategoriesGridProps {
  /**
   * Array of categories
   */
  categories: Category[]
  /**
   * Number of columns (2 or 3)
   */
  columns?: 2 | 3
  /**
   * Callback when category is pressed
   */
  onCategoryPress?: (category: Category) => void
}

export const CategoriesGrid: React.FC<CategoriesGridProps> = ({
  categories,
  columns = 2,
  onCategoryPress,
}) => {
  const renderCategoriesGrid = () => {
    const rows = []
    for (let i = 0; i < categories.length; i += columns) {
      const rowCategories = categories.slice(i, i + columns)
      rows.push(
        <View key={i} style={$categoriesRow}>
          {rowCategories.map((category) => (
            <CategoryItem
              key={category.id}
              category={category}
              columns={columns}
              onPress={onCategoryPress}
            />
          ))}
          {/* Fill empty spaces if needed */}
          {rowCategories.length < columns && 
            Array.from({ length: columns - rowCategories.length }).map((_, index) => (
              <View key={`empty-${index}`} style={{ flex: 1 / columns }} />
            ))
          }
        </View>
      )
    }
    return rows
  }

  return (
    <View style={$container}>
      {renderCategoriesGrid()}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  gap: spacing.sm, // 12px gap between rows
}

const $categoriesRow: ViewStyle = {
  flexDirection: "row",
  gap: spacing.sm, // 12px gap between items
}
