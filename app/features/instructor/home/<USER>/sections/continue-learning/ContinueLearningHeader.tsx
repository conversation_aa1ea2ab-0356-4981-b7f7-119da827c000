import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { OverallProgress } from "./OverallProgress"

interface ContinueLearningHeaderProps {
  /**
   * Section title
   */
  title?: string
  /**
   * Overall learning progress
   */
  overallProgress?: number
  /**
   * Show overall progress
   */
  showOverallProgress?: boolean
}

export const ContinueLearningHeader: React.FC<ContinueLearningHeaderProps> = ({
  title = "Continue Learning",
  overallProgress = 0,
  showOverallProgress = true,
}) => {
  return (
    <View style={$container}>
      <Text
        style={$sectionTitle}
        text={title}
      />

      {showOverallProgress && overallProgress > 0 && (
        <OverallProgress progress={overallProgress} />
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  marginBottom: spacing.md, // 16px below header
}

const $sectionTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for section headings
  fontSize: 18,
  lineHeight: 24,
  color: colors.palette.primary700, // Deep navy
  marginBottom: spacing.sm, // 12px below title
}
