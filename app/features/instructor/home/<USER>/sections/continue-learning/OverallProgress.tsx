import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing, typography } from "app/theme"
import { ProgressIndicator } from "../progress-indicator"

interface OverallProgressProps {
  /**
   * Progress percentage (0-100)
   */
  progress: number
  /**
   * Progress label text
   */
  label?: string
  /**
   * Show percentage text
   */
  showPercentage?: boolean
  /**
   * Progress bar height
   */
  height?: number
}

export const OverallProgress: React.FC<OverallProgressProps> = ({
  progress,
  label = "Overall Progress",
  showPercentage = true,
  height = 6,
}) => {
  return (
    <View style={$container}>
      <Text
        style={$progressLabel}
        text={label}
      />
      <ProgressIndicator
        progress={progress}
        height={height}
        showPercentage={showPercentage}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: colors.palette.primary100, // Light blue background
  padding: spacing.sm, // 12px internal padding
  borderRadius: 8,
  marginBottom: spacing.sm, // 12px below progress
}

const $progressLabel: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary600, // Dark blue
  marginBottom: spacing.xs, // 8px below label
}
