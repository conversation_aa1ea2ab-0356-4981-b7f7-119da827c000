import React from "react"
import { View, ViewStyle } from "react-native"
import { spacing } from "app/theme"
import { ResumeButton } from "../resume-button"

interface ContinueLearningActionsProps {
  /**
   * Continue learning button text
   */
  continueText?: string
  /**
   * View all courses button text
   */
  viewAllText?: string
  /**
   * Callback when continue button is pressed
   */
  onContinuePress?: () => void
  /**
   * Callback when view all button is pressed
   */
  onViewAllPress?: () => void
  /**
   * Show continue button
   */
  showContinueButton?: boolean
  /**
   * Show view all button
   */
  showViewAllButton?: boolean
}

export const ContinueLearningActions: React.FC<ContinueLearningActionsProps> = ({
  continueText = "Continue Learning",
  viewAllText = "View All Courses",
  onContinuePress,
  onViewAllPress,
  showContinueButton = true,
  showViewAllButton = true,
}) => {
  return (
    <View style={$container}>
      {showContinueButton && (
        <ResumeButton
          text={continueText}
          onPress={onContinuePress}
          size="medium"
          variant="primary"
          showIcon={true}
        />
      )}

      {showViewAllButton && (
        <ResumeButton
          text={viewAllText}
          onPress={onViewAllPress}
          size="medium"
          variant="outline"
          showIcon={false}
        />
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  flexDirection: "row",
  gap: spacing.sm, // 12px gap between buttons
  marginTop: spacing.md, // 16px above actions
}
