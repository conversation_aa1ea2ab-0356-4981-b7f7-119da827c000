import React from "react"
import { View, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { ContinueLearningHeader } from "./ContinueLearningHeader"
import { ContinueLearningActions } from "./ContinueLearningActions"
import { LastWatchedCourse } from "../last-watched-course"

interface ContinueLearningProps {
  /**
   * Last watched course data
   */
  lastCourse?: {
    id: string
    title: string
    instructor: string
    thumbnailUrl?: string
    lastLessonTitle: string
    progress: number
  }
  /**
   * Overall learning progress
   */
  overallProgress?: number
  /**
   * Navigation callbacks
   */
  onContinueCourse?: () => void
  onViewAllCourses?: () => void
  /**
   * Show section
   */
  show?: boolean
  /**
   * Section title
   */
  title?: string
  /**
   * Show overall progress
   */
  showOverallProgress?: boolean
}

export const ContinueLearning: React.FC<ContinueLearningProps> = ({
  lastCourse,
  overallProgress = 0,
  onContinueCourse,
  onViewAllCourses,
  show = true,
  title = "Continue Learning",
  showOverallProgress = true,
}) => {
  if (!show || !lastCourse) {
    return null
  }

  return (
    <View style={$container}>
      <ContinueLearningHeader
        title={title}
        overallProgress={overallProgress}
        showOverallProgress={showOverallProgress}
      />

      <LastWatchedCourse
        course={lastCourse}
        onPress={onContinueCourse}
        showPlayIcon={true}
      />

      <ContinueLearningActions
        onContinuePress={onContinueCourse}
        onViewAllPress={onViewAllCourses}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}
