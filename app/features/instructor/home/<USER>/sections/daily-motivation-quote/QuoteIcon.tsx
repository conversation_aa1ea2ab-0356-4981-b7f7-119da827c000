import React from "react"
import { View, ViewStyle } from "react-native"
import { Icon } from "app/components"
import { colors, spacing } from "app/theme"

export const QuoteIcon: React.FC = () => {
  return (
    <View style={$iconContainer}>
      <Icon
        icon="heart"
        size={24}
        color={colors.palette.primary500}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $iconContainer: ViewStyle = {
  marginRight: spacing.sm, // 12px spacing from text
  marginTop: spacing.xs, // 8px top margin for alignment
}
