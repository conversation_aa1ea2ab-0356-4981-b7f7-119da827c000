import React from "react"
import { View, ViewStyle, Pressable } from "react-native"
import { colors, spacing } from "app/theme"
import { QuoteIcon } from "./QuoteIcon"
import { QuoteText } from "./QuoteText"

interface DailyMotivationQuoteProps {
  /**
   * Custom quote text
   */
  quote?: string
  /**
   * Quote author
   */
  author?: string
  /**
   * Show quote icon
   */
  showIcon?: boolean
  /**
   * Callback when quote is pressed
   */
  onPress?: () => void
}

export const DailyMotivationQuote: React.FC<DailyMotivationQuoteProps> = ({
  quote,
  author,
  showIcon = true,
  onPress,
}) => {
  // Default quotes array
  const defaultQuotes = [
    {
      text: "The only way to do great work is to love what you do.",
      author: "<PERSON>"
    },
    {
      text: "Learning never exhausts the mind.",
      author: "Leonardo da Vinci"
    },
    {
      text: "Education is the most powerful weapon which you can use to change the world.",
      author: "<PERSON>"
    },
    {
      text: "The beautiful thing about learning is that no one can take it away from you.",
      author: "<PERSON><PERSON><PERSON><PERSON>"
    },
    {
      text: "Success is not final, failure is not fatal: it is the courage to continue that counts.",
      author: "<PERSON>"
    }
  ]

  // Get daily quote (based on day of year for consistency)
  const getDailyQuote = () => {
    if (quote && author) {
      return { text: quote, author }
    }

    const dayOfYear = Math.floor((Date.now() - new Date(new Date().getFullYear(), 0, 0).getTime()) / 86400000)
    const quoteIndex = dayOfYear % defaultQuotes.length
    return defaultQuotes[quoteIndex]
  }

  const dailyQuote = getDailyQuote()
  const Component = onPress ? Pressable : View

  return (
    <Component
      style={$container}
      onPress={onPress}
      accessibilityRole={onPress ? "button" : undefined}
      accessibilityLabel={onPress ? `Daily quote: ${dailyQuote.text} by ${dailyQuote.author}` : undefined}
    >
      {showIcon && <QuoteIcon />}

      <QuoteText
        quote={dailyQuote.text}
        author={dailyQuote.author}
      />
    </Component>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: colors.background, // White background - MANDATORY
  borderRadius: 12, // 12px border radius per guideline
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginVertical: spacing.sm, // 12px vertical spacing
  flexDirection: "row",
  alignItems: "flex-start",
  // Subtle shadow for elevation
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 2,
}
