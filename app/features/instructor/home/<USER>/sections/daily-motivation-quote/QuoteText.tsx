import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing } from "app/theme"

interface QuoteTextProps {
  /**
   * Quote text
   */
  quote: string
  /**
   * Quote author
   */
  author: string
}

export const QuoteText: React.FC<QuoteTextProps> = ({
  quote,
  author,
}) => {
  return (
    <View style={$textContainer}>
      <Text
        text={`"${quote}"`}
        preset="description" // Light weight (300) - MANDATORY
        size="sm"
        style={$quoteText}
      />
      
      <Text
        text={`— ${author}`}
        preset="description" // Light weight (300) - MANDATORY
        size="xs"
        style={$authorText}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $textContainer: ViewStyle = {
  flex: 1,
}

const $quoteText: TextStyle = {
  color: colors.text,
  lineHeight: 20,
  marginBottom: spacing.xs, // 8px spacing
  fontStyle: "italic",
}

const $authorText: TextStyle = {
  color: colors.textDim,
  textAlign: "right",
}
