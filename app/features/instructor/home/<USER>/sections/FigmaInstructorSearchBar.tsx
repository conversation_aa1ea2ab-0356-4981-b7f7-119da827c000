import React from "react"
import { View, Text, TouchableOpacity, StyleSheet } from "react-native"
import { Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface FigmaInstructorSearchBarProps {
  /**
   * Search placeholder text
   */
  placeholder?: string
  /**
   * Callbacks
   */
  onSearchPress?: () => void
  onFilterPress?: () => void
}

/**
 * FigmaInstructorSearchBar - Search bar component based on exact Figma design
 * 
 * Replicates the SEARCH section from Figma:
 * - White background with shadow
 * - Search icon on left
 * - "Search for.." placeholder text (adapted for instructor)
 * - Filter button on right with blue background
 */
export const FigmaInstructorSearchBar: React.FC<FigmaInstructorSearchBarProps> = ({
  placeholder = "Search for courses, students..",
  onSearchPress,
  onFilterPress,
}) => {
  return (
    <View style={styles.container}>
      {/* Main search bar background */}
      <View style={styles.searchBackground}>
        {/* Search icon */}
        <Icon 
          icon="search" 
          size={20} 
          color="#000000" // From Figma: fill_BN34AD
          style={styles.searchIcon}
        />
        
        {/* Search placeholder text */}
        <TouchableOpacity 
          style={styles.searchTextContainer}
          onPress={onSearchPress}
          activeOpacity={0.7}
        >
          <Text style={styles.placeholderText}>
            {placeholder}
          </Text>
        </TouchableOpacity>
        
        {/* Filter button */}
        <TouchableOpacity 
          style={styles.filterButton}
          onPress={onFilterPress}
          activeOpacity={0.7}
        >
          <View style={styles.filterBackground}>
            <Icon 
              icon="settings" 
              size={19} 
              color="#0961F5" // From Figma: fill_IV1JZW (blue)
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: 360, // From Figma: width: 360
    height: 64, // From Figma: height: 64
  },
  searchBackground: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFFFFF", // From Figma: fill_ORCEJY (white)
    borderRadius: 15, // From Figma: borderRadius: 15px
    paddingHorizontal: 13, // From Figma: search icon x: 13
    // Shadow from Figma: effect_XXLBBD
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 3,
    },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5, // For Android
  },
  searchIcon: {
    marginRight: 9, // From Figma: placeholder x: 42, icon x: 13, so gap = 42-13-20 = 9
  },
  searchTextContainer: {
    flex: 1,
    justifyContent: "center",
  },
  placeholderText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 16,
    lineHeight: 20,
    color: "#B4BDC4",
  },
  filterButton: {
    marginLeft: spacing.xs,
  },
  filterBackground: {
    width: 38, // From Figma: width: 38
    height: 38, // From Figma: height: 38
    borderRadius: 10, // From Figma: borderRadius: 10px
    backgroundColor: "#E8F1FF", // From Figma: fill_IV1JZW (light blue)
    alignItems: "center",
    justifyContent: "center",
  },
})
