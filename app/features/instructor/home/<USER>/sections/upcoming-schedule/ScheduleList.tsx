import React from "react"
import { View, ViewStyle } from "react-native"
import { spacing } from "app/theme"
import { ScheduleItem } from "./ScheduleItem"
import { ScheduleItem as ScheduleItemType } from "./types"

interface ScheduleListProps {
  /**
   * Array of schedule items
   */
  items: ScheduleItemType[]
  /**
   * Maximum number of items to show
   */
  maxItems?: number
  /**
   * Callback when item is pressed
   */
  onItemPress?: (item: ScheduleItemType) => void
}

export const ScheduleList: React.FC<ScheduleListProps> = ({
  items,
  maxItems = 3,
  onItemPress,
}) => {
  const displayItems = items.slice(0, maxItems)

  return (
    <View style={$scheduleList}>
      {displayItems.map((item) => (
        <ScheduleItem
          key={item.id}
          item={item}
          onPress={onItemPress}
        />
      ))}
    </View>
  )
}

// Themed styles following design guidelines
const $scheduleList: ViewStyle = {
  gap: spacing.sm, // 12px gap between items
}
