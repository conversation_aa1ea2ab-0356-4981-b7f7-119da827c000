import React from "react"
import { View, ViewStyle } from "react-native"
import { spacing } from "app/theme"
import { UpcomingScheduleHeader } from "./UpcomingScheduleHeader"
import { ScheduleList } from "./ScheduleList"
import { EmptySchedule } from "./EmptySchedule"
import { UpcomingScheduleProps } from "./types"

export const UpcomingSchedule: React.FC<UpcomingScheduleProps> = ({
  scheduleItems,
  title = "Upcoming Schedule",
  showViewAll = true,
  onViewAllPress,
  onScheduleItemPress,
  show = true,
}) => {
  if (!show) {
    return null
  }

  if (scheduleItems.length === 0) {
    return (
      <EmptySchedule
        title={title}
        message="No upcoming events"
      />
    )
  }

  return (
    <View style={$container}>
      <UpcomingScheduleHeader
        title={title}
        showViewAll={showViewAll}
        onViewAllPress={onViewAllPress}
      />

      <ScheduleList
        items={scheduleItems}
        maxItems={3}
        onItemPress={onScheduleItemPress}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}
