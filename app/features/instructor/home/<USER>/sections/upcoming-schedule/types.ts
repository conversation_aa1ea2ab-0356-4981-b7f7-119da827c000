/**
 * Type definitions for UpcomingSchedule components
 */

export interface ScheduleItem {
  id: string
  title: string
  type: "live_class" | "exam" | "assignment" | "workshop"
  courseName: string
  instructor?: string
  dateTime: string
  duration?: string
  location?: string
  isOnline?: boolean
  priority: "high" | "medium" | "low"
  status: "upcoming" | "in_progress" | "completed"
}

export interface UpcomingScheduleProps {
  /**
   * Array of upcoming schedule items
   */
  scheduleItems: ScheduleItem[]
  /**
   * Section title
   */
  title?: string
  /**
   * Show view all button
   */
  showViewAll?: boolean
  /**
   * Callback when view all is pressed
   */
  onViewAllPress?: () => void
  /**
   * Callback when schedule item is pressed
   */
  onScheduleItemPress?: (item: ScheduleItem) => void
  /**
   * Show schedule section
   */
  show?: boolean
}
