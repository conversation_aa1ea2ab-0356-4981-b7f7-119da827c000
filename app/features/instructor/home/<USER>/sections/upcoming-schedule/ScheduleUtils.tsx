import { colors } from "app/theme"

/**
 * Utility functions for schedule items
 */

/**
 * Get icon name for schedule item type
 */
export const getTypeIcon = (type: string): string => {
  switch (type) {
    case "live_class":
      return "video"
    case "exam":
      return "file-text"
    case "assignment":
      return "edit"
    case "workshop":
      return "users"
    default:
      return "calendar"
  }
}

/**
 * Get color for schedule item type
 */
export const getTypeColor = (type: string): string => {
  switch (type) {
    case "live_class":
      return colors.palette.primary500
    case "exam":
      return colors.palette.angry500
    case "assignment":
      return colors.palette.warning500
    case "workshop":
      return colors.palette.success500
    default:
      return colors.palette.neutral500
  }
}

/**
 * Get label for schedule item type
 */
export const getTypeLabel = (type: string): string => {
  switch (type) {
    case "live_class":
      return "Live Class"
    case "exam":
      return "Exam"
    case "assignment":
      return "Assignment"
    case "workshop":
      return "Workshop"
    default:
      return "Event"
  }
}

/**
 * Get color for priority level
 */
export const getPriorityColor = (priority: string): string => {
  switch (priority) {
    case "high":
      return colors.palette.angry500
    case "medium":
      return colors.palette.warning500
    case "low":
      return colors.palette.success500
    default:
      return colors.palette.primary500
  }
}

/**
 * Format date and time for display
 */
export const formatDateTime = (dateTimeString: string): { date: string; time: string } => {
  const date = new Date(dateTimeString)
  const now = new Date()
  const diffTime = date.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  let dateText = ""
  if (diffDays === 0) dateText = "Today"
  else if (diffDays === 1) dateText = "Tomorrow"
  else if (diffDays < 7) dateText = `In ${diffDays} days`
  else dateText = date.toLocaleDateString()

  const timeText = date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })

  return { date: dateText, time: timeText }
}
