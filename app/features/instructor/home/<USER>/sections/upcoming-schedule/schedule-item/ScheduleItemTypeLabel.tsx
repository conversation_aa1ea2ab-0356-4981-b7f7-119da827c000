/**
 * Schedule Item Type Label Component
 * 
 * Displays the type label for the schedule item
 */

import React from "react"
import { View, Text } from "react-native"
import { getTypeColor, getTypeLabel } from "../ScheduleUtils"
import type { ScheduleItemTypeLabelProps } from "./types"
import { $typeLabel, $typeLabelText } from "./styles"

export const ScheduleItemTypeLabel: React.FC<ScheduleItemTypeLabelProps> = ({
  item,
}) => {
  return (
    <View style={$typeLabel}>
      <Text
        style={[$typeLabelText, { color: getTypeColor(item.type) }]}
        text={getTypeLabel(item.type)}
      />
    </View>
  )
}
