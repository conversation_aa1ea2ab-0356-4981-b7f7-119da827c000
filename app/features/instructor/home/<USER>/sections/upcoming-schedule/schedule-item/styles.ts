/**
 * Styles for ScheduleItem components
 */

import type { ViewStyle, TextStyle } from "react-native"
import { colors, spacing, typography } from "app/theme"

// Main container styles
export const $scheduleItem: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  padding: spacing.sm, // 12px internal padding
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}

// Header styles
export const $itemHeader: ViewStyle = {
  flexDirection: "row",
  alignItems: "flex-start",
  gap: spacing.sm, // 12px gap
  marginBottom: spacing.sm, // 12px below header
}

export const $typeIconContainer: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 20,
  alignItems: "center",
  justifyContent: "center",
}

export const $itemInfo: ViewStyle = {
  flex: 1,
  gap: spacing.xs / 2, // 4px gap
}

export const $titleRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
}

export const $itemTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for card titles
  fontSize: 14,
  lineHeight: 18,
  color: colors.palette.primary700, // Deep navy
  flex: 1,
}

export const $priorityIndicator: ViewStyle = {
  width: 8,
  height: 8,
  borderRadius: 4,
}

export const $courseName: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 13,
  lineHeight: 17,
  color: colors.palette.primary600, // Dark blue
}

export const $instructorName: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.neutral600,
  fontStyle: "italic",
}

// Details styles
export const $itemDetails: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.md, // 16px gap between details
  marginBottom: spacing.xs, // 8px below details
}

export const $timeContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs / 2, // 4px gap
}

export const $dateTime: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary600, // Dark blue
}

export const $durationContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs / 2, // 4px gap
}

export const $duration: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.neutral600,
}

export const $locationContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs / 2, // 4px gap
}

export const $location: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.neutral600,
}

// Type label styles
export const $typeLabel: ViewStyle = {
  alignSelf: "flex-end",
}

export const $typeLabelText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  fontWeight: "600",
}
