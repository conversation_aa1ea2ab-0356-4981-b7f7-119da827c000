/**
 * Schedule Item Component - Refactored
 * 
 * Main schedule item component using smaller, focused sub-components.
 * Reduced from 245 lines to ~60 lines for better maintainability.
 */

import React from "react"
import { TouchableOpacity } from "react-native"
import { formatDateTime } from "../ScheduleUtils"
import { getTypeLabel } from "../ScheduleUtils"
import type { ScheduleItemProps } from "./types"
import { ScheduleItemHeader } from "./ScheduleItemHeader"
import { ScheduleItemDetails } from "./ScheduleItemDetails"
import { ScheduleItemTypeLabel } from "./ScheduleItemTypeLabel"
import { $scheduleItem } from "./styles"

/**
 * Schedule Item Component
 * 
 * Displays a schedule item with type icon, title, course info, time details, and type label.
 * Features accessibility support and touch interactions.
 */
export const ScheduleItem: React.FC<ScheduleItemProps> = ({
  item,
  onPress,
}) => {
  const { date, time } = formatDateTime(item.dateTime)

  return (
    <TouchableOpacity
      style={$scheduleItem}
      onPress={() => onPress?.(item)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`${getTypeLabel(item.type)}: ${item.title} for ${item.courseName}, ${date} at ${time}`}
      accessibilityRole="button"
    >
      <ScheduleItemHeader item={item} />
      
      <ScheduleItemDetails item={item} />
      
      <ScheduleItemTypeLabel item={item} />
    </TouchableOpacity>
  )
}
