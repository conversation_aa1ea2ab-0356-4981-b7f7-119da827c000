/**
 * Types for ScheduleItem components
 */

import type { ScheduleItem as ScheduleItemType } from "../types"

export interface ScheduleItemProps {
  /**
   * Schedule item data
   */
  item: ScheduleItemType
  /**
   * Callback when item is pressed
   */
  onPress?: (item: ScheduleItemType) => void
}

export interface ScheduleItemHeaderProps {
  item: ScheduleItemType
}

export interface ScheduleItemDetailsProps {
  item: ScheduleItemType
}

export interface ScheduleItemTypeLabelProps {
  item: ScheduleItemType
}

export { ScheduleItemType }
