/**
 * Schedule Item Details Component
 * 
 * Displays the details section with time, duration, and location information
 */

import React from "react"
import { View, Text } from "react-native"
import { Icon } from "app/components"
import { colors } from "app/theme"
import { formatDateTime } from "../ScheduleUtils"
import type { ScheduleItemDetailsProps } from "./types"
import {
  $itemDetails,
  $timeContainer,
  $dateTime,
  $durationContainer,
  $duration,
  $locationContainer,
  $location,
} from "./styles"

export const ScheduleItemDetails: React.FC<ScheduleItemDetailsProps> = ({
  item,
}) => {
  const { date, time } = formatDateTime(item.dateTime)

  return (
    <View style={$itemDetails}>
      <View style={$timeContainer}>
        <Icon
          icon="clock"
          size={14}
          color={colors.palette.primary500}
        />
        <Text
          style={$dateTime}
          text={`${date} • ${time}`}
        />
      </View>

      {item.duration && (
        <View style={$durationContainer}>
          <Icon
            icon="timer"
            size={14}
            color={colors.palette.neutral500}
          />
          <Text
            style={$duration}
            text={item.duration}
          />
        </View>
      )}

      {item.isOnline !== undefined && (
        <View style={$locationContainer}>
          <Icon
            icon={item.isOnline ? "wifi" : "map-pin"}
            size={14}
            color={item.isOnline ? colors.palette.success500 : colors.palette.warning500}
          />
          <Text
            style={$location}
            text={item.isOnline ? "Online" : (item.location || "In-person")}
          />
        </View>
      )}
    </View>
  )
}
