/**
 * Schedule Item Header Component
 * 
 * Displays the header section with type icon, title, course name, and instructor
 */

import React from "react"
import { View, Text } from "react-native"
import { Icon } from "app/components"
import { getTypeIcon, getTypeColor, getPriorityColor } from "../ScheduleUtils"
import type { ScheduleItemHeaderProps } from "./types"
import {
  $itemHeader,
  $typeIconContainer,
  $itemInfo,
  $titleRow,
  $itemTitle,
  $priorityIndicator,
  $courseName,
  $instructorName,
} from "./styles"

export const ScheduleItemHeader: React.FC<ScheduleItemHeaderProps> = ({
  item,
}) => {
  return (
    <View style={$itemHeader}>
      <View style={[$typeIconContainer, { backgroundColor: getTypeColor(item.type) + "20" }]}>
        <Icon
          icon={getTypeIcon(item.type)}
          size={20}
          color={getTypeColor(item.type)}
        />
      </View>

      <View style={$itemInfo}>
        <View style={$titleRow}>
          <Text
            style={$itemTitle}
            numberOfLines={1}
          >
            {item.title}
          </Text>

          <View style={[$priorityIndicator, { backgroundColor: getPriorityColor(item.priority) }]} />
        </View>

        <Text
          style={$courseName}
          numberOfLines={1}
        >
          {item.courseName}
        </Text>

        {item.instructor && (
          <Text
            style={$instructorName}
            numberOfLines={1}
          >
            {`with ${item.instructor}`}
          </Text>
        )}
      </View>
    </View>
  )
}
