import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface EmptyScheduleProps {
  /**
   * Section title
   */
  title?: string
  /**
   * Empty state message
   */
  message?: string
}

export const EmptySchedule: React.FC<EmptyScheduleProps> = ({
  title = "Upcoming Schedule",
  message = "No upcoming events",
}) => {
  return (
    <View style={$container}>
      <Text
        style={$sectionTitle}
        text={title}
      />

      <View style={$emptyState}>
        <Icon
          icon="calendar"
          size={48}
          color={colors.palette.neutral400}
        />
        <Text
          style={$emptyText}
          text={message}
        />
      </View>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  borderRadius: 12,
  padding: spacing.md, // 16px internal padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}

const $sectionTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for section headings
  fontSize: 18,
  lineHeight: 24,
  color: colors.palette.primary700, // Deep navy
  marginBottom: spacing.md, // 16px below title
}

const $emptyState: ViewStyle = {
  alignItems: "center",
  paddingVertical: spacing.lg, // 24px vertical padding
  gap: spacing.sm, // 12px gap
}

const $emptyText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 14,
  lineHeight: 20,
  color: colors.palette.neutral500,
  textAlign: "center",
}
