/**
 * Schedule Item Components
 *
 * Refactored from a single 245-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the schedule-item module
export {
  ScheduleItem,
  ScheduleItemHeader,
  ScheduleItemDetails,
  ScheduleItemTypeLabel,
  type ScheduleItemProps,
  type ScheduleItemHeaderProps,
  type ScheduleItemDetailsProps,
  type ScheduleItemTypeLabelProps,
  type ScheduleItemType,
} from "./schedule-item"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
