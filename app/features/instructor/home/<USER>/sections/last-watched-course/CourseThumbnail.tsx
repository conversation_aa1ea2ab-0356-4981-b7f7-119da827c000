import React from "react"
import { View, ViewStyle, TextStyle, ImageStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing } from "app/theme"

interface CourseThumbnailProps {
  /**
   * Course thumbnail URL
   */
  thumbnailUrl?: string
  /**
   * Course progress (0-100)
   */
  progress: number
}

export const CourseThumbnail: React.FC<CourseThumbnailProps> = ({
  thumbnailUrl,
  progress,
}) => {
  return (
    <View style={$thumbnailContainer}>
      <View style={$thumbnail}>
        {thumbnailUrl ? (
          // TODO: Add Image component when available
          <View style={$placeholderImage}>
            <Icon
              icon="components"
              size={32}
              color={colors.palette.neutral400}
            />
          </View>
        ) : (
          <View style={$placeholderImage}>
            <Icon
              icon="components"
              size={32}
              color={colors.palette.neutral400}
            />
          </View>
        )}
        
        {/* Progress overlay */}
        <View style={$progressOverlay}>
          <Text
            text={`${Math.round(progress)}%`}
            preset="description" // Light weight (300) - MANDATORY
            size="xs"
            style={$progressText}
          />
        </View>
      </View>
      
      {/* Progress bar */}
      <View style={$progressBarContainer}>
        <View style={$progressBarTrack}>
          <View 
            style={[
              $progressBarFill,
              { width: `${Math.max(0, Math.min(100, progress))}%` }
            ]}
          />
        </View>
      </View>
    </View>
  )
}

// Themed styles following design guidelines
const $thumbnailContainer: ViewStyle = {
  marginRight: spacing.md, // 16px spacing from content
}

const $thumbnail: ViewStyle = {
  width: 80,
  height: 60,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral200,
  position: "relative",
  overflow: "hidden",
}

const $placeholderImage: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: colors.palette.neutral100,
}

const $progressOverlay: ViewStyle = {
  position: "absolute",
  top: 4,
  right: 4,
  backgroundColor: colors.palette.neutral900,
  borderRadius: 4,
  paddingHorizontal: 4,
  paddingVertical: 2,
}

const $progressText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 10,
}

const $progressBarContainer: ViewStyle = {
  marginTop: spacing.xs, // 8px spacing
}

const $progressBarTrack: ViewStyle = {
  width: 80,
  height: 4,
  backgroundColor: colors.palette.neutral300,
  borderRadius: 2,
  overflow: "hidden",
}

const $progressBarFill: ViewStyle = {
  height: "100%",
  backgroundColor: colors.palette.primary500, // Orange progress - MANDATORY
  borderRadius: 2,
}
