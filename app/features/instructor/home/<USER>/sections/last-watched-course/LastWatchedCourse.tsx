import React from "react"
import { View, Pressable, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing } from "app/theme"
import { CourseThumbnail } from "./CourseThumbnail"
import { CourseInfo } from "./CourseInfo"

interface LastWatchedCourseProps {
  /**
   * Course data
   */
  course?: {
    id: string
    title: string
    instructor: string
    thumbnailUrl?: string
    lastLessonTitle?: string
    progress: number
  }
  /**
   * Show section even if no course
   */
  showEmpty?: boolean
  /**
   * Callback when course is pressed
   */
  onPress?: () => void
  /**
   * Callback when continue button is pressed
   */
  onContinue?: () => void
}

export const LastWatchedCourse: React.FC<LastWatchedCourseProps> = ({
  course,
  showEmpty = true,
  onPress,
  onContinue,
}) => {
  if (!course && !showEmpty) {
    return null
  }

  if (!course) {
    return (
      <View style={$container}>
        <View style={$header}>
          <Text
            text="Continue Learning"
            weight="semiBold"
            size="lg"
            style={$sectionTitle}
          />
        </View>

        <View style={$emptyState}>
          <Text
            text="No recent courses"
            preset="description" // Light weight (300) - MANDATORY
            size="sm"
            style={$emptyText}
          />
          <Text
            text="Start learning to see your progress here"
            preset="description" // Light weight (300) - MANDATORY
            size="xs"
            style={$emptySubtext}
          />
        </View>
      </View>
    )
  }

  return (
    <View style={$container}>
      <View style={$header}>
        <Text
          text="Continue Learning"
          weight="semiBold"
          size="lg"
          style={$sectionTitle}
        />
      </View>

      <Pressable
        style={$courseCard}
        onPress={onPress}
        accessibilityRole="button"
        accessibilityLabel={`Continue ${course.title} by ${course.instructor}`}
      >
        <CourseThumbnail
          thumbnailUrl={course.thumbnailUrl}
          progress={course.progress}
        />

        <CourseInfo
          course={course}
          onContinue={onContinue}
        />
      </Pressable>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  // No paddingHorizontal - parent section handles 12px padding
  marginVertical: spacing.sm, // 12px vertical spacing
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.sm, // 12px spacing
}

const $sectionTitle: TextStyle = {
  color: colors.text,
}

const $courseCard: ViewStyle = {
  backgroundColor: colors.background, // White background - MANDATORY
  borderRadius: 12, // 12px border radius per guideline
  padding: spacing.md, // 16px padding
  flexDirection: "row",
  alignItems: "center",
  // Subtle shadow for elevation
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 2,
}

const $emptyState: ViewStyle = {
  backgroundColor: colors.background, // White background - MANDATORY
  borderRadius: 12, // 12px border radius per guideline
  padding: spacing.lg, // 24px padding
  alignItems: "center",
  justifyContent: "center",
  minHeight: 120,
}

const $emptyText: TextStyle = {
  color: colors.textDim,
  textAlign: "center",
  marginBottom: spacing.xs, // 8px spacing
}

const $emptySubtext: TextStyle = {
  color: colors.textDim,
  textAlign: "center",
}
