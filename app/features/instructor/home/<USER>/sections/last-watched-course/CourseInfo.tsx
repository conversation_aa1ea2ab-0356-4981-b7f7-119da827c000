import React from "react"
import { View, Pressable, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing } from "app/theme"

interface CourseInfoProps {
  /**
   * Course data
   */
  course: {
    title: string
    instructor: string
    lastLessonTitle?: string
    progress: number
  }
  /**
   * Callback when continue button is pressed
   */
  onContinue?: () => void
}

export const CourseInfo: React.FC<CourseInfoProps> = ({
  course,
  onContinue,
}) => {
  return (
    <View style={$infoContainer}>
      <View style={$courseDetails}>
        <Text
          text={course.title}
          weight="semiBold"
          size="md"
          style={$courseTitle}
          numberOfLines={2}
        />
        
        <Text
          text={`by ${course.instructor}`}
          preset="description" // Light weight (300) - MANDATORY
          size="sm"
          style={$instructorText}
          numberOfLines={1}
        />
        
        {course.lastLessonTitle && (
          <Text
            text={`Last: ${course.lastLessonTitle}`}
            preset="description" // Light weight (300) - MANDATORY
            size="xs"
            style={$lastLessonText}
            numberOfLines={1}
          />
        )}
      </View>
      
      {onContinue && (
        <Pressable
          style={$continueButton}
          onPress={onContinue}
          accessibilityRole="button"
          accessibilityLabel="Continue learning"
        >
          <Icon
            icon="caretRight"
            size={16}
            color={colors.palette.neutral100}
          />
        </Pressable>
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $infoContainer: ViewStyle = {
  flex: 1,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
}

const $courseDetails: ViewStyle = {
  flex: 1,
  marginRight: spacing.sm, // 12px spacing from button
}

const $courseTitle: TextStyle = {
  color: colors.text,
  marginBottom: spacing.xs, // 8px spacing
}

const $instructorText: TextStyle = {
  color: colors.textDim,
  marginBottom: spacing.xs, // 8px spacing
}

const $lastLessonText: TextStyle = {
  color: colors.textDim,
}

const $continueButton: ViewStyle = {
  width: 36,
  height: 36,
  borderRadius: 18,
  backgroundColor: colors.palette.primary500,
  alignItems: "center",
  justifyContent: "center",
  // Subtle shadow for elevation
  shadowColor: colors.palette.neutral800,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 3,
}
