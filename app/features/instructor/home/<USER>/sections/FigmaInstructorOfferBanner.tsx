import React from "react"
import { View, Text, StyleSheet } from "react-native"
import { colors, spacing, typography } from "app/theme"

interface FigmaInstructorOfferBannerProps {
  /**
   * Offer percentage
   */
  offerPercentage?: string
  /**
   * Offer title
   */
  offerTitle?: string
  /**
   * Offer description
   */
  offerDescription?: string
}

/**
 * FigmaInstructorOfferBanner - Offer banner component based on exact Figma design
 * 
 * Replicates the OFFER section from Figma:
 * - Blue background with rounded corners
 * - "25% Off*" text (adapted for instructor)
 * - "Today's Special" title (adapted for instructor)
 * - Description text (adapted for instructor)
 * - Pagination dots at bottom
 * - Decorative graphics (simplified)
 */
export const FigmaInstructorOfferBanner: React.FC<FigmaInstructorOfferBannerProps> = ({
  offerPercentage = "Free",
  offerTitle = "Today's Special",
  offerDescription = "Get Free Access to Premium Teaching Tools only Valid for Today.!",
}) => {
  return (
    <View style={styles.container}>
      {/* Main banner background */}
      <View style={styles.bannerBackground}>
        {/* Offer content */}
        <View style={styles.contentContainer}>
          {/* Offer percentage */}
          <Text style={styles.offerPercentageText}>
            {offerPercentage}*
          </Text>
          
          {/* Offer title */}
          <Text style={styles.offerTitleText}>
            {offerTitle}
          </Text>
          
          {/* Offer description */}
          <Text style={styles.offerDescriptionText}>
            {offerDescription}
          </Text>
        </View>
        
        {/* Decorative graphics area - simplified */}
        <View style={styles.graphicsContainer}>
          {/* Placeholder for decorative elements */}
          <View style={styles.decorativeElement1} />
          <View style={styles.decorativeElement2} />
        </View>
        
        {/* Pagination dots */}
        <View style={styles.paginationContainer}>
          <View style={[styles.paginationDot, styles.paginationDotInactive]} />
          <View style={[styles.paginationDot, styles.paginationDotInactive]} />
          <View style={[styles.paginationDot, styles.paginationDotActive]} />
          <View style={[styles.paginationDot, styles.paginationDotInactive]} />
          <View style={[styles.paginationDot, styles.paginationDotInactive]} />
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: 360, // From Figma: width: 360
    height: 180, // From Figma: height: 180
  },
  bannerBackground: {
    flex: 1,
    backgroundColor: "#0961F5", // From Figma: fill_WWX2RT (blue)
    borderRadius: 22, // From Figma: borderRadius: 22px
    position: "relative",
    overflow: "hidden",
  },
  contentContainer: {
    position: "absolute",
    left: 24, // From Figma: x: 24
    top: 30, // From Figma: y: 30
    zIndex: 2,
  },
  offerPercentageText: {
    fontFamily: typography.primary.semiBold, // 600 weight - for headings per design guidelines
    fontSize: 15,
    lineHeight: 19,
    textTransform: "uppercase",
    color: "#FFFFFF",
    marginBottom: 0,
  },
  offerTitleText: {
    fontFamily: typography.primary.semiBold, // 600 weight - for headings per design guidelines
    fontSize: 22,
    lineHeight: 28,
    color: "#FFFFFF",
    marginBottom: 10,
  },
  offerDescriptionText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 13,
    lineHeight: 16,
    color: "#FFFFFF",
    width: 181,
  },
  graphicsContainer: {
    position: "absolute",
    right: 0,
    top: 0,
    bottom: 0,
    width: 181, // From Figma: graphics area width
    zIndex: 1,
  },
  decorativeElement1: {
    position: "absolute",
    right: 20,
    top: 20,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  decorativeElement2: {
    position: "absolute",
    right: 40,
    bottom: 30,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "rgba(255, 255, 255, 0.05)",
  },
  paginationContainer: {
    position: "absolute",
    bottom: 16, // From Figma: y: 156 (180-24=156, so 24 from bottom)
    left: 139, // From Figma: x: 139
    flexDirection: "row",
    alignItems: "center",
    width: 82, // From Figma: width: 82
    height: 8, // From Figma: height: 8
    zIndex: 2,
  },
  paginationDot: {
    width: 8, // From Figma: width: 8
    height: 8, // From Figma: height: 8
    borderRadius: 4,
    marginRight: 8, // Spacing between dots
  },
  paginationDotInactive: {
    backgroundColor: "#1A6EFC", // From Figma: fill_B2PFT1
  },
  paginationDotActive: {
    backgroundColor: "#FAC840", // From Figma: fill_MBKWV7 (yellow)
    width: 18, // From Figma: active dot is wider
  },
})
