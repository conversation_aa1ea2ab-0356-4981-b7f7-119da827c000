import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface RecommendedCourseThumbnailProps {
  /**
   * Show AI recommended badge
   */
  isAIRecommended?: boolean
  /**
   * Match percentage
   */
  matchPercentage?: number
}

export const RecommendedCourseThumbnail: React.FC<RecommendedCourseThumbnailProps> = ({
  isAIRecommended = false,
  matchPercentage,
}) => {
  return (
    <View style={$container}>
      <View style={$thumbnailPlaceholder}>
        <Icon
          icon="play-circle"
          size={32}
          color={colors.palette.primary500}
        />
      </View>
      
      {isAIRecommended && (
        <View style={$aiBadge}>
          <Icon
            icon="zap"
            size={12}
            color={colors.palette.neutral100}
          />
          <Text
            style={$aiBadgeText}
            text="AI"
          />
        </View>
      )}
      
      {matchPercentage && (
        <View style={$matchBadge}>
          <Text
            style={$matchText}
            text={`${matchPercentage}% match`}
          />
        </View>
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  position: "relative",
  marginBottom: spacing.sm, // 12px below thumbnail
}

const $thumbnailPlaceholder: ViewStyle = {
  width: "100%",
  height: 120,
  backgroundColor: colors.palette.primary100, // Light blue background
  borderRadius: 8,
  alignItems: "center",
  justifyContent: "center",
}

const $aiBadge: ViewStyle = {
  position: "absolute",
  top: 8,
  left: 8,
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.accent500, // Orange AI badge
  borderRadius: 12,
  paddingHorizontal: 6,
  paddingVertical: 2,
  gap: 2,
}

const $aiBadgeText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 10,
  lineHeight: 12,
  color: colors.palette.neutral100, // White text
}

const $matchBadge: ViewStyle = {
  position: "absolute",
  top: 8,
  right: 8,
  backgroundColor: colors.palette.success500,
  borderRadius: 8,
  paddingHorizontal: 6,
  paddingVertical: 2,
}

const $matchText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 10,
  lineHeight: 12,
  color: colors.palette.neutral100, // White text
}
