import React from "react"
import { TouchableOpacity, View, ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { RecommendedCourseThumbnail } from "./RecommendedCourseThumbnail"
import { RecommendedCourseInfo } from "./RecommendedCourseInfo"

interface RecommendedCourse {
  id: string
  title: string
  instructor: string
  thumbnailUrl?: string
  rating: number
  reviewCount: number
  price: number
  originalPrice?: number
  duration: string
  level: "beginner" | "intermediate" | "advanced"
  category: string
  isAIRecommended?: boolean
  matchPercentage?: number
}

interface RecommendedCourseCardProps {
  /**
   * Course data
   */
  course: RecommendedCourse
  /**
   * Callback when course is pressed
   */
  onPress?: (course: RecommendedCourse) => void
}

export const RecommendedCourseCard: React.FC<RecommendedCourseCardProps> = ({
  course,
  onPress,
}) => {
  const formatPrice = (price: number): string => {
    if (price === 0) return "Free"
    return `$${price.toFixed(0)}`
  }

  return (
    <TouchableOpacity
      style={$container}
      onPress={() => onPress?.(course)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`${course.title} by ${course.instructor}, ${course.rating} stars, ${formatPrice(course.price)}`}
      accessibilityRole="button"
    >
      <RecommendedCourseThumbnail
        isAIRecommended={course.isAIRecommended}
        matchPercentage={course.matchPercentage}
      />

      <View style={$courseInfo}>
        <RecommendedCourseInfo
          title={course.title}
          instructor={course.instructor}
          rating={course.rating}
          reviewCount={course.reviewCount}
          level={course.level}
          price={course.price}
          originalPrice={course.originalPrice}
          duration={course.duration}
        />
      </View>
    </TouchableOpacity>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  width: 220,
  backgroundColor: colors.palette.neutral100,
  borderRadius: 8,
  padding: spacing.sm, // 12px internal padding
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}

const $courseInfo: ViewStyle = {
  gap: spacing.xs / 2, // 4px gap between elements
}
