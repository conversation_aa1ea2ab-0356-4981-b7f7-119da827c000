import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface RecommendedCourseInfoProps {
  /**
   * Course title
   */
  title: string
  /**
   * Instructor name
   */
  instructor: string
  /**
   * Course rating
   */
  rating: number
  /**
   * Review count
   */
  reviewCount: number
  /**
   * Course level
   */
  level: "beginner" | "intermediate" | "advanced"
  /**
   * Current price
   */
  price: number
  /**
   * Original price (for discounts)
   */
  originalPrice?: number
  /**
   * Course duration
   */
  duration: string
}

export const RecommendedCourseInfo: React.FC<RecommendedCourseInfoProps> = ({
  title,
  instructor,
  rating,
  reviewCount,
  level,
  price,
  originalPrice,
  duration,
}) => {
  const getLevelColor = (level: string): string => {
    switch (level) {
      case "beginner":
        return colors.palette.success500
      case "intermediate":
        return colors.palette.warning500
      case "advanced":
        return colors.palette.angry500
      default:
        return colors.palette.primary500
    }
  }

  const formatPrice = (price: number): string => {
    if (price === 0) return "Free"
    return `$${price.toFixed(0)}`
  }

  return (
    <View style={$container}>
      <Text
        style={$courseTitle}
        text={title}
        numberOfLines={2}
      />
      
      <Text
        style={$instructorName}
        text={instructor}
        numberOfLines={1}
      />
      
      <View style={$ratingContainer}>
        <View style={$ratingStars}>
          <Icon
            icon="star"
            size={12}
            color={colors.palette.warning500}
          />
          <Text
            style={$ratingText}
            text={rating.toFixed(1)}
          />
          <Text
            style={$reviewCount}
            text={`(${reviewCount})`}
          />
        </View>
        
        <View style={[$levelBadge, { backgroundColor: getLevelColor(level) + "20" }]}>
          <Text
            style={[$levelText, { color: getLevelColor(level) }]}
            text={level.toUpperCase()}
          />
        </View>
      </View>
      
      <View style={$priceContainer}>
        <Text
          style={$currentPrice}
          text={formatPrice(price)}
        />
        {originalPrice && originalPrice > price && (
          <Text
            style={$originalPrice}
            text={formatPrice(originalPrice)}
          />
        )}
        <Text
          style={$duration}
          text={duration}
        />
      </View>
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  gap: spacing.xs / 2, // 4px gap between elements
}

const $courseTitle: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for card titles
  fontSize: 14,
  lineHeight: 18,
  color: colors.palette.primary700, // Deep navy
}

const $instructorName: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary600, // Dark blue
}

const $ratingContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
}

const $ratingStars: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs / 2, // 4px gap
}

const $ratingText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.primary700, // Deep navy
}

const $reviewCount: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  color: colors.palette.neutral500,
}

const $levelBadge: ViewStyle = {
  paddingHorizontal: 6,
  paddingVertical: 2,
  borderRadius: 4,
}

const $levelText: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 10,
  lineHeight: 12,
}

const $priceContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
}

const $currentPrice: TextStyle = {
  fontFamily: typography.primary.semiBold, // 600 weight for emphasis
  fontSize: 14,
  lineHeight: 18,
  color: colors.palette.primary700, // Deep navy
}

const $originalPrice: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 12,
  lineHeight: 16,
  color: colors.palette.neutral500,
  textDecorationLine: "line-through",
}

const $duration: TextStyle = {
  fontFamily: typography.primary.light, // 300 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  color: colors.palette.neutral500,
}
