import React from "react"
import { View } from "react-native"
import { WelcomeBannerProps } from "./types"
import { getBannerContainerStyle } from "./BannerStyles"
import { BannerContent } from "./BannerContent"

export const WelcomeBanner: React.FC<WelcomeBannerProps> = ({
  user,
  customGreeting,
  quote,
  showQuote = true,
  variant = "default",
  show = true,
}) => {
  if (!show) {
    return null
  }

  return (
    <View style={getBannerContainerStyle(variant)}>
      <BannerContent
        user={user}
        customGreeting={customGreeting}
        quote={quote}
        showQuote={showQuote}
        variant={variant}
      />
    </View>
  )
}
