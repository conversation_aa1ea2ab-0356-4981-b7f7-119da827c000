/**
 * Type definitions for WelcomeBanner components
 */

export type BannerVariant = "default" | "gradient" | "minimal"

export interface UserInfo {
  displayName: string
}

export interface QuoteInfo {
  text: string
  author?: string
}

export interface WelcomeBannerProps {
  /**
   * User information
   */
  user: UserInfo
  /**
   * Custom greeting message (optional)
   */
  customGreeting?: string
  /**
   * Quote configuration
   */
  quote?: QuoteInfo
  /**
   * Show motivation quote
   */
  showQuote?: boolean
  /**
   * Background style variant
   */
  variant?: BannerVariant
  /**
   * Show welcome banner
   */
  show?: boolean
}
