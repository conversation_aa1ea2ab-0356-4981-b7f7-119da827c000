import React from "react"
import { PersonalizedGreeting } from "../personalized-greeting"
import { DailyMotivationQuote } from "../daily-motivation-quote"
import { UserInfo, QuoteInfo, BannerVariant } from "./types"

interface BannerContentProps {
  /**
   * User information
   */
  user: UserInfo
  /**
   * Custom greeting message (optional)
   */
  customGreeting?: string
  /**
   * Quote configuration
   */
  quote?: QuoteInfo
  /**
   * Show motivation quote
   */
  showQuote?: boolean
  /**
   * Background style variant
   */
  variant?: BannerVariant
}

export const BannerContent: React.FC<BannerContentProps> = ({
  user,
  customGreeting,
  quote,
  showQuote = true,
  variant = "default",
}) => {
  return (
    <>
      <PersonalizedGreeting
        userName={user.displayName}
        customGreeting={customGreeting}
      />

      {showQuote && (
        <DailyMotivationQuote
          quote={quote?.text}
          author={quote?.author}
          showIcon={variant !== "minimal"}
        />
      )}
    </>
  )
}
