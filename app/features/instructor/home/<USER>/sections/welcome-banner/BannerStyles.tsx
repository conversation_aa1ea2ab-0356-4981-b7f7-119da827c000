import { ViewStyle } from "react-native"
import { colors, spacing } from "app/theme"
import { BannerVariant } from "./types"

/**
 * Get container styles based on variant
 */
export const getBannerContainerStyle = (variant: BannerVariant): ViewStyle[] => {
  switch (variant) {
    case "gradient":
      return [$container, $gradientContainer]
    case "minimal":
      return [$container, $minimalContainer]
    default:
      return [$container]
  }
}

// Base container styles
const $container: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background - MANDATORY
  paddingHorizontal: spacing.md, // 16px internal padding
  paddingVertical: spacing.lg, // 24px vertical padding
  // No marginHorizontal - parent section handles 12px padding
  marginBottom: spacing.md, // 16px between components
  borderRadius: 12,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3,
}

// Gradient variant styles
const $gradientContainer: ViewStyle = {
  backgroundColor: colors.palette.primary100, // Light blue background
  borderWidth: 1,
  borderColor: colors.palette.primary200,
}

// Minimal variant styles
const $minimalContainer: ViewStyle = {
  backgroundColor: "transparent",
  paddingVertical: spacing.md, // 16px vertical padding
  shadowOpacity: 0,
  elevation: 0,
}
