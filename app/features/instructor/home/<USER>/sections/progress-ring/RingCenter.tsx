import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "app/components"
import { colors, spacing } from "app/theme"

interface RingCenterProps {
  /**
   * Ring size
   */
  size: number
  /**
   * Progress value (0-100)
   */
  progress: number
  /**
   * Custom center content
   */
  customContent?: React.ReactNode
}

export const RingCenter: React.FC<RingCenterProps> = ({
  size,
  progress,
  customContent,
}) => {
  if (customContent) {
    return (
      <View style={[$center, { width: size * 0.6, height: size * 0.6 }]}>
        {customContent}
      </View>
    )
  }

  return (
    <View style={[$center, { width: size * 0.6, height: size * 0.6 }]}>
      <Text
        text={`${Math.round(progress)}%`}
        weight="bold"
        size="xl"
        style={$progressText}
      />
      <Text
        text="Complete"
        preset="description" // Light weight (300) - MANDATORY
        size="xs"
        style={$labelText}
      />
    </View>
  )
}

// Themed styles following design guidelines
const $center: ViewStyle = {
  position: "absolute",
  alignItems: "center",
  justifyContent: "center",
}

const $progressText: TextStyle = {
  color: colors.text,
  marginBottom: spacing.xs, // 8px spacing
}

const $labelText: TextStyle = {
  color: colors.textDim,
}
