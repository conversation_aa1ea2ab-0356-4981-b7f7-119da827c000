import React from "react"
import { View, ViewStyle } from "react-native"

interface RingBackgroundProps {
  /**
   * Ring size
   */
  size: number
  /**
   * Ring stroke width
   */
  strokeWidth: number
  /**
   * Background color
   */
  color: string
  /**
   * Ring radius
   */
  radius: number
}

export const RingBackground: React.FC<RingBackgroundProps> = ({
  size,
  strokeWidth,
  color,
  radius,
}) => {
  // For now, we'll use a simple View as background
  // In a real implementation, you might want to use SVG or react-native-svg
  return (
    <View
      style={[
        $ring,
        {
          width: size,
          height: size,
          borderWidth: strokeWidth,
          borderColor: color,
          borderRadius: size / 2,
        }
      ]}
    />
  )
}

// Themed styles following design guidelines
const $ring: ViewStyle = {
  position: "absolute",
}
