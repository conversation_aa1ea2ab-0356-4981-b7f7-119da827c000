/**
 * Progress Ring Utilities
 * 
 * Utility functions for progress ring calculations and animations
 */

/**
 * Calculate circumference from radius
 */
export const calculateCircumference = (radius: number): number => {
  return 2 * Math.PI * radius
}

/**
 * Calculate stroke dash array for progress
 */
export const calculateStrokeDashArray = (progress: number, circumference: number): string => {
  const progressLength = (progress / 100) * circumference
  const remainingLength = circumference - progressLength
  return `${progressLength} ${remainingLength}`
}

/**
 * Calculate progress angle in degrees
 */
export const calculateProgressAngle = (progress: number): number => {
  return (progress / 100) * 360
}

/**
 * Get progress color based on value
 */
export const getProgressRingColor = (progress: number): string => {
  if (progress >= 80) return "#10B981" // Green for high progress
  if (progress >= 50) return "#F59E0B" // Orange for medium progress
  return "#EF4444" // Red for low progress
}

/**
 * Calculate ring dimensions
 */
export const calculateRingDimensions = (size: number, strokeWidth: number) => {
  const radius = (size - strokeWidth) / 2
  const circumference = calculateCircumference(radius)
  const center = size / 2
  
  return {
    radius,
    circumference,
    center,
    strokeWidth,
    size
  }
}
