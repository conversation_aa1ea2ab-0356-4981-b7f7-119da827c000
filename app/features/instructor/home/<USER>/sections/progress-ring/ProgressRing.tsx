import React from "react"
import { View, ViewStyle } from "react-native"
import { colors } from "app/theme"
import { RingBackground } from "./RingBackground"
import { RingProgress } from "./RingProgress"
import { RingCenter } from "./RingCenter"

interface ProgressRingProps {
  /**
   * Progress value (0-100)
   */
  progress: number
  /**
   * Ring size
   */
  size?: number
  /**
   * Ring stroke width
   */
  strokeWidth?: number
  /**
   * Progress color
   */
  progressColor?: string
  /**
   * Background color
   */
  backgroundColor?: string
  /**
   * Show center content
   */
  showCenter?: boolean
  /**
   * Center content
   */
  centerContent?: React.ReactNode
  /**
   * Custom style
   */
  style?: ViewStyle
}

export const ProgressRing: React.FC<ProgressRingProps> = ({
  progress,
  size = 120,
  strokeWidth = 8,
  progressColor = colors.palette.primary500,
  backgroundColor = colors.palette.neutral300,
  showCenter = true,
  centerContent,
  style,
}) => {
  const normalizedProgress = Math.max(0, Math.min(100, progress))
  const radius = (size - strokeWidth) / 2
  const circumference = 2 * Math.PI * radius

  return (
    <View style={[$container, { width: size, height: size }, style]}>
      {/* Background ring */}
      <RingBackground
        size={size}
        strokeWidth={strokeWidth}
        color={backgroundColor}
        radius={radius}
      />
      
      {/* Progress ring */}
      <RingProgress
        size={size}
        strokeWidth={strokeWidth}
        color={progressColor}
        radius={radius}
        progress={normalizedProgress}
        circumference={circumference}
      />
      
      {/* Center content */}
      {showCenter && (
        <RingCenter
          size={size}
          progress={normalizedProgress}
          customContent={centerContent}
        />
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $container: ViewStyle = {
  position: "relative",
  alignItems: "center",
  justifyContent: "center",
}
