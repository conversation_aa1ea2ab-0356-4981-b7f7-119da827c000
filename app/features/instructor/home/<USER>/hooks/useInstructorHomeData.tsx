import { useState, useEffect } from "react"
import { useStores } from "@/models"

/**
 * Hook to manage core data for InstructorHomeScreen
 * Provides instructor-specific data including user info, teaching stats, and notifications
 */
export const useInstructorHomeData = () => {
  const { userStore } = useStores()
  const [isLoading, setIsLoading] = useState(false)

  // Get current instructor info (adapted for HomeHeader interface)
  const currentUser = userStore.currentUser
  const userData = {
    displayName: currentUser ? `${currentUser.firstName} ${currentUser.lastName}`.trim() : "Instructor",
    imageUrl: currentUser?.avatar || "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
    firstName: currentUser?.firstName || "Instructor",
    lastName: currentUser?.lastName || "",
    email: currentUser?.email || "<EMAIL>",
    role: "Instructor",
    department: "Computer Science",
    employeeId: "INS-001",
  }

  // Mock streak data (adapted for instructor - teaching streak)
  const streakData = {
    count: 15, // 15 days of consistent teaching
    showAnimation: false,
  }

  // Mock notification data (adapted for HomeHeader interface)
  const notificationData = {
    unreadCount: 7,
    showAnimation: false,
  }

  // Mock active courses data
  const activeCourses = [
    {
      id: "course-1",
      title: "React Native Fundamentals",
      code: "CS-301",
      students: 32,
      progress: 75,
      nextClass: "Today, 2:00 PM",
      status: "active",
      thumbnail: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=200&fit=crop",
    },
    {
      id: "course-2",
      title: "Advanced JavaScript",
      code: "CS-401",
      students: 28,
      progress: 60,
      nextClass: "Tomorrow, 10:00 AM",
      status: "active",
      thumbnail: "https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=300&h=200&fit=crop",
    },
    {
      id: "course-3",
      title: "Mobile App Design",
      code: "DES-201",
      students: 24,
      progress: 45,
      nextClass: "Wed, 3:00 PM",
      status: "active",
      thumbnail: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=300&h=200&fit=crop",
    },
  ]

  // Mock recent student activity
  const recentStudentActivity = [
    {
      id: "activity-1",
      studentName: "John Doe",
      studentAvatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      action: "Completed Assignment 3",
      course: "React Native Fundamentals",
      timestamp: "5 minutes ago",
      type: "assignment",
    },
    {
      id: "activity-2",
      studentName: "Sarah Wilson",
      studentAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
      action: "Asked a question",
      course: "Advanced JavaScript",
      timestamp: "1 hour ago",
      type: "question",
    },
    {
      id: "activity-3",
      studentName: "Mike Johnson",
      studentAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
      action: "Started new lesson",
      course: "Mobile App Design",
      timestamp: "2 hours ago",
      type: "progress",
    },
  ]

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true)
      try {
        // Simulate data loading
        await new Promise(resolve => setTimeout(resolve, 500))
        console.log("Instructor home data loaded")
      } catch (error) {
        console.error("Failed to load instructor home data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  // Mock data for student home structure (adapted for instructor)
  const lastCourseData = {
    title: "React Native Fundamentals",
    progress: 75,
    thumbnail: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=200&fit=crop",
    nextLesson: "State Management with Redux",
    timeSpent: "2h 30m",
    totalDuration: "8h 45m",
  }

  const dailyTargetData = {
    target: 120, // minutes
    completed: 85, // minutes
    percentage: 71,
    streak: 5,
  }

  const quickStatsData = [
    {
      label: "Active Courses",
      value: 5,
      icon: "components",
      progress: 75,
      onPress: () => console.log("Navigate to courses"),
    },
    {
      label: "Total Students",
      value: 156,
      icon: "community",
      progress: 85,
      onPress: () => console.log("Navigate to students"),
    },
    {
      label: "Pending Grading",
      value: 23,
      icon: "check",
      progress: 60,
      onPress: () => console.log("Navigate to grading"),
    },
    {
      label: "Teaching Hours",
      value: "45h",
      icon: "view",
      progress: 90,
      onPress: () => console.log("Navigate to analytics"),
    },
  ]

  const quickActionsData = [
    {
      id: "action-1",
      title: "Create Course",
      icon: "plus",
      color: "#4F46E5",
      onPress: () => console.log("Create course"),
    },
    {
      id: "action-2",
      title: "Grade Assignments",
      icon: "check",
      color: "#059669",
      onPress: () => console.log("Grade assignments"),
    },
    {
      id: "action-3",
      title: "Schedule Class",
      icon: "calendar",
      color: "#DC2626",
      onPress: () => console.log("Schedule class"),
    },
  ]

  return {
    userData,
    streakData,
    notificationData,
    lastCourseData,
    dailyTargetData,
    quickStatsData,
    quickActionsData,
    isLoading,
  }
}
