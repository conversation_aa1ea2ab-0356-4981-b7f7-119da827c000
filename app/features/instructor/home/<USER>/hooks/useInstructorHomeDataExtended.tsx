/**
 * Hook to manage extended data for InstructorHomeScreen
 * Provides additional data matching student home structure but with instructor content
 */
export const useInstructorHomeDataExtended = () => {
  // Mock recent courses data (instructor's courses)
  const recentCoursesData = [
    {
      id: "course-1",
      title: "React Native Fundamentals",
      progress: 75,
      thumbnail: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=200&fit=crop",
      students: 32,
      nextClass: "Today, 2:00 PM",
      status: "active",
    },
    {
      id: "course-2",
      title: "Advanced JavaScript",
      progress: 60,
      thumbnail: "https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=300&h=200&fit=crop",
      students: 28,
      nextClass: "Tomorrow, 10:00 AM",
      status: "active",
    },
    {
      id: "course-3",
      title: "Mobile App Design",
      progress: 45,
      thumbnail: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=300&h=200&fit=crop",
      students: 24,
      nextClass: "Wed, 3:00 PM",
      status: "active",
    },
  ]

  // Mock upcoming assignments data (instructor's assignments to grade)
  const upcomingAssignmentsData = [
    {
      id: "assignment-1",
      title: "React Components Assignment",
      course: "React Native Fundamentals",
      dueDate: "Today",
      submittedCount: 25,
      totalStudents: 32,
      priority: "high",
    },
    {
      id: "assignment-2",
      title: "JavaScript Quiz",
      course: "Advanced JavaScript",
      dueDate: "Tomorrow",
      submittedCount: 20,
      totalStudents: 28,
      priority: "medium",
    },
    {
      id: "assignment-3",
      title: "UI Design Project",
      course: "Mobile App Design",
      dueDate: "Friday",
      submittedCount: 15,
      totalStudents: 24,
      priority: "low",
    },
  ]

  // Mock recommended courses data (courses instructor might want to create or explore)
  const recommendedCoursesData = [
    {
      id: "rec-1",
      title: "Advanced React Patterns",
      instructor: "Teaching Resources",
      rating: 4.8,
      students: 1250,
      thumbnail: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=200&fit=crop",
      price: "Free",
      duration: "8 weeks",
    },
    {
      id: "rec-2",
      title: "Modern Teaching Methods",
      instructor: "Education Hub",
      rating: 4.9,
      students: 890,
      thumbnail: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=300&h=200&fit=crop",
      price: "Free",
      duration: "6 weeks",
    },
  ]

  // Mock popular categories data
  const popularCategoriesData = [
    {
      id: "cat-1",
      name: "Programming",
      courseCount: 45,
      icon: "components",
      color: "#4F46E5",
    },
    {
      id: "cat-2",
      name: "Design",
      courseCount: 32,
      icon: "settings",
      color: "#059669",
    },
    {
      id: "cat-3",
      name: "Teaching",
      courseCount: 28,
      icon: "community",
      color: "#DC2626",
    },
  ]

  // Mock new releases data (new teaching resources or course templates)
  const newReleasesData = [
    {
      id: "new-1",
      title: "Interactive Coding Exercises",
      instructor: "EdTech Solutions",
      rating: 4.9,
      students: 567,
      thumbnail: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=200&fit=crop",
      isNew: true,
      releaseDate: "2 days ago",
    },
    {
      id: "new-2",
      title: "Virtual Classroom Tools",
      instructor: "Teaching Innovation",
      rating: 4.7,
      students: 423,
      thumbnail: "https://images.unsplash.com/photo-1588196749597-9ff075ee6b5b?w=300&h=200&fit=crop",
      isNew: true,
      releaseDate: "1 week ago",
    },
  ]

  // Mock trending courses data
  const trendingCoursesData = [
    {
      id: "trend-1",
      title: "AI in Education",
      instructor: "Future Learning",
      rating: 4.8,
      students: 2340,
      thumbnail: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=300&h=200&fit=crop",
      trending: true,
      growth: "+45%",
    },
    {
      id: "trend-2",
      title: "Gamification Techniques",
      instructor: "Engagement Experts",
      rating: 4.6,
      students: 1890,
      thumbnail: "https://images.unsplash.com/photo-1556075798-4825dfaaf498?w=300&h=200&fit=crop",
      trending: true,
      growth: "+32%",
    },
  ]

  // Mock upcoming schedule data
  const upcomingScheduleData = [
    {
      id: "schedule-1",
      title: "React Native Fundamentals",
      time: "2:00 PM - 3:30 PM",
      date: "Today",
      room: "Room 301",
      students: 32,
      type: "lecture",
    },
    {
      id: "schedule-2",
      title: "Advanced JavaScript",
      time: "10:00 AM - 11:30 AM",
      date: "Tomorrow",
      room: "Room 205",
      students: 28,
      type: "workshop",
    },
    {
      id: "schedule-3",
      title: "Office Hours",
      time: "9:00 AM - 11:00 AM",
      date: "Today",
      room: "Office 412",
      type: "office_hours",
    },
  ]

  return {
    recentCoursesData,
    upcomingAssignmentsData,
    recommendedCoursesData,
    popularCategoriesData,
    newReleasesData,
    trendingCoursesData,
    upcomingScheduleData,
  }
}
