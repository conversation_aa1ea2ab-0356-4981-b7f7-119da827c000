/**
 * Student Home Components
 *
 * Export all components used in the student home feature.
 * Each component is designed to be small, reusable, and follow app guidelines.
 */

// New Header Components
export { UserAvatar } from './header/UserAvatar'
export { UserGreeting } from './header/UserGreeting'
export { NotificationBell } from './header/NotificationBell'
export { SearchIcon } from './header/SearchIcon'
export { StreakCounter } from './header/StreakCounter'
export { HomeHeader } from './header/HomeHeader'

// New Section Components (from modular folders)
export { PersonalizedGreeting } from './sections/personalized-greeting'
export * from './sections/daily-motivation-quote'
export { WelcomeBanner } from './sections/welcome-banner'
export * from './sections/last-watched-course'
export { ProgressIndicator } from './sections/progress-indicator'
export { ResumeButton } from './sections/resume-button'
export * from './sections/continue-learning'
export * from './sections/daily-target'
export { ProgressRing } from './sections/progress-ring'

// Updated Section Components with new design (from modular folders)
export { QuickStats as QuickStatsSection } from './sections/quick-stats'
export * from './sections/recent-courses'
export { QuickActions as QuickActionsSection } from './sections/quick-actions'
export * from './sections/upcoming-assignments'

// New complete home screen components
export * from './sections/recommended-courses'
export * from './sections/popular-categories'
export * from './sections/new-releases'
export * from './sections/trending-now'
export { UpcomingSchedule as UpcomingScheduleSection } from './sections/upcoming-schedule'

