import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, TextInput } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface SearchBarWithFilterProps {
  placeholder?: string
  onSearchPress?: () => void
  onFilterPress?: () => void
  onChangeText?: (text: string) => void
  value?: string
}

export function SearchBarWithFilter({ 
  placeholder = "Search for..",
  onSearchPress,
  onFilterPress,
  onChangeText,
  value
}: SearchBarWithFilterProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <View style={themed($searchContainer)}>
        {/* Search Icon */}
        <TouchableOpacity onPress={onSearchPress} style={themed($searchIcon)}>
          <Icon icon="search" size={20} color="#000000" />
        </TouchableOpacity>

        {/* Search Input */}
        <TouchableOpacity
          style={{ flex: 1, justifyContent: "center" }}
          onPress={onSearchPress}
        >
          <Text style={themed($searchInput)}>
            {value || placeholder}
          </Text>
        </TouchableOpacity>

        {/* Filter Button */}
        <TouchableOpacity 
          style={themed($filterButton)}
          onPress={onFilterPress}
        >
          <Icon icon="settings" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
  marginBottom: 20,
}

const $searchContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF",
  borderRadius: 15,
  paddingHorizontal: 13,
  paddingVertical: 12,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 3 },
  shadowOpacity: 0.1,
  shadowRadius: 12,
  elevation: 4,
}

const $searchIcon: ViewStyle = {
  marginRight: 10,
}

const $searchInput: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY
  fontSize: 16,
  lineHeight: 20,
  color: "#B4BDC4", // Placeholder color
}

const $filterButton: ViewStyle = {
  width: 38,
  height: 38,
  borderRadius: 10,
  backgroundColor: "#0961F5",
  justifyContent: "center",
  alignItems: "center",
  marginLeft: 10,
}
