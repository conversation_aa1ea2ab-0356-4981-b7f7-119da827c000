import React, { useState } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, ScrollView, Image } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface Course {
  id: string
  title: string
  category: string
  price: string
  rating: number
  students: string
  image?: string
}

interface CourseTab {
  id: string
  title: string
  isActive?: boolean
}

interface PopularCoursesProps {
  courses?: Course[]
  tabs?: CourseTab[]
  onCoursePress?: (course: Course) => void
  onSeeAllPress?: () => void
  onTabPress?: (tab: CourseTab) => void
}

const defaultTabs: CourseTab[] = [
  { id: "all", title: "All", isActive: false },
  { id: "graphic-design", title: "Graphic Design", isActive: true },
  { id: "3d-design", title: "3D Design", isActive: false },
  { id: "arts-humanities", title: "Arts & Humanities", isActive: false },
]

const defaultCourses: Course[] = [
  {
    id: "1",
    title: "Graphic Design Advanced",
    category: "Graphic Design",
    price: "850/-",
    rating: 4.2,
    students: "7830 Std",
  },
  {
    id: "2",
    title: "Advertisement Designing",
    category: "Graphic Design",
    price: "400/-",
    rating: 4.2,
    students: "12580 Std",
  },
]

export function PopularCourses({
  courses = defaultCourses,
  tabs = defaultTabs,
  onCoursePress,
  onSeeAllPress,
  onTabPress
}: PopularCoursesProps) {
  const { themed } = useAppTheme()
  const [selectedTab, setSelectedTab] = useState<string>("graphic-design")

  const handleTabPress = (tab: CourseTab) => {
    setSelectedTab(tab.id)
    onTabPress?.(tab)
  }

  return (
    <View style={themed($container)}>
      {/* Header */}
      <View style={themed($header)}>
        <Text style={themed($title)}>Popular Courses</Text>
        <TouchableOpacity onPress={onSeeAllPress} style={themed($seeAllButton)}>
          <Text style={themed($seeAllText)}>SEE ALL</Text>
          <Icon icon="caretRight" size={10} color="#0961F5" />
        </TouchableOpacity>
      </View>

      {/* Course Category Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={themed($tabScrollView)}
        contentContainerStyle={themed($tabScrollContent)}
      >
        {tabs.map((tab) => {
          const isSelected = selectedTab === tab.id

          return (
            <TouchableOpacity
              key={tab.id}
              style={themed([
                $tab,
                isSelected && $selectedTab
              ])}
              onPress={() => handleTabPress(tab)}
            >
              <Text style={themed([
                $tabText,
                isSelected && $selectedTabText
              ])}>
                {tab.title}
              </Text>
            </TouchableOpacity>
          )
        })}
      </ScrollView>

      {/* Course Cards */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={themed($courseScrollView)}
        contentContainerStyle={themed($courseScrollContent)}
      >
        {courses.map((course, index) => (
          <TouchableOpacity
            key={course.id}
            style={themed([$courseCard, index === 0 && $firstCard])}
            onPress={() => onCoursePress?.(course)}
          >
            {/* Course Image */}
            <View style={themed($courseImage)} />

            {/* Course Info */}
            <View style={themed($courseInfo)}>
              <Text style={themed($courseCategory)}>{course.category}</Text>
              <Text style={themed($courseTitle)}>{course.title}</Text>

              {/* Bookmark Icon */}
              <View style={themed($bookmarkIcon)}>
                <Icon icon="heart" size={14} color="#167F71" />
              </View>

              {/* Course Details */}
              <View style={themed($courseDetails)}>
                <Text style={themed($coursePrice)}>{course.price}</Text>
                <Text style={themed($separator)}>|</Text>
                <View style={themed($ratingContainer)}>
                  <Icon icon="heart" size={12} color="#FCCB40" />
                  <Text style={themed($ratingText)}>{course.rating}</Text>
                </View>
                <Text style={themed($separator)}>|</Text>
                <Text style={themed($studentsText)}>{course.students}</Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
  marginBottom: 30,
}

const $header: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: 15,
}

const $title: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for headings
  fontSize: 18,
  lineHeight: 26,
  color: "#202244",
}

const $seeAllButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $seeAllText: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for emphasis
  fontSize: 12,
  lineHeight: 15,
  color: "#0961F5",
  textTransform: "uppercase",
  marginRight: 5,
}

const $tabScrollView: ViewStyle = {
  marginBottom: 20,
}

const $tabScrollContent: ViewStyle = {
  paddingRight: 20,
}

const $tab: ViewStyle = {
  paddingHorizontal: 20,
  paddingVertical: 8,
  borderRadius: 15,
  backgroundColor: "#E8F1FF",
  marginRight: 12,
}

const $selectedTab: ViewStyle = {
  backgroundColor: "#167F71",
}

const $tabText: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY for descriptions, body text, button text
  fontSize: 13,
  lineHeight: 16,
  color: "#202244",
  textAlign: "center",
}

const $selectedTabText: TextStyle = {
  color: "#FFFFFF",
}

const $courseScrollView: ViewStyle = {
  // No specific styles needed
}

const $courseScrollContent: ViewStyle = {
  paddingRight: 20,
}

const $courseCard: ViewStyle = {
  width: 280,
  backgroundColor: "#FFFFFF",
  borderRadius: 20,
  marginRight: 20,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4,
}

const $firstCard: ViewStyle = {
  // First card specific styles if needed
}

const $courseImage: ViewStyle = {
  width: "100%",
  height: 134,
  backgroundColor: "#000000",
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
}

const $courseInfo: ViewStyle = {
  padding: 14,
  position: "relative",
}

const $courseCategory: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY for descriptions, body text, button text
  fontSize: 12,
  lineHeight: 15,
  color: "#FF6B00",
  marginBottom: 4,
}

const $courseTitle: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for titles
  fontSize: 16,
  lineHeight: 23,
  color: "#202244",
  marginBottom: 15,
}

const $bookmarkIcon: ViewStyle = {
  position: "absolute",
  top: 14,
  right: 14,
}

const $courseDetails: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $coursePrice: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for prices
  fontSize: 15,
  lineHeight: 19,
  color: "#0961F5",
}

const $separator: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY for descriptions, body text, button text
  fontSize: 14,
  lineHeight: 18,
  color: "#000000",
  marginHorizontal: 6,
}

const $ratingContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $ratingText: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for ratings
  fontSize: 11,
  lineHeight: 14,
  color: "#202244",
  marginLeft: 4,
}

const $studentsText: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for stats
  fontSize: 11,
  lineHeight: 14,
  color: "#202244",
}
