import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface OfferBannerProps {
  discount?: string
  title?: string
  description?: string
  onPress?: () => void
}

export function OfferBanner({ 
  discount = "25% Off*",
  title = "Today's Special",
  description = "Get a Discount for Every Course Order only Valid for Today.!",
  onPress
}: OfferBannerProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <TouchableOpacity 
        style={themed($banner)}
        onPress={onPress}
        activeOpacity={0.8}
      >
        {/* Background decorative elements */}
        <View style={themed($decorativeElements)}>
          {/* Geometric shapes for decoration */}
          <View style={themed($circle1)} />
          <View style={themed($circle2)} />
          <View style={themed($triangle)} />
        </View>

        {/* Content */}
        <View style={themed($content)}>
          <Text style={themed($discountText)}>{discount}</Text>
          <Text style={themed($titleText)}>{title}</Text>
          <Text style={themed($descriptionText)}>{description}</Text>
        </View>

        {/* Pagination dots */}
        <View style={themed($pagination)}>
          <View style={themed($dot)} />
          <View style={themed($dot)} />
          <View style={themed($activeDot)} />
          <View style={themed($dot)} />
          <View style={themed($dot)} />
        </View>
      </TouchableOpacity>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
  marginBottom: 30,
}

const $banner: ViewStyle = {
  backgroundColor: "#0961F5",
  borderRadius: 22,
  height: 180,
  position: "relative",
  overflow: "hidden",
}

const $decorativeElements: ViewStyle = {
  position: "absolute",
  top: 0,
  right: 0,
  width: "50%",
  height: "100%",
}

const $circle1: ViewStyle = {
  position: "absolute",
  top: 30,
  right: 40,
  width: 40,
  height: 10,
  borderRadius: 20,
  borderWidth: 2,
  borderColor: "#1A6EFC",
}

const $circle2: ViewStyle = {
  position: "absolute",
  top: 60,
  right: 20,
  width: 20,
  height: 20,
  borderRadius: 10,
  borderWidth: 2,
  borderColor: "#1A6EFC",
}

const $triangle: ViewStyle = {
  position: "absolute",
  top: 100,
  right: 60,
  width: 0,
  height: 0,
  borderLeftWidth: 8,
  borderRightWidth: 8,
  borderBottomWidth: 8,
  borderLeftColor: "transparent",
  borderRightColor: "transparent",
  borderBottomColor: "#1A6EFC",
}

const $content: ViewStyle = {
  padding: 24,
  flex: 1,
}

const $discountText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for emphasis
  fontSize: 15,
  lineHeight: 19,
  color: "#FFFFFF",
  textTransform: "uppercase",
  marginBottom: 4,
}

const $titleText: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for titles
  fontSize: 22,
  lineHeight: 28,
  color: "#FFFFFF",
  marginBottom: 8,
}

const $descriptionText: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY
  fontSize: 13,
  lineHeight: 16,
  color: "#FFFFFF",
  maxWidth: "70%",
}

const $pagination: ViewStyle = {
  position: "absolute",
  bottom: 16,
  left: 24,
  flexDirection: "row",
  alignItems: "center",
}

const $dot: ViewStyle = {
  width: 8,
  height: 8,
  borderRadius: 4,
  backgroundColor: "#1A6EFC",
  marginRight: 8,
}

const $activeDot: ViewStyle = {
  width: 18,
  height: 8,
  borderRadius: 5,
  backgroundColor: "#FAC840",
  marginRight: 8,
}
