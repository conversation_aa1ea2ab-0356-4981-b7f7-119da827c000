import React from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import {
  NewHomeHeader,
  SearchBarWithFilter,
  OfferBanner,
  CategoryTabs,
  PopularCourses,
  TopMentors
} from "./index"

interface NewHomeScreenContentProps {
  userName?: string
  onNotificationPress?: () => void
  onSearchPress?: () => void
  onFilterPress?: () => void
  onOfferPress?: () => void
  onCategoryPress?: (category: any) => void
  onCoursePress?: (course: any) => void
  onMentorPress?: (mentor: any) => void
  onSeeAllPress?: (section: string) => void
}

/**
 * NewHomeScreenContent - Updated home screen based on Figma design
 * 
 * This component implements the new home screen design with:
 * - NewHomeHeader: "Hi, ALEX" + notification
 * - SearchBarWithFilter: Search with filter button
 * - OfferBanner: "25% Off Today's Special"
 * - CategoryTabs: 3D Design, Arts & Humanities, etc.
 * - PopularCourses: Course cards with tabs
 * - TopMentors: Mentor profiles
 */
export function NewHomeScreenContent({
  userName = "ALEX",
  onNotificationPress,
  onSearchPress,
  onFilterPress,
  onOfferPress,
  onCategoryPress,
  onCoursePress,
  onMentorPress,
  onSeeAllPress
}: NewHomeScreenContentProps) {
  const { themed } = useAppTheme()

  console.log("🏠 NewHomeScreenContent rendering...")

  const handleSeeAll = (section: string) => {
    console.log(`🏠 See all pressed for: ${section}`)
    onSeeAllPress?.(section)
  }

  const handleNotificationPress = () => {
    console.log("🏠 Notification pressed")
    onNotificationPress?.()
  }

  const handleSearchPress = () => {
    console.log("🏠 Search pressed")
    onSearchPress?.()
  }

  const handleFilterPress = () => {
    console.log("🏠 Filter pressed")
    onFilterPress?.()
  }

  const handleOfferPress = () => {
    console.log("🏠 Offer banner pressed")
    onOfferPress?.()
  }

  const handleCategoryPress = (category: any) => {
    console.log("🏠 Category pressed:", category)
    onCategoryPress?.(category)
  }

  const handleCoursePress = (course: any) => {
    console.log("🏠 Course pressed:", course)
    onCoursePress?.(course)
  }

  const handleMentorPress = (mentor: any) => {
    console.log("🏠 Mentor pressed:", mentor)
    onMentorPress?.(mentor)
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
      contentContainerStyle={{
        flexGrow: 1,
        paddingBottom: 100, // Extra space for bottom navigation
      }}
    >
      <View style={themed($content)}>
        {/* Header with greeting and notification */}
        <NewHomeHeader
          userName={userName}
          onNotificationPress={handleNotificationPress}
          notificationCount={3}
        />

        {/* Search bar with filter */}
        <SearchBarWithFilter
          onSearchPress={handleSearchPress}
          onFilterPress={handleFilterPress}
        />

        {/* Offer banner */}
        <OfferBanner
          onPress={handleOfferPress}
        />

        {/* Category tabs */}
        <CategoryTabs
          onCategoryPress={handleCategoryPress}
          onSeeAllPress={() => handleSeeAll("categories")}
        />

        {/* Popular courses */}
        <PopularCourses
          onCoursePress={handleCoursePress}
          onSeeAllPress={() => handleSeeAll("courses")}
        />

        {/* Top mentors */}
        <TopMentors
          onMentorPress={handleMentorPress}
          onSeeAllPress={() => handleSeeAll("mentors")}
        />
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  paddingBottom: 100, // Increased to avoid bottom navigation overlap
  marginBottom: 20, // Additional margin for better spacing
}
