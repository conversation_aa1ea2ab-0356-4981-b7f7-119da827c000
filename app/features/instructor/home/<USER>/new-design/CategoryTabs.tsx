import React, { useState } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, ScrollView } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface Category {
  id: string
  name: string
}

interface CategoryTabsProps {
  categories?: Category[]
  onCategoryPress?: (category: Category) => void
  onSeeAllPress?: () => void
}

const defaultCategories: Category[] = [
  { id: "3d-design", name: "3D Design" },
  { id: "arts-humanities", name: "Arts & Humanities" },
  { id: "graphic-design", name: "Graphic Design" },
]

export function CategoryTabs({ 
  categories = defaultCategories,
  onCategoryPress,
  onSeeAllPress
}: CategoryTabsProps) {
  const { themed } = useAppTheme()
  const [selectedCategory, setSelectedCategory] = useState<string>(categories[0]?.id || "")

  const handleCategoryPress = (category: Category) => {
    setSelectedCategory(category.id)
    onCategoryPress?.(category)
  }

  return (
    <View style={themed($container)}>
      {/* Header */}
      <View style={themed($header)}>
        <Text style={themed($title)}>Categories</Text>
        <TouchableOpacity onPress={onSeeAllPress} style={themed($seeAllButton)}>
          <Text style={themed($seeAllText)}>SEE ALL</Text>
          <Icon icon="caretRight" size={10} color="#0961F5" />
        </TouchableOpacity>
      </View>

      {/* Category Tabs */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={themed($scrollView)}
        contentContainerStyle={themed($scrollContent)}
      >
        {categories.map((category, index) => {
          const isSelected = selectedCategory === category.id
          const isFirst = index === 0
          
          return (
            <TouchableOpacity
              key={category.id}
              style={themed([
                $categoryTab,
                isSelected && $selectedTab,
                isFirst && $firstTab
              ])}
              onPress={() => handleCategoryPress(category)}
            >
              <Text style={themed([
                $categoryText,
                isSelected && $selectedText
              ])}>
                {category.name}
              </Text>
            </TouchableOpacity>
          )
        })}
      </ScrollView>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
  marginBottom: 30,
}

const $header: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: 15,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for headings
  fontSize: 18,
  lineHeight: 26,
  color: "#202244",
}

const $seeAllButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $seeAllText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for emphasis
  fontSize: 12,
  lineHeight: 15,
  color: "#0961F5",
  textTransform: "uppercase",
  marginRight: 5,
}

const $scrollView: ViewStyle = {
  // No specific styles needed
}

const $scrollContent: ViewStyle = {
  paddingRight: 20,
}

const $categoryTab: ViewStyle = {
  paddingHorizontal: 0,
  paddingVertical: 8,
  marginRight: 25,
}

const $selectedTab: ViewStyle = {
  // Selected state handled by text color
}

const $firstTab: ViewStyle = {
  // First tab specific styles if needed
}

const $categoryText: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY
  fontSize: 15,
  lineHeight: 19,
  color: "#A0A4AB",
}

const $selectedText: TextStyle = {
  color: "#202244",
}
