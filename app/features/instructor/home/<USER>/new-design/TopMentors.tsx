import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, ScrollView } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface Mentor {
  id: string
  name: string
  image?: string
}

interface TopMentorsProps {
  mentors?: <PERSON><PERSON>[]
  onMentorPress?: (mentor: <PERSON><PERSON>) => void
  onSeeAllPress?: () => void
}

const defaultMentors: <PERSON><PERSON>[] = [
  { id: "1", name: "<PERSON><PERSON>" },
  { id: "2", name: "<PERSON><PERSON>" },
  { id: "3", name: "Rahul.J" },
  { id: "4", name: "<PERSON><PERSON>" },
]

export function TopMentors({ 
  mentors = defaultMentors,
  onMentorPress,
  onSeeAllPress
}: TopMentorsProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Header */}
      <View style={themed($header)}>
        <Text style={themed($title)}>Top Mentor</Text>
        <TouchableOpacity onPress={onSeeAllPress} style={themed($seeAllButton)}>
          <Text style={themed($seeAllText)}>SEE ALL</Text>
          <Icon icon="caretRight" size={10} color="#0961F5" />
        </TouchableOpacity>
      </View>

      {/* Mentor Cards */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={themed($mentorScrollView)}
        contentContainerStyle={themed($mentorScrollContent)}
      >
        {mentors.map((mentor, index) => (
          <TouchableOpacity
            key={mentor.id}
            style={themed([$mentorCard, index === 0 && $firstCard])}
            onPress={() => onMentorPress?.(mentor)}
          >
            {/* Mentor Image */}
            <View style={themed($mentorImage)} />
            
            {/* Mentor Name */}
            <Text style={themed($mentorName)}>{mentor.name}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
  marginBottom: 40,
}

const $header: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: 15,
}

const $title: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for headings
  fontSize: 18,
  lineHeight: 26,
  color: "#202244",
}

const $seeAllButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $seeAllText: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for emphasis
  fontSize: 12,
  lineHeight: 15,
  color: "#0961F5",
  textTransform: "uppercase",
  marginRight: 5,
}

const $mentorScrollView: ViewStyle = {
  // No specific styles needed
}

const $mentorScrollContent: ViewStyle = {
  paddingRight: 20,
}

const $mentorCard: ViewStyle = {
  alignItems: "center",
  marginRight: 18,
  width: 80,
}

const $firstCard: ViewStyle = {
  // First card specific styles if needed
}

const $mentorImage: ViewStyle = {
  width: 80,
  height: 70,
  backgroundColor: "#000000",
  borderRadius: 20,
  marginBottom: 7,
}

const $mentorName: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for names
  fontSize: 13,
  lineHeight: 19,
  color: "#202244",
  textAlign: "center",
}
