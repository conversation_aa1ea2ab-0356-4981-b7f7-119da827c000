import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface NewHomeHeaderProps {
  userName?: string
  onNotificationPress?: () => void
  notificationCount?: number
}

export function NewHomeHeader({ 
  userName = "ALEX", 
  onNotificationPress,
  notificationCount = 0 
}: NewHomeHeaderProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Left side - Greeting */}
      <View style={themed($leftSection)}>
        <Text style={themed($greeting)}>Hi, {userName}</Text>
        <Text style={themed($subtitle)}>
          What Would you like to learn Today? Search Below.
        </Text>
      </View>

      {/* Right side - Notification */}
      <TouchableOpacity 
        style={themed($notificationButton)}
        onPress={onNotificationPress}
      >
        <View style={themed($notificationIcon)}>
          <Icon icon="bell" size={20} color="#167F71" />
          {notificationCount > 0 && (
            <View style={themed($notificationBadge)}>
              <Text style={themed($badgeText)}>{notificationCount}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "flex-start",
  justifyContent: "space-between",
  paddingHorizontal: 16,
  paddingTop: 25,
  paddingBottom: 20,
}

const $leftSection: ViewStyle = {
  flex: 1,
  marginRight: 20,
}

const $greeting: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for headings
  fontSize: 24,
  lineHeight: 35,
  color: "#202244",
  marginBottom: 4,
}

const $subtitle: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY
  fontSize: 13,
  lineHeight: 16,
  color: "rgba(84, 84, 84, 0.8)",
}

const $notificationButton: ViewStyle = {
  padding: 4,
}

const $notificationIcon: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 20,
  borderWidth: 2,
  borderColor: "#167F71",
  justifyContent: "center",
  alignItems: "center",
  position: "relative",
}

const $notificationBadge: ViewStyle = {
  position: "absolute",
  top: -2,
  right: -2,
  width: 16,
  height: 16,
  borderRadius: 8,
  backgroundColor: "#FF4444",
  justifyContent: "center",
  alignItems: "center",
}

const $badgeText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY
  fontSize: 10,
  color: "#FFFFFF",
}
