import React from "react"
import { View, TouchableOpacity, StyleSheet } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing } from "app/theme"

interface StreakCounterProps {
  /**
   * Number of consecutive learning days
   */
  streakCount: number
  /**
   * Callback when streak counter is pressed
   */
  onPress?: () => void
  /**
   * Show fire animation for active streak
   */
  showAnimation?: boolean
  /**
   * Size variant
   */
  size?: "small" | "medium" | "large"
}

const SIZE_CONFIG = {
  small: {
    iconSize: 16,
    fontSize: 12,
    padding: spacing.xs,
  },
  medium: {
    iconSize: 20,
    fontSize: 14,
    padding: spacing.sm,
  },
  large: {
    iconSize: 24,
    fontSize: 16,
    padding: spacing.md,
  },
}

export const StreakCounter: React.FC<StreakCounterProps> = ({
  streakCount,
  onPress,
  showAnimation = false,
  size = "medium",
}) => {
  const config = SIZE_CONFIG[size]
  const hasStreak = streakCount > 0

  const getStreakColor = (count: number): string => {
    if (count >= 30) return colors.palette.secondary500 // Gold for 30+ days
    if (count >= 7) return colors.palette.primary500   // Blue for 7+ days
    if (count >= 3) return colors.palette.success500   // Green for 3+ days
    return colors.palette.neutral400                   // Gray for < 3 days
  }

  const getMotivationalText = (count: number): string => {
    if (count === 0) return "Start your streak!"
    if (count === 1) return "Great start!"
    if (count < 7) return "Keep it up!"
    if (count < 30) return "Amazing streak!"
    return "Incredible dedication!"
  }

  return (
    <TouchableOpacity
      style={[styles.container, { padding: config.padding }]}
      onPress={onPress}
      activeOpacity={0.8}
      accessible={true}
      accessibilityLabel={`Learning streak: ${streakCount} days. ${getMotivationalText(streakCount)}`}
      accessibilityRole="button"
    >
      <View style={styles.content}>
        <Icon
          icon="heart"
          size={config.iconSize}
          color={getStreakColor(streakCount)}
          style={[
            styles.fireIcon,
            showAnimation && hasStreak && styles.animatedFire,
          ]}
        />

        <View style={styles.textContainer}>
          <View style={styles.streakRow}>
            <Text
              weight="light" // Use weight prop instead of inline fontWeight
              style={[styles.streakNumber, { fontSize: config.fontSize + 2 }]}
              text={streakCount.toString()}
            />
            <Text
              weight="light" // Use weight prop instead of inline fontWeight
              style={[styles.streakLabel, { fontSize: config.fontSize - 2 }]}
              text="day streak"
            />
          </View>

          {hasStreak && (
            <Text
              weight="light" // Use weight prop instead of inline fontWeight
              style={[styles.motivationText, { fontSize: config.fontSize - 2 }]}
              text={getMotivationalText(streakCount)}
            />
          )}
        </View>
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "transparent", // Clean design per user request
    borderRadius: 12, // 12px border radius per design guidelines
    alignItems: "center",
    justifyContent: "center",
    minWidth: 80, // Minimum width for content
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  fireIcon: {
    marginRight: spacing.xs,
  },
  animatedFire: {
    // Animation could be added here
    transform: [{ scale: 1.1 }],
  },
  textContainer: {
    alignItems: "flex-start", // Changed from center to flex-start for better alignment
    marginLeft: spacing.xs, // Add some space between icon and text
  },
  streakRow: {
    flexDirection: "row", // Make number and "day streak" horizontal
    alignItems: "baseline", // Align text baselines
    gap: spacing.xxs, // Small gap between number and label
  },
  streakNumber: {
    color: "#132339", // Deep navy per design guidelines
    fontSize: 16, // Standard size for numbers
    lineHeight: 20, // Optimal line height
    // fontWeight handled by Text component weight prop (semiBold for numbers)
  },
  streakLabel: {
    color: "#1A3270", // Dark blue per design guidelines
    fontSize: 14, // Standard description size
    lineHeight: 18, // Optimal line height
    // fontWeight handled by Text component weight prop (light per guidelines)
  },
  motivationText: {
    marginTop: spacing.xxs, // 4px top margin
    color: "#978F8A", // Balanced gray per design guidelines
    fontSize: 12, // Caption size per typography system
    lineHeight: 16, // Optimal line height for small text
    textAlign: "left", // Left alignment for horizontal layout
    // fontWeight handled by Text component weight prop (light per guidelines)
  },
})
