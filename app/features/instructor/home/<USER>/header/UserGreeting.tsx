import React from "react"
import { View, StyleSheet, Text } from "react-native"
import { spacing } from "app/theme"

interface UserGreetingProps {
  /**
   * User's display name
   */
  displayName: string
  /**
   * Show greeting text (Good morning, Good afternoon, etc.)
   */
  showGreeting?: boolean
  /**
   * Custom style override
   */
  style?: any
}

export const UserGreeting: React.FC<UserGreetingProps> = ({
  displayName,
  showGreeting = true,
  style,
}) => {
  // Get current time for greeting
  const getGreeting = () => {
    const hour = new Date().getHours()
    if (hour < 12) return "Chào buổi sáng"
    if (hour < 18) return "Chào buổi chiều"
    return "Chào buổi tối"
  }

  // Get first name from display name
  const getFirstName = (fullName: string) => {
    const names = fullName.trim().split(" ")
    return names[names.length - 1] // Vietnamese names: last word is usually first name
  }

  const greeting = getGreeting()
  const firstName = getFirstName(displayName)

  return (
    <View style={[styles.container, style]}>
      {showGreeting && (
        <Text
          style={styles.greetingText}
          numberOfLines={1}
        >
          {greeting}
        </Text>
      )}
      <Text
        style={styles.nameText}
        numberOfLines={1}
      >
        {firstName}!
      </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    justifyContent: "flex-start", // Top alignment for better text visibility
    marginLeft: spacing.xs, // 8px margin from avatar
    paddingTop: spacing.xxs, // 4px top padding for alignment
  },
  greetingText: {
    color: "#1A3270", // Dark blue per design guidelines (textDim)
    fontSize: 14, // Standard description size per typography system
    lineHeight: 20, // Optimal line height for readability
    marginBottom: 2, // Small margin between greeting and name
    paddingTop: 2, // Top padding to prevent text clipping
    fontWeight: "300", // MANDATORY: Light weight (300) per typography guidelines
  },
  nameText: {
    color: "#132339", // Deep navy per design guidelines (primary text)
    fontSize: 18, // Larger size for name prominence
    lineHeight: 24, // Proportional line height
    fontWeight: "300", // MANDATORY: Light weight (300) per typography guidelines
  },
})
