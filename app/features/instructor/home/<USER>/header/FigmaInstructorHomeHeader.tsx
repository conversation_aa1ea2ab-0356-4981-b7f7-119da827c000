import React from "react"
import { View, Text, TouchableOpacity, StyleSheet } from "react-native"
import { Icon } from "app/components"
import { colors, spacing, typography } from "app/theme"

interface FigmaInstructorHomeHeaderProps {
  /**
   * Instructor name
   */
  instructorName?: string
  /**
   * Notification count
   */
  notificationCount?: number
  /**
   * Navigation callbacks
   */
  onNotificationPress?: () => void
}

/**
 * FigmaInstructorHomeHeader - Header component based on exact Figma design
 * 
 * Replicates the NOV BAR section from Figma:
 * - "Hi, AL<PERSON>" greeting (adapted for instructor)
 * - "What Would you like to learn Today? Search Below." subtitle (adapted for instructor)
 * - Notification bell icon with circle background
 */
export const FigmaInstructorHomeHeader: React.FC<FigmaInstructorHomeHeaderProps> = ({
  instructorName = "ALEX",
  notificationCount = 0,
  onNotificationPress,
}) => {
  return (
    <View style={styles.container}>
      {/* Left side - Greeting text */}
      <View style={styles.leftSection}>
        {/* Main greeting - "Hi, <PERSON><PERSON>" */}
        <Text style={styles.greetingText}>
          Hi, {instructorName.toUpperCase()}
        </Text>
        
        {/* Subtitle - adapted for instructor */}
        <Text style={styles.subtitleText}>
          What Would you like to teach Today? Manage Below.
        </Text>
      </View>

      {/* Right side - Notification icon */}
      <View style={styles.rightSection}>
        <TouchableOpacity 
          style={styles.notificationButton}
          onPress={onNotificationPress}
          activeOpacity={0.7}
        >
          {/* Notification circle background */}
          <View style={styles.notificationCircle}>
            {/* Notification bell icon */}
            <Icon 
              icon="bell" 
              size={20} 
              color="#167F71" 
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    width: 359, // From Figma: width: 359
    height: 72, // From Figma: height: 72
  },
  leftSection: {
    flex: 1,
    justifyContent: "flex-start",
  },
  greetingText: {
    fontFamily: typography.primary.semiBold, // 600 weight - for headings per design guidelines
    fontSize: 24,
    lineHeight: 35,
    color: "#132339", // Deep navy from design guidelines
    marginBottom: 5,
  },
  subtitleText: {
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions per design guidelines
    fontSize: 13,
    lineHeight: 16,
    color: "rgba(84, 84, 84, 0.8)",
    width: 244,
  },
  rightSection: {
    alignItems: "center",
    justifyContent: "center",
    paddingTop: 18, // From Figma: y: 18 (relative positioning)
  },
  notificationButton: {
    // No additional styling needed - just for touch handling
  },
  notificationCircle: {
    width: 40, // From Figma: width: 40
    height: 40, // From Figma: height: 40
    borderRadius: 20, // Perfect circle
    borderWidth: 2, // From Figma: strokeWeight: 2px
    borderColor: "#167F71", // From Figma: stroke_3FQR6K
    backgroundColor: "transparent", // Transparent background, only border
    alignItems: "center",
    justifyContent: "center",
  },
})
