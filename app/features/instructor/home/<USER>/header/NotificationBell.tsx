import React from "react"
import { View, TouchableOpacity, StyleSheet } from "react-native"
import { Text, Icon } from "app/components"
import { colors, spacing } from "app/theme"

interface NotificationBellProps {
  /**
   * Number of unread notifications
   */
  unreadCount?: number
  /**
   * Callback when bell is pressed
   */
  onPress?: () => void
  /**
   * Show animation for new notifications
   */
  showAnimation?: boolean
  /**
   * Size of the bell icon
   */
  size?: "small" | "medium" | "large"
}

const ICON_SIZES = {
  small: 20,
  medium: 24,
  large: 28,
}

export const NotificationBell: React.FC<NotificationBellProps> = ({
  unreadCount = 0,
  onPress,
  showAnimation = false,
  size = "medium",
}) => {
  const iconSize = ICON_SIZES[size]
  const hasNotifications = unreadCount > 0

  const formatCount = (count: number): string => {
    if (count > 99) return "99+"
    return count.toString()
  }

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
      accessible={true}
      accessibilityLabel={
        hasNotifications
          ? `${unreadCount} unread notifications`
          : "No new notifications"
      }
      accessibilityRole="button"
    >
      <View style={styles.bellContainer}>
        <Icon
          icon="bell"
          size={iconSize}
          color={hasNotifications ? colors.palette.primary500 : colors.palette.neutral400}
          style={[
            styles.bellIcon,
            showAnimation && hasNotifications && styles.animatedBell,
          ]}
        />

        {hasNotifications && (
          <View style={styles.badge}>
            <Text
              weight="light" // Use weight prop instead of inline fontWeight
              style={styles.badgeText}
              text={formatCount(unreadCount)}
            />
          </View>
        )}
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: spacing.xs, // 8px padding for touch area
    alignItems: "center",
    justifyContent: "center",
    minWidth: 44, // Minimum touch target per accessibility guidelines
    minHeight: 44, // Minimum touch target per accessibility guidelines
  },
  bellContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
  },
  bellIcon: {
    // Base icon styles
  },
  animatedBell: {
    // Animation styles could be added here
    // For now, just a subtle transform
    transform: [{ scale: 1.1 }],
  },
  badge: {
    position: "absolute",
    top: -6, // Position above bell icon
    right: -6, // Position to the right of bell icon
    minWidth: 18, // Minimum width for single digits
    height: 18, // Fixed height for circular badge
    borderRadius: 9, // Half of height for perfect circle
    backgroundColor: "#C03403", // Error red per design guidelines
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: spacing.xxs, // 4px horizontal padding
  },
  badgeText: {
    color: "#FFFFFF", // White text on red background
    fontSize: 10, // Small font size for badge
    lineHeight: 12, // Tight line height for badge
    // fontWeight handled by Text component weight prop (light per guidelines)
  },
})
