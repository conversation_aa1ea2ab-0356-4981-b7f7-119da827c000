import React from "react"
import { View, TouchableOpacity, Image, StyleSheet } from "react-native"
import { Text } from "app/components"
// Design guidelines colors used directly in styles

interface UserAvatarProps {
  /**
   * User's profile image URL
   */
  imageUrl?: string
  /**
   * User's display name for fallback
   */
  displayName: string
  /**
   * Size of the avatar
   */
  size?: "small" | "medium" | "large"
  /**
   * Callback when avatar is pressed
   */
  onPress?: () => void
  /**
   * Show online status indicator
   */
  showOnlineStatus?: boolean
}

const AVATAR_SIZES = {
  small: 32,
  medium: 40,
  large: 56,
}

export const UserAvatar: React.FC<UserAvatarProps> = ({
  imageUrl,
  displayName,
  size = "medium",
  onPress,
  showOnlineStatus = false,
}) => {
  const avatarSize = AVATAR_SIZES[size]

  // Get initials from display name
  const getInitials = (name: string): string => {
    return name
      .split(" ")
      .map(word => word.charAt(0))
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }

  const renderAvatar = () => {
    if (imageUrl) {
      return (
        <Image
          source={{ uri: imageUrl }}
          style={[styles.avatarImage, { width: avatarSize, height: avatarSize }]}
        />
      )
    }

    return (
      <View style={[styles.avatarFallback, { width: avatarSize, height: avatarSize }]}>
        <Text
          weight="light" // Use weight prop instead of inline fontWeight
          style={[styles.initialsText, { fontSize: avatarSize * 0.4 }]}
          text={getInitials(displayName)}
        />
      </View>
    )
  }

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
      accessible={true}
      accessibilityLabel={`Profile picture of ${displayName}`}
      accessibilityRole="button"
    >
      <View style={styles.avatarContainer}>
        {renderAvatar()}
        {showOnlineStatus && <View style={styles.onlineIndicator} />}
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  avatarContainer: {
    position: "relative",
  },
  avatarImage: {
    borderRadius: 100, // Perfect circle
    backgroundColor: "#F4F2F1", // Light gray background per design guidelines
  },
  avatarFallback: {
    borderRadius: 100, // Perfect circle
    backgroundColor: "#23408B", // Royal blue per design guidelines
    alignItems: "center",
    justifyContent: "center",
  },
  initialsText: {
    color: "#FFFFFF", // White text on blue background
    // fontWeight handled by Text component weight prop (light per guidelines)
  },
  onlineIndicator: {
    position: "absolute",
    bottom: 0, // Bottom edge of avatar
    right: 0, // Right edge of avatar
    width: 12, // Small indicator size
    height: 12, // Small indicator size
    borderRadius: 6, // Perfect circle (half of width/height)
    backgroundColor: "#10B981", // Success green per design guidelines
    borderWidth: 2, // White border for contrast
    borderColor: "#FFFFFF", // White border per design guidelines
  },
})
