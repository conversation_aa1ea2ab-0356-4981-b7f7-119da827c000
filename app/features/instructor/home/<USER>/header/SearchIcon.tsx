import React from "react"
import { TouchableOpacity, StyleSheet, Text } from "react-native"
import { colors, spacing } from "app/theme"

interface SearchIconProps {
  /**
   * Callback when search icon is pressed
   */
  onPress?: () => void
  /**
   * Size of the search icon
   */
  size?: "small" | "medium" | "large"
  /**
   * Icon color
   */
  color?: string
  /**
   * Show voice search indicator
   */
  showVoiceSearch?: boolean
}

const ICON_SIZES = {
  small: 20,
  medium: 24,
  large: 28,
}

export const SearchIcon: React.FC<SearchIconProps> = ({
  onPress,
  size = "medium",
  color = colors.palette.neutral600,
  showVoiceSearch = false,
}) => {
  const iconSize = ICON_SIZES[size]

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.7}
      accessible={true}
      accessibilityLabel={
        showVoiceSearch
          ? "Settings with voice search available"
          : "Settings and preferences"
      }
      accessibilityRole="button"
    >
      <Text style={[styles.searchIcon, { fontSize: iconSize, color }]}>
        ⚙️
      </Text>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  container: {
    padding: spacing.xs, // 8px padding for touch area
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 12, // 12px border radius per design guidelines
    minWidth: 44, // Minimum touch target per accessibility guidelines
    minHeight: 44, // Minimum touch target per accessibility guidelines
  },
  searchIcon: {
    // Clean icon styling per design guidelines
    // No background per user request for minimal appearance
  },
})
