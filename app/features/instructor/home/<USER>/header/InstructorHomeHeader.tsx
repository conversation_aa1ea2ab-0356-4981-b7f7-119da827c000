import React from "react"
import { View, StyleSheet } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { colors, spacing } from "app/theme"
import { UserAvatar } from "./UserAvatar"
import { UserGreeting } from "./UserGreeting"
import { NotificationBell } from "./NotificationBell"
import { SearchIcon } from "./SearchIcon"
import { StreakCounter } from "./StreakCounter"

interface InstructorHomeHeaderProps {
  /**
   * User information
   */
  user: {
    displayName: string
    imageUrl?: string
  }
  /**
   * Notification data
   */
  notifications: {
    unreadCount: number
    showAnimation?: boolean
  }
  /**
   * Streak information (adapted for instructor - could be teaching streak)
   */
  streak: {
    count: number
    showAnimation?: boolean
  }
  /**
   * Navigation callbacks
   */
  onNavigateToProfile?: () => void
  onNavigateToNotifications?: () => void
  onNavigateToSearch?: () => void
  onViewStreak?: () => void
}

export const InstructorHomeHeader: React.FC<InstructorHomeHeaderProps> = ({
  user,
  notifications,
  streak,
  onNavigateToProfile,
  onNavigateToNotifications,
  onNavigateToSearch,
  onViewStreak,
}) => {
  return (
    <View style={styles.container}>
      {/* Modern gradient background */}
      <LinearGradient
        colors={[colors.palette.primary100, colors.palette.neutral100]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradientBackground}
      />

      {/* Header content with new layout */}
      <View style={styles.content}>
        {/* Left side - User info and streak */}
        <View style={styles.leftSection}>
          {/* Top row: Avatar + Greeting */}
          <View style={styles.userInfoRow}>
            <View style={styles.avatarContainer}>
              <UserAvatar
                imageUrl={user.imageUrl}
                displayName={user.displayName}
                size="large"
                onPress={onNavigateToProfile}
                showOnlineStatus={true}
              />
            </View>
            <UserGreeting
              displayName={user.displayName}
              showGreeting={true}
            />
          </View>

          {/* Bottom row: Streak Counter */}
          <View style={styles.streakRow}>
            <StreakCounter
              streakCount={streak.count}
              onPress={onViewStreak}
              showAnimation={streak.showAnimation}
              size="medium"
            />
          </View>
        </View>

        {/* Right side - Action icons only */}
        <View style={styles.rightSection}>
          <SearchIcon
            onPress={onNavigateToSearch}
            size="large"
            showVoiceSearch={true}
          />

          <NotificationBell
            unreadCount={notifications.unreadCount}
            onPress={onNavigateToNotifications}
            showAnimation={notifications.showAnimation}
            size="large"
          />
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    position: "relative",
    paddingTop: spacing.md, // 16px - optimal spacing per design guidelines
    paddingBottom: spacing.md, // 16px - consistent vertical spacing
    paddingHorizontal: spacing.sm, // 12px - per horizontal padding rule
    backgroundColor: "#FFFFFF", // MANDATORY white background per design guidelines
    // Professional shadow effect per design system
    shadowColor: "#000000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  gradientBackground: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.3, // Reduced opacity for subtle effect per design guidelines
  },
  content: {
    flexDirection: "row",
    alignItems: "flex-start", // Changed from center to flex-start for top alignment
    justifyContent: "space-between",
    zIndex: 1,
  },
  leftSection: {
    flex: 1,
    flexDirection: "column", // Changed to column for vertical layout
    justifyContent: "flex-start", // Changed to flex-start for top alignment
  },
  userInfoRow: {
    flexDirection: "row", // Horizontal layout for avatar + greeting
    alignItems: "flex-start", // Top alignment to prevent text clipping
    marginBottom: spacing.xs, // 8px space between user info and streak
    paddingTop: spacing.xxs, // 4px top padding for text visibility
  },
  streakRow: {
    flexDirection: "row", // Horizontal layout for streak
    alignItems: "center",
    paddingLeft: spacing.lg, // 24px - align with greeting text (avatar width + margin)
  },
  avatarContainer: {
    marginRight: spacing.sm, // 8px margin for proper spacing
    // Professional avatar shadow per design guidelines
    shadowColor: "#132339", // Deep navy shadow color
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  rightSection: {
    flexDirection: "row",
    alignItems: "flex-start", // Top alignment with avatar
    gap: spacing.xs, // 8px space between icons
    paddingTop: spacing.xxs, // 4px padding for alignment
  },
  // actionButton removed - icons now have no background per user request
  // streakContainer removed - streak counter now in leftSection
})
