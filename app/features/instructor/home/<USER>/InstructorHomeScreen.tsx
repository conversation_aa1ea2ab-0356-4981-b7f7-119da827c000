import React from "react"
import { <PERSON>, ScrollView, StyleSheet } from "react-native"
import { Screen } from "app/components"
import { colors, spacing } from "app/theme"
import { FigmaInstructorHomeHeader } from "../components/header/FigmaInstructorHomeHeader"
import { FigmaInstructorSearchBar } from "../components/sections/FigmaInstructorSearchBar"
import { FigmaInstructorOfferBanner } from "../components/sections/FigmaInstructorOfferBanner"
import { FigmaInstructorCategories } from "../components/sections/FigmaInstructorCategories"
import { FigmaInstructorPopularCourses } from "../components/sections/FigmaInstructorPopularCourses"
import { FigmaInstructorTopMentors } from "../components/sections/FigmaInstructorTopMentors"

interface InstructorHomeScreenProps {
  /**
   * Navigation prop for screen navigation
   */
  navigation?: any
}

/**
 * FigmaInstructorHomeScreen - Instructor home screen based on exact Figma design
 * 
 * This screen replicates the Figma design at node-id=1-725 but with instructor-specific content:
 * - Header with greeting and notification
 * - Search bar with filter
 * - Offer banner (instructor-focused)
 * - Categories (teaching categories)
 * - Popular Courses (instructor courses)
 * - Top Mentors (fellow instructors)
 */
export const InstructorHomeScreen: React.FC<InstructorHomeScreenProps> = ({
  navigation,
}) => {
  console.log("📚 FigmaInstructorHomeScreen rendering...")

  const handleNotificationPress = () => {
    console.log("📚 Navigate to notifications")
    if (navigation) {
      navigation.navigate("InstructorNotifications")
    }
  }

  return (
    <Screen
      preset="scroll"
      style={styles.container}
      backgroundColor="#FFFFFF"
      safeAreaEdges={["top"]}
    >
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Header Section - "Hi, ALEX" + notification */}
        <View style={styles.headerSection}>
          <FigmaInstructorHomeHeader onNotificationPress={handleNotificationPress} />
        </View>

        {/* Search Section */}
        <View style={styles.searchSection}>
          <FigmaInstructorSearchBar />
        </View>

        {/* Offer Banner Section */}
        <View style={styles.offerSection}>
          <FigmaInstructorOfferBanner />
        </View>

        {/* Categories Section */}
        <View style={styles.categoriesSection}>
          <FigmaInstructorCategories />
        </View>

        {/* Popular Courses Section */}
        <View style={styles.popularCoursesSection}>
          <FigmaInstructorPopularCourses />
        </View>

        {/* Top Mentors Section */}
        <View style={styles.topMentorsSection}>
          <FigmaInstructorTopMentors />
        </View>
      </ScrollView>
    </Screen>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF", // White background as requested
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: spacing.xl, // Extra padding at bottom
    paddingHorizontal: 16, // 16px horizontal padding as requested
  },
  headerSection: {
    paddingTop: 52, // From Figma: y: 52
    marginBottom: spacing.lg,
  },
  searchSection: {
    marginBottom: spacing.lg,
  },
  offerSection: {
    marginBottom: spacing.lg,
  },
  categoriesSection: {
    marginBottom: spacing.lg,
  },
  popularCoursesSection: {
    marginBottom: spacing.lg,
  },
  topMentorsSection: {
    marginBottom: spacing.xl,
  },
})


