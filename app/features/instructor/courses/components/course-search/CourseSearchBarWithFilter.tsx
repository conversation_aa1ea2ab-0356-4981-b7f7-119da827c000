import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, TextInput } from "react-native"
import { Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface CourseSearchBarWithFilterProps {
  placeholder?: string
  value?: string
  onChangeText?: (text: string) => void
  onSearchPress?: () => void
  onFilterPress?: () => void
}

/**
 * CourseSearchBarWithFilter - Instructor's course search bar with filter component
 * 
 * This component implements the search bar design for instructor's course search screen.
 * Identical to student version but searches instructor's teaching courses.
 * 
 * Features:
 * - Search input with placeholder and auto-focus
 * - Search icon button
 * - Filter button with settings icon
 * - Touch handling for search and filter functionality
 * - Consistent styling with app design
 */
export function CourseSearchBarWithFilter({ 
  placeholder = "Search for courses..",
  value,
  onChangeText,
  onSearchPress,
  onFilterPress
}: CourseSearchBarWithFilterProps) {
  const { themed } = useAppTheme()

  const handleSubmitEditing = () => {
    console.log("🔍📚 Instructor search submitted:", value)
    onSearchPress?.()
  }

  const handleSearchPress = () => {
    console.log("🔍📚 Instructor search icon pressed")
    onSearchPress?.()
  }

  const handleFilterPress = () => {
    console.log("🔍📚 Instructor filter pressed")
    onFilterPress?.()
  }

  return (
    <View style={themed($container)}>
      <View style={themed($searchContainer)}>
        {/* Search Icon */}
        <TouchableOpacity 
          onPress={handleSearchPress} 
          style={themed($searchIcon)}
          activeOpacity={0.7}
        >
          <Icon icon="search" size={20} color="#000000" />
        </TouchableOpacity>

        {/* Search Input */}
        <TextInput
          style={themed($searchInput)}
          placeholder={placeholder}
          placeholderTextColor="#B4BDC4"
          value={value}
          onChangeText={onChangeText}
          onSubmitEditing={handleSubmitEditing}
          returnKeyType="search"
          autoFocus={true}
        />

        {/* Filter Button */}
        <TouchableOpacity 
          style={themed($filterButton)}
          onPress={handleFilterPress}
          activeOpacity={0.7}
        >
          <Icon icon="settings" size={16} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
  marginBottom: 30,
}

const $searchContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF",
  borderRadius: 15,
  paddingLeft: 13,
  paddingRight: 13,
  paddingVertical: 12,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 3 },
  shadowOpacity: 0.1,
  shadowRadius: 12,
  elevation: 4,
}

const $searchIcon: ViewStyle = {
  marginRight: 10,
}

const $searchInput: TextStyle = {
  flex: 1,
  fontFamily: "lexendDecaLight", // 300 weight for input text - per design guidelines
  fontSize: 16,
  lineHeight: 20,
  color: "#202244", // Text color for typed text
  paddingVertical: 0, // Remove default padding
}

const $filterButton: ViewStyle = {
  width: 38,
  height: 38,
  borderRadius: 10,
  backgroundColor: "#0961F5",
  justifyContent: "center",
  alignItems: "center",
  marginLeft: 10,
}
