import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface RecentCourseSearchesSectionProps {
  recentSearches: string[]
  onRecentSearchPress?: (searchTerm: string) => void
  onDeleteRecentSearch?: (searchTerm: string) => void
  onSeeAllPress?: () => void
}

/**
 * RecentCourseSearchesSection - Instructor's recent course searches section
 * 
 * This component implements the recent searches section for instructor's course search screen.
 * Identical to student version but for instructor's course search history.
 * 
 * Features:
 * - Recent Course Search title with See All button
 * - List of recent search terms with delete option
 * - Touch handling for search term selection and deletion
 * - Consistent styling with app design
 */
export function RecentCourseSearchesSection({
  recentSearches,
  onRecentSearchPress,
  onDeleteRecentSearch,
  onSeeAllPress
}: RecentCourseSearchesSectionProps) {
  const { themed } = useAppTheme()

  const handleSeeAllPress = () => {
    console.log("🔍📚 Instructor see all recent searches pressed")
    onSeeAllPress?.()
  }

  return (
    <View style={themed($container)}>
      {/* Header with title and See All */}
      <View style={themed($header)}>
        <Text style={themed($title)}>Recent Course Search</Text>
        <TouchableOpacity 
          onPress={handleSeeAllPress} 
          style={themed($seeAllContainer)}
          activeOpacity={0.7}
        >
          <Text style={themed($seeAllText)}>See All</Text>
          <Icon icon="caretRight" size={5} color="#0961F5" />
        </TouchableOpacity>
      </View>

      {/* Recent course searches list */}
      <View style={themed($listContainer)}>
        {recentSearches.map((searchTerm, index) => (
          <RecentCourseSearchItem
            key={index}
            searchTerm={searchTerm}
            onPress={() => onRecentSearchPress?.(searchTerm)}
            onDelete={() => onDeleteRecentSearch?.(searchTerm)}
          />
        ))}
      </View>
    </View>
  )
}

interface RecentCourseSearchItemProps {
  searchTerm: string
  onPress?: () => void
  onDelete?: () => void
}

function RecentCourseSearchItem({ searchTerm, onPress, onDelete }: RecentCourseSearchItemProps) {
  const { themed } = useAppTheme()

  const handlePress = () => {
    console.log("🔍📚 Instructor recent search pressed:", searchTerm)
    onPress?.()
  }

  const handleDelete = () => {
    console.log("🔍📚 Instructor delete recent search:", searchTerm)
    onDelete?.()
  }

  return (
    <View style={themed($itemContainer)}>
      <TouchableOpacity 
        onPress={handlePress} 
        style={themed($itemContent)}
        activeOpacity={0.7}
      >
        <Text style={themed($itemText)}>{searchTerm}</Text>
      </TouchableOpacity>
      
      <TouchableOpacity 
        onPress={handleDelete} 
        style={themed($deleteButton)}
        activeOpacity={0.7}
      >
        <Text style={themed($deleteText)}>X</Text>
      </TouchableOpacity>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
}

const $header: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: 20,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight
  fontSize: 15,
  lineHeight: 22,
  color: "#202244",
}

const $seeAllContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $seeAllText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for labels - per design guidelines
  fontSize: 12,
  lineHeight: 15,
  color: "#0961F5",
  textTransform: "uppercase",
  marginRight: 5,
}

const $listContainer: ViewStyle = {
  // Container for the list items
}

const $itemContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingVertical: 15,
  borderBottomWidth: 1,
  borderBottomColor: "#F0F0F0",
}

const $itemContent: ViewStyle = {
  flex: 1,
}

const $itemText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for body text - per design guidelines
  fontSize: 15,
  lineHeight: 19,
  color: "#A0A4AB",
}

const $deleteButton: ViewStyle = {
  padding: 5,
}

const $deleteText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for body text - per design guidelines
  fontSize: 15,
  lineHeight: 19,
  color: "#472D2D",
}
