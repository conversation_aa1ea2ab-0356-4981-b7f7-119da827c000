import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface CourseSearchHeaderProps {
  onBackPress?: () => void
}

/**
 * CourseSearchHeader - Instructor's course search header component
 * 
 * This component implements the header design for instructor's course search screen.
 * Following the same pattern as profile headers for consistency.
 * 
 * Features:
 * - Back button with TouchableOpacity and proper touch feedback
 * - "Search Course" title
 * - Consistent styling with app design and profile headers
 */
export function CourseSearchHeader({ onBackPress }: CourseSearchHeaderProps) {
  const { themed } = useAppTheme()

  const handleBackPress = () => {
    console.log("🔍📚 Instructor CourseSearchHeader back pressed")
    onBackPress?.()
  }

  return (
    <View style={themed($container)}>
      <TouchableOpacity
        style={themed($backButton)}
        onPress={handleBackPress}
        activeOpacity={0.7}
      >
        <Icon icon="back" size={26} color="#202244" />
      </TouchableOpacity>
      
      <Text style={themed($title)}>Search Course</Text>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35,
  paddingTop: 25,
  paddingBottom: 20,
}

const $backButton: ViewStyle = {
  marginRight: 12,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight
  fontSize: 21,
  lineHeight: 30,
  color: "#202244",
}
