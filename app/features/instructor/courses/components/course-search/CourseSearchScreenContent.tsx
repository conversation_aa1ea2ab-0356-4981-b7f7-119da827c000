import React, { useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import {
  CourseSearchHeader,
  CourseSearchBarWithFilter,
  RecentCourseSearchesSection
} from "./index"

interface CourseSearchScreenContentProps {
  onBackPress?: () => void
  onSearch?: (query: string) => void
  onFilterPress?: () => void
  onRecentSearchPress?: (searchTerm: string) => void
  onDeleteRecentSearch?: (searchTerm: string) => void
  onSeeAllPress?: () => void
  initialQuery?: string
}

// Mock data for instructor's recent course searches
const mockInstructorRecentSearches = [
  "React Development",
  "UI/UX Design",
  "JavaScript Advanced",
  "Mobile App Design",
  "Web Development",
]

/**
 * CourseSearchScreenContent - Instructor's course search screen based on Figma design
 *
 * This component implements the Course Search design for instructor's course search.
 * Identical design to student version but with instructor-specific functionality.
 *
 * Features:
 * - CourseSearchHeader: "Search Course" title with back button
 * - CourseSearchBarWithFilter: Search input with filter button
 * - RecentCourseSearchesSection: List of recent course searches with delete option
 */
export function CourseSearchScreenContent({
  onBackPress,
  onSearch,
  onFilterPress,
  onRecentSearchPress,
  onDeleteRecentSearch,
  onSeeAllPress,
  initialQuery = ""
}: CourseSearchScreenContentProps) {
  const { themed } = useAppTheme()
  const [searchQuery, setSearchQuery] = useState<string>(initialQuery)

  console.log("🔍📚 Instructor CourseSearchScreenContent rendering...")

  const handleBackPress = () => {
    console.log("🔍📚 Instructor back pressed")
    onBackPress?.()
  }

  const handleSearchChange = (text: string) => {
    console.log("🔍📚 Instructor search text changed:", text)
    setSearchQuery(text)
  }

  const handleSearchSubmit = () => {
    console.log("🔍📚 Instructor search submitted:", searchQuery)
    onSearch?.(searchQuery)
  }

  const handleFilterPress = () => {
    console.log("🔍📚 Instructor filter pressed")
    onFilterPress?.()
  }

  const handleRecentSearchPress = (searchTerm: string) => {
    console.log("🔍📚 Instructor recent search pressed:", searchTerm)
    setSearchQuery(searchTerm)
    onRecentSearchPress?.(searchTerm)
  }

  const handleDeleteRecentSearch = (searchTerm: string) => {
    console.log("🔍📚 Instructor delete recent search:", searchTerm)
    onDeleteRecentSearch?.(searchTerm)
  }

  const handleSeeAllPress = () => {
    console.log("🔍📚 Instructor see all pressed")
    onSeeAllPress?.()
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
    >
      <View style={themed($content)}>
        {/* Header with back button */}
        <CourseSearchHeader onBackPress={handleBackPress} />
        
        {/* Search bar with filter */}
        <CourseSearchBarWithFilter
          value={searchQuery}
          onChangeText={handleSearchChange}
          onSearchPress={handleSearchSubmit}
          onFilterPress={handleFilterPress}
          placeholder="Search for courses.."
        />
        
        {/* Recent course searches section */}
        <RecentCourseSearchesSection
          recentSearches={mockInstructorRecentSearches}
          onRecentSearchPress={handleRecentSearchPress}
          onDeleteRecentSearch={handleDeleteRecentSearch}
          onSeeAllPress={handleSeeAllPress}
        />
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  flex: 1,
}
