import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import { MyCourse } from "../../types"

interface CourseCardProps {
  course: MyCourse
  onPress?: (course: MyCourse) => void
}

/**
 * CourseCard - Instructor's class card component
 *
 * This component implements the class card design for instructor's classes screen.
 * Identical design to student version but represents instructor's teaching classes.
 *
 * Features:
 * - Course image placeholder
 * - Course category and class name (or course title as fallback)
 * - Rating and duration display
 * - Progress bar showing class completion
 * - Touch handling for class navigation
 * - Consistent styling with app design
 */
export function CourseCard({ course, onPress }: CourseCardProps) {
  const { themed } = useAppTheme()

  const progressPercentage = (course.completedLessons / course.totalLessons) * 100

  // Get progress bar color based on course progress
  const getProgressColor = () => {
    if (progressPercentage >= 80) return "#167F71" // Green for high progress
    if (progressPercentage >= 50) return "#FF6B00" // Orange for medium progress
    return "#FCCB40" // Yellow for low progress
  }

  return (
    <TouchableOpacity
      style={themed($container)}
      onPress={() => onPress?.(course)}
      activeOpacity={0.8}
    >
      {/* Course Image */}
      <View style={themed($courseImage)} />

      {/* Course Content */}
      <View style={themed($content)}>
        {/* Category */}
        <Text style={themed($category)}>{course.category}</Text>

        {/* Title - Show class name if available, otherwise course title */}
        <Text style={themed($title)}>{course.className || course.title}</Text>

        {/* Rating and Duration */}
        <View style={themed($ratingContainer)}>
          <View style={themed($starContainer)}>
            <Icon icon="heart" size={12} color="#FCCB40" />
            <Text style={themed($ratingText)}>{course.rating}</Text>
          </View>

          <Text style={themed($separator)}>|</Text>

          <Text style={themed($durationText)}>{course.duration}</Text>
        </View>

        {/* Progress Bar */}
        <View style={themed($progressContainer)}>
          <View style={themed($progressTrack)}>
            <View style={themed([
              $progressFill,
              {
                width: `${progressPercentage}%`,
                backgroundColor: getProgressColor()
              }
            ])} />
          </View>

          <Text style={themed($progressText)}>
            {course.completedLessons}/{course.totalLessons}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  marginHorizontal: 16, // 16px margin from screen edges
  marginBottom: 20,
  borderWidth: 0.5,
  borderColor: "#E5E5E5",
}

const $courseImage: ViewStyle = {
  width: 130,
  height: 134,
  backgroundColor: "#000000",
  borderTopLeftRadius: 16,
  borderBottomLeftRadius: 16,
}

const $content: ViewStyle = {
  flex: 1,
  padding: 14,
  justifyContent: "space-between",
}

const $category: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for categories
  fontSize: 12,
  lineHeight: 15,
  color: "#23408B", // Brand primary color - consistent with student app
  marginBottom: 5,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaBold", // 700 weight for titles - bold for better emphasis
  fontWeight: "700", // Explicit bold weight for stronger visual hierarchy
  fontSize: 16,
  lineHeight: 23,
  color: "#333333", // Primary text color from updated design guidelines
  marginBottom: 10,
  marginTop: 0, // Align to top for better visual hierarchy
}

const $ratingContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: 15,
}

const $starContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $ratingText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for ratings
  fontSize: 11,
  lineHeight: 14,
  color: "#333333", // Primary text color from updated design guidelines
  marginLeft: 3,
}

const $separator: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#000000",
  marginHorizontal: 16,
}

const $durationText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for duration
  fontSize: 11,
  lineHeight: 14,
  color: "#333333", // Primary text color from updated design guidelines
}

const $progressContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
}

const $progressTrack: ViewStyle = {
  flex: 1,
  height: 6,
  backgroundColor: "#F5F9FF",
  borderRadius: 5,
  borderWidth: 2,
  borderColor: "#E8F1FF",
  marginRight: 12,
  overflow: "hidden",
}

const $progressFill: ViewStyle = {
  height: "100%",
  borderRadius: 5,
  // backgroundColor will be set dynamically based on progress
}

const $progressText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for progress
  fontSize: 11,
  lineHeight: 14,
  color: "#333333", // Primary text color from updated design guidelines
}
