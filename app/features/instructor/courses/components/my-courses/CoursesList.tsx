import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "app/utils/useAppTheme"
import { CourseCard } from "./CourseCard"
import { MyCourse } from "../../types"

interface CoursesListProps {
  courses: MyCourse[]
  onCoursePress?: (course: MyCourse) => void
}

/**
 * CoursesList - Instructor's courses list component
 * 
 * This component implements the courses list for instructor's courses screen.
 * Identical to student version but displays instructor's teaching courses.
 * 
 * Features:
 * - List of course cards
 * - Course press handling
 * - Proper spacing and layout
 * - Bottom navigation safe area
 */
export function CoursesList({ courses, onCoursePress }: CoursesListProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {courses.map((course) => (
        <CourseCard
          key={course.id}
          course={course}
          onPress={onCoursePress}
        />
      ))}
    </View>
  )
}

const $container: ViewStyle = {
  paddingBottom: 100, // Increased to avoid bottom navigation overlap
  marginBottom: 20, // Additional margin for better spacing
}
