import React, { useState } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { CourseStatusTab } from "../../types"

interface CourseStatusTabsProps {
  tabs?: CourseStatusTab[]
  onTabPress?: (tab: CourseStatusTab) => void
}

// Updated tabs for instructor: Active courses vs Archived courses
const defaultTabs: CourseStatusTab[] = [
  { id: "archived", title: "Archived" },
  { id: "active", title: "Active" },
]

/**
 * CourseStatusTabs - Instructor's course status tabs component
 * 
 * This component implements the status tabs for instructor's courses screen.
 * Updated for instructor workflow with Active/Archived instead of Completed/Ongoing.
 * 
 * Features:
 * - Active tab for currently teaching courses
 * - Archived tab for completed/inactive courses
 * - Tab selection with visual feedback
 * - Consistent styling with app design
 */
export function CourseStatusTabs({
  tabs = defaultTabs,
  onTabPress
}: CourseStatusTabsProps) {
  const { themed } = useAppTheme()
  const [selectedTab, setSelectedTab] = useState<string>("active")

  const handleTabPress = (tab: CourseStatusTab) => {
    setSelectedTab(tab.id)
    onTabPress?.(tab)
  }

  return (
    <View style={themed($container)}>
      {tabs.map((tab) => {
        const isSelected = selectedTab === tab.id

        return (
          <TouchableOpacity
            key={tab.id}
            style={themed([
              $tab,
              isSelected && $selectedTab
            ])}
            onPress={() => handleTabPress(tab)}
          >
            <Text style={themed([
              $tabText,
              isSelected && $selectedTabText
            ])}>
              {tab.title}
            </Text>
          </TouchableOpacity>
        )
      })}
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  paddingHorizontal: 16,
  marginBottom: 20,
}

const $tab: ViewStyle = {
  flex: 1,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#E8F1FF",
  justifyContent: "center",
  alignItems: "center",
  marginRight: 20,
}

const $selectedTab: ViewStyle = {
  backgroundColor: "#167F71",
}

const $tabText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for tab labels
  fontSize: 15,
  lineHeight: 19,
  color: "#202244",
  textAlign: "center",
}

const $selectedTabText: TextStyle = {
  color: "#FFFFFF",
}
