import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface MyCoursesHeaderProps {
  onBackPress?: () => void
}

/**
 * MyCoursesHeader - Instructor's My Classes header component
 *
 * This component implements the header design for instructor's classes screen.
 * Following the same pattern as instructor ProfileHeader for consistency.
 *
 * Features:
 * - Back button with TouchableOpacity and proper touch feedback
 * - "My Classes" title
 * - Consistent styling with app design and profile headers
 */
export function MyCoursesHeader({ onBackPress }: MyCoursesHeaderProps) {
  const { themed } = useAppTheme()

  const handleBackPress = () => {
    console.log("📚 MyCoursesHeader back pressed")
    onBackPress?.()
  }

  return (
    <View style={themed($container)}>
      <TouchableOpacity
        style={themed($backButton)}
        onPress={handleBackPress}
        activeOpacity={0.7}
      >
        <Icon icon="back" size={26} color="#202244" />
      </TouchableOpacity>

      <Text style={themed($title)}>My Classes</Text>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 16,
  paddingTop: 25,
  paddingBottom: 20,
}

const $backButton: ViewStyle = {
  marginRight: 12,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for headings
  fontSize: 21,
  lineHeight: 30,
  color: "#202244",
}
