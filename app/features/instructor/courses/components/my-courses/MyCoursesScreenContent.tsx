import React, { useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import {
  MyCoursesHeader,
  MyCoursesSearchBar,
  CourseStatusTabs,
  CoursesList
} from "./index"
import { MyCourse, CourseStatusTab } from "../../types"
import { useInstructorCourses } from "../../hooks/useInstructorCourses"

interface MyCoursesScreenContentProps {
  onBackPress?: () => void
  onSearchPress?: () => void
  onCoursePress?: (course: MyCourse) => void
  onTabPress?: (tab: CourseStatusTab) => void
  onSearchTextChange?: (text: string) => void
}



/**
 * MyCoursesScreenContent - Instructor's My Classes screen based on Figma design
 *
 * This component implements the My Classes design for instructor's teaching classes.
 * Identical design to student version but with instructor-specific content and data.
 *
 * Features:
 * - MyCoursesHeader: "My Classes" title with back button
 * - MyCoursesSearchBar: Search with blue search button
 * - CourseStatusTabs: "Active" and "Archived" tabs for instructor workflow
 * - CoursesList: List of instructor's teaching class cards with progress bars
 */
export function MyCoursesScreenContent({
  onBackPress,
  onSearchPress,
  onCoursePress,
  onTabPress,
  onSearchTextChange
}: MyCoursesScreenContentProps) {
  const { themed } = useAppTheme()

  // Use real API data from useInstructorCourses hook
  const {
    courses,
    isLoading,
    isRefreshing,
    error,
    searchQuery,
    selectedTab,
    refreshCourses,
    setSearchQuery,
    setSelectedTab,
    clearError,
  } = useInstructorCourses()

  console.log("📚 Instructor MyCoursesScreenContent rendering...")
  console.log("📚 API Courses loaded:", courses.length)
  console.log("📚 Loading:", isLoading)
  console.log("📚 Error:", error)

  const handleTabPress = (tab: CourseStatusTab) => {
    console.log("📚 Instructor tab pressed:", tab.title)
    setSelectedTab(tab)
    onTabPress?.(tab)
  }

  const handleSearchPress = () => {
    console.log("📚 Instructor search pressed")
    onSearchPress?.()
  }

  const handleCoursePress = (course: MyCourse) => {
    console.log("📚 Instructor course pressed:", course.title)
    onCoursePress?.(course)
  }

  const handleBackPress = () => {
    console.log("📚 Instructor back pressed")
    onBackPress?.()
  }

  const handleSearchTextChange = (text: string) => {
    console.log("📚 Instructor search text changed:", text)
    setSearchQuery(text)
    onSearchTextChange?.(text)
  }

  console.log("📚 Filtered courses:", courses.length, "for tab:", selectedTab.title)

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#FFFFFF"
      style={themed($screen)}
      contentContainerStyle={{
        flexGrow: 1,
        paddingBottom: 100, // Extra space for bottom navigation
      }}
    >
      <View style={themed($content)}>
        {/* Header with back button */}
        <MyCoursesHeader onBackPress={handleBackPress} />

        {/* Search bar with blue search button */}
        <MyCoursesSearchBar
          onSearchPress={handleSearchPress}
          onChangeText={handleSearchTextChange}
          value={searchQuery}
        />

        {/* Status tabs - Active/Archived */}
        <CourseStatusTabs onTabPress={handleTabPress} />

        {/* Course cards list - now using real API data */}
        <CoursesList
          courses={courses}
          onCoursePress={handleCoursePress}
        />
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  flex: 1,
}
