import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, TextInput } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface MyCoursesSearchBarProps {
  placeholder?: string
  onSearchPress?: () => void
  onChangeText?: (text: string) => void
  value?: string
}

/**
 * MyCoursesSearchBar - Instructor's My Classes search bar component
 *
 * This component implements the search bar design for instructor's classes screen.
 * Identical to student version but searches instructor's teaching classes.
 *
 * Features:
 * - Search input with placeholder
 * - Blue search button with icon
 * - Touch handling for search functionality
 * - Consistent styling with app design
 */
export function MyCoursesSearchBar({ 
  placeholder = "Search for …",
  onSearchPress,
  onChangeText,
  value
}: MyCoursesSearchBarProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <View style={themed($searchContainer)}>
        <TouchableOpacity
          style={{ flex: 1, justifyContent: "center" }}
          onPress={onSearchPress}
        >
          <Text style={themed($searchInput)}>
            {value || placeholder}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={themed($searchButton)}
          onPress={onSearchPress}
        >
          <Icon icon="search" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
  marginBottom: 20,
}

const $searchContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF",
  borderRadius: 15,
  paddingLeft: 15,
  paddingRight: 13,
  paddingVertical: 12,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 3 },
  shadowOpacity: 0.1,
  shadowRadius: 12,
  elevation: 4,
}

const $searchInput: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY
  fontSize: 16,
  lineHeight: 20,
  color: "#B4BDC4", // Placeholder color
}

const $searchButton: ViewStyle = {
  width: 38,
  height: 38,
  borderRadius: 10,
  backgroundColor: "#0961F5",
  justifyContent: "center",
  alignItems: "center",
  marginLeft: 10,
}
