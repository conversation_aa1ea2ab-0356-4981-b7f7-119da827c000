/**
 * useInstructorCourses Hook
 * 
 * Custom hook for managing instructor courses from real API.
 * Follows the same pattern as useStudentCourses for consistency.
 */

import { useState, useEffect, useCallback } from "react"
import { useStores } from "app/models"
import { instructorApi } from "app/services/api/api"
import { InstructorCourse } from "app/services/api/instructorApi"
import { MyCourse, CourseStatusTab } from "../types"

export interface UseInstructorCoursesResult {
  // Data
  courses: MyCourse[]
  allCourses: MyCourse[]
  
  // State
  isLoading: boolean
  isRefreshing: boolean
  error: string | null
  
  // Pagination
  hasMore: boolean
  currentPage: number
  totalCourses: number
  
  // Filtering
  searchQuery: string
  selectedTab: CourseStatusTab
  
  // Actions
  refreshCourses: () => Promise<void>
  loadMoreCourses: () => Promise<void>
  setSearchQuery: (query: string) => void
  setSelectedTab: (tab: CourseStatusTab) => void
  clearError: () => void
}

/**
 * Convert API response to UI MyCourse
 * Based on actual API response structure
 */
const convertToMyCourse = (apiCourse: any): MyCourse => {
  return {
    id: apiCourse.id.toString(),
    title: apiCourse.name,
    category: apiCourse.subject_name || "General",
    rating: 0, // Not provided in API
    duration: "N/A", // Not provided in API
    progress: apiCourse.completed_lessons && apiCourse.total_lessons
      ? Math.round((apiCourse.completed_lessons / apiCourse.total_lessons) * 100)
      : 0,
    totalLessons: apiCourse.total_lessons || 0,
    completedLessons: apiCourse.completed_lessons || 0,
    isCompleted: apiCourse.state === "Hoàn thành" || apiCourse.state === "Đã kết thúc",
    isOngoing: apiCourse.state === "Đang diễn ra" || apiCourse.state === "Tuyển sinh",
    // Additional instructor-specific fields
    code: apiCourse.code,
    description: apiCourse.course_name || apiCourse.name,
    level: "Beginner", // Not provided in API
    totalStudents: apiCourse.total_students || 0,
    activeStudents: apiCourse.total_students || 0, // Assume all students are active
    className: apiCourse.name,
    classStatus: apiCourse.state,
    canManage: apiCourse.is_primary_instructor || true,
    needsAttention: apiCourse.needs_attention || false,
    attentionReason: apiCourse.attention_reason || "",
    startDate: apiCourse.next_lesson_date || "",
    endDate: null,
    price: 0, // Not provided in API
    currency: "VND",
  }
}

/**
 * Filter courses based on search query
 */
const filterCoursesBySearch = (courses: MyCourse[], query: string): MyCourse[] => {
  if (!query.trim()) return courses
  
  const lowercaseQuery = query.toLowerCase()
  return courses.filter(course =>
    course.title.toLowerCase().includes(lowercaseQuery) ||
    course.description.toLowerCase().includes(lowercaseQuery) ||
    course.code.toLowerCase().includes(lowercaseQuery) ||
    course.category.toLowerCase().includes(lowercaseQuery)
  )
}

/**
 * Filter courses based on selected tab
 */
const filterCoursesByTab = (courses: MyCourse[], tab: CourseStatusTab): MyCourse[] => {
  switch (tab.id) {
    case "archived":
      return courses.filter(course => course.isCompleted)
    case "active":
      return courses.filter(course => course.isOngoing)
    default:
      return courses
  }
}

/**
 * useInstructorCourses Hook
 */
export function useInstructorCourses(): UseInstructorCoursesResult {
  // Store access
  const { authenticationStore } = useStores()
  
  // State
  const [allCourses, setAllCourses] = useState<MyCourse[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCourses, setTotalCourses] = useState(0)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedTab, setSelectedTab] = useState<CourseStatusTab>({ id: "active", title: "Active" })

  /**
   * Load courses from API
   */
  const loadCourses = useCallback(async (page = 1, append = false) => {
    try {
      if (page === 1) {
        setIsLoading(true)
      }
      setError(null)
      
      // Set auth token for API
      const authToken = authenticationStore.authToken
      if (!authToken) {
        throw new Error("No authentication token available")
      }
      
      instructorApi.setAuthToken(authToken)
      
      // Call API
      const result = await instructorApi.getInstructorCourses(page, 20)
      
      if (result.kind === "ok" && result.data) {
        const apiCourses = result.data.data
        const convertedCourses = apiCourses.map(convertToMyCourse)
        
        if (append) {
          setAllCourses(prev => [...prev, ...convertedCourses])
        } else {
          setAllCourses(convertedCourses)
        }
        
        setHasMore(result.data.pagination.has_next)
        setCurrentPage(page)
        setTotalCourses(result.data.pagination.total_count)
        
        console.log("📚 Loaded instructor courses:", convertedCourses.length)
        console.log("📊 Total courses:", result.data.pagination.total_count)
        
      } else {
        throw new Error("Failed to load courses from API")
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to load courses"
      setError(errorMessage)
      console.error("❌ Error loading instructor courses:", err)
    } finally {
      setIsLoading(false)
      setIsRefreshing(false)
    }
  }, [authenticationStore.authToken])

  /**
   * Refresh courses (pull-to-refresh)
   */
  const refreshCourses = useCallback(async () => {
    setIsRefreshing(true)
    await loadCourses(1, false)
  }, [loadCourses])

  /**
   * Load more courses (pagination)
   */
  const loadMoreCourses = useCallback(async () => {
    if (hasMore && !isLoading) {
      await loadCourses(currentPage + 1, true)
    }
  }, [hasMore, isLoading, currentPage, loadCourses])

  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  /**
   * Load courses on mount and when auth token changes
   */
  useEffect(() => {
    if (authenticationStore.authToken) {
      loadCourses(1, false)
    }
  }, [authenticationStore.authToken, loadCourses])

  /**
   * Filter courses based on search and tab
   */
  const filteredCourses = filterCoursesByTab(
    filterCoursesBySearch(allCourses, searchQuery),
    selectedTab
  )

  return {
    // Data
    courses: filteredCourses,
    allCourses,
    
    // State
    isLoading,
    isRefreshing,
    error,
    
    // Pagination
    hasMore,
    currentPage,
    totalCourses,
    
    // Filtering
    searchQuery,
    selectedTab,
    
    // Actions
    refreshCourses,
    loadMoreCourses,
    setSearchQuery,
    setSelectedTab,
    clearError,
  }
}
