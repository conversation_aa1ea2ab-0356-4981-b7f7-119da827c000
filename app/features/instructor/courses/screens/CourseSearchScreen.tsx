import React from "react"
import { CourseSearchScreenContent } from "../components/course-search"

export interface CourseSearchScreenProps {
  navigation?: any
  route?: {
    params?: {
      initialQuery?: string
    }
  }
}

/**
 * CourseSearchScreen - Instructor's course search screen
 * 
 * This screen implements the Course Search design from Figma for instructor's course search.
 * Identical design to student version but with instructor-specific functionality and navigation.
 * 
 * Features:
 * - CourseSearchHeader: "Search Course" title with back button
 * - CourseSearchBarWithFilter: Search input with filter button
 * - RecentCourseSearchesSection: List of recent course searches with delete option
 * - Proper background color and spacing
 */
export function CourseSearchScreen({ navigation, route }: CourseSearchScreenProps) {
  console.log("🔍📚 Instructor CourseSearchScreen rendering...")

  const initialQuery = route?.params?.initialQuery

  const handleBackPress = () => {
    console.log("🔍📚 Instructor navigate back")
    if (navigation) {
      navigation.goBack()
    }
  }

  const handleSearch = (query: string) => {
    console.log("🔍📚 Instructor course search query:", query)
    // TODO: Implement instructor course search functionality
    // This could navigate to instructor course search results or update the current screen
    if (navigation) {
      navigation.navigate("CourseSearchResults", {
        query: query,
        userType: "instructor"
      })
    }
  }

  const handleFilterPress = () => {
    console.log("🔍📚 Instructor course filter pressed")
    if (navigation) {
      // Navigate to instructor course filter (to be implemented)
      navigation.navigate("CourseFilter", {
        userType: "instructor"
      })
    }
  }

  const handleRecentSearchPress = (searchTerm: string) => {
    console.log("🔍📚 Instructor recent course search pressed:", searchTerm)
    handleSearch(searchTerm)
  }

  const handleDeleteRecentSearch = (searchTerm: string) => {
    console.log("🔍📚 Instructor delete recent course search:", searchTerm)
    // TODO: Implement delete recent course search functionality for instructor
  }

  const handleSeeAllPress = () => {
    console.log("🔍📚 Instructor see all recent searches pressed")
    // TODO: Implement see all functionality for instructor
    if (navigation) {
      navigation.navigate("RecentSearches", {
        userType: "instructor",
        searchType: "courses"
      })
    }
  }

  return (
    <CourseSearchScreenContent
      onBackPress={handleBackPress}
      onSearch={handleSearch}
      onFilterPress={handleFilterPress}
      onRecentSearchPress={handleRecentSearchPress}
      onDeleteRecentSearch={handleDeleteRecentSearch}
      onSeeAllPress={handleSeeAllPress}
      initialQuery={initialQuery}
    />
  )
}
