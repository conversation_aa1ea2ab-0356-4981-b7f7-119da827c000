import React from "react"
import { MyCoursesScreenContent } from "../components/my-courses"
import { MyCoursesScreenProps, MyCourse, CourseStatusTab } from "../types"

/**
 * MyCoursesScreen - Instructor's teaching classes screen
 *
 * This screen implements the My Classes design from Figma for instructor's teaching classes.
 * Identical design to student version but with instructor-specific functionality and navigation.
 *
 * Features:
 * - MyCoursesHeader: "My Classes" title with back button
 * - MyCoursesSearchBar: Search with blue search button
 * - CourseStatusTabs: "Active" and "Archived" tabs for instructor workflow
 * - CoursesList: List of instructor's teaching class cards with progress bars
 */
export function MyCoursesScreen({ navigation }: MyCoursesScreenProps) {
  console.log("📚 Instructor MyCoursesScreen rendering...")

  const handleBackPress = () => {
    console.log("📚 Instructor navigate back")
    if (navigation) {
      navigation.goBack()
    }
  }

  const handleSearchPress = () => {
    console.log("📚 Instructor navigate to course search")
    if (navigation) {
      // Navigate to instructor course search (to be implemented)
      navigation.navigate("CourseSearch")
    }
  }

  const handleCoursePress = (course: MyCourse) => {
    console.log("📚 Instructor navigate to course management:", course.title)
    if (navigation) {
      // Navigate to instructor course detail/management
      navigation.navigate("MyCourseDetail", {
        courseId: course.id,
        course: course
      })
    }
  }

  const handleTabPress = (tab: CourseStatusTab) => {
    console.log("📚 Instructor tab changed to:", tab.title)
    // Handle tab analytics or other instructor-specific logic
  }

  const handleSearchTextChange = (text: string) => {
    console.log("📚 Instructor search text:", text)
    // Handle search logic or analytics for instructor courses
  }

  return (
    <MyCoursesScreenContent
      onBackPress={handleBackPress}
      onSearchPress={handleSearchPress}
      onCoursePress={handleCoursePress}
      onTabPress={handleTabPress}
      onSearchTextChange={handleSearchTextChange}
    />
  )
}
