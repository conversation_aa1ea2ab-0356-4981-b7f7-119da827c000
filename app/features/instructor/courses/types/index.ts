// Instructor Courses Types
// Based on student course types but adapted for instructor workflow

// Navigation types
export interface MyCoursesScreenProps {
  navigation?: any
}

export interface CourseSearchScreenProps {
  navigation?: any
  route?: {
    params?: {
      initialQuery?: string
    }
  }
}

// Course status tab for instructor workflow
export interface CourseStatusTab {
  id: string
  title: string
}

// Instructor's course (teaching course) - similar to student MyCourse but for teaching
export interface MyCourse {
  id: string
  title: string
  category: string
  rating: number
  duration: string
  progress: number // Course completion progress by students
  totalLessons: number
  completedLessons: number // Lessons completed by students
  image?: string
  isCompleted: boolean // Course is archived/completed
  isOngoing: boolean // Course is currently active
  // Additional instructor-specific fields from API
  code: string
  description: string
  level: string
  totalStudents: number
  activeStudents: number
  className?: string | null
  classStatus?: string | null
  canManage: boolean
  needsAttention: boolean
  attentionReason: string
  startDate: string
  endDate?: string | null
  price: number
  currency: string
  // Legacy fields for backward compatibility
  enrolledStudents?: number
  totalRevenue?: number
  lastUpdated?: string
  status?: "draft" | "published" | "archived"
}

// Instructor course management types
export interface InstructorCourse extends MyCourse {
  enrolledStudents: number
  totalRevenue: number
  lastUpdated: string
  status: "draft" | "published" | "archived"
  createdAt: string
  updatedAt: string
}

// Course creation/editing types
export interface CourseFormData {
  title: string
  description: string
  category: string
  difficulty: "beginner" | "intermediate" | "advanced"
  price: number
  duration: string
  thumbnail?: string
  tags: string[]
}

// Course analytics types
export interface CourseAnalytics {
  courseId: string
  enrollments: number
  completionRate: number
  averageRating: number
  totalRevenue: number
  monthlyEnrollments: number[]
  studentProgress: {
    completed: number
    inProgress: number
    notStarted: number
  }
}

// Student progress in instructor's course
export interface StudentProgress {
  studentId: string
  studentName: string
  enrolledAt: string
  progress: number
  completedLessons: number
  totalLessons: number
  lastActivity: string
  status: "active" | "completed" | "dropped"
}

// Course management actions
export type CourseAction = 
  | "edit"
  | "view_analytics" 
  | "manage_students"
  | "duplicate"
  | "archive"
  | "delete"
  | "publish"
  | "unpublish"

// Filter options for instructor courses
export interface InstructorCourseFilters {
  status?: "all" | "active" | "archived" | "draft"
  category?: string
  sortBy?: "newest" | "oldest" | "enrollments" | "revenue" | "rating"
  search?: string
}
