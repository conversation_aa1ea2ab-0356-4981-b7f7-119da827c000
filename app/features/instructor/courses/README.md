# Instructor Courses Feature

## Overview
This feature handles comprehensive course management for instructors including course listing, creation, editing, and performance tracking.

## Structure
- `components/` - React components (CourseList, CourseCard, CourseFilters, etc.)
- `hooks/` - Custom hooks for business logic (useCourses, useCourseManagement, etc.)
- `screens/` - Screen components (CoursesScreen, CourseEditScreen, etc.)
- `services/` - API and data services (courseService, analyticsService, etc.)
- `types/` - TypeScript type definitions (Course, CourseStatus, etc.)
- `utils/` - Utility functions (course helpers, validation, etc.)

## Key Components
- **CourseList** - Display all instructor courses with filters
- **CourseCard** - Individual course card with stats and actions
- **CourseFilters** - Filter by status, category, performance
- **BulkOperations** - Bulk actions for multiple courses
- **CourseActions** - Edit, preview, analytics, archive actions

## Features
- Course management (create, edit, duplicate, archive)
- Performance tracking (enrollment, revenue, ratings)
- Bulk operations (price updates, status changes)
- Course analytics and reporting
- Filter and search functionality

## Usage
```typescript
import { CourseList, CourseCard, CourseFilters } from '@/features/instructor/courses'
```
