import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { Screen, Text, Header, Button } from "@/components"
import { useStores } from "@/models"
import { InstructorTabScreenProps } from "@/navigators"
import { useAppTheme } from "@/utils/useAppTheme"

interface InstructorDashboardScreenProps extends InstructorTabScreenProps<"InstructorDashboard"> {}

export const InstructorDashboardScreen: FC<InstructorDashboardScreenProps> = observer(
  function InstructorDashboardScreen() {
    const { userStore, instructorStore, authenticationStore } = useStores()
    const { themed } = useAppTheme()

    const handleLogout = () => {
      authenticationStore.logout()
    }

    return (
      <Screen preset="scroll" safeAreaEdges={["top"]} contentContainerStyle={themed($container)}>
        <Header
          title={`Welcome, ${userStore.currentUser?.firstName || "Instructor"}!`}
          titleStyle={themed($headerTitle)}
        />

        <View style={themed($content)}>
          <Text preset="heading" text="Instructor Dashboard" style={themed($title)} />
          <Text
            text="This is your instructor dashboard. Here you can manage your courses, students, and assignments."
            style={themed($description)}
          />

          <View style={themed($statsContainer)}>
            <View style={themed($statCard)}>
              <Text preset="bold" text={`${instructorStore.totalCourses}`} style={themed($statNumber)} />
              <Text text="Total Courses" style={themed($statLabel)} />
            </View>

            <View style={themed($statCard)}>
              <Text preset="bold" text={`${instructorStore.totalStudents}`} style={themed($statNumber)} />
              <Text text="Total Students" style={themed($statLabel)} />
            </View>

            <View style={themed($statCard)}>
              <Text preset="bold" text={`${instructorStore.pendingGradingCount}`} style={themed($statNumber)} />
              <Text text="Pending Grading" style={themed($statLabel)} />
            </View>
          </View>

          <Button
            text="Logout"
            style={themed($logoutButton)}
            preset="default"
            onPress={handleLogout}
          />
        </View>
      </Screen>
    )
  }
)

const $container: ViewStyle = {
  flex: 1,
}

const $headerTitle = {
  fontSize: 18,
  fontWeight: "600" as const,
}

const $content: ViewStyle = {
  flex: 1,
  paddingHorizontal: 16,
  paddingTop: 16,
}

const $title = {
  marginBottom: 8,
}

const $description = {
  marginBottom: 24,
  opacity: 0.8,
}

const $statsContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  marginBottom: 24,
}

const $statCard: ViewStyle = {
  flex: 1,
  backgroundColor: "#F8F9FA",
  borderRadius: 12,
  padding: 16,
  marginHorizontal: 4,
  alignItems: "center",
}

const $statNumber = {
  fontSize: 24,
  marginBottom: 4,
}

const $statLabel = {
  fontSize: 12,
  opacity: 0.7,
  textAlign: "center" as const,
}

const $logoutButton: ViewStyle = {
  marginTop: 32,
  borderRadius: 12,
}
