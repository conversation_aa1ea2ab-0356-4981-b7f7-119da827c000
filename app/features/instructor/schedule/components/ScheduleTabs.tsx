import React, { useState } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { ScheduleTab } from "../types"

interface ScheduleTabsProps {
  tabs?: ScheduleTab[]
  onTabPress?: (tab: ScheduleTab) => void
}

const defaultTabs: ScheduleTab[] = [
  { id: "schedules", title: "My Lesson", isActive: true },
  { id: "calendar", title: "Calendar", isActive: false },
]

/**
 * ScheduleTabs - Bottom tab navigation for Schedule/Calendar
 *
 * Features:
 * - Two separate tabs: "My Lesson" and "Calendar"
 * - Active: #167F71 background, white text (My Lesson)
 * - Inactive: #E8F1FF background, #202244 text (Calendar)
 * - Individual 24px border radius, 48px height
 * - 170px width each, 20px gap between tabs
 * - Mulish 800, 15px font
 * - Matches exact Figma design at node-id=56-921
 */
export function ScheduleTabs({
  tabs = defaultTabs,
  onTabPress
}: ScheduleTabsProps) {
  const { themed } = useAppTheme()
  const [selectedTab, setSelectedTab] = useState<string>("schedules")

  const handleTabPress = (tab: ScheduleTab) => {
    console.log("📅 ScheduleTabs tab pressed:", tab.title)
    setSelectedTab(tab.id)
    onTabPress?.(tab)
  }

  return (
    <View style={themed($container)}>
      <View style={themed($tabsContainer)}>
        {tabs.map((tab) => {
          const isSelected = selectedTab === tab.id

          return (
            <TouchableOpacity
              key={tab.id}
              style={themed([
                $tab,
                isSelected && $activeTab
              ])}
              onPress={() => handleTabPress(tab)}
            >
              <Text style={themed([
                $tabText,
                isSelected && $activeTabText
              ])}>
                {tab.title}
              </Text>
            </TouchableOpacity>
          )
        })}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34, // Exact from Figma: x: 34
  marginBottom: 20, // Space to filters below (204-115-48=41, use 20 for better spacing)
  backgroundColor: "transparent",
}

const $tabsContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between", // Space between separate tabs
  alignItems: "center",
  height: 48, // Exact height from Figma: 48px
  width: 360, // Exact width from Figma: 360px
  alignSelf: "center", // Center the container
}

const $tab: ViewStyle = {
  width: 170, // Exact width from Figma: 170px
  height: 48, // Exact height from Figma: 48px
  backgroundColor: "#E8F1FF", // Exact inactive background from Figma
  borderRadius: 24, // Exact border radius from Figma: 24px
  alignItems: "center",
  justifyContent: "center",
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 }, // Subtle shadow for individual tabs
  shadowOpacity: 0.05,
  shadowRadius: 4,
  elevation: 2,
}

const $activeTab: ViewStyle = {
  backgroundColor: "#23408B", // Main brand color from design guidelines
}

const $tabText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for navigation text
  fontSize: 15,
  lineHeight: 19,
  color: "#666666", // App design guideline color
  textAlign: "center",
}

const $activeTabText: TextStyle = {
  color: "#FFFFFF", // Exact active text color from Figma
}
