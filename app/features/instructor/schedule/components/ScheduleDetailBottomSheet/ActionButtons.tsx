import React from "react"
import { View, Text, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { ScheduleItem } from "../../types"

interface ActionButtonsProps {
  item: ScheduleItem
  onCheckin?: (item: ScheduleItem) => void
  onCreateQuiz?: (item: ScheduleItem) => void
}

/**
 * ActionButtons - Action buttons for instructor schedule item
 *
 * Features:
 * - Checkin button (secondary style)
 * - Create Quiz button (primary style) - for instructor
 * - Responsive layout with equal width
 * - Follows app design guidelines
 */
export function ActionButtons({ item, onCheckin, onCreateQuiz }: ActionButtonsProps) {
  const { themed } = useAppTheme()

  const handleCheckin = () => {
    console.log("📅 Checkin pressed:", item.courseTitle)
    onCheckin?.(item)
  }

  const handleCreateQuiz = () => {
    console.log("📅 Tạo Quiz pressed:", item.courseTitle)
    onCreateQuiz?.(item)
  }

  return (
    <View style={themed($actionButtons)}>
      <TouchableOpacity
        style={themed($secondaryButton)}
        onPress={handleCheckin}
      >
        <Text style={themed($secondaryButtonText)}>Checkin</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={themed($primaryButton)}
        onPress={handleCreateQuiz}
      >
        <Text style={themed($primaryButtonText)}>Tạo Quiz</Text>
      </TouchableOpacity>
    </View>
  )
}

// Styles
const $actionButtons: ViewStyle = {
  flexDirection: "row",
  gap: 12,
}

const $secondaryButton: ViewStyle = {
  flex: 1,
  backgroundColor: "#E8F1FF",
  borderRadius: 30,
  paddingVertical: 16,
  paddingHorizontal: 24,
  alignItems: "center",
  borderWidth: 2,
  borderColor: "rgba(180, 189, 196, 0.2)",
}

const $secondaryButtonText: TextStyle = {
  fontFamily: "Lexend Deca",
  fontWeight: "300", // MANDATORY light weight for button text
  fontSize: 16,
  lineHeight: 22,
  color: "#202244",
}

const $primaryButton: ViewStyle = {
  flex: 1,
  backgroundColor: "#0961F5",
  borderRadius: 30,
  paddingVertical: 16,
  paddingHorizontal: 24,
  alignItems: "center",
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 4,
}

const $primaryButtonText: TextStyle = {
  fontFamily: "Lexend Deca",
  fontWeight: "300", // MANDATORY light weight for button text
  fontSize: 16,
  lineHeight: 22,
  color: "#FFFFFF",
}
