import React from "react"
import { View, ViewStyle, ScrollView, RefreshControl, ActivityIndicator, TextStyle } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { ScheduleItem } from "./ScheduleItem"
import { ScheduleItem as ScheduleItemType } from "../types"

interface ScheduleListProps {
  items: ScheduleItemType[]
  onItemPress: (item: ScheduleItemType) => void
  loading?: boolean
  refreshing?: boolean
  onRefresh?: () => void
}

/**
 * ScheduleList - List of schedule items
 *
 * Features:
 * - Scrollable list of schedule items
 * - Pull-to-refresh functionality
 * - Loading states
 * - Empty state handling
 */
export function ScheduleList({
  items,
  onItemPress,
  loading = false,
  refreshing = false,
  onRefresh
}: ScheduleListProps) {
  const { themed } = useAppTheme()

  // Loading state
  if (loading && items.length === 0) {
    return (
      <View style={themed($loadingContainer)}>
        <ActivityIndicator size="large" color="#23408B" />
        <Text style={$loadingText}>Loading lessons...</Text>
      </View>
    )
  }

  // Empty state
  if (!loading && (!items || items.length === 0)) {
    return (
      <View style={themed($emptyContainer)}>
        <Text style={$emptyTitle}>No Lessons Scheduled</Text>
        <Text style={$emptyMessage}>
          You don't have any lessons scheduled yet. You may need to be assigned to classes first.
        </Text>
      </View>
    )
  }

  return (
    <ScrollView
      style={themed($container)}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={["#23408B"]}
            tintColor="#23408B"
          />
        ) : undefined
      }
    >
      {items.map((item) => (
        <ScheduleItem
          key={item.id}
          item={item}
          onPress={onItemPress}
        />
      ))}
    </ScrollView>
  )
}

const $container: ViewStyle = {
  paddingTop: 12, // Reduced space from tabs to first card
  paddingBottom: 100, // Extra padding for bottom navigation
}

const $loadingContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 16,
  paddingVertical: 40,
}

const $loadingText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for body text
  fontWeight: "300",
  fontSize: 14,
  lineHeight: 18,
  color: "#A0A4AB",
  marginTop: 16,
  textAlign: "center",
}

const $emptyContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 16, // Updated to 16px for consistent spacing
  paddingVertical: 40,
}

const $emptyTitle: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight - for empty state titles
  fontWeight: "600",
  fontSize: 18,
  lineHeight: 24,
  color: "#202244",
  textAlign: "center",
  marginBottom: 8,
}

const $emptyMessage: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for body text
  fontWeight: "300",
  fontSize: 14,
  lineHeight: 20,
  color: "#A0A4AB",
  textAlign: "center",
}
