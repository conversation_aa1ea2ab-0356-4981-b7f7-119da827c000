import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, ScrollView } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { ScheduleFilter } from "../types"

interface ScheduleFiltersProps {
  filters: ScheduleFilter[]
  onFilterPress: (filter: ScheduleFilter) => void
}

/**
 * ScheduleFilters - Filter tabs component for Schedule screen
 *
 * Features:
 * - Horizontal scrollable filter tabs (All, Today, Week, Month)
 * - Active: #167F71 background, white text
 * - Inactive: #E8F1FF background, #202244 text
 * - 15px border radius, exact spacing from Figma
 * - Matches Figma design filter section at node-id=56-797
 */
export function ScheduleFilters({ filters, onFilterPress }: ScheduleFiltersProps) {
  const { themed } = useAppTheme()

  const handleFilterPress = (filter: ScheduleFilter) => {
    console.log("📅 ScheduleFilters filter pressed:", filter.title)
    onFilterPress(filter)
  }

  return (
    <View style={themed($container)}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={themed($scrollContent)}
      >
        {filters.map((filter, index) => (
          <TouchableOpacity
            key={filter.id}
            style={themed([
              $filterButton,
              filter.isActive && $activeFilterButton,
              index === 0 && $firstFilter
            ])}
            onPress={() => handleFilterPress(filter)}
          >
            <Text style={themed([
              $filterText,
              filter.isActive && $activeFilterText
            ])}>
              {filter.title}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 35, // Exact from Figma: x: 35
  marginBottom: 22, // Space to first schedule item (256-204-30=22)
}

const $scrollContent: ViewStyle = {
  paddingRight: 20, // Extra padding for last item
}

const $filterButton: ViewStyle = {
  backgroundColor: "#E8F1FF", // Exact inactive background from Figma
  borderRadius: 15, // Exact border radius from Figma
  paddingHorizontal: 20, // Horizontal padding
  paddingVertical: 7, // Vertical padding for 30px height
  marginRight: 10, // Space between filters
  height: 30, // Exact height from Figma
  alignItems: "center",
  justifyContent: "center",
}

const $activeFilterButton: ViewStyle = {
  backgroundColor: "#167F71", // Exact active background from Figma
}

const $firstFilter: ViewStyle = {
  width: 90, // Exact width for "All" button from Figma
}

const $filterText: TextStyle = {
  fontFamily: "Montserrat", // App design guideline font
  fontWeight: "300", // Light weight for filter text
  fontSize: 13,
  lineHeight: 16,
  color: "#666666", // App design guideline color
  textAlign: "center",
}

const $activeFilterText: TextStyle = {
  color: "#FFFFFF", // Exact active text color from Figma
}
