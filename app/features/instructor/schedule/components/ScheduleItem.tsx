import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { ScheduleItem as ScheduleItemType } from "../types"

interface ScheduleItemProps {
  item: ScheduleItemType
  onPress: (item: ScheduleItemType) => void
}

/**
 * ScheduleItem - Individual schedule item component
 *
 * Features:
 * - 46x46px black circular avatar (exact from Figma)
 * - Course title (Jost 600, 16px) and subject text (Mulish 700, 13px)
 * - Lesson and date/time info with icons (Mulish 800, 12px)
 * - More options icon (3-dot menu)
 * - White background with 16px border radius and shadow
 * - 21px padding, exact spacing from Figma
 * - Matches Figma design schedule cards at node-id=56-797
 */
export function ScheduleItem({ item, onPress }: ScheduleItemProps) {
  const { themed } = useAppTheme()

  const handlePress = () => {
    console.log("📅 ScheduleItem pressed:", item.courseTitle)
    onPress(item)
  }

  const handleMorePress = () => {
    console.log("📅 ScheduleItem more options pressed:", item.id)
    // Handle more options menu
  }

  return (
    <TouchableOpacity style={themed($container)} onPress={handlePress}>
      <View style={themed($card)}>
        {/* Avatar */}
        <View style={themed($avatar)} />

        {/* Content */}
        <View style={themed($content)}>
          {/* Header with title */}
          <View style={themed($header)}>
            <Text style={themed($courseTitle)} numberOfLines={1}>
              {item.courseTitle}
            </Text>
          </View>

          {/* Subject */}
          <Text style={themed($subject)} numberOfLines={2}>
            {item.subject}
          </Text>

          {/* Bottom info */}
          <View style={themed($bottomInfo)}>
            {/* Lesson info */}
            <View style={themed($infoGroup)}>
              <View style={themed($iconContainer)}>
                <Text style={themed($iconSymbol)}>●</Text>
              </View>
              <Text style={themed($infoText)}>{item.lesson}</Text>
            </View>

            {/* Date/time info */}
            <View style={themed($infoGroup)}>
              <View style={themed($iconContainer)}>
                <Text style={themed($iconSymbol)}>○</Text>
              </View>
              <Text style={themed($infoText)}>{item.dateTime}</Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34, // Exact from Figma: x: 34
  marginBottom: 12, // Reduced space between items for tighter layout
  minHeight: 100, // Ensure minimum height
}

const $card: ViewStyle = {
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 16, // Exact border radius from Figma
  padding: 12, // Reduced padding for tighter spacing
  flexDirection: "row",
  alignItems: "flex-start",
  borderWidth: 0.5, // Thin gray border instead of shadow
  borderColor: "#E2E8F0", // Light gray border color
}

const $avatar: ViewStyle = {
  width: 46, // Exact size from Figma
  height: 46, // Exact size from Figma
  borderRadius: 8, // Square with rounded corners instead of circle
  backgroundColor: "#000000", // Exact black background from Figma
  marginRight: 12, // Space to content (79-67=12)
}

const $content: ViewStyle = {
  flex: 1,
}

const $header: ViewStyle = {
  flexDirection: "row",
  alignItems: "flex-start",
  marginBottom: 11, // Space to subject
}

const $courseTitle: TextStyle = {
  fontFamily: "Montserrat", // App design guideline font
  fontWeight: "400", // Medium weight for titles
  fontSize: 16,
  lineHeight: 23,
  color: "#1A1A1A", // App design guideline color
}

const $moreButton: ViewStyle = {
  padding: 2, // Touch area
}

const $subject: TextStyle = {
  fontFamily: "Lexend Deca", // App design guideline font
  fontWeight: "300", // Light weight for descriptions
  fontSize: 13,
  lineHeight: 16,
  color: "#666666", // App design guideline color
  marginBottom: 18, // Space to bottom info (exact from Figma)
}

const $bottomInfo: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center", // Back to center for proper icon alignment
}

const $infoGroup: ViewStyle = {
  flexDirection: "row",
  alignItems: "center", // Back to center for proper icon alignment
}

const $infoText: TextStyle = {
  fontFamily: "Lexend Deca", // App design guideline font
  fontWeight: "300", // Light weight for info text
  fontSize: 12,
  lineHeight: 16, // Match icon height for better alignment
  color: "#666666", // App design guideline color
  marginLeft: 8, // Space from icon
}

const $iconContainer: ViewStyle = {
  width: 16,
  height: 16,
  justifyContent: "center",
  alignItems: "center",
}

const $iconSymbol: TextStyle = {
  fontFamily: "Lexend Deca",
  fontSize: 12,
  lineHeight: 16,
  color: "#666666",
  textAlign: "center",
}

const $moreIcon: TextStyle = {
  fontSize: 18,
  lineHeight: 18,
  color: "#E8F1FF", // Same color as original icon
  fontWeight: "bold",
}
