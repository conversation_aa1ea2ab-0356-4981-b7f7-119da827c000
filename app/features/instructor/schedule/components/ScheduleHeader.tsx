import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface ScheduleHeaderProps {
  onBackPress?: () => void
}

/**
 * ScheduleHeader - Header component for Schedule screen
 *
 * Features:
 * - Back button with arrow icon (26.21x20px from Figma)
 * - "Schedule" title (Jost 600, 21px from Figma)
 * - Exact spacing: 35px horizontal padding
 * - Matches Figma design at node-id=56-797
 */
export function ScheduleHeader({ onBackPress }: ScheduleHeaderProps) {
  const { themed } = useAppTheme()

  const handleBackPress = () => {
    console.log("📅 ScheduleHeader back button pressed")
    onBackPress?.()
  }

  return (
    <View style={themed($container)}>
      <TouchableOpacity style={themed($backButton)} onPress={handleBackPress}>
        <Icon icon="caretLeft" size={26} color="#202244" />
      </TouchableOpacity>

      <Text style={themed($title)}>Schedule</Text>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35, // Exact from Figma: x: 35
  paddingTop: 25, // Spacing from top
  paddingBottom: 20, // Spacing to filters
  backgroundColor: "#F5F9FF", // Match screen background
}

const $backButton: ViewStyle = {
  marginRight: 12, // Space between back button and title
  padding: 4, // Touch area
  width: 26.21, // Exact from Figma
  height: 20, // Exact from Figma
  justifyContent: "center",
  alignItems: "center",
}

const $title: TextStyle = {
  fontFamily: "Montserrat", // App design guideline font
  fontWeight: "400", // Slightly bolder for header
  fontSize: 21,
  lineHeight: 30,
  color: "#1A1A1A", // App design guideline color
  textAlign: "left",
}
