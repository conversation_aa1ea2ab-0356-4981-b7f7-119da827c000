import React from "react"
import { NotificationsScreenContent } from "../components/notifications"
import { NotificationsScreenProps, Notification } from "../types"

/**
 * InstructorNotificationsScreen - Instructor's notifications screen
 * 
 * This screen implements the same Notifications design from Figma as student inbox:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-2131
 * 
 * Cloned from student InboxScreen but adapted for instructor workflow.
 * 
 * Features:
 * - NotificationsHeader: "Notifications" title with back button
 * - NotificationGroups: Grouped by date (Today, Yesterday, Nov 20, 2022)
 * - NotificationCards: Cards with avatar, title, and description (instructor content)
 */
export function InstructorNotificationsScreen({ navigation }: NotificationsScreenProps) {
  console.log("📬 InstructorNotificationsScreen rendering...")

  const handleBackPress = () => {
    console.log("📬 Navigate back")
    if (navigation) {
      navigation.goBack()
    }
  }

  const handleNotificationPress = (notification: Notification) => {
    console.log("📬 Navigate to notification details:", notification.title)
    // navigation?.navigate("NotificationDetails", { notificationId: notification.id })
  }

  return (
    <NotificationsScreenContent
      onBackPress={handleBackPress}
      onNotificationPress={handleNotificationPress}
    />
  )
}
