/**
 * Instructor Notifications Feature Types
 *
 * Type definitions for the instructor notifications feature.
 * Cloned from student inbox but adapted for instructor workflow.
 * Includes notification data, grouping, and interaction types.
 */

export interface Notification {
  /**
   * Unique notification identifier
   */
  id: string
  /**
   * Notification title
   */
  title: string
  /**
   * Notification description/message
   */
  description: string
  /**
   * Notification type
   */
  type: NotificationType
  /**
   * Whether notification is read
   */
  isRead: boolean
  /**
   * Notification creation date
   */
  createdAt: string
  /**
   * Avatar/icon for the notification
   */
  avatar?: string
  /**
   * Background color for avatar
   */
  avatarColor?: string
}

export interface NotificationGroup {
  /**
   * Group identifier (date)
   */
  id: string
  /**
   * Group title (Today, Yesterday, etc.)
   */
  title: string
  /**
   * Notifications in this group
   */
  notifications: Notification[]
}

export type NotificationType = 
  | "course"
  | "student" 
  | "grading"
  | "account"
  | "system"
  | "schedule"

export interface NotificationsScreenProps {
  navigation?: any
}
