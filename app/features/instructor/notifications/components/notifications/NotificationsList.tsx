import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { NotificationGroup } from "./NotificationGroup"
import { NotificationGroup as NotificationGroupType, Notification } from "../../types"

interface NotificationsListProps {
  groups: NotificationGroupType[]
  onNotificationPress?: (notification: Notification) => void
}

export function NotificationsList({ groups, onNotificationPress }: NotificationsListProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {groups.map((group) => (
        <NotificationGroup
          key={group.id}
          group={group}
          onNotificationPress={onNotificationPress}
        />
      ))}
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
  paddingBottom: 40,
}
