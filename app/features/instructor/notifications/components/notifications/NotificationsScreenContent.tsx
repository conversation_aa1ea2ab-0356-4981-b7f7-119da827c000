import React from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import {
  NotificationsHeader,
  NotificationsList
} from "./index"
import { NotificationGroup, Notification } from "../../types"

interface NotificationsScreenContentProps {
  onBackPress?: () => void
  onNotificationPress?: (notification: Notification) => void
}

// Mock data adapted for instructor workflow
const mockNotificationGroups: NotificationGroup[] = [
  {
    id: "today",
    title: "Today",
    notifications: [
      {
        id: "1",
        title: "New Student Enrolled.!",
        description: "<PERSON> enrolled in React Native Course..",
        type: "student",
        isRead: false,
        createdAt: "2024-01-25T10:00:00Z",
      },
      {
        id: "2", 
        title: "Assignment Submitted.!",
        description: "<PERSON> submitted Assignment 3 for grading…",
        type: "grading",
        isRead: false,
        createdAt: "2024-01-25T09:00:00Z",
      },
      {
        id: "3",
        title: "Class Schedule Updated",
        description: "Your React Native class has been rescheduled.",
        type: "schedule",
        isRead: false,
        createdAt: "2024-01-25T08:00:00Z",
      },
    ],
  },
  {
    id: "yesterday",
    title: "Yesterday",
    notifications: [
      {
        id: "4",
        title: "Course Analytics Ready.!",
        description: "Monthly analytics report is now available.!",
        type: "course",
        isRead: true,
        createdAt: "2024-01-24T15:00:00Z",
      },
    ],
  },
  {
    id: "nov-20-2022",
    title: "Nov 20, 2022",
    notifications: [
      {
        id: "5",
        title: "Instructor Account Verified.!",
        description: "Your Instructor Account has been Verified.",
        type: "account",
        isRead: true,
        createdAt: "2022-11-20T12:00:00Z",
      },
    ],
  },
]

/**
 * NotificationsScreenContent - Instructor Notifications screen based on Figma design
 * 
 * This component implements the same Notifications design from Figma as student inbox:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-2131
 * 
 * Cloned from student InboxScreenContent but adapted for instructor workflow.
 * 
 * Features:
 * - NotificationsHeader: "Notifications" title with back button
 * - NotificationGroups: Grouped by date (Today, Yesterday, etc.)
 * - NotificationCards: Cards with avatar, title, and description (instructor content)
 */
export function NotificationsScreenContent({
  onBackPress,
  onNotificationPress
}: NotificationsScreenContentProps) {
  const { themed } = useAppTheme()

  console.log("📬 NotificationsScreenContent rendering...")

  const handleBackPress = () => {
    console.log("📬 Back pressed")
    onBackPress?.()
  }

  const handleNotificationPress = (notification: Notification) => {
    console.log("📬 Notification pressed:", notification.title)
    onNotificationPress?.(notification)
  }

  console.log("📬 Notification groups:", mockNotificationGroups.length)

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
    >
      <View style={themed($content)}>
        {/* Header with back button */}
        <NotificationsHeader onBackPress={handleBackPress} />
        
        {/* Notifications grouped by date */}
        <NotificationsList
          groups={mockNotificationGroups}
          onNotificationPress={handleNotificationPress}
        />
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  flex: 1,
}
