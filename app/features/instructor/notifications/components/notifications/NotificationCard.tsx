import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { Notification } from "../../types"

interface NotificationCardProps {
  notification: Notification
  onPress?: (notification: Notification) => void
}

export function NotificationCard({ notification, onPress }: NotificationCardProps) {
  const { themed } = useAppTheme()

  // Get avatar background color based on notification type
  const getAvatarColor = () => {
    switch (notification.type) {
      case "course":
        return "#167F71"
      case "payment":
        return "#FF6B00"
      case "offer":
        return "#FCCB40"
      case "account":
        return "#0961F5"
      default:
        return "#202244"
    }
  }

  // Get icon based on notification type
  const getIcon = () => {
    switch (notification.type) {
      case "course":
        return "book"
      case "payment":
        return "heart" // Using heart as placeholder for payment
      case "offer":
        return "star"
      case "account":
        return "settings"
      default:
        return "bell"
    }
  }

  return (
    <TouchableOpacity
      style={themed($container)}
      onPress={() => onPress?.(notification)}
      activeOpacity={0.8}
    >
      {/* Avatar Circle */}
      <View style={themed([
        $avatar,
        { backgroundColor: getAvatarColor() }
      ])}>
        <Icon
          icon={getIcon()}
          size={20} // Reduced from 24 to 20 for better proportion
          color="#FFFFFF"
        />
      </View>

      {/* Content */}
      <View style={themed($content)}>
        <Text style={themed($title)}>{notification.title}</Text>
        <Text style={themed($description)}>{notification.description}</Text>
      </View>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#E8F1FF",
  borderRadius: 18,
  paddingVertical: 21,
  paddingHorizontal: 18,
  marginBottom: 12,
  borderWidth: 2,
  borderColor: "rgba(180, 189, 196, 0.2)",
}

const $avatar: ViewStyle = {
  width: 44, // Reduced from 52 to 44 for better proportion
  height: 44, // Reduced from 52 to 44 for better proportion
  borderRadius: 22, // Half of width/height for perfect circle
  justifyContent: "center",
  alignItems: "center",
  marginRight: 8,
}

const $content: ViewStyle = {
  flex: 1,
}

const $title: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for titles
  fontSize: 16, // Reduced from 19 to 16 per design guidelines (md size)
  lineHeight: 23, // Adjusted line height proportionally
  color: "#202244",
  marginBottom: 5,
}

const $description: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY for descriptions, body text, button text
  fontSize: 14,
  lineHeight: 18,
  color: "#545454",
}
