import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { NotificationCard } from "./NotificationCard"
import { NotificationGroup as NotificationGroupType, Notification } from "../../types"

interface NotificationGroupProps {
  group: NotificationGroupType
  onNotificationPress?: (notification: Notification) => void
}

export function NotificationGroup({ group, onNotificationPress }: NotificationGroupProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Group Title */}
      <Text style={themed($groupTitle)}>{group.title}</Text>

      {/* Notifications */}
      <View style={themed($notificationsContainer)}>
        {group.notifications.map((notification) => (
          <NotificationCard
            key={notification.id}
            notification={notification}
            onPress={onNotificationPress}
          />
        ))}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  marginBottom: 30,
}

const $groupTitle: TextStyle = {
  fontFamily: "lexendDecaBold", // 700 weight for group titles
  fontSize: 16,
  lineHeight: 23,
  color: "#202244",
  marginBottom: 20,
}

const $notificationsContainer: ViewStyle = {
  // No additional styling needed
}
