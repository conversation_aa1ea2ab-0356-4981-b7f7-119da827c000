# Instructor Assessment Feature

## Overview
This feature handles comprehensive assessment creation including quizzes, exams, assignments, and question bank management.

## Structure
- `components/` - React components (QuizBuilder, QuestionBank, AssignmentCreator, etc.)
- `hooks/` - Custom hooks for business logic (useAssessment, useQuestionBank, etc.)
- `screens/` - Screen components (AssessmentScreen, QuizBuilderScreen, etc.)
- `services/` - API and data services (assessmentService, questionService, etc.)
- `types/` - TypeScript type definitions (Question, Quiz, Assignment, etc.)
- `utils/` - Utility functions (assessment helpers, validation, etc.)

## Key Components
- **QuizBuilder** - Advanced quiz creation with multiple question types
- **QuestionBank** - Centralized question management and categorization
- **AssignmentCreator** - Project and assignment creation tools
- **ExamBuilder** - Comprehensive exam creation with security features
- **FeedbackSystem** - Rich feedback and explanation tools

## Features
- Advanced quiz builder with diverse question types
- Question bank management with categorization and tagging
- Assignment and project creation
- Exam builder with security and proctoring features
- Rich feedback system with explanations and hints
- Import/export capabilities for questions and assessments

## Usage
```typescript
import { QuizBuilder, QuestionBank, AssignmentCreator } from '@/features/instructor/assessment'
```
