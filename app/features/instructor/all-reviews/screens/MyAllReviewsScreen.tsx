/**
 * MyAllReviewsScreen - Instructor Version
 *
 * Main Review Management screen for instructor's course reviews
 * Based on student MyAllReviewsScreen but with instructor-specific features:
 * - Student review management with reply functionality
 * - Review analytics and performance tracking
 * - Feedback moderation and bulk operations
 * - Instructor-specific review actions
 *
 * Following the same pattern as student version with:
 * - useMyAllReviewsData: Manages all review data and analytics
 * - useMyAllReviewsHandlers: Manages all interaction handlers
 * - MyAllReviewsScreenContent: Contains all the scrollable content sections
 *
 * Benefits of this architecture:
 * - Easier to maintain and update
 * - Better separation of concerns
 * - Reusable hooks and components
 * - Cleaner code organization
 * - Easier testing
 * - Independent from student version for future development
 */

import React from "react"
import { Screen } from "app/components"
import { colors } from "app/theme"
import { MyAllReviewsScreenContent } from "./components/MyAllReviewsScreenContent"
import { useMyAllReviewsData } from "./hooks/useMyAllReviewsData"
import { useMyAllReviewsHandlers } from "./hooks/useMyAllReviewsHandlers"
import type { MyAllReviewsScreenProps } from "../types"

/**
 * MyAllReviewsScreen - Instructor's review management screen
 * 
 * This screen implements the review management interface for instructors.
 * Identical design structure to student version but with instructor-specific content.
 * 
 * Features:
 * - Student reviews with reply and moderation options
 * - Review analytics and performance metrics
 * - Bulk reply and export functionality
 * - Filter and sort options for review management
 * - Instructor-specific actions and navigation
 */
export function MyAllReviewsScreen({ navigation, route }: MyAllReviewsScreenProps) {
  console.log("👨‍🏫 Instructor MyAllReviewsScreen rendering...")

  // Get review data using the data hook
  const data = useMyAllReviewsData({
    courseId: route?.params?.courseId,
    course: route?.params?.course
  })

  // Get all handlers using the handlers hook
  const handlers = useMyAllReviewsHandlers({
    courseId: route?.params?.courseId,
    course: route?.params?.course,
    navigation,
    onBackPress: () => {
      console.log("👨‍🏫 Instructor All Reviews: Navigate back")
      if (navigation) {
        navigation.goBack()
      }
    }
  })

  console.log("👨‍🏫 Instructor Review Data:", data)
  console.log("👨‍🏫 Instructor Review Handlers:", handlers)

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor={colors.palette.neutral100}
    >
      {/* Main Content - Scrollable sections with review management features */}
      <MyAllReviewsScreenContent
        data={data}
        handlers={handlers}
      />
    </Screen>
  )
}
