/**
 * useMyAllReviewsData Hook - Instructor Version
 * 
 * Manages all data for Instructor's Review Management screen including:
 * - Student reviews with reply functionality
 * - Review analytics and performance metrics
 * - Feedback moderation and management
 * - Instructor-specific review data
 */

import { useState, useEffect } from "react"
import type { MyAllReviewsData, InstructorReview, ReviewAnalytics, UseMyAllReviewsDataParams } from "../../types"

export function useMyAllReviewsData({ courseId, course }: UseMyAllReviewsDataParams): MyAllReviewsData {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>()

  // Mock data for Instructor's Review Management
  const mockInstructorReviews: InstructorReview[] = [
    {
      id: "review-1",
      student: {
        name: "<PERSON>",
        avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face",
        enrolledDate: "2024-01-05",
      },
      rating: 5,
      comment: "Excellent course! The advanced patterns section was particularly helpful. The instructor explains complex concepts very clearly.",
      date: "2024-01-20",
      likes: 12,
      isLiked: false,
      instructorReply: "Thank you Sarah! I'm glad you found the patterns section useful. Keep up the great work!",
      replyDate: "2024-01-21",
      isReplied: true,
      isModerated: false,
      isFlagged: false,
      helpfulCount: 8,
      reportCount: 0,
      status: "active",
    },
    {
      id: "review-2",
      student: {
        name: "Mike Chen",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face",
        enrolledDate: "2024-01-08",
      },
      rating: 4,
      comment: "Great content overall, would love more examples on testing. The course structure is well organized.",
      date: "2024-01-18",
      likes: 7,
      isLiked: false,
      instructorReply: undefined,
      replyDate: undefined,
      isReplied: false,
      isModerated: false,
      isFlagged: false,
      helpfulCount: 5,
      reportCount: 0,
      status: "active",
    },
    {
      id: "review-3",
      student: {
        name: "Emily Davis",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face",
        enrolledDate: "2024-01-12",
      },
      rating: 5,
      comment: "Amazing course! The hands-on projects really helped me understand the concepts. Highly recommended!",
      date: "2024-01-22",
      likes: 15,
      isLiked: false,
      instructorReply: "Thank you Emily! I'm thrilled that the hands-on approach worked well for you. Best of luck with your projects!",
      replyDate: "2024-01-22",
      isReplied: true,
      isModerated: false,
      isFlagged: false,
      helpfulCount: 12,
      reportCount: 0,
      status: "active",
    },
    {
      id: "review-4",
      student: {
        name: "David Wilson",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face",
        enrolledDate: "2024-01-15",
      },
      rating: 3,
      comment: "Good course but could use more advanced topics. The pace is a bit slow for experienced developers.",
      date: "2024-01-25",
      likes: 3,
      isLiked: false,
      instructorReply: undefined,
      replyDate: undefined,
      isReplied: false,
      isModerated: false,
      isFlagged: false,
      helpfulCount: 2,
      reportCount: 0,
      status: "active",
    },
    {
      id: "review-5",
      student: {
        name: "Lisa Anderson",
        avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=50&h=50&fit=crop&crop=face",
        enrolledDate: "2024-01-10",
      },
      rating: 5,
      comment: "Perfect for beginners! The step-by-step approach made everything easy to follow. Thank you!",
      date: "2024-01-24",
      likes: 9,
      isLiked: false,
      instructorReply: "So happy to hear that Lisa! The step-by-step approach is designed exactly for that. Keep learning!",
      replyDate: "2024-01-24",
      isReplied: true,
      isModerated: false,
      isFlagged: false,
      helpfulCount: 7,
      reportCount: 0,
      status: "active",
    },
    {
      id: "review-6",
      student: {
        name: "Alex Thompson",
        avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=50&h=50&fit=crop&crop=face",
        enrolledDate: "2024-01-18",
      },
      rating: 2,
      comment: "Course content is outdated. Some examples don't work with current versions.",
      date: "2024-01-26",
      likes: 1,
      isLiked: false,
      instructorReply: undefined,
      replyDate: undefined,
      isReplied: false,
      isModerated: false,
      isFlagged: true,
      helpfulCount: 0,
      reportCount: 1,
      status: "flagged",
    }
  ]

  // Calculate review analytics
  const reviewAnalytics: ReviewAnalytics = {
    totalReviews: mockInstructorReviews.length,
    averageRating: 4.2,
    ratingDistribution: {
      5: mockInstructorReviews.filter(r => r.rating === 5).length,
      4: mockInstructorReviews.filter(r => r.rating === 4).length,
      3: mockInstructorReviews.filter(r => r.rating === 3).length,
      2: mockInstructorReviews.filter(r => r.rating === 2).length,
      1: mockInstructorReviews.filter(r => r.rating === 1).length,
    },
    repliedReviews: mockInstructorReviews.filter(r => r.isReplied).length,
    pendingReplies: mockInstructorReviews.filter(r => !r.isReplied).length,
    flaggedReviews: mockInstructorReviews.filter(r => r.isFlagged).length,
    helpfulReplies: mockInstructorReviews.filter(r => r.isReplied && r.helpfulCount > 5).length,
    responseRate: 60, // 3 out of 5 replied
    averageResponseTime: 4.5, // hours
  }

  // Simulate loading state
  useEffect(() => {
    if (courseId) {
      setIsLoading(true)
      const timer = setTimeout(() => {
        setIsLoading(false)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [courseId])

  return {
    courseName: course?.title || "Advanced React Development",
    totalReviews: reviewAnalytics.totalReviews,
    averageRating: reviewAnalytics.averageRating,
    reviews: mockInstructorReviews,
    analytics: reviewAnalytics,
    isLoading,
    error,
  }
}
