/**
 * MyAllReviewsContent - Instructor Version
 * 
 * Main content section for Instructor's Review Management
 * Based on student version but adapted for instructor workflow
 */

import React from "react"
import { View, ViewStyle, ScrollView } from "react-native"
import { useAppTheme } from "app/utils/useAppTheme"
import { MyAllReviewsCard } from "../cards/MyAllReviewsCard"
import type { MyAllReviewsContentProps } from "../../../types"

/**
 * MyAllReviewsContent - Instructor's review management content
 * 
 * Features:
 * - Scrollable list of student reviews with management options
 * - Each review card shows reply functionality and moderation
 * - Instructor-specific actions and status indicators
 * - Bottom spacing for fixed action button
 */
export function MyAllReviewsContent({
  reviews,
  onReviewReply,
  onReviewModerate,
  onBulkReply
}: MyAllReviewsContentProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <ScrollView
        style={themed($scrollView)}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        {/* Reviews List */}
        {reviews.map((review) => (
          <MyAllReviewsCard
            key={review.id}
            review={review}
            onReply={onReviewReply}
            onModerate={onReviewModerate}
          />
        ))}

        {/* Bottom spacing for fixed action button */}
        <View style={themed($bottomSpacing)} />
      </ScrollView>
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
}

const $scrollView: ViewStyle = {
  flex: 1,
}

const $bottomSpacing: ViewStyle = {
  height: 120, // Space for fixed action button
}
