/**
 * MyAllReviewsHeader - Instructor Version
 * 
 * Header for Instructor's Review Management screen
 * Based on student version but adapted for instructor workflow with analytics
 */

import React, { useState } from "react"
import { View, ViewStyle, TouchableOpacity, ScrollView, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyAllReviewsHeaderProps } from "../../../types"

/**
 * MyAllReviewsHeader - Instructor's review management header
 * 
 * Features:
 * - Back button with proper touch feedback (following profile header pattern)
 * - "Student Reviews" title for instructor context
 * - Rating summary with instructor analytics
 * - Filter tabs for review management (All, Replied, Pending, Flagged)
 * - Analytics button for detailed insights
 */
export function MyAllReviewsHeader({
  courseName,
  totalReviews,
  averageRating,
  analytics,
  onBackPress,
  onFilterChange,
  onViewAnalytics
}: MyAllReviewsHeaderProps) {
  const { themed } = useAppTheme()
  const [selectedFilter, setSelectedFilter] = useState("All")

  // Updated filter options for instructor workflow
  const filterOptions = [
    { id: "All", label: "All", color: "#167F71" },
    { id: "Replied", label: "Replied", color: "#0961F5" },
    { id: "Pending", label: "Pending", color: "#FF6B00" },
    { id: "Flagged", label: "Flagged", color: "#FF4444" },
    { id: "5_Stars", label: "5★", color: "#FFD700" }
  ]

  const handleBackPress = () => {
    console.log("🔙 Instructor All Reviews Header: Back pressed")
    onBackPress?.()
  }

  const handleFilterPress = (filterId: string) => {
    console.log("🔍 Instructor All Reviews Header: Filter changed:", filterId)
    setSelectedFilter(filterId)
    onFilterChange?.(filterId)
  }

  const handleAnalyticsPress = () => {
    console.log("📊 Instructor All Reviews Header: Analytics pressed")
    onViewAnalytics?.()
  }

  const renderStars = () => {
    const stars = []
    for (let i = 0; i < 5; i++) {
      const isFilled = i < Math.floor(averageRating)
      const isHalf = i === Math.floor(averageRating) && averageRating % 1 !== 0

      stars.push(
        <Icon
          key={i}
          icon="star"
          size={13}
          color={isFilled || isHalf ? "#FF9C07" : "#E0E0E0"}
        />
      )
    }
    return stars
  }

  return (
    <View style={themed($headerContainer)}>
      {/* Navigation Header */}
      <View style={themed($headerTop)}>
        <TouchableOpacity
          style={themed($backBtn)}
          onPress={handleBackPress}
          activeOpacity={0.7}
        >
          <Icon icon="back" size={26} color="#202244" />
        </TouchableOpacity>

        <Text style={themed($headerTitle)}>Student Reviews</Text>

        {/* Analytics Button */}
        <TouchableOpacity
          style={themed($analyticsBtn)}
          onPress={handleAnalyticsPress}
          activeOpacity={0.7}
        >
          <Icon icon="chart" size={20} color="#0961F5" />
        </TouchableOpacity>
      </View>

      {/* Rating Summary with Instructor Analytics */}
      <View style={themed($ratingSection)}>
        <Text style={themed($ratingNum)}>{averageRating.toFixed(1)}</Text>

        <View style={themed($starsRow)}>
          {renderStars()}
        </View>

        <Text style={themed($reviewsText)}>
          Based on {totalReviews} Reviews
        </Text>

        {/* Instructor Analytics Summary */}
        <View style={themed($analyticsRow)}>
          <View style={themed($analyticItem)}>
            <Text style={themed($analyticValue)}>{analytics.repliedReviews}</Text>
            <Text style={themed($analyticLabel)}>Replied</Text>
          </View>
          <View style={themed($analyticItem)}>
            <Text style={themed($analyticValue)}>{analytics.pendingReplies}</Text>
            <Text style={themed($analyticLabel)}>Pending</Text>
          </View>
          <View style={themed($analyticItem)}>
            <Text style={themed($analyticValue)}>{analytics.responseRate}%</Text>
            <Text style={themed($analyticLabel)}>Response Rate</Text>
          </View>
        </View>
      </View>

      {/* Filter Tabs */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={themed($filtersScroll)}
        contentContainerStyle={themed($filtersContent)}
      >
        {filterOptions.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={themed([
              $filterTab,
              { backgroundColor: selectedFilter === filter.id ? filter.color : "#E8F1FF" }
            ])}
            onPress={() => handleFilterPress(filter.id)}
            activeOpacity={0.7}
          >
            <Text style={themed([
              $filterText,
              { color: selectedFilter === filter.id ? "#FFFFFF" : "#202244" }
            ])}>
              {filter.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </View>
  )
}

const $headerContainer: ViewStyle = {
  backgroundColor: "#F5F9FF",
  paddingBottom: 20,
}

const $headerTop: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: 35,
  paddingTop: 25,
  paddingBottom: 30,
}

const $backBtn: ViewStyle = {
  marginRight: 12,
}

const $headerTitle: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for titles
  fontSize: 21,
  lineHeight: 30,
  color: "#202244",
  flex: 1,
}

const $analyticsBtn: ViewStyle = {
  padding: 8,
}

const $ratingSection: ViewStyle = {
  alignItems: "center",
  marginBottom: 20,
}

const $ratingNum: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for ratings
  fontSize: 38,
  lineHeight: 55,
  color: "#202244",
  textAlign: "center",
  marginBottom: 4,
}

const $starsRow: ViewStyle = {
  flexDirection: "row",
  gap: 8,
  marginBottom: 4,
}

const $reviewsText: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight for descriptions
  fontSize: 13,
  lineHeight: 16,
  color: "#545454",
  textAlign: "center",
  marginBottom: 16,
}

const $analyticsRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "center",
  gap: 24,
}

const $analyticItem: ViewStyle = {
  alignItems: "center",
}

const $analyticValue: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for values
  fontSize: 16,
  lineHeight: 20,
  color: "#0961F5",
  marginBottom: 2,
}

const $analyticLabel: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight for labels
  fontSize: 11,
  lineHeight: 14,
  color: "#A0A4AB",
}

const $filtersScroll: ViewStyle = {
  paddingLeft: 35,
}

const $filtersContent: ViewStyle = {
  paddingRight: 35,
  gap: 10,
}

const $filterTab: ViewStyle = {
  paddingHorizontal: 18,
  paddingVertical: 7,
  borderRadius: 15,
  minWidth: 70,
  alignItems: "center",
}

const $filterText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for filter labels
  fontSize: 13,
  lineHeight: 16,
}
