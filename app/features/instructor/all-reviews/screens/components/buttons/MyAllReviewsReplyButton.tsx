/**
 * MyAllReviewsReplyButton - Instructor Version
 * 
 * Fixed action button for Instructor's Review Management
 * Based on student WriteButton but adapted for instructor workflow
 */

import React from "react"
import { View, ViewStyle, TouchableOpacity, TextStyle } from "react-native"
import { Text, Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyAllReviewsReplyButtonProps } from "../../../types"

/**
 * MyAllReviewsReplyButton - Instructor's review management action button
 * 
 * Features:
 * - Bulk Reply button (primary action)
 * - View Analytics button
 * - Pending replies counter
 * - Fixed positioning at bottom
 * - Instructor-specific actions
 */
export function MyAllReviewsReplyButton({
  pendingReplies,
  onBulkReply,
  onViewAnalytics
}: MyAllReviewsReplyButtonProps) {
  const { themed } = useAppTheme()

  const handleBulkReplyPress = () => {
    console.log("📝 Instructor All Reviews: Bulk reply pressed")
    onBulkReply?.()
  }

  const handleAnalyticsPress = () => {
    console.log("📊 Instructor All Reviews: View analytics pressed")
    onViewAnalytics?.()
  }

  return (
    <View style={themed($container)}>
      {/* Pending Replies Info */}
      {pendingReplies > 0 && (
        <View style={themed($pendingInfo)}>
          <Text style={themed($pendingText)}>
            {pendingReplies} review{pendingReplies > 1 ? 's' : ''} pending reply
          </Text>
        </View>
      )}

      {/* Action Buttons Row */}
      <View style={themed($buttonsRow)}>
        {/* Analytics Button */}
        <TouchableOpacity
          style={themed($analyticsButton)}
          onPress={handleAnalyticsPress}
          activeOpacity={0.8}
        >
          <Icon
            icon="chart"
            size={20}
            color="#0961F5"
          />
        </TouchableOpacity>

        {/* Bulk Reply Button */}
        <TouchableOpacity
          style={themed($bulkReplyButton)}
          onPress={handleBulkReplyPress}
          activeOpacity={0.8}
        >
          {/* Button Background */}
          <View style={themed($buttonBackground)} />

          {/* Reply Icon */}
          <View style={themed($iconContainer)}>
            <View style={themed($iconCircle)}>
              <Icon
                icon="reply"
                size={16}
                color="#FF6B00"
              />
            </View>
          </View>

          {/* Button Text */}
          <Text style={themed($buttonText)}>
            Bulk Reply
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  backgroundColor: "#FFFFFF",
  paddingHorizontal: 34,
  paddingTop: 16,
  paddingBottom: 34,
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: -2 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 10,
}

const $pendingInfo: ViewStyle = {
  backgroundColor: "#FFF3E0",
  borderRadius: 12,
  paddingHorizontal: 16,
  paddingVertical: 8,
  marginBottom: 12,
  borderLeftWidth: 3,
  borderLeftColor: "#FF6B00",
}

const $pendingText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for info text
  fontSize: 13,
  lineHeight: 16,
  color: "#FF6B00",
  textAlign: "center",
}

const $buttonsRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 12,
}

const $analyticsButton: ViewStyle = {
  width: 60,
  height: 60,
  borderRadius: 30,
  backgroundColor: "#E8F1FF",
  borderWidth: 2,
  borderColor: "rgba(9, 97, 245, 0.2)",
  justifyContent: "center",
  alignItems: "center",
}

const $bulkReplyButton: ViewStyle = {
  flex: 1,
  height: 60,
  borderRadius: 30,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  overflow: "hidden",
}

const $buttonBackground: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "#FF6B00",
  borderRadius: 30,
}

const $iconContainer: ViewStyle = {
  marginRight: 12,
}

const $iconCircle: ViewStyle = {
  width: 32,
  height: 32,
  borderRadius: 16,
  backgroundColor: "#FFFFFF",
  justifyContent: "center",
  alignItems: "center",
}

const $buttonText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for button text
  fontSize: 16,
  lineHeight: 20,
  color: "#FFFFFF",
}
