/**
 * MyAllReviewsCard - Instructor Version
 * 
 * Individual review card component for instructor's review management
 * Based on student version but adapted for instructor workflow with reply functionality
 */

import React from "react"
import { View, ViewStyle, TouchableOpacity, TextStyle } from "react-native"
import { Text, Icon, AutoImage } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyAllReviewsCardProps } from "../../../types"

/**
 * MyAllReviewsCard - Instructor's review management card
 * 
 * Features:
 * - Student info with avatar and name
 * - Rating badge and review content
 * - Instructor reply section (if replied)
 * - Reply button and moderation options
 * - Status indicators (replied, flagged, etc.)
 * - Consistent styling with student version
 */
export function MyAllReviewsCard({
  review,
  onReply,
  onModerate
}: MyAllReviewsCardProps) {
  const { themed } = useAppTheme()

  const handleReplyPress = () => {
    console.log("💬 Instructor Review Card: Reply pressed for review:", review.id)
    onReply?.(review.id)
  }

  const handleModeratePress = (action: "hide" | "show" | "flag") => {
    console.log("🛡️ Instructor Review Card: Moderate pressed:", { reviewId: review.id, action })
    onModerate?.(review.id, action)
  }

  const renderStars = () => {
    const stars = []
    for (let i = 0; i < 5; i++) {
      const isFilled = i < review.rating
      stars.push(
        <Icon
          key={i}
          icon="star"
          size={10}
          color={isFilled ? "#FF9C07" : "#E0E0E0"}
        />
      )
    }
    return stars
  }

  return (
    <View style={themed($container)}>
      <View style={themed($card)}>
        {/* Top Row: Avatar, Name, Rating Badge, Status */}
        <View style={themed($topRow)}>
          <View style={themed($avatarContainer)}>
            <AutoImage
              source={{ uri: review.student.avatar }}
              style={themed($avatar)}
            />
          </View>

          <View style={themed($studentInfo)}>
            <Text style={themed($studentName)}>{review.student.name}</Text>
            <Text style={themed($enrolledDate)}>
              Enrolled: {new Date(review.student.enrolledDate).toLocaleDateString()}
            </Text>
          </View>

          <View style={themed($ratingBadge)}>
            <View style={themed($ratingBadgeInner)}>
              <View style={themed($starsContainer)}>
                {renderStars()}
              </View>
              <Text style={themed($ratingText)}>{review.rating.toFixed(1)}</Text>
            </View>
          </View>

          {/* Status Indicators */}
          <View style={themed($statusContainer)}>
            {review.isReplied && (
              <View style={themed($repliedBadge)}>
                <Icon icon="check" size={10} color="#167F71" />
              </View>
            )}
            {review.isFlagged && (
              <View style={themed($flaggedBadge)}>
                <Icon icon="flag" size={10} color="#FF4444" />
              </View>
            )}
          </View>
        </View>

        {/* Comment Section */}
        <Text style={themed($comment)}>{review.comment}</Text>

        {/* Instructor Reply Section */}
        {review.instructorReply && (
          <View style={themed($replySection)}>
            <View style={themed($replyHeader)}>
              <Icon icon="reply" size={12} color="#0961F5" />
              <Text style={themed($replyLabel)}>Your Reply:</Text>
              <Text style={themed($replyDate)}>{review.replyDate}</Text>
            </View>
            <Text style={themed($replyText)}>{review.instructorReply}</Text>
          </View>
        )}

        {/* Bottom Row: Actions and Date */}
        <View style={themed($bottomRow)}>
          <View style={themed($actionsRow)}>
            {/* Reply Button */}
            <TouchableOpacity
              style={themed([
                $actionButton,
                review.isReplied ? $editReplyButton : $replyButton
              ])}
              onPress={handleReplyPress}
              activeOpacity={0.7}
            >
              <Icon
                icon={review.isReplied ? "edit" : "reply"}
                size={14}
                color={review.isReplied ? "#FF6B00" : "#0961F5"}
              />
              <Text style={themed([
                $actionButtonText,
                { color: review.isReplied ? "#FF6B00" : "#0961F5" }
              ])}>
                {review.isReplied ? "Edit Reply" : "Reply"}
              </Text>
            </TouchableOpacity>

            {/* Helpful Count */}
            <View style={themed($helpfulContainer)}>
              <Icon icon="thumbsUp" size={12} color="#167F71" />
              <Text style={themed($helpfulText)}>{review.helpfulCount}</Text>
            </View>
          </View>

          <Text style={themed($dateText)}>{review.date}</Text>
        </View>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
  marginBottom: 15,
}

const $card: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4,
  padding: 21,
}

const $topRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "flex-start",
  marginBottom: 12,
}

const $avatarContainer: ViewStyle = {
  width: 46,
  height: 46,
  borderRadius: 23,
  backgroundColor: "#000000",
  overflow: "hidden",
  marginRight: 12,
}

const $avatar: ViewStyle = {
  width: 46,
  height: 46,
  borderRadius: 23,
}

const $studentInfo: ViewStyle = {
  flex: 1,
}

const $studentName: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for names
  fontSize: 17,
  lineHeight: 25,
  color: "#202244",
  marginBottom: 2,
}

const $enrolledDate: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY
  fontSize: 11,
  lineHeight: 14,
  color: "#A0A4AB",
}

const $ratingBadge: ViewStyle = {
  marginRight: 8,
}

const $ratingBadgeInner: ViewStyle = {
  alignItems: "center",
  backgroundColor: "#E8F1FF",
  borderWidth: 2,
  borderColor: "#4D81E5",
  borderRadius: 13,
  paddingHorizontal: 8,
  paddingVertical: 4,
}

const $starsContainer: ViewStyle = {
  flexDirection: "row",
  gap: 1,
  marginBottom: 2,
}

const $ratingText: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for ratings
  fontSize: 11,
  lineHeight: 14,
  color: "#202244",
}

const $statusContainer: ViewStyle = {
  flexDirection: "row",
  gap: 4,
}

const $repliedBadge: ViewStyle = {
  width: 20,
  height: 20,
  borderRadius: 10,
  backgroundColor: "rgba(22, 127, 113, 0.1)",
  justifyContent: "center",
  alignItems: "center",
}

const $flaggedBadge: ViewStyle = {
  width: 20,
  height: 20,
  borderRadius: 10,
  backgroundColor: "rgba(255, 68, 68, 0.1)",
  justifyContent: "center",
  alignItems: "center",
}

const $comment: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for comments
  fontSize: 13,
  lineHeight: 18,
  color: "#545454",
  marginBottom: 12,
  marginLeft: 58, // Align with name (46px avatar + 12px margin)
}

const $replySection: ViewStyle = {
  marginLeft: 58,
  marginBottom: 12,
  backgroundColor: "#F8F9FA",
  borderRadius: 12,
  padding: 12,
  borderLeftWidth: 3,
  borderLeftColor: "#0961F5",
}

const $replyHeader: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: 6,
}

const $replyLabel: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for labels
  fontSize: 11,
  lineHeight: 14,
  color: "#0961F5",
  marginLeft: 4,
  flex: 1,
}

const $replyDate: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for dates
  fontSize: 10,
  lineHeight: 12,
  color: "#A0A4AB",
}

const $replyText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for reply text
  fontSize: 12,
  lineHeight: 16,
  color: "#202244",
}

const $bottomRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginLeft: 58, // Align with name (46px avatar + 12px margin)
}

const $actionsRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 16,
}

const $actionButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 6,
  paddingHorizontal: 8,
  paddingVertical: 4,
  borderRadius: 8,
}

const $replyButton: ViewStyle = {
  backgroundColor: "rgba(9, 97, 245, 0.1)",
}

const $editReplyButton: ViewStyle = {
  backgroundColor: "rgba(255, 107, 0, 0.1)",
}

const $actionButtonText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for button text
  fontSize: 12,
  lineHeight: 15,
}

const $helpfulContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 4,
}

const $helpfulText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for counts
  fontSize: 11,
  lineHeight: 14,
  color: "#167F71",
}

const $dateText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for dates
  fontSize: 12,
  lineHeight: 15,
  color: "#A0A4AB",
}
