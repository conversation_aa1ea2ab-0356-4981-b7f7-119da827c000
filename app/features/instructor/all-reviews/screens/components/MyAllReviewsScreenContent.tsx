/**
 * MyAllReviewsScreenContent - Instructor Version
 * 
 * Main content component for Instructor's Review Management screen
 * Based on student version but adapted for instructor review management workflow
 */

import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyAllReviewsData } from "../hooks/useMyAllReviewsData"
import type { MyAllReviewsHandlers } from "../hooks/useMyAllReviewsHandlers"
import {
  MyAllReviewsHeader,
  MyAllReviewsContent
} from "./sections"
import { MyAllReviewsReplyButton } from "./buttons"

interface MyAllReviewsScreenContentProps {
  data: MyAllReviewsData
  handlers: MyAllReviewsHandlers
}

/**
 * MyAllReviewsScreenContent - Instructor's review management content
 * 
 * Features:
 * - Header section with analytics and filter options
 * - Content section with student reviews and reply functionality
 * - Fixed action section with bulk reply and analytics buttons
 * - Instructor-specific functionality and navigation
 */
export function MyAllReviewsScreenContent({
  data,
  handlers
}: MyAllReviewsScreenContentProps) {
  const { themed } = useAppTheme()

  console.log("📝 Instructor MyAllReviewsScreenContent rendering...")

  return (
    <View style={themed($container)}>
      {/* Header Section - Rating summary, analytics, and filters */}
      <MyAllReviewsHeader
        courseName={data.courseName}
        totalReviews={data.totalReviews}
        averageRating={data.averageRating}
        analytics={data.analytics}
        onBackPress={handlers.handleBackPress}
        onFilterChange={handlers.handleFilterChange}
        onViewAnalytics={handlers.handleViewAnalytics}
      />

      {/* Content Section - All student reviews with management options */}
      <MyAllReviewsContent
        reviews={data.reviews}
        onReviewReply={handlers.handleReviewReply}
        onReviewModerate={handlers.handleReviewModerate}
        onBulkReply={handlers.handleBulkReply}
      />

      {/* Fixed Action Section - Bulk reply and analytics buttons */}
      <MyAllReviewsReplyButton
        pendingReplies={data.analytics.pendingReplies}
        onBulkReply={handlers.handleBulkReply}
        onViewAnalytics={handlers.handleViewAnalytics}
      />
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: "#F5F9FF", // Figma background color
}
