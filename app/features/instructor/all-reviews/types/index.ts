/**
 * Instructor All Reviews Types
 * 
 * Types specific to instructor's review management with reply functionality,
 * feedback moderation, and instructor-specific features.
 * Based on student types but adapted for instructor workflow.
 */

// Navigation Types
export interface MyAllReviewsScreenProps {
  navigation?: any
  route?: {
    params?: {
      courseId?: string
      course?: any
      userType?: "instructor"
    }
  }
}

// Instructor Review (with reply functionality)
export interface InstructorReview {
  id: string
  student: {
    name: string
    avatar: string
    enrolledDate: string
  }
  rating: number
  comment: string
  date: string
  likes: number
  isLiked: boolean
  // Instructor-specific fields
  instructorReply?: string
  replyDate?: string
  isReplied: boolean
  isModerated: boolean
  isFlagged: boolean
  helpfulCount: number
  reportCount: number
  status: "active" | "hidden" | "flagged"
}

// Review Analytics for Instructor
export interface ReviewAnalytics {
  totalReviews: number
  averageRating: number
  ratingDistribution: {
    5: number
    4: number
    3: number
    2: number
    1: number
  }
  repliedReviews: number
  pendingReplies: number
  flaggedReviews: number
  helpfulReplies: number
  responseRate: number
  averageResponseTime: number // in hours
}

// Instructor All Reviews Data
export interface MyAllReviewsData {
  courseName: string
  totalReviews: number
  averageRating: number
  reviews: InstructorReview[]
  analytics: ReviewAnalytics
  isLoading: boolean
  error?: string
}

// Instructor All Reviews Handlers
export interface MyAllReviewsHandlers {
  handleBackPress: () => void
  handleReviewReply: (reviewId: string) => void
  handleReviewModerate: (reviewId: string, action: "hide" | "show" | "flag") => void
  handleFilterChange: (filter: string) => void
  handleSortChange: (sort: string) => void
  handleBulkReply: () => void
  handleExportReviews: () => void
  handleViewAnalytics: () => void
}

// Component Props Types
export interface MyAllReviewsHeaderProps {
  courseName: string
  totalReviews: number
  averageRating: number
  analytics: ReviewAnalytics
  onBackPress: () => void
  onFilterChange?: (filter: string) => void
  onSortChange?: (sort: string) => void
  onViewAnalytics?: () => void
}

export interface MyAllReviewsContentProps {
  reviews: InstructorReview[]
  onReviewReply?: (reviewId: string) => void
  onReviewModerate?: (reviewId: string, action: "hide" | "show" | "flag") => void
  onBulkReply?: () => void
}

export interface MyAllReviewsCardProps {
  review: InstructorReview
  onReply?: (reviewId: string) => void
  onModerate?: (reviewId: string, action: "hide" | "show" | "flag") => void
}

export interface MyAllReviewsReplyButtonProps {
  pendingReplies: number
  onBulkReply?: () => void
  onViewAnalytics?: () => void
}

// Hook Params Types
export interface UseMyAllReviewsDataParams {
  courseId?: string
  course?: any
}

export interface UseMyAllReviewsHandlersParams {
  courseId?: string
  course?: any
  navigation?: any
  onBackPress?: () => void
  onReviewReply?: (reviewId: string) => void
  onReviewModerate?: (reviewId: string, action: "hide" | "show" | "flag") => void
}

// Filter and Sort Types
export type ReviewFilter = 
  | "all"
  | "5_stars"
  | "4_stars" 
  | "3_stars"
  | "2_stars"
  | "1_star"
  | "replied"
  | "pending_reply"
  | "flagged"

export type ReviewSort = 
  | "newest"
  | "oldest"
  | "highest_rating"
  | "lowest_rating"
  | "most_helpful"
  | "needs_reply"

// Review Management Actions
export type ReviewAction = 
  | "reply"
  | "edit_reply"
  | "delete_reply"
  | "hide_review"
  | "show_review"
  | "flag_review"
  | "unflag_review"
  | "mark_helpful"
  | "export_reviews"
  | "bulk_reply"

// Reply Management Types
export interface ReviewReply {
  id: string
  reviewId: string
  instructorId: string
  content: string
  createdAt: string
  updatedAt: string
  isEdited: boolean
  helpfulCount: number
}

// Bulk Operations Types
export interface BulkReplyData {
  selectedReviewIds: string[]
  replyTemplate: string
  personalizeReplies: boolean
}

// Export Types
export interface ReviewExportData {
  courseId: string
  courseName: string
  exportDate: string
  totalReviews: number
  averageRating: number
  reviews: InstructorReview[]
  analytics: ReviewAnalytics
}
