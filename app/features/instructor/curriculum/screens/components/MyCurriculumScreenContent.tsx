/**
 * MyCurriculumScreenContent - Instructor Version
 * 
 * Main content component for Instructor's Curriculum Management screen
 * Based on student version but adapted for instructor curriculum management workflow
 */

import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCurriculumData } from "../hooks/useMyCurriculumData"
import type { MyCurriculumHandlers } from "../hooks/useMyCurriculumHandlers"
import {
  MyCurriculumHeaderSection,
  MyCurriculumContentSection,
  MyCurriculumActionSection
} from "./sections"

interface MyCurriculumScreenContentProps {
  data: MyCurriculumData
  handlers: MyCurriculumHandlers
}

/**
 * MyCurriculumScreenContent - Instructor's curriculum management content
 * 
 * Features:
 * - Header section with back button and "Curriculum Management" title
 * - Content section with curriculum sections and lesson management
 * - Fixed action section with management buttons
 * - Instructor-specific functionality and navigation
 */
export function MyCurriculumScreenContent({
  data,
  handlers
}: MyCurriculumScreenContentProps) {
  const { themed } = useAppTheme()

  console.log("📚 Instructor MyCurriculumScreenContent rendering...")

  return (
    <View style={themed($container)}>
      {/* Header Section - Back button and title - Fixed at top */}
      <MyCurriculumHeaderSection
        onBackPress={handlers.handleBackPress}
      />

      {/* Content Section - All curriculum sections and lessons with management */}
      <MyCurriculumContentSection
        sections={data.curriculumSections}
        onLessonPress={handlers.handleLessonPress}
        onSectionPress={handlers.handleSectionPress}
        onEditLesson={handlers.handleEditLesson}
        onAddLesson={handlers.handleAddLesson}
        onEditSection={handlers.handleEditSection}
      />

      {/* Fixed Action Section - Management buttons */}
      <MyCurriculumActionSection
        performance={data.coursePerformance}
        nextLessonToEdit={data.nextLessonToEdit}
        onManageContent={handlers.handleManageContent}
        onAddSection={handlers.handleAddSection}
        onViewAnalytics={handlers.handleViewAnalytics}
        isLoading={data.isLoading}
      />
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: "#F5F9FF", // Figma background color
}
