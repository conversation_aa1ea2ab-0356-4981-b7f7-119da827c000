/**
 * MyCurriculumHeaderSection - Instructor Version
 * 
 * Navigation header for Instructor's Curriculum Management
 * Based on student version but adapted for instructor workflow
 */

import React from "react"
import { View, ViewStyle, Text, TextStyle, TouchableOpacity } from "react-native"
import { Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCurriculumHeaderSectionProps } from "../../../types"

/**
 * MyCurriculumHeaderSection - Instructor's curriculum header
 * 
 * Features:
 * - Back button with proper touch feedback (following profile header pattern)
 * - "Curriculum Management" title for instructor context
 * - Consistent styling with app design
 */
export function MyCurriculumHeaderSection({
  onBackPress
}: MyCurriculumHeaderSectionProps) {
  const { themed } = useAppTheme()

  const handleBackPress = () => {
    console.log("🔙 Instructor Curriculum Header: Back pressed")
    onBackPress?.()
  }

  return (
    <View style={themed($container)}>
      {/* Back Button */}
      <TouchableOpacity
        style={themed($backButton)}
        onPress={handleBackPress}
        activeOpacity={0.7}
      >
        <Icon
          icon="caretLeft"
          size={20}
          color="#202244"
        />
      </TouchableOpacity>

      {/* Title */}
      <Text style={themed($title)}>
        Curriculum Management
      </Text>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35, // Figma spacing
  paddingVertical: 20,
  backgroundColor: "#F5F9FF",
}

const $backButton: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "#FFFFFF",
  justifyContent: "center",
  alignItems: "center",
  marginRight: 16,
  // Shadow for iOS
  shadowColor: "#000",
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.1,
  shadowRadius: 3.84,
  // Elevation for Android
  elevation: 5,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for titles
  fontSize: 21,
  lineHeight: 30,
  color: "#202244",
}
