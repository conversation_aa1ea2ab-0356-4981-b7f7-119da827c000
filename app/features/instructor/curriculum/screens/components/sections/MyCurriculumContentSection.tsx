/**
 * MyCurriculumContentSection - Instructor Version
 * 
 * Main curriculum content for Instructor's Curriculum Management
 * Based on student version but adapted for instructor workflow
 */

import React from "react"
import { View, ViewStyle, ScrollView, Text, TextStyle, TouchableOpacity } from "react-native"
import { Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCurriculumContentSectionProps } from "../../../types"

/**
 * MyCurriculumContentSection - Instructor's curriculum content
 * 
 * Features:
 * - Scrollable list of curriculum sections with management options
 * - Each section shows publishing status and performance metrics
 * - Lessons show editing options and analytics
 * - Add lesson and edit section buttons
 * - Performance indicators for instructor insights
 */
export function MyCurriculumContentSection({
  sections,
  onLessonPress,
  onSectionPress,
  onEditLesson,
  onAddLesson,
  onEditSection
}: MyCurriculumContentSectionProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <ScrollView
        style={themed($scrollView)}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        {/* Main Curriculum Card */}
        <View style={themed($curriculumCard)}>
          {sections.map((section, index) => (
            <InstructorCurriculumSectionCard
              key={section.id}
              section={section}
              isFirst={index === 0}
              isLast={index === sections.length - 1}
              onLessonPress={onLessonPress}
              onSectionPress={onSectionPress}
              onEditLesson={onEditLesson}
              onAddLesson={onAddLesson}
              onEditSection={onEditSection}
            />
          ))}
        </View>

        {/* Bottom spacing for fixed action button */}
        <View style={themed($bottomSpacing)} />
      </ScrollView>
    </View>
  )
}

// Inline Instructor Curriculum Section Card Component
function InstructorCurriculumSectionCard({
  section,
  isFirst,
  isLast,
  onLessonPress,
  onSectionPress,
  onEditLesson,
  onAddLesson,
  onEditSection
}: any) {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$sectionContainer, isFirst && $firstSection, isLast && $lastSection])}>
      {/* Section Header */}
      <TouchableOpacity
        style={themed($sectionHeader)}
        onPress={() => onSectionPress?.(section.id)}
        activeOpacity={0.7}
      >
        <View style={themed($sectionInfo)}>
          <Text style={themed($sectionTitle)}>{section.title}</Text>
          <Text style={themed($sectionMeta)}>
            {section.duration} • {section.completedLessons}/{section.totalLessons} lessons • {section.progress}% completion
          </Text>
        </View>
        
        <View style={themed($sectionActions)}>
          <TouchableOpacity
            style={themed($editButton)}
            onPress={() => onEditSection?.(section.id)}
            activeOpacity={0.7}
          >
            <Icon icon="edit" size={16} color="#0961F5" />
          </TouchableOpacity>
          
          <View style={themed([
            $statusBadge,
            section.isPublished ? $publishedBadge : $draftBadge
          ])}>
            <Text style={themed([
              $statusText,
              section.isPublished ? $publishedText : $draftText
            ])}>
              {section.isPublished ? "Published" : "Draft"}
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      {/* Lessons List */}
      <View style={themed($lessonsContainer)}>
        {section.lessons.map((lesson: any, lessonIndex: number) => (
          <TouchableOpacity
            key={lesson.id}
            style={themed($lessonItem)}
            onPress={() => onLessonPress?.(section.id, lesson.id)}
            activeOpacity={0.7}
          >
            <View style={themed($lessonInfo)}>
              <Text style={themed($lessonNumber)}>{lesson.number}</Text>
              <View style={themed($lessonDetails)}>
                <Text style={themed($lessonTitle)}>{lesson.title}</Text>
                <Text style={themed($lessonMeta)}>
                  {lesson.duration} • {lesson.completionRate}% completion • {lesson.viewCount} views
                </Text>
              </View>
            </View>
            
            <View style={themed($lessonActions)}>
              <TouchableOpacity
                style={themed($editLessonButton)}
                onPress={() => onEditLesson?.(section.id, lesson.id)}
                activeOpacity={0.7}
              >
                <Icon icon="edit" size={14} color="#FF6B00" />
              </TouchableOpacity>
              
              <View style={themed([
                $lessonStatusBadge,
                lesson.status === "published" ? $publishedBadge : $draftBadge
              ])}>
                <Text style={themed([
                  $lessonStatusText,
                  lesson.status === "published" ? $publishedText : $draftText
                ])}>
                  {lesson.status === "published" ? "Live" : "Draft"}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
        
        {/* Add Lesson Button */}
        <TouchableOpacity
          style={themed($addLessonButton)}
          onPress={() => onAddLesson?.(section.id)}
          activeOpacity={0.7}
        >
          <Icon icon="plus" size={16} color="#167F71" />
          <Text style={themed($addLessonText)}>Add Lesson</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
  paddingHorizontal: 34,
}

const $scrollView: ViewStyle = {
  flex: 1,
}

const $curriculumCard: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 20,
  paddingVertical: 24,
  paddingHorizontal: 20,
  marginTop: 20,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 3,
}

const $sectionContainer: ViewStyle = {
  marginBottom: 24,
}

const $firstSection: ViewStyle = {}

const $lastSection: ViewStyle = {
  marginBottom: 0,
}

const $sectionHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 16,
}

const $sectionInfo: ViewStyle = {
  flex: 1,
}

const $sectionTitle: TextStyle = {
  fontFamily: "lexendDecaSemiBold",
  fontSize: 16,
  lineHeight: 20,
  color: "#202244",
  marginBottom: 4,
}

const $sectionMeta: TextStyle = {
  fontFamily: "lexendDecaLight",
  fontSize: 12,
  lineHeight: 16,
  color: "#A0A4AB",
}

const $sectionActions: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 8,
}

const $editButton: ViewStyle = {
  padding: 8,
}

const $statusBadge: ViewStyle = {
  paddingHorizontal: 8,
  paddingVertical: 4,
  borderRadius: 12,
}

const $publishedBadge: ViewStyle = {
  backgroundColor: "rgba(22, 127, 113, 0.1)",
}

const $draftBadge: ViewStyle = {
  backgroundColor: "rgba(255, 107, 0, 0.1)",
}

const $statusText: TextStyle = {
  fontFamily: "lexendDecaMedium",
  fontSize: 10,
  lineHeight: 12,
}

const $publishedText: TextStyle = {
  color: "#167F71",
}

const $draftText: TextStyle = {
  color: "#FF6B00",
}

const $lessonsContainer: ViewStyle = {
  paddingLeft: 16,
}

const $lessonItem: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingVertical: 12,
  borderBottomWidth: 1,
  borderBottomColor: "#F0F0F0",
}

const $lessonInfo: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
}

const $lessonNumber: TextStyle = {
  fontFamily: "lexendDecaSemiBold",
  fontSize: 14,
  lineHeight: 18,
  color: "#0961F5",
  width: 24,
  marginRight: 12,
}

const $lessonDetails: ViewStyle = {
  flex: 1,
}

const $lessonTitle: TextStyle = {
  fontFamily: "lexendDecaMedium",
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
  marginBottom: 2,
}

const $lessonMeta: TextStyle = {
  fontFamily: "lexendDecaLight",
  fontSize: 11,
  lineHeight: 14,
  color: "#A0A4AB",
}

const $lessonActions: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 6,
}

const $editLessonButton: ViewStyle = {
  padding: 6,
}

const $lessonStatusBadge: ViewStyle = {
  paddingHorizontal: 6,
  paddingVertical: 2,
  borderRadius: 8,
}

const $lessonStatusText: TextStyle = {
  fontFamily: "lexendDecaMedium",
  fontSize: 9,
  lineHeight: 11,
}

const $addLessonButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  paddingVertical: 12,
  marginTop: 8,
  backgroundColor: "rgba(22, 127, 113, 0.1)",
  borderRadius: 8,
  gap: 8,
}

const $addLessonText: TextStyle = {
  fontFamily: "lexendDecaMedium",
  fontSize: 14,
  lineHeight: 18,
  color: "#167F71",
}

const $bottomSpacing: ViewStyle = {
  height: 180,
}
