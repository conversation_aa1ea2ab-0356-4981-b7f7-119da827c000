/**
 * MyCurriculumActionSection - Instructor Version
 * 
 * Action buttons for Instructor's Curriculum Management
 * Based on student version but adapted for instructor workflow
 */

import React from "react"
import { View, ViewStyle, Text, TextStyle, TouchableOpacity, ActivityIndicator } from "react-native"
import { Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCurriculumActionSectionProps } from "../../../types"

/**
 * MyCurriculumActionSection - Instructor's curriculum action section
 * 
 * Features:
 * - Manage Content button (primary action)
 * - Add Section button
 * - View Analytics button
 * - Performance information display
 * - Instructor-specific actions and metrics
 */
export function MyCurriculumActionSection({
  performance,
  nextLessonToEdit,
  onManageContent,
  onAddSection,
  onViewAnalytics,
  isLoading = false,
}: MyCurriculumActionSectionProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Performance Information */}
      <View style={themed($performanceInfo)}>
        <Text style={themed($performanceText)}>
          {performance.publishedLessons}/{performance.totalLessons} lessons published
        </Text>
        <Text style={themed($nextActionText)}>
          {nextLessonToEdit
            ? `Next to edit: ${nextLessonToEdit.title}`
            : "All lessons are published"
          }
        </Text>
      </View>

      {/* Action Buttons */}
      <View style={themed($buttonsContainer)}>
        {/* Manage Content Button (Primary) */}
        <TouchableOpacity
          style={themed($manageButton)}
          onPress={onManageContent}
          disabled={isLoading}
          activeOpacity={0.8}
        >
          {/* Button Background */}
          <View style={themed($buttonBackground)} />

          {/* Manage Icon */}
          <View style={themed($iconContainer)}>
            <View style={themed($iconCircle)}>
              {isLoading ? (
                <ActivityIndicator size="small" color="#FF6B00" />
              ) : (
                <Icon
                  icon="settings"
                  size={16}
                  color="#FF6B00"
                />
              )}
            </View>
          </View>

          {/* Button Text */}
          <Text style={themed($buttonText)}>
            Manage Content
          </Text>
        </TouchableOpacity>

        {/* Secondary Actions Row */}
        <View style={themed($secondaryActionsRow)}>
          {/* Add Section Button */}
          <TouchableOpacity
            style={themed($addSectionButton)}
            onPress={onAddSection}
            disabled={isLoading}
            activeOpacity={0.8}
          >
            <Icon
              icon="plus"
              size={16}
              color="#167F71"
            />
            <Text style={themed($addSectionButtonText)}>
              Add Section
            </Text>
          </TouchableOpacity>

          {/* View Analytics Button */}
          <TouchableOpacity
            style={themed($analyticsButton)}
            onPress={onViewAnalytics}
            disabled={isLoading}
            activeOpacity={0.8}
          >
            <Icon
              icon="chart"
              size={16}
              color="#0961F5"
            />
            <Text style={themed($analyticsButtonText)}>
              Analytics
            </Text>
          </TouchableOpacity>
        </View>

        {/* Performance Summary */}
        <View style={themed($performanceSummary)}>
          <Text style={themed($summaryText)}>
            {performance.averageCompletionRate}% avg completion • {performance.studentEngagement}% engagement
          </Text>
        </View>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  backgroundColor: "#FFFFFF",
  paddingHorizontal: 34,
  paddingTop: 20,
  paddingBottom: 34,
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  shadowColor: "#000",
  shadowOffset: { width: 0, height: -2 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 10,
}

const $performanceInfo: ViewStyle = {
  marginBottom: 16,
}

const $performanceText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for performance metrics
  fontSize: 14,
  lineHeight: 18,
  color: "#0961F5",
  textAlign: "center",
  marginBottom: 4,
}

const $nextActionText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for descriptions
  fontSize: 12,
  lineHeight: 16,
  color: "#545454",
  textAlign: "center",
}

const $buttonsContainer: ViewStyle = {
  gap: 12,
}

const $manageButton: ViewStyle = {
  height: 60,
  borderRadius: 30,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  overflow: "hidden",
}

const $buttonBackground: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "#FF6B00",
  borderRadius: 30,
}

const $iconContainer: ViewStyle = {
  marginRight: 12,
}

const $iconCircle: ViewStyle = {
  width: 32,
  height: 32,
  borderRadius: 16,
  backgroundColor: "#FFFFFF",
  justifyContent: "center",
  alignItems: "center",
}

const $buttonText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for button text
  fontSize: 16,
  lineHeight: 20,
  color: "#FFFFFF",
}

const $secondaryActionsRow: ViewStyle = {
  flexDirection: "row",
  gap: 12,
}

const $addSectionButton: ViewStyle = {
  flex: 1,
  height: 50,
  borderRadius: 25,
  backgroundColor: "rgba(22, 127, 113, 0.1)",
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: 8,
}

const $addSectionButtonText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for secondary buttons
  fontSize: 14,
  lineHeight: 18,
  color: "#167F71",
}

const $analyticsButton: ViewStyle = {
  flex: 1,
  height: 50,
  borderRadius: 25,
  backgroundColor: "rgba(9, 97, 245, 0.1)",
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: 8,
}

const $analyticsButtonText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for secondary buttons
  fontSize: 14,
  lineHeight: 18,
  color: "#0961F5",
}

const $performanceSummary: ViewStyle = {
  padding: 12,
  backgroundColor: "#F5F9FF",
  borderRadius: 12,
  borderWidth: 1,
  borderColor: "rgba(9, 97, 245, 0.1)",
}

const $summaryText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for summary text
  fontSize: 12,
  lineHeight: 16,
  color: "#0961F5",
  textAlign: "center",
}
