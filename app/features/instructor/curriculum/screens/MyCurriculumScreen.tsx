/**
 * MyCurriculumScreen - Instructor Version
 *
 * Main Curriculum Management screen for instructor's course curriculum
 * Based on student MyCurriculumScreen but with instructor-specific features:
 * - Lesson editing and management
 * - Section organization and publishing
 * - Content performance analytics
 * - Course structure management
 *
 * Following the same pattern as student version with:
 * - useMyCurriculumData: Manages all curriculum data and performance
 * - useMyCurriculumHandlers: Manages all interaction handlers
 * - MyCurriculumScreenContent: Contains all the scrollable content sections
 *
 * Benefits of this architecture:
 * - Easier to maintain and update
 * - Better separation of concerns
 * - Reusable hooks and components
 * - Cleaner code organization
 * - Easier testing
 * - Independent from student version for future development
 */

import React from "react"
import { Screen } from "app/components"
import { colors } from "app/theme"
import { MyCurriculumScreenContent } from "./components/MyCurriculumScreenContent"
import { useMyCurriculumData } from "./hooks/useMyCurriculumData"
import { useMyCurriculumHandlers } from "./hooks/useMyCurriculumHandlers"
import type { MyCurriculumScreenProps } from "../types"

/**
 * MyCurriculumScreen - Instructor's curriculum management screen
 * 
 * This screen implements the curriculum management interface for instructors.
 * Identical design structure to student version but with instructor-specific content.
 * 
 * Features:
 * - Curriculum sections with lesson management
 * - Lesson editing and publishing controls
 * - Section organization and analytics
 * - Content performance tracking
 * - Add/edit lesson and section functionality
 */
export function MyCurriculumScreen({ navigation, route }: MyCurriculumScreenProps) {
  console.log("👨‍🏫 Instructor MyCurriculumScreen rendering...")

  // Get curriculum data using the data hook
  const data = useMyCurriculumData(route?.params?.courseId, route?.params?.course)

  // Get all handlers using the handlers hook
  const handlers = useMyCurriculumHandlers(navigation)

  console.log("👨‍🏫 Instructor Curriculum Data:", data)
  console.log("👨‍🏫 Instructor Curriculum Handlers:", handlers)

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor={colors.palette.neutral100}
    >
      {/* Main Content - Scrollable sections with management features */}
      <MyCurriculumScreenContent
        data={data}
        handlers={handlers}
      />
    </Screen>
  )
}
