/**
 * useMyCurriculumData Hook - Instructor Version
 * 
 * Manages all data for <PERSON>structor's Curriculum Management screen including:
 * - Curriculum sections with lesson management
 * - Course performance and analytics
 * - Lesson editing and content management
 * - Instructor-specific curriculum data
 */

import { useState, useEffect } from "react"
import type { MyCurriculumData, MyCurriculumSection, CurriculumPerformance } from "../../types"

export function useMyCurriculumData(
  courseId?: string,
  course?: any
): MyCurriculumData {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Mock data for Instructor's Curriculum Management
  const mockInstructorCurriculumSections: MyCurriculumSection[] = [
    {
      id: "section-01",
      title: "Section 01 - Introduction",
      duration: "25 Mins",
      description: "Introduction to React development fundamentals",
      completedLessons: 2,
      totalLessons: 2,
      progress: 85, // Average student completion rate
      isPublished: true,
      createdAt: "2024-01-01",
      lastUpdated: "2024-01-15",
      lessons: [
        {
          id: "lesson-01",
          number: 1,
          title: "Why Using React Development",
          duration: "15 Mins",
          status: "published",
          progress: 88, // Student completion rate
          videoUrl: "https://example.com/lesson1.mp4",
          description: "Learn the fundamentals of React and why it's important",
          materials: ["React Basics PDF", "Code Examples"],
          viewCount: 156,
          averageWatchTime: 12.5,
          completionRate: 88,
          lastUpdated: "2024-01-15",
          isPublished: true,
        },
        {
          id: "lesson-02",
          number: 2,
          title: "Setup Your React Environment",
          duration: "10 Mins",
          status: "published",
          progress: 82,
          videoUrl: "https://example.com/lesson2.mp4",
          description: "Set up your development environment for React",
          materials: ["Setup Guide", "Environment Config"],
          viewCount: 142,
          averageWatchTime: 8.3,
          completionRate: 82,
          lastUpdated: "2024-01-12",
          isPublished: true,
        }
      ]
    },
    {
      id: "section-02",
      title: "Section 02 - React Components",
      duration: "55 Mins",
      description: "Deep dive into React components and state management",
      completedLessons: 3,
      totalLessons: 4,
      progress: 75,
      isPublished: true,
      createdAt: "2024-01-05",
      lastUpdated: "2024-01-20",
      lessons: [
        {
          id: "lesson-03",
          number: 3,
          title: "Understanding React Components",
          duration: "18 Mins",
          status: "published",
          progress: 92,
          videoUrl: "https://example.com/lesson3.mp4",
          description: "Learn about functional and class components",
          materials: ["Component Examples", "Best Practices"],
          viewCount: 134,
          averageWatchTime: 16.2,
          completionRate: 92,
          lastUpdated: "2024-01-18",
          isPublished: true,
        },
        {
          id: "lesson-04",
          number: 4,
          title: "State Management with Hooks",
          duration: "22 Mins",
          status: "published",
          progress: 78,
          videoUrl: "https://example.com/lesson4.mp4",
          description: "Master useState and useEffect hooks",
          materials: ["Hooks Cheatsheet", "Practice Exercises"],
          viewCount: 128,
          averageWatchTime: 18.7,
          completionRate: 78,
          lastUpdated: "2024-01-20",
          isPublished: true,
        },
        {
          id: "lesson-05",
          number: 5,
          title: "Props and Component Communication",
          duration: "15 Mins",
          status: "published",
          progress: 65,
          videoUrl: "https://example.com/lesson5.mp4",
          description: "Learn how components communicate through props",
          materials: ["Props Examples", "Communication Patterns"],
          viewCount: 98,
          averageWatchTime: 12.1,
          completionRate: 65,
          lastUpdated: "2024-01-22",
          isPublished: true,
        },
        {
          id: "lesson-06",
          number: 6,
          title: "Advanced Component Patterns",
          duration: "20 Mins",
          status: "draft",
          progress: 0,
          videoUrl: "",
          description: "Advanced patterns like render props and HOCs",
          materials: [],
          viewCount: 0,
          averageWatchTime: 0,
          completionRate: 0,
          lastUpdated: "2024-01-25",
          isPublished: false,
        }
      ]
    },
    {
      id: "section-03",
      title: "Section 03 - Advanced Topics",
      duration: "45 Mins",
      description: "Advanced React concepts and performance optimization",
      completedLessons: 1,
      totalLessons: 3,
      progress: 45,
      isPublished: false,
      createdAt: "2024-01-10",
      lastUpdated: "2024-01-25",
      lessons: [
        {
          id: "lesson-07",
          number: 7,
          title: "Performance Optimization",
          duration: "25 Mins",
          status: "published",
          progress: 45,
          videoUrl: "https://example.com/lesson7.mp4",
          description: "Optimize React app performance",
          materials: ["Performance Guide", "Optimization Tools"],
          viewCount: 67,
          averageWatchTime: 18.3,
          completionRate: 45,
          lastUpdated: "2024-01-23",
          isPublished: true,
        },
        {
          id: "lesson-08",
          number: 8,
          title: "Testing React Components",
          duration: "20 Mins",
          status: "draft",
          progress: 0,
          videoUrl: "",
          description: "Learn testing strategies for React components",
          materials: [],
          viewCount: 0,
          averageWatchTime: 0,
          completionRate: 0,
          lastUpdated: "2024-01-25",
          isPublished: false,
        }
      ]
    }
  ]

  // Calculate course performance
  const coursePerformance: CurriculumPerformance = {
    totalSections: mockInstructorCurriculumSections.length,
    publishedSections: mockInstructorCurriculumSections.filter(s => s.isPublished).length,
    totalLessons: mockInstructorCurriculumSections.reduce((acc, section) => acc + section.totalLessons, 0),
    publishedLessons: mockInstructorCurriculumSections.reduce((acc, section) => 
      acc + section.lessons.filter(l => l.isPublished).length, 0),
    averageCompletionRate: 72,
    totalWatchTime: 450, // minutes
    studentEngagement: 78,
    lastUpdated: "2024-01-25",
  }

  // Find next lesson to edit (first draft lesson)
  const findNextLessonToEdit = () => {
    for (const section of mockInstructorCurriculumSections) {
      for (const lesson of section.lessons) {
        if (lesson.status === "draft") {
          return lesson
        }
      }
    }
    return undefined
  }

  const nextLessonToEdit = findNextLessonToEdit()

  // Simulate loading state
  useEffect(() => {
    if (courseId) {
      setIsLoading(true)
      const timer = setTimeout(() => {
        setIsLoading(false)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [courseId])

  return {
    curriculumSections: mockInstructorCurriculumSections,
    coursePerformance,
    nextLessonToEdit,
    isLoading,
    error,
  }
}
