/**
 * Instructor Curriculum Types
 * 
 * Types specific to instructor's curriculum management with lesson editing,
 * content management, and instructor-specific features.
 * Based on student types but adapted for instructor workflow.
 */

import type { NavigationProp } from "@react-navigation/native"

// Navigation Types
export interface MyCurriculumScreenProps {
  navigation: NavigationProp<any>
  route?: {
    params?: {
      courseId?: string
      course?: any
    }
  }
}

// Instructor Curriculum Lesson (with management features)
export interface MyCurriculumLesson {
  id: string
  number: number
  title: string
  duration: string
  status: "published" | "draft" | "archived"
  progress?: number // Student completion rate for this lesson
  videoUrl?: string
  description?: string
  materials?: string[]
  viewCount: number
  averageWatchTime: number
  completionRate: number // Percentage of students who completed this lesson
  lastUpdated: string
  isPublished: boolean
}

// Instructor Curriculum Section (with management features)
export interface MyCurriculumSection {
  id: string
  title: string
  duration: string
  lessons: MyCurriculumLesson[]
  completedLessons: number // Number of published lessons
  totalLessons: number
  progress: number // Overall section completion by students
  description?: string
  isPublished: boolean
  createdAt: string
  lastUpdated: string
}

// Course Performance for Curriculum
export interface CurriculumPerformance {
  totalSections: number
  publishedSections: number
  totalLessons: number
  publishedLessons: number
  averageCompletionRate: number
  totalWatchTime: number
  studentEngagement: number
  lastUpdated: string
}

// Instructor Curriculum Data
export interface MyCurriculumData {
  curriculumSections: MyCurriculumSection[]
  coursePerformance: CurriculumPerformance
  nextLessonToEdit?: MyCurriculumLesson
  isLoading: boolean
  error: string | null
}

// Instructor Curriculum Handlers
export interface MyCurriculumHandlers {
  handleBackPress: () => void
  handleLessonPress: (sectionId: string, lessonId: string) => void
  handleSectionPress: (sectionId: string) => void
  handleEditLesson: (sectionId: string, lessonId: string) => void
  handleAddLesson: (sectionId: string) => void
  handleEditSection: (sectionId: string) => void
  handleAddSection: () => void
  handleManageContent: () => void
  handlePublishSection: (sectionId: string) => void
  handleArchiveLesson: (sectionId: string, lessonId: string) => void
  handleViewAnalytics: () => void
}

// Component Props Types
export interface MyCurriculumHeaderSectionProps {
  onBackPress: () => void
}

export interface MyCurriculumContentSectionProps {
  sections: MyCurriculumSection[]
  onLessonPress: (sectionId: string, lessonId: string) => void
  onSectionPress: (sectionId: string) => void
  onEditLesson: (sectionId: string, lessonId: string) => void
  onAddLesson: (sectionId: string) => void
  onEditSection: (sectionId: string) => void
}

export interface MyCurriculumActionSectionProps {
  performance: CurriculumPerformance
  nextLessonToEdit?: MyCurriculumLesson
  onManageContent: () => void
  onAddSection: () => void
  onViewAnalytics: () => void
  isLoading?: boolean
}

// Lesson Management Types
export interface LessonFormData {
  title: string
  description: string
  duration: string
  videoUrl: string
  materials: string[]
  isPublished: boolean
}

// Section Management Types
export interface SectionFormData {
  title: string
  description: string
  isPublished: boolean
}

// Analytics Types for Curriculum
export interface LessonAnalytics {
  lessonId: string
  viewCount: number
  averageWatchTime: number
  completionRate: number
  dropOffPoints: number[]
  studentFeedback: number
  lastUpdated: string
}

export interface SectionAnalytics {
  sectionId: string
  totalViews: number
  averageCompletionTime: number
  completionRate: number
  studentSatisfaction: number
  lessonsAnalytics: LessonAnalytics[]
}

// Content Management Actions
export type CurriculumAction = 
  | "edit_lesson"
  | "add_lesson"
  | "edit_section"
  | "add_section"
  | "publish_lesson"
  | "archive_lesson"
  | "publish_section"
  | "archive_section"
  | "view_analytics"
  | "manage_content"
