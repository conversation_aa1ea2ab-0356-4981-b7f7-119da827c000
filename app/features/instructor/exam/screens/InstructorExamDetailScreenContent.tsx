/**
 * InstructorExamDetailScreenContent - Instructor Version
 * 
 * Content component for instructor's exam detail screen
 * Based on student ExamDetailScreenContent but adapted for instructor workflow
 */

import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Screen, Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { Ionicons } from "@expo/vector-icons"
import type { InstructorExamDetailData, InstructorExamDetailHandlers } from "../types"
import { InstructorExamDetailHeader, InstructorExamQuestionCard } from "../components"

interface InstructorExamDetailScreenContentProps {
  data: InstructorExamDetailData
  handlers: InstructorExamDetailHandlers
}

/**
 * InstructorExamDetailScreenContent - Main content for instructor exam detail
 * 
 * Features:
 * - Header with management actions
 * - Question display with analytics
 * - Navigation between questions
 * - Management action buttons
 * - Performance overview
 */
export function InstructorExamDetailScreenContent({ 
  data, 
  handlers 
}: InstructorExamDetailScreenContentProps) {
  const { themed } = useAppTheme()

  console.log("📝 Instructor ExamDetailScreenContent rendering...")

  const currentQuestion = data.questions[data.currentQuestionIndex]

  if (data.isLoading) {
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["top"]}
        backgroundColor="#F5F9FF"
        style={themed($screen)}
      >
        <View style={themed($loadingContainer)}>
          <Text style={themed($loadingText)}>Loading exam details...</Text>
        </View>
      </Screen>
    )
  }

  if (data.error) {
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["top"]}
        backgroundColor="#F5F9FF"
        style={themed($screen)}
      >
        <View style={themed($errorContainer)}>
          <Text style={themed($errorText)}>{data.error}</Text>
        </View>
      </Screen>
    )
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
    >
      <View style={themed($content)}>
        {/* Header */}
        <InstructorExamDetailHeader
          onBackPress={handlers.handleBackPress}
          currentQuestionNumber={data.currentQuestionIndex + 1}
          totalQuestions={data.questions.length}
          onEditQuestion={() => currentQuestion && handlers.handleEditQuestion(currentQuestion.id)}
          onAddQuestion={handlers.handleAddQuestion}
          onPreviewExam={handlers.handlePreviewExam}
        />

        {/* Question Card */}
        {currentQuestion && (
          <InstructorExamQuestionCard question={currentQuestion} />
        )}

        {/* Navigation Buttons */}
        {data.questions.length > 1 && (
          <View style={themed($navigationContainer)}>
            <View style={themed($navButtonContainer)}>
              {data.currentQuestionIndex > 0 ? (
                <TouchableOpacity
                  style={themed($navButton)}
                  onPress={handlers.handlePreviousQuestion}
                  activeOpacity={0.7}
                >
                  <Ionicons name="chevron-back" size={20} color="#FFFFFF" />
                  <Text style={themed($navButtonText)}>Previous</Text>
                </TouchableOpacity>
              ) : (
                <View style={themed($navButtonPlaceholder)} />
              )}
            </View>

            <View style={themed($navButtonContainer)}>
              {data.currentQuestionIndex < data.questions.length - 1 ? (
                <TouchableOpacity
                  style={themed($navButton)}
                  onPress={handlers.handleNextQuestion}
                  activeOpacity={0.7}
                >
                  <Text style={themed($navButtonText)}>Next</Text>
                  <Ionicons name="chevron-forward" size={20} color="#FFFFFF" />
                </TouchableOpacity>
              ) : (
                <View style={themed($navButtonPlaceholder)} />
              )}
            </View>
          </View>
        )}

        {/* Management Actions */}
        <View style={themed($actionsContainer)}>
          <TouchableOpacity
            style={themed([$actionButton, $publishButton])}
            onPress={handlers.handlePublishExam}
            activeOpacity={0.7}
          >
            <Ionicons name="cloud-upload-outline" size={20} color="#FFFFFF" />
            <Text style={themed($actionButtonText)}>Publish Exam</Text>
          </TouchableOpacity>

          <View style={themed($secondaryActions)}>
            <TouchableOpacity
              style={themed([$actionButton, $secondaryButton])}
              onPress={handlers.handleViewAnalytics}
              activeOpacity={0.7}
            >
              <Ionicons name="analytics-outline" size={18} color="#167F71" />
              <Text style={themed($secondaryButtonText)}>Analytics</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={themed([$actionButton, $secondaryButton])}
              onPress={handlers.handleViewResults}
              activeOpacity={0.7}
            >
              <Ionicons name="document-text-outline" size={18} color="#167F71" />
              <Text style={themed($secondaryButtonText)}>Results</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  paddingBottom: 40,
}

const $loadingContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 34,
}

const $loadingText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 16,
  lineHeight: 20,
  color: "#545454",
}

const $errorContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 34,
}

const $errorText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 16,
  lineHeight: 20,
  color: "#F44336",
  textAlign: "center",
}

const $navigationContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingHorizontal: 34,
  marginTop: 20,
  marginBottom: 30,
}

const $navButtonContainer: ViewStyle = {
  flex: 1,
  alignItems: "center",
}

const $navButtonPlaceholder: ViewStyle = {
  minWidth: 100,
  height: 48,
}

const $navButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#167F71",
  paddingHorizontal: 20,
  paddingVertical: 12,
  borderRadius: 12,
  minWidth: 100,
  gap: 6,
  shadowColor: "#167F71",
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 3,
}

const $navButtonText: TextStyle = {
  color: "#FFFFFF",
  fontSize: 14,
  fontFamily: "lexendDecaMedium", // 500 weight
}

const $actionsContainer: ViewStyle = {
  paddingHorizontal: 34,
  marginTop: 20,
}

const $actionButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  paddingVertical: 14,
  borderRadius: 12,
  gap: 8,
  marginBottom: 12,
}

const $publishButton: ViewStyle = {
  backgroundColor: "#167F71",
  shadowColor: "#167F71",
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 3,
}

const $actionButtonText: TextStyle = {
  color: "#FFFFFF",
  fontSize: 16,
  fontFamily: "lexendDecaMedium", // 500 weight
}

const $secondaryActions: ViewStyle = {
  flexDirection: "row",
  gap: 12,
}

const $secondaryButton: ViewStyle = {
  flex: 1,
  backgroundColor: "#F5F9FF",
  borderWidth: 1,
  borderColor: "#167F71",
}

const $secondaryButtonText: TextStyle = {
  color: "#167F71",
  fontSize: 14,
  fontFamily: "lexendDecaMedium", // 500 weight
}
