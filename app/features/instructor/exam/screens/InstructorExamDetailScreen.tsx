/**
 * InstructorExamDetailScreen - Instructor Version
 * 
 * Main exam detail screen for instructor's exam management
 * Based on student ExamDetailScreen but adapted for instructor workflow
 */

import React from "react"
import { useInstructorExamDetailData } from "../hooks/useInstructorExamDetailData"
import { useInstructorExamDetailHandlers } from "../hooks/useInstructorExamDetailHandlers"
import { InstructorExamDetailScreenContent } from "./InstructorExamDetailScreenContent"
import type { ExamDetailScreenProps } from "../types"

/**
 * InstructorExamDetailScreen - Exam management detail screen
 * 
 * Features:
 * - Question-by-question review with analytics
 * - Edit and manage exam questions
 * - View performance analytics
 * - Publish and manage exam status
 * - Student results overview
 * - Following the same pattern as student version
 */
export function InstructorExamDetailScreen({ navigation, route }: ExamDetailScreenProps) {
  console.log("📝 Instructor ExamDetailScreen rendering...")

  // Get exam data using the data hook
  const { updateCurrentQuestionIndex, ...data } = useInstructorExamDetailData(route?.params?.exam)

  // Get all handlers using the handlers hook
  const handlers = useInstructorExamDetailHandlers(
    navigation,
    updateCurrentQuestionIndex,
    data.currentQuestionIndex,
    data.questions.length
  )

  console.log("📝 Instructor ExamDetail Data:", {
    examTitle: data.exam?.courseTitle,
    questionsCount: data.questions.length,
    currentQuestion: data.currentQuestionIndex + 1,
    loading: data.isLoading,
    error: data.error
  })

  console.log("📝 Instructor ExamDetail Handlers:", Object.keys(handlers))

  return (
    <InstructorExamDetailScreenContent
      data={data}
      handlers={handlers}
    />
  )
}
