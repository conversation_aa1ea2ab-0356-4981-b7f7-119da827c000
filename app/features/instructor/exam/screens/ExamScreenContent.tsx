/**
 * ExamScreenContent - Instructor Version
 * 
 * Main content component for Instructor's Exam Management screen
 * Based on student version but adapted for instructor exam management workflow
 */

import React from "react"
import { View, ViewStyle, ScrollView, TouchableOpacity, TextStyle } from "react-native"
import { SafeAreaView } from "react-native-safe-area-context"
import { useAppTheme } from "@/utils/useAppTheme"
import { Text, Icon } from "@/components"
import type { ExamScreenData, ExamScreenHandlers } from "../types"
import {
  ExamHeader,
  ExamFilters,
  ExamList
} from "../components"

interface ExamScreenContentProps {
  data: ExamScreenData
  handlers: ExamScreenHandlers
}

/**
 * ExamScreenContent - Instructor's exam management content
 * 
 * Features:
 * - Header with back button and create exam action
 * - Filter tabs for exam status (All/Published/Draft/Scheduled/Archived)
 * - Scrollable list of exam management cards
 * - Loading and empty states
 * - Instructor-specific exam management features
 */
export function ExamScreenContent({
  data,
  handlers
}: ExamScreenContentProps) {
  const { themed } = useAppTheme()

  console.log("📝 Instructor ExamScreenContent rendering...")

  return (
    <SafeAreaView style={themed($container)} edges={["top"]}>
      {/* Header Section - Navigation and create exam */}
      <ExamHeader
        onBackPress={handlers.handleBackPress}
        onCreateExam={handlers.handleCreateExam}
      />

      {/* Filters Section - Status filters with counts */}
      <ExamFilters
        filters={data.filters}
        onFilterPress={handlers.handleFilterPress}
      />

      {/* Content Section - Scrollable exam list */}
      <ScrollView
        style={themed($scrollView)}
        showsVerticalScrollIndicator={false}
        bounces={false}
        contentContainerStyle={themed($scrollContent)}
      >
        <ExamList
          exams={data.filteredExams}
          onExamPress={handlers.handleExamPress}
          onExamEdit={handlers.handleExamEdit}
          onViewAnalytics={handlers.handleViewAnalytics}
          onViewResults={handlers.handleViewResults}
          loading={data.loading}
        />
      </ScrollView>

      {/* Floating Create New Exam Button */}
      <TouchableOpacity
        style={themed($floatingButton)}
        onPress={handlers.handleCreateExam}
        activeOpacity={0.8}
      >
        <Icon
          icon="plus"
          size={24}
          color="#FFFFFF"
        />
        <Text style={themed($floatingButtonText)}>Create New Exam</Text>
      </TouchableOpacity>
    </SafeAreaView>
  )
}

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: "#F5F9FF", // Figma background color
}

const $scrollView: ViewStyle = {
  flex: 1,
}

const $scrollContent: ViewStyle = {
  paddingBottom: 100, // Space for floating button
}

const $floatingButton: ViewStyle = {
  position: "absolute",
  bottom: 30,
  left: 34,
  right: 34,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: "#167F71",
  paddingVertical: 16,
  borderRadius: 16,
  gap: 8,
  borderWidth: 0.5,
  borderColor: "#167F71",
}

const $floatingButtonText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 16,
  lineHeight: 20,
  color: "#FFFFFF",
}
