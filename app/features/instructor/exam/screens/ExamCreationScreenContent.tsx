/**
 * ExamCreationScreenContent - Exam Creation Screen Content
 * 
 * Content component for instructor's exam creation screen
 * Based on InstructorExamDetailScreenContent but adapted for exam creation workflow
 */

import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Screen, Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { Ionicons } from "@expo/vector-icons"
import type { ExamCreationData, ExamCreationHandlers } from "../types"
import { ExamCreationHeader, ExamCreationQuestionCard } from "../components"

interface ExamCreationScreenContentProps {
  data: ExamCreationData
  handlers: ExamCreationHandlers
}

/**
 * ExamCreationScreenContent - Main content for exam creation
 * 
 * Features:
 * - Header with creation actions
 * - Question creation and editing
 * - Navigation between questions
 * - Save and publish actions
 * - Exam configuration
 */
export function ExamCreationScreenContent({ 
  data, 
  handlers 
}: ExamCreationScreenContentProps) {
  const { themed } = useAppTheme()

  console.log("📝 Exam Creation ScreenContent rendering...")

  const currentQuestion = data.questions[data.currentQuestionIndex]

  if (data.isLoading) {
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["top"]}
        backgroundColor="#F5F9FF"
        style={themed($screen)}
      >
        <View style={themed($loadingContainer)}>
          <Text style={themed($loadingText)}>Setting up exam creation...</Text>
        </View>
      </Screen>
    )
  }

  if (data.error) {
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["top"]}
        backgroundColor="#F5F9FF"
        style={themed($screen)}
      >
        <View style={themed($errorContainer)}>
          <Text style={themed($errorText)}>{data.error}</Text>
        </View>
      </Screen>
    )
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
    >
      <View style={themed($content)}>
        {/* Header */}
        <ExamCreationHeader
          onBackPress={handlers.handleBackPress}
          currentQuestionNumber={data.currentQuestionIndex + 1}
          totalQuestions={data.questions.length}
          onSaveDraft={handlers.handleSaveDraft}
          onPreviewExam={handlers.handlePreviewExam}
          examTitle={data.examInfo.title || "Untitled Exam"}
        />

        {/* Exam Info Section */}
        <View style={themed($examInfoContainer)}>
          <Text style={themed($sectionTitle)}>Exam Information</Text>
          <View style={themed($examInfoCard)}>
            <Text style={themed($examInfoText)}>
              Course: {data.examInfo.courseName || "Select Course"}
            </Text>
            <Text style={themed($examInfoText)}>
              Duration: {data.examInfo.duration} minutes
            </Text>
            <Text style={themed($examInfoText)}>
              Total Points: {data.examInfo.totalPoints}
            </Text>
            <Text style={themed($examInfoText)}>
              Questions: {data.questions.length}
            </Text>
          </View>
        </View>

        {/* Question Card */}
        {currentQuestion && (
          <ExamCreationQuestionCard 
            question={currentQuestion}
            onUpdateQuestion={handlers.handleUpdateQuestion}
            onDeleteQuestion={handlers.handleDeleteQuestion}
          />
        )}

        {/* Navigation Buttons */}
        {data.questions.length > 1 && (
          <View style={themed($navigationContainer)}>
            <View style={themed($navButtonContainer)}>
              {data.currentQuestionIndex > 0 ? (
                <TouchableOpacity
                  style={themed($navButton)}
                  onPress={handlers.handlePreviousQuestion}
                  activeOpacity={0.7}
                >
                  <Ionicons name="chevron-back" size={20} color="#FFFFFF" />
                  <Text style={themed($navButtonText)}>Previous</Text>
                </TouchableOpacity>
              ) : (
                <View style={themed($navButtonPlaceholder)} />
              )}
            </View>

            <View style={themed($navButtonContainer)}>
              {data.currentQuestionIndex < data.questions.length - 1 ? (
                <TouchableOpacity
                  style={themed($navButton)}
                  onPress={handlers.handleNextQuestion}
                  activeOpacity={0.7}
                >
                  <Text style={themed($navButtonText)}>Next</Text>
                  <Ionicons name="chevron-forward" size={20} color="#FFFFFF" />
                </TouchableOpacity>
              ) : (
                <View style={themed($navButtonPlaceholder)} />
              )}
            </View>
          </View>
        )}

        {/* Add Question Button */}
        <View style={themed($addQuestionContainer)}>
          <TouchableOpacity
            style={themed($addQuestionButton)}
            onPress={handlers.handleAddQuestion}
            activeOpacity={0.7}
          >
            <Ionicons name="add-circle-outline" size={24} color="#167F71" />
            <Text style={themed($addQuestionText)}>Add New Question</Text>
          </TouchableOpacity>
        </View>

        {/* Action Buttons */}
        <View style={themed($actionsContainer)}>
          <TouchableOpacity
            style={themed([$actionButton, $createButton])}
            onPress={handlers.handlePublishExam}
            activeOpacity={0.7}
          >
            <Ionicons name="add-circle-outline" size={20} color="#FFFFFF" />
            <Text style={themed($actionButtonText)}>Create Exam</Text>
          </TouchableOpacity>

          <View style={themed($secondaryActions)}>
            <TouchableOpacity
              style={themed([$actionButton, $secondaryButton])}
              onPress={handlers.handleSaveDraft}
              activeOpacity={0.7}
            >
              <Ionicons name="save-outline" size={18} color="#167F71" />
              <Text style={themed($secondaryButtonText)}>Save Draft</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={themed([$actionButton, $secondaryButton])}
              onPress={handlers.handlePreviewExam}
              activeOpacity={0.7}
            >
              <Ionicons name="eye-outline" size={18} color="#167F71" />
              <Text style={themed($secondaryButtonText)}>Preview</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  paddingBottom: 40,
}

const $loadingContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 34,
}

const $loadingText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 16,
  lineHeight: 20,
  color: "#545454",
}

const $errorContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 34,
}

const $errorText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 16,
  lineHeight: 20,
  color: "#F44336",
  textAlign: "center",
}

const $examInfoContainer: ViewStyle = {
  paddingHorizontal: 34,
  marginBottom: 16,
}

const $sectionTitle: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 16,
  lineHeight: 20,
  color: "#202244",
  marginBottom: 12,
}

const $examInfoCard: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 12,
  padding: 16,
  borderWidth: 0.5,
  borderColor: "#E5E5E5",
}

const $examInfoText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#545454",
  marginBottom: 4,
}

const $navigationContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingHorizontal: 34,
  marginTop: 20,
  marginBottom: 30,
}

const $navButtonContainer: ViewStyle = {
  flex: 1,
  alignItems: "center",
}

const $navButtonPlaceholder: ViewStyle = {
  minWidth: 100,
  height: 48,
}

const $navButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#167F71",
  paddingHorizontal: 20,
  paddingVertical: 12,
  borderRadius: 12,
  minWidth: 100,
  gap: 6,
  borderWidth: 0.5,
  borderColor: "#167F71",
}

const $navButtonText: TextStyle = {
  color: "#FFFFFF",
  fontSize: 14,
  fontFamily: "lexendDecaMedium", // 500 weight
}

const $addQuestionContainer: ViewStyle = {
  paddingHorizontal: 34,
  marginBottom: 30,
}

const $addQuestionButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: "#F5F9FF",
  borderWidth: 2,
  borderColor: "#167F71",
  borderStyle: "dashed",
  paddingVertical: 16,
  borderRadius: 12,
  gap: 8,
}

const $addQuestionText: TextStyle = {
  color: "#167F71",
  fontSize: 16,
  fontFamily: "lexendDecaMedium", // 500 weight
}

const $actionsContainer: ViewStyle = {
  paddingHorizontal: 34,
  marginTop: 20,
}

const $actionButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  paddingVertical: 14,
  borderRadius: 12,
  gap: 8,
  marginBottom: 12,
}

const $createButton: ViewStyle = {
  backgroundColor: "#167F71",
  borderWidth: 0.5,
  borderColor: "#167F71",
}

const $actionButtonText: TextStyle = {
  color: "#FFFFFF",
  fontSize: 16,
  fontFamily: "lexendDecaMedium", // 500 weight
}

const $secondaryActions: ViewStyle = {
  flexDirection: "row",
  gap: 12,
}

const $secondaryButton: ViewStyle = {
  flex: 1,
  backgroundColor: "#F5F9FF",
  borderWidth: 1,
  borderColor: "#167F71",
}

const $secondaryButtonText: TextStyle = {
  color: "#167F71",
  fontSize: 14,
  fontFamily: "lexendDecaMedium", // 500 weight
}
