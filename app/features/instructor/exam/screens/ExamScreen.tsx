/**
 * ExamScreen - Instructor Version
 *
 * Main Exam Management screen for instructor's exam administration
 * Based on student ExamScreen but with instructor-specific features:
 * - Exam creation and management
 * - Student performance analytics
 * - Exam status management (draft, published, archived)
 * - Results and analytics viewing
 *
 * Following the same pattern as student version with:
 * - useExamData: Manages all exam data and filtering
 * - useExamHandlers: Manages all interaction handlers
 * - ExamScreenContent: Contains all the scrollable content sections
 *
 * Benefits of this architecture:
 * - Easier to maintain and update
 * - Better separation of concerns
 * - Reusable hooks and components
 * - Cleaner code organization
 * - Easier testing
 * - Independent from student version for future development
 */

import React from "react"
import { Screen } from "@/components"
import { colors } from "@/theme"
import { ExamScreenContent } from "./ExamScreenContent"
import { useExamData } from "../hooks/useExamData"
import { useExamHandlers } from "../hooks/useExamHandlers"
import type { ExamScreenProps } from "../types"

/**
 * ExamScreen - Instructor's exam management screen
 * 
 * This screen implements the exam management interface for instructors.
 * Identical design structure to student version but with instructor-specific content.
 * 
 * Features:
 * - Exam list with status filtering
 * - Create new exam functionality
 * - Exam management actions (edit, duplicate, archive)
 * - Analytics and results viewing
 * - Student performance tracking
 */
export function ExamScreen({ navigation }: ExamScreenProps) {
  console.log("👨‍🏫 Instructor ExamScreen rendering...")

  // Get exam data using the data hook
  const { data, allExams, setActiveFilter, refresh } = useExamData()

  // Get all handlers using the handlers hook
  const handlers = useExamHandlers(navigation, setActiveFilter, allExams)

  console.log("👨‍🏫 Instructor Exam Data:", {
    totalExams: data.totalExams,
    filteredCount: data.filteredExams.length,
    activeFilter: data.activeFilter,
    loading: data.loading
  })

  return (
    <ExamScreenContent
      data={data}
      handlers={handlers}
    />
  )
}
