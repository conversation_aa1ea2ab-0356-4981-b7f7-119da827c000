/**
 * ExamCreationScreen - Exam Creation Screen
 * 
 * Main exam creation screen for instructor's exam creation workflow
 * Based on InstructorExamDetailScreen but adapted for creating new exams
 */

import React from "react"
import { useExamCreationData } from "../hooks/useExamCreationData"
import { useExamCreationHandlers } from "../hooks/useExamCreationHandlers"
import { ExamCreationScreenContent } from "./ExamCreationScreenContent"
import type { ExamCreationScreenProps } from "../types"

/**
 * ExamCreationScreen - Exam creation and management screen
 * 
 * Features:
 * - Create new exam with multiple questions
 * - Configure exam settings and parameters
 * - Add/edit/delete questions
 * - Save as draft or publish exam
 * - Preview exam before publishing
 * - Following the same pattern as exam detail screen
 */
export function ExamCreationScreen({ navigation, route }: ExamCreationScreenProps) {
  console.log("📝 Exam Creation Screen rendering...")

  // Get exam creation data using the data hook
  const { 
    updateExamInfo,
    updateQuestion,
    addQuestion,
    deleteQuestion,
    updateCurrentQuestionIndex,
    ...data 
  } = useExamCreationData(route?.params?.courseId, route?.params?.courseName)

  // Get all handlers using the handlers hook
  const handlers = useExamCreationHandlers(
    navigation,
    updateExamInfo,
    updateQuestion,
    addQuestion,
    deleteQuestion,
    updateCurrentQuestionIndex,
    data.currentQuestionIndex,
    data.questions.length,
    data
  )

  console.log("📝 Exam Creation Data:", {
    examTitle: data.examInfo.title || "New Exam",
    questionsCount: data.questions.length,
    currentQuestion: data.currentQuestionIndex + 1,
    totalPoints: data.examInfo.totalPoints,
    loading: data.isLoading,
    error: data.error
  })

  console.log("📝 Exam Creation Handlers:", Object.keys(handlers))

  return (
    <ExamCreationScreenContent
      data={data}
      handlers={handlers}
    />
  )
}
