/**
 * InstructorExamDetailHeader - Instructor Version
 * 
 * Header component for instructor's exam detail screen
 * Based on student ExamDetailHeader but adapted for instructor workflow
 */

import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { Ionicons } from "@expo/vector-icons"

interface InstructorExamDetailHeaderProps {
  onBackPress: () => void
  currentQuestionNumber: number
  totalQuestions: number
  onEditQuestion?: () => void
  onAddQuestion?: () => void
  onPreviewExam?: () => void
}

/**
 * InstructorExamDetailHeader - Header for instructor exam detail
 * 
 * Features:
 * - Back navigation
 * - Question counter (current/total)
 * - Edit question action
 * - Add question action
 * - Preview exam action
 * - Instructor-specific management tools
 */
export function InstructorExamDetailHeader({
  onBackPress,
  currentQuestionNumber,
  totalQuestions,
  onEditQuestion,
  onAddQuestion,
  onPreviewExam
}: InstructorExamDetailHeaderProps) {
  const { themed } = useAppTheme()

  console.log("📝 Instructor ExamDetailHeader rendering...")

  const handleBackPress = () => {
    console.log("🔙 Instructor Exam Detail Header: Back pressed")
    onBackPress()
  }

  const handleEditPress = () => {
    console.log("✏️ Instructor Exam Detail Header: Edit pressed")
    onEditQuestion?.()
  }

  const handleAddPress = () => {
    console.log("➕ Instructor Exam Detail Header: Add pressed")
    onAddQuestion?.()
  }

  const handlePreviewPress = () => {
    console.log("👁️ Instructor Exam Detail Header: Preview pressed")
    onPreviewExam?.()
  }

  return (
    <View style={themed($container)}>
      {/* Top Row - Back button and actions */}
      <View style={themed($topRow)}>
        {/* Back Button */}
        <TouchableOpacity
          style={themed($backButton)}
          onPress={handleBackPress}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-back" size={24} color="#202244" />
        </TouchableOpacity>

        {/* Title */}
        <Text style={themed($title)}>Exam Management</Text>

        {/* Action Buttons */}
        <View style={themed($actionButtons)}>
          <TouchableOpacity
            style={themed($actionButton)}
            onPress={handleEditPress}
            activeOpacity={0.7}
          >
            <Ionicons name="create-outline" size={20} color="#167F71" />
          </TouchableOpacity>

          <TouchableOpacity
            style={themed($actionButton)}
            onPress={handleAddPress}
            activeOpacity={0.7}
          >
            <Ionicons name="add-circle-outline" size={20} color="#167F71" />
          </TouchableOpacity>

          <TouchableOpacity
            style={themed($actionButton)}
            onPress={handlePreviewPress}
            activeOpacity={0.7}
          >
            <Ionicons name="eye-outline" size={20} color="#167F71" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Bottom Row - Question counter */}
      <View style={themed($bottomRow)}>
        <Text style={themed($questionCounter)}>
          Question {currentQuestionNumber} of {totalQuestions}
        </Text>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  backgroundColor: "#FFFFFF",
  paddingHorizontal: 34,
  paddingTop: 16,
  paddingBottom: 20,
  borderBottomWidth: 1,
  borderBottomColor: "#E8F1FF",
}

const $topRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: 12,
}

const $backButton: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "#F5F9FF",
  justifyContent: "center",
  alignItems: "center",
}

const $title: TextStyle = {
  flex: 1,
  textAlign: "center",
  fontFamily: "lexendDecaMedium", // 500 weight for header
  fontSize: 18,
  lineHeight: 22,
  color: "#202244",
  marginHorizontal: 16,
}

const $actionButtons: ViewStyle = {
  flexDirection: "row",
  gap: 8,
}

const $actionButton: ViewStyle = {
  width: 36,
  height: 36,
  borderRadius: 18,
  backgroundColor: "#F5F9FF",
  justifyContent: "center",
  alignItems: "center",
}

const $bottomRow: ViewStyle = {
  alignItems: "center",
}

const $questionCounter: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for counter
  fontSize: 14,
  lineHeight: 18,
  color: "#545454",
}
