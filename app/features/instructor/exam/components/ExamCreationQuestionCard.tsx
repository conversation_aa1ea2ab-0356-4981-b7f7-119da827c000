/**
 * ExamCreationQuestionCard - Exam Creation Question Card
 * 
 * Question card component for instructor's exam creation screen
 * Based on InstructorExamQuestionCard but adapted for creating/editing questions
 */

import React, { useState } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, TextInput } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { Ionicons } from "@expo/vector-icons"
import type { ExamCreationQuestion } from "../types"

interface ExamCreationQuestionCardProps {
  question: ExamCreationQuestion
  onUpdateQuestion: (questionId: string, updates: Partial<ExamCreationQuestion>) => void
  onDeleteQuestion?: (questionId: string) => void
}

/**
 * ExamCreationQuestionCard - Editable question card for exam creation
 * 
 * Features:
 * - Inline question editing
 * - Question type selection
 * - Points and time limit configuration
 * - Answer options editing (for multiple choice)
 * - Correct answer selection
 * - Delete question action
 */
export function ExamCreationQuestionCard({ 
  question, 
  onUpdateQuestion,
  onDeleteQuestion 
}: ExamCreationQuestionCardProps) {
  const { themed } = useAppTheme()
  const [isEditing, setIsEditing] = useState(false)

  console.log("📝 Exam Creation QuestionCard rendering for:", question.id)

  const handleQuestionChange = (text: string) => {
    onUpdateQuestion(question.id, { question: text })
  }

  const handlePointsChange = (text: string) => {
    const points = parseInt(text) || 0
    onUpdateQuestion(question.id, { points })
  }

  const handleTimeLimitChange = (text: string) => {
    const timeLimit = parseInt(text) || 0
    onUpdateQuestion(question.id, { timeLimit })
  }

  const handleOptionChange = (index: number, text: string) => {
    if (question.options) {
      const newOptions = [...question.options]
      newOptions[index] = text
      onUpdateQuestion(question.id, { options: newOptions })
    }
  }

  const handleCorrectAnswerSelect = (option: string) => {
    onUpdateQuestion(question.id, { correctAnswer: option })
  }

  const handleTypeChange = (type: 'multiple-choice' | 'essay' | 'true-false') => {
    let updates: Partial<ExamCreationQuestion> = { type }
    
    if (type === 'multiple-choice') {
      updates.options = ["", "", "", ""]
      updates.correctAnswer = ""
    } else if (type === 'true-false') {
      updates.options = ["True", "False"]
      updates.correctAnswer = ""
    } else {
      updates.options = undefined
      updates.correctAnswer = undefined
    }
    
    onUpdateQuestion(question.id, updates)
  }

  const handleDeletePress = () => {
    console.log("🗑️ Delete question pressed:", question.id)
    onDeleteQuestion?.(question.id)
  }

  const getQuestionTypeLabel = (type: string) => {
    switch (type) {
      case 'multiple-choice': return "Multiple Choice"
      case 'essay': return "Essay"
      case 'true-false': return "True/False"
      default: return "Unknown"
    }
  }

  return (
    <View style={themed($container)}>
      {/* Question Header */}
      <View style={themed($header)}>
        <View style={themed($typeContainer)}>
          <TouchableOpacity 
            style={themed($typeButton)}
            onPress={() => setIsEditing(!isEditing)}
          >
            <Text style={themed($typeText)}>{getQuestionTypeLabel(question.type)}</Text>
            <Ionicons name="chevron-down" size={16} color="#167F71" />
          </TouchableOpacity>
        </View>
        
        <View style={themed($configContainer)}>
          <View style={themed($inputGroup)}>
            <Text style={themed($inputLabel)}>Points:</Text>
            <TextInput
              style={themed($smallInput)}
              value={question.points.toString()}
              onChangeText={handlePointsChange}
              keyboardType="numeric"
              placeholder="10"
            />
          </View>
          
          <View style={themed($inputGroup)}>
            <Text style={themed($inputLabel)}>Time (s):</Text>
            <TextInput
              style={themed($smallInput)}
              value={question.timeLimit?.toString() || ""}
              onChangeText={handleTimeLimitChange}
              keyboardType="numeric"
              placeholder="120"
            />
          </View>

          <TouchableOpacity
            style={themed($deleteButton)}
            onPress={handleDeletePress}
            activeOpacity={0.7}
          >
            <Ionicons name="trash-outline" size={18} color="#F44336" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Question Input */}
      <View style={themed($questionContainer)}>
        <Text style={themed($questionLabel)}>Question:</Text>
        <TextInput
          style={themed($questionInput)}
          value={question.question}
          onChangeText={handleQuestionChange}
          placeholder="Enter your question here..."
          multiline
          numberOfLines={3}
        />
      </View>

      {/* Answer Options (for multiple choice and true/false) */}
      {(question.type === 'multiple-choice' || question.type === 'true-false') && question.options && (
        <View style={themed($optionsContainer)}>
          <Text style={themed($optionsLabel)}>Answer Options:</Text>
          {question.options.map((option, index) => (
            <View key={index} style={themed($optionRow)}>
              <TouchableOpacity
                style={themed([
                  $optionSelector,
                  option === question.correctAnswer && $selectedOption
                ])}
                onPress={() => handleCorrectAnswerSelect(option)}
                activeOpacity={0.7}
              >
                <Text style={themed($optionLetter)}>
                  {question.type === 'true-false' 
                    ? (index === 0 ? 'T' : 'F')
                    : String.fromCharCode(65 + index)
                  }
                </Text>
              </TouchableOpacity>
              
              <TextInput
                style={themed($optionInput)}
                value={option}
                onChangeText={(text) => handleOptionChange(index, text)}
                placeholder={`Option ${String.fromCharCode(65 + index)}`}
                editable={question.type !== 'true-false'}
              />
              
              {option === question.correctAnswer && (
                <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
              )}
            </View>
          ))}
          
          <Text style={themed($correctAnswerHint)}>
            Tap the letter to mark as correct answer
          </Text>
        </View>
      )}

      {/* Essay Question Note */}
      {question.type === 'essay' && (
        <View style={themed($essayNote)}>
          <Ionicons name="information-circle-outline" size={16} color="#167F71" />
          <Text style={themed($essayNoteText)}>
            Essay questions will be manually graded by the instructor
          </Text>
        </View>
      )}
    </View>
  )
}

const $container: ViewStyle = {
  backgroundColor: "#FFFFFF",
  marginHorizontal: 34,
  marginVertical: 16,
  borderRadius: 16,
  padding: 20,
  borderWidth: 0.5,
  borderColor: "#E5E5E5",
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 16,
}

const $typeContainer: ViewStyle = {
  flex: 1,
}

const $typeButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#F5F9FF",
  paddingHorizontal: 12,
  paddingVertical: 8,
  borderRadius: 12,
  borderWidth: 1,
  borderColor: "#E8F1FF",
  gap: 6,
}

const $typeText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 12,
  lineHeight: 15,
  color: "#167F71",
}

const $configContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 12,
}

const $inputGroup: ViewStyle = {
  alignItems: "center",
  gap: 4,
}

const $inputLabel: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 10,
  lineHeight: 12,
  color: "#545454",
}

const $smallInput: ViewStyle = {
  width: 50,
  height: 32,
  borderWidth: 1,
  borderColor: "#E8F1FF",
  borderRadius: 8,
  paddingHorizontal: 8,
  textAlign: "center",
  fontFamily: "lexendDecaLight",
  fontSize: 12,
  color: "#202244",
}

const $deleteButton: ViewStyle = {
  width: 32,
  height: 32,
  borderRadius: 16,
  backgroundColor: "#FFF5F5",
  justifyContent: "center",
  alignItems: "center",
}

const $questionContainer: ViewStyle = {
  marginBottom: 16,
}

const $questionLabel: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
  marginBottom: 8,
}

const $questionInput: ViewStyle = {
  borderWidth: 1,
  borderColor: "#E8F1FF",
  borderRadius: 12,
  padding: 12,
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 14,
  lineHeight: 20,
  color: "#202244",
  minHeight: 80,
  textAlignVertical: "top",
}

const $optionsContainer: ViewStyle = {
  marginBottom: 16,
}

const $optionsLabel: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
  marginBottom: 12,
}

const $optionRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: 8,
  gap: 12,
}

const $optionSelector: ViewStyle = {
  width: 32,
  height: 32,
  borderRadius: 16,
  backgroundColor: "#F5F9FF",
  borderWidth: 2,
  borderColor: "#E8F1FF",
  justifyContent: "center",
  alignItems: "center",
}

const $selectedOption: ViewStyle = {
  backgroundColor: "#E8F5E8",
  borderColor: "#4CAF50",
}

const $optionLetter: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
}

const $optionInput: ViewStyle = {
  flex: 1,
  borderWidth: 1,
  borderColor: "#E8F1FF",
  borderRadius: 8,
  paddingHorizontal: 12,
  paddingVertical: 8,
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
}

const $correctAnswerHint: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 11,
  lineHeight: 14,
  color: "#545454",
  fontStyle: "italic",
  marginTop: 8,
}

const $essayNote: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#F5F9FF",
  padding: 12,
  borderRadius: 8,
  gap: 8,
}

const $essayNoteText: TextStyle = {
  flex: 1,
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 12,
  lineHeight: 15,
  color: "#167F71",
}
