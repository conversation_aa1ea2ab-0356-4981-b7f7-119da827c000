/**
 * ExamCard - Instructor Version
 * 
 * Individual exam card component for instructor's exam management
 * Based on student ExamCard but adapted for instructor workflow with analytics
 */

import React from "react"
import { View, Text, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { InstructorExamItem } from "../types"

interface ExamCardProps {
  exam: InstructorExamItem
  onPress: (exam: InstructorExamItem) => void
  onEdit?: (examId: string) => void
  onViewAnalytics?: (examId: string) => void
  onViewResults?: (examId: string) => void
}

/**
 * ExamCard - Instructor's exam management card
 * 
 * Features:
 * - Course info with exam title and subject
 * - Status badge (draft, published, archived, scheduled)
 * - Student analytics (count, completion rate, average score)
 * - Management actions (edit, analytics, results)
 * - Consistent styling with student version
 */
export function ExamCard({ 
  exam, 
  onPress, 
  onEdit, 
  onViewAnalytics, 
  onViewResults 
}: ExamCardProps) {
  const { themed } = useAppTheme()

  console.log("📝 Instructor ExamCard rendering for:", exam.courseTitle)

  const handleCardPress = () => {
    console.log("📝 Instructor ExamCard pressed:", exam.courseTitle)
    onPress(exam)
  }

  const handleEditPress = (e: any) => {
    e.stopPropagation()
    console.log("✏️ Instructor ExamCard edit pressed:", exam.id)
    onEdit?.(exam.id)
  }

  const handleAnalyticsPress = (e: any) => {
    e.stopPropagation()
    console.log("📊 Instructor ExamCard analytics pressed:", exam.id)
    onViewAnalytics?.(exam.id)
  }

  const handleResultsPress = (e: any) => {
    e.stopPropagation()
    console.log("📋 Instructor ExamCard results pressed:", exam.id)
    onViewResults?.(exam.id)
  }

  const getStatusColor = () => {
    switch (exam.status) {
      case "published": return "#167F71"
      case "draft": return "#FF6B00"
      case "scheduled": return "#0961F5"
      case "archived": return "#A0A4AB"
      default: return "#A0A4AB"
    }
  }

  const getStatusText = () => {
    switch (exam.status) {
      case "published": return "Published"
      case "draft": return "Draft"
      case "scheduled": return "Scheduled"
      case "archived": return "Archived"
      default: return "Unknown"
    }
  }

  return (
    <TouchableOpacity
      style={themed($cardContainer)}
      onPress={handleCardPress}
      activeOpacity={0.7}
    >
      {/* Course Avatar */}
      <View style={themed($avatarContainer)}>
        <View style={themed($avatar)} />
      </View>

      {/* Content */}
      <View style={themed($contentContainer)}>
        {/* Header Row: Course Title + Status */}
        <View style={themed($headerRow)}>
          <Text style={themed($courseTitle)} numberOfLines={1}>
            {exam.courseTitle}
          </Text>
          <View style={themed([$statusBadge, { backgroundColor: `${getStatusColor()}20` }])}>
            <Text style={themed([$statusText, { color: getStatusColor() }])}>
              {getStatusText()}
            </Text>
          </View>
        </View>

        {/* Subject */}
        <Text style={themed($subject)} numberOfLines={2}>
          {exam.subject}
        </Text>

        {/* Analytics Row */}
        <View style={themed($analyticsRow)}>
          <View style={themed($analyticItem)}>
            <Icon icon="users" size={12} color="#0961F5" />
            <Text style={themed($analyticText)}>{exam.studentCount} students</Text>
          </View>
          <View style={themed($analyticItem)}>
            <Icon icon="checkCircle" size={12} color="#167F71" />
            <Text style={themed($analyticText)}>{exam.completionRate.toFixed(1)}% completed</Text>
          </View>
        </View>

        {/* Bottom Info */}
        <View style={themed($bottomInfo)}>
          {/* Lesson Info */}
          <View style={themed($infoGroup)}>
            <Icon icon="book" size={14} color="#545454" />
            <Text style={themed($infoText)}>{exam.lesson}</Text>
          </View>

          {/* Date/Time Info */}
          <View style={themed($infoGroup)}>
            <Icon icon="clock" size={14} color="#545454" />
            <Text style={themed($infoText)}>{exam.dateTime}</Text>
          </View>
        </View>
      </View>

      {/* Actions Menu */}
      <View style={themed($actionsContainer)}>
        {/* Edit Button */}
        <TouchableOpacity 
          style={themed($actionButton)}
          onPress={handleEditPress}
          activeOpacity={0.7}
        >
          <Icon icon="edit" size={16} color="#0961F5" />
        </TouchableOpacity>

        {/* Analytics Button */}
        <TouchableOpacity 
          style={themed($actionButton)}
          onPress={handleAnalyticsPress}
          activeOpacity={0.7}
        >
          <Icon icon="chart" size={16} color="#167F71" />
        </TouchableOpacity>

        {/* Results Button */}
        <TouchableOpacity 
          style={themed($actionButton)}
          onPress={handleResultsPress}
          activeOpacity={0.7}
        >
          <Icon icon="list" size={16} color="#FF6B00" />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  )
}

const $cardContainer: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  padding: 20,
  marginHorizontal: 34,
  marginBottom: 15,
  flexDirection: "row",
  alignItems: "flex-start",
  borderWidth: 0.5,
  borderColor: "#E5E5E5",
}

const $avatarContainer: ViewStyle = {
  marginRight: 12,
  marginTop: 2,
}

const $avatar: ViewStyle = {
  width: 46,
  height: 46,
  borderRadius: 23,
  backgroundColor: "#000000",
}

const $contentContainer: ViewStyle = {
  flex: 1,
  paddingRight: 8,
}

const $headerRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 8,
}

const $courseTitle: TextStyle = {
  fontSize: 16,
  fontFamily: "lexendDecaSemiBold", // 600 weight for card titles
  color: "#202244",
  lineHeight: 23,
  flex: 1,
  marginRight: 8,
}

const $statusBadge: ViewStyle = {
  paddingHorizontal: 8,
  paddingVertical: 4,
  borderRadius: 12,
}

const $statusText: TextStyle = {
  fontSize: 10,
  fontFamily: "lexendDecaMedium", // 500 weight for status
  lineHeight: 12,
}

const $subject: TextStyle = {
  fontSize: 13,
  fontFamily: "lexendDecaLight", // 300 weight for descriptions
  color: "#545454",
  lineHeight: 16,
  marginBottom: 12,
}

const $analyticsRow: ViewStyle = {
  flexDirection: "row",
  gap: 16,
  marginBottom: 12,
}

const $analyticItem: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 4,
}

const $analyticText: TextStyle = {
  fontSize: 11,
  fontFamily: "lexendDecaMedium", // 500 weight for analytics
  color: "#545454",
  lineHeight: 14,
}

const $bottomInfo: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
}

const $infoGroup: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 8,
}

const $infoText: TextStyle = {
  fontSize: 12,
  fontFamily: "lexendDecaLight", // 300 weight for caption text
  color: "#202244",
  lineHeight: 15,
}

const $actionsContainer: ViewStyle = {
  flexDirection: "column",
  gap: 8,
  marginTop: 2,
}

const $actionButton: ViewStyle = {
  width: 32,
  height: 32,
  borderRadius: 16,
  backgroundColor: "#F8F9FA",
  alignItems: "center",
  justifyContent: "center",
}
