/**
 * InstructorExamQuestionCard - Instructor Version
 * 
 * Question card component for instructor's exam detail screen
 * Based on student ExamQuestionCard but with analytics and management features
 */

import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import type { InstructorExamQuestion } from "../types"

interface InstructorExamQuestionCardProps {
  question: InstructorExamQuestion
}

/**
 * InstructorExamQuestionCard - Question display with analytics
 * 
 * Features:
 * - Question text and type display
 * - Points and time limit info
 * - Analytics overview (attempts, success rate, difficulty)
 * - Performance indicators
 * - Management-focused layout
 */
export function InstructorExamQuestionCard({ question }: InstructorExamQuestionCardProps) {
  const { themed } = useAppTheme()

  console.log("📝 Instructor ExamQuestionCard rendering for:", question.id)

  const successRate = question.analytics.totalAttempts > 0 
    ? Math.round((question.analytics.correctAnswers / question.analytics.totalAttempts) * 100)
    : 0

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'easy': return "#4CAF50"
      case 'medium': return "#FF9800"
      case 'hard': return "#F44336"
      default: return "#757575"
    }
  }

  const getQuestionTypeLabel = (type: string) => {
    switch (type) {
      case 'multiple-choice': return "Multiple Choice"
      case 'essay': return "Essay"
      case 'true-false': return "True/False"
      default: return "Unknown"
    }
  }

  return (
    <View style={themed($container)}>
      {/* Question Header */}
      <View style={themed($header)}>
        <View style={themed($typeContainer)}>
          <Text style={themed($typeText)}>{getQuestionTypeLabel(question.type)}</Text>
          <View style={themed([$difficultyBadge, { backgroundColor: getDifficultyColor(question.analytics.difficultyLevel) }])}>
            <Text style={themed($difficultyText)}>{question.analytics.difficultyLevel.toUpperCase()}</Text>
          </View>
        </View>
        
        <View style={themed($pointsContainer)}>
          <Text style={themed($pointsText)}>{question.points} pts</Text>
          {question.timeLimit && (
            <Text style={themed($timeText)}>{question.timeLimit}s</Text>
          )}
        </View>
      </View>

      {/* Question Text */}
      <View style={themed($questionContainer)}>
        <Text style={themed($questionText)}>{question.question}</Text>
      </View>

      {/* Answer Options (for multiple choice) */}
      {question.type === 'multiple-choice' && question.options && (
        <View style={themed($optionsContainer)}>
          {question.options.map((option, index) => {
            const isCorrect = option === question.correctAnswer
            return (
              <View 
                key={index} 
                style={themed([
                  $optionItem,
                  isCorrect && $correctOption
                ])}
              >
                <Text style={themed([
                  $optionText,
                  isCorrect && $correctOptionText
                ])}>
                  {String.fromCharCode(65 + index)}. {option}
                </Text>
                {isCorrect && (
                  <Text style={themed($correctLabel)}>✓ Correct</Text>
                )}
              </View>
            )
          })}
        </View>
      )}

      {/* Analytics Section */}
      <View style={themed($analyticsContainer)}>
        <Text style={themed($analyticsTitle)}>Performance Analytics</Text>
        
        <View style={themed($analyticsRow)}>
          <View style={themed($analyticItem)}>
            <Text style={themed($analyticValue)}>{question.analytics.totalAttempts}</Text>
            <Text style={themed($analyticLabel)}>Attempts</Text>
          </View>
          
          <View style={themed($analyticItem)}>
            <Text style={themed($analyticValue)}>{successRate}%</Text>
            <Text style={themed($analyticLabel)}>Success Rate</Text>
          </View>
          
          <View style={themed($analyticItem)}>
            <Text style={themed($analyticValue)}>{question.analytics.averageTime}s</Text>
            <Text style={themed($analyticLabel)}>Avg Time</Text>
          </View>
        </View>

        {/* Common Mistakes */}
        {question.analytics.commonMistakes.length > 0 && (
          <View style={themed($mistakesContainer)}>
            <Text style={themed($mistakesTitle)}>Common Mistakes:</Text>
            {question.analytics.commonMistakes.slice(0, 2).map((mistake, index) => (
              <Text key={index} style={themed($mistakeText)}>• {mistake}</Text>
            ))}
          </View>
        )}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  backgroundColor: "#FFFFFF",
  marginHorizontal: 34,
  marginVertical: 16,
  borderRadius: 16,
  padding: 20,
  borderWidth: 0.5,
  borderColor: "#E5E5E5",
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 16,
}

const $typeContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  gap: 8,
}

const $typeText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 12,
  lineHeight: 15,
  color: "#167F71",
}

const $difficultyBadge: ViewStyle = {
  paddingHorizontal: 8,
  paddingVertical: 2,
  borderRadius: 8,
}

const $difficultyText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 10,
  lineHeight: 12,
  color: "#FFFFFF",
}

const $pointsContainer: ViewStyle = {
  alignItems: "flex-end",
}

const $pointsText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
}

const $timeText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 12,
  lineHeight: 15,
  color: "#545454",
}

const $questionContainer: ViewStyle = {
  marginBottom: 16,
}

const $questionText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 16,
  lineHeight: 22,
  color: "#202244",
}

const $optionsContainer: ViewStyle = {
  marginBottom: 16,
}

const $optionItem: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  padding: 12,
  marginBottom: 8,
  backgroundColor: "#F5F9FF",
  borderRadius: 12,
  borderWidth: 1,
  borderColor: "#E8F1FF",
}

const $correctOption: ViewStyle = {
  backgroundColor: "#E8F5E8",
  borderColor: "#4CAF50",
}

const $optionText: TextStyle = {
  flex: 1,
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
}

const $correctOptionText: TextStyle = {
  color: "#2E7D32",
}

const $correctLabel: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 12,
  lineHeight: 15,
  color: "#4CAF50",
}

const $analyticsContainer: ViewStyle = {
  borderTopWidth: 1,
  borderTopColor: "#E8F1FF",
  paddingTop: 16,
}

const $analyticsTitle: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
  marginBottom: 12,
}

const $analyticsRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-around",
  marginBottom: 12,
}

const $analyticItem: ViewStyle = {
  alignItems: "center",
}

const $analyticValue: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 18,
  lineHeight: 22,
  color: "#167F71",
}

const $analyticLabel: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 12,
  lineHeight: 15,
  color: "#545454",
}

const $mistakesContainer: ViewStyle = {
  marginTop: 8,
}

const $mistakesTitle: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight
  fontSize: 12,
  lineHeight: 15,
  color: "#F44336",
  marginBottom: 4,
}

const $mistakeText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 11,
  lineHeight: 14,
  color: "#757575",
  marginBottom: 2,
}
