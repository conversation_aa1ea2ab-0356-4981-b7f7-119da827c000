/**
 * ExamHeader - Instructor Version
 * 
 * Header component for Instructor's Exam Management screen
 * Based on student ExamHeader but adapted for instructor workflow
 */

import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface ExamHeaderProps {
  onBackPress?: () => void
  onCreateExam?: () => void
}

/**
 * ExamHeader - Instructor's exam management header
 * 
 * Features:
 * - Back button with proper touch feedback (following profile header pattern)
 * - "Exam Management" title for instructor context
 * - Create exam button for adding new exams
 * - Consistent styling with other instructor screens
 */
export function ExamHeader({ onBackPress, onCreateExam }: ExamHeaderProps) {
  const { themed } = useAppTheme()

  const handleBackPress = () => {
    console.log("🔙 Instructor Exam Header: Back pressed")
    onBackPress?.()
  }

  const handleCreatePress = () => {
    console.log("➕ Instructor Exam Header: Create exam pressed")
    onCreateExam?.()
  }

  return (
    <View style={themed($header)}>
      {/* Back Button */}
      <TouchableOpacity
        onPress={handleBackPress}
        activeOpacity={0.7}
      >
        <Icon
          icon="back"
          size={26}
          color="#202244"
        />
      </TouchableOpacity>

      {/* Title */}
      <Text style={themed($headerTitle)}>Exam Management</Text>

      {/* Create Exam Button */}
      <TouchableOpacity
        style={themed($createButton)}
        onPress={handleCreatePress}
        activeOpacity={0.7}
      >
        <Icon
          icon="plus"
          size={16}
          color="#FFFFFF"
        />
        <Text style={themed($createButtonText)}>Create</Text>
      </TouchableOpacity>
    </View>
  )
}

const $header: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: 35,
  paddingTop: 25,
  paddingBottom: 20,
}

const $headerTitle: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // 600 weight for headings
  fontSize: 21,
  lineHeight: 30,
  color: "#202244",
  flex: 1,
  marginLeft: 12,
}

const $createButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 12,
  paddingVertical: 8,
  borderRadius: 16,
  backgroundColor: "#167F71",
  gap: 4,
  shadowColor: "#167F71",
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 3,
}

const $createButtonText: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY for descriptions, body text, button text
  fontSize: 12,
  lineHeight: 15,
  color: "#FFFFFF",
}
