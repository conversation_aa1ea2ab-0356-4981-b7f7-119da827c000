/**
 * ExamCreationHeader - Exam Creation Header
 * 
 * Header component for instructor's exam creation screen
 * Based on InstructorExamDetailHeader but adapted for exam creation workflow
 */

import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { Ionicons } from "@expo/vector-icons"

interface ExamCreationHeaderProps {
  onBackPress: () => void
  currentQuestionNumber: number
  totalQuestions: number
  onSaveDraft?: () => void
  onPreviewExam?: () => void
  examTitle?: string
}

/**
 * ExamCreationHeader - Header for exam creation
 * 
 * Features:
 * - Back navigation
 * - Exam title display
 * - Question counter (current/total)
 * - Save draft action
 * - Preview exam action
 * - Creation-focused management tools
 */
export function ExamCreationHeader({
  onBackPress,
  currentQuestionNumber,
  totalQuestions,
  onSaveDraft,
  onPreviewExam,
  examTitle = "New Exam"
}: ExamCreationHeaderProps) {
  const { themed } = useAppTheme()

  console.log("📝 Exam Creation Header rendering...")

  const handleBackPress = () => {
    console.log("🔙 Exam Creation Header: Back pressed")
    onBackPress()
  }

  const handleSaveDraftPress = () => {
    console.log("💾 Exam Creation Header: Save draft pressed")
    onSaveDraft?.()
  }

  const handlePreviewPress = () => {
    console.log("👁️ Exam Creation Header: Preview pressed")
    onPreviewExam?.()
  }

  return (
    <View style={themed($container)}>
      {/* Top Row - Back button and actions */}
      <View style={themed($topRow)}>
        {/* Back Button */}
        <TouchableOpacity
          style={themed($backButton)}
          onPress={handleBackPress}
          activeOpacity={0.7}
        >
          <Ionicons name="chevron-back" size={24} color="#202244" />
        </TouchableOpacity>

        {/* Title */}
        <View style={themed($titleContainer)}>
          <Text style={themed($title)}>Create Exam</Text>
          {examTitle && (
            <Text style={themed($subtitle)} numberOfLines={1}>
              {examTitle}
            </Text>
          )}
        </View>

        {/* Action Buttons */}
        <View style={themed($actionButtons)}>
          <TouchableOpacity
            style={themed($actionButton)}
            onPress={handleSaveDraftPress}
            activeOpacity={0.7}
          >
            <Ionicons name="save-outline" size={20} color="#167F71" />
          </TouchableOpacity>

          <TouchableOpacity
            style={themed($actionButton)}
            onPress={handlePreviewPress}
            activeOpacity={0.7}
          >
            <Ionicons name="eye-outline" size={20} color="#167F71" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Bottom Row - Question counter */}
      <View style={themed($bottomRow)}>
        <Text style={themed($questionCounter)}>
          Question {currentQuestionNumber} of {totalQuestions}
        </Text>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  backgroundColor: "#FFFFFF",
  paddingHorizontal: 34,
  paddingTop: 16,
  paddingBottom: 20,
  borderBottomWidth: 1,
  borderBottomColor: "#E8F1FF",
}

const $topRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: 12,
}

const $backButton: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "#F5F9FF",
  justifyContent: "center",
  alignItems: "center",
}

const $titleContainer: ViewStyle = {
  flex: 1,
  alignItems: "center",
  marginHorizontal: 16,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for header
  fontSize: 18,
  lineHeight: 22,
  color: "#202244",
}

const $subtitle: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for subtitle
  fontSize: 12,
  lineHeight: 15,
  color: "#545454",
  marginTop: 2,
}

const $actionButtons: ViewStyle = {
  flexDirection: "row",
  gap: 8,
}

const $actionButton: ViewStyle = {
  width: 36,
  height: 36,
  borderRadius: 18,
  backgroundColor: "#F5F9FF",
  justifyContent: "center",
  alignItems: "center",
}

const $bottomRow: ViewStyle = {
  alignItems: "center",
}

const $questionCounter: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for counter
  fontSize: 14,
  lineHeight: 18,
  color: "#545454",
}
