/**
 * ExamList - Instructor Version
 * 
 * List component for Instructor's Exam Management
 * Based on student ExamList but adapted for instructor workflow
 */

import React from "react"
import { View, Text, ViewStyle, TextStyle, ActivityIndicator } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { InstructorExamItem } from "../types"
import { ExamCard } from "./ExamCard"

interface ExamListProps {
  exams: InstructorExamItem[]
  onExamPress: (exam: InstructorExamItem) => void
  onExamEdit?: (examId: string) => void
  onViewAnalytics?: (examId: string) => void
  onViewResults?: (examId: string) => void
  loading?: boolean
}

/**
 * ExamList - Instructor's exam management list
 * 
 * Features:
 * - Scrollable list of exam management cards
 * - Loading and empty states
 * - Management actions for each exam
 * - Instructor-specific exam data display
 */
export function ExamList({ 
  exams, 
  onExamPress, 
  onExamEdit,
  onViewAnalytics,
  onViewResults,
  loading 
}: ExamListProps) {
  const { themed } = useAppTheme()

  console.log("📝 Instructor ExamList rendering with", exams.length, "exams")

  if (loading) {
    return (
      <View style={themed($loadingContainer)}>
        <ActivityIndicator size="large" color="#0961F5" />
        <Text style={themed($loadingText)}>Loading exams...</Text>
      </View>
    )
  }

  if (exams.length === 0) {
    return (
      <View style={themed($emptyContainer)}>
        <Text style={themed($emptyTitle)}>No exams found</Text>
        <Text style={themed($emptySubtitle)}>
          Create your first exam to get started with student assessments
        </Text>
      </View>
    )
  }

  return (
    <View style={themed($container)}>
      {exams.map((exam) => (
        <ExamCard
          key={exam.id}
          exam={exam}
          onPress={onExamPress}
          onEdit={onExamEdit}
          onViewAnalytics={onViewAnalytics}
          onViewResults={onViewResults}
        />
      ))}

      {/* Bottom spacing for navigation */}
      <View style={themed($bottomSpacing)} />
    </View>
  )
}

const $container: ViewStyle = {
  paddingTop: 5,
}

const $loadingContainer: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  paddingVertical: 60,
  gap: 12,
}

const $loadingText: TextStyle = {
  fontSize: 16,
  fontFamily: "lexendDecaLight", // 300 weight for loading text
  color: "#545454",
  lineHeight: 20,
}

const $emptyContainer: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  paddingVertical: 60,
  paddingHorizontal: 40,
}

const $emptyTitle: TextStyle = {
  fontSize: 18,
  fontFamily: "lexendDecaSemiBold", // 600 weight for empty title
  color: "#202244",
  lineHeight: 24,
  marginBottom: 8,
  textAlign: "center",
}

const $emptySubtitle: TextStyle = {
  fontSize: 14,
  fontFamily: "lexendDecaLight", // 300 weight for empty subtitle
  color: "#545454",
  lineHeight: 18,
  textAlign: "center",
}

const $bottomSpacing: ViewStyle = {
  height: 100,
}
