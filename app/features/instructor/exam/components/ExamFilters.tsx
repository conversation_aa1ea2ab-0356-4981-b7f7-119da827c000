/**
 * ExamFilters - Instructor Version
 *
 * Filter component for Instructor's Exam Management screen
 * Based on MyCourse CourseStatusTabs pattern for consistent UI/UX
 */

import React, { useState } from "react"
import { View, Text, TouchableOpacity, ViewStyle, TextStyle, ScrollView } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { InstructorExamFilter } from "../types"

interface ExamFiltersProps {
  filters: InstructorExamFilter[]
  onFilterPress: (filterId: string) => void
}

/**
 * ExamFilters - Instructor's exam management filters
 *
 * Features:
 * - Horizontal scrollable filter tabs (following MyCourse pattern)
 * - All/Published/Draft/Scheduled/Archived filters
 * - Count badges for each filter
 * - Active state styling consistent with app design
 * - Local state management for selected filter
 */
export function ExamFilters({ filters, onFilterPress }: ExamFiltersProps) {
  const { themed } = useAppTheme()
  const [selectedFilter, setSelectedFilter] = useState<string>("all")

  console.log("📝 Instructor ExamFilters rendering with", filters.length, "filters")

  const handleFilterPress = (filterId: string) => {
    console.log("🔍 Instructor Exam Filter pressed:", filterId)
    setSelectedFilter(filterId)
    onFilterPress(filterId)
  }

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      style={themed($scrollContainer)}
      contentContainerStyle={themed($filtersContainer)}
    >
      {filters.map((filter) => {
        const isSelected = selectedFilter === filter.id

        return (
          <TouchableOpacity
            key={filter.id}
            style={themed([
              $filterButton,
              isSelected ? $filterButtonActive : $filterButtonInactive
            ])}
            onPress={() => handleFilterPress(filter.id)}
            activeOpacity={0.7}
          >
            <Text
              style={themed([
                $filterText,
                isSelected ? $filterTextActive : $filterTextInactive
              ])}
            >
              {filter.title}
            </Text>

            {/* Count Badge */}
            {filter.count > 0 && (
              <View style={themed([
                $countBadge,
                isSelected ? $countBadgeActive : $countBadgeInactive
              ])}>
                <Text style={themed([
                  $countText,
                  isSelected ? $countTextActive : $countTextInactive
                ])}>
                  {filter.count}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        )
      })}
    </ScrollView>
  )
}

const $scrollContainer: ViewStyle = {
  paddingHorizontal: 34, // Match MyCourse padding
  marginBottom: 20, // Match MyCourse spacing
  maxHeight: 60, // Limit height to prevent taking full screen
}

const $filtersContainer: ViewStyle = {
  gap: 8, // Reduced gap for more compact layout
  paddingRight: 12,
  alignItems: "center", // Center align filters
}

const $filterButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 14, // Slightly reduced padding
  paddingVertical: 8, // Reduced padding for more compact
  borderRadius: 16, // Slightly less rounded
  gap: 6,
  height: 36, // Fixed height instead of minHeight
}

const $filterButtonActive: ViewStyle = {
  backgroundColor: "#23408B", // Main brand color from design guidelines
}

const $filterButtonInactive: ViewStyle = {
  backgroundColor: "#E8F1FF", // Match MyCourse inactive color
}

const $filterText: TextStyle = {
  fontSize: 13, // Slightly smaller for more compact
  fontFamily: "lexendDecaMedium", // Medium weight like MyCourse
  lineHeight: 16, // Reduced line height
}

const $filterTextActive: TextStyle = {
  color: "#FFFFFF",
}

const $filterTextInactive: TextStyle = {
  color: "#202244", // Match MyCourse text color
}

const $countBadge: ViewStyle = {
  minWidth: 18, // Slightly smaller
  height: 18, // Slightly smaller
  borderRadius: 9,
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 4, // Reduced padding
}

const $countBadgeActive: ViewStyle = {
  backgroundColor: "rgba(255, 255, 255, 0.3)", // More visible on green background
}

const $countBadgeInactive: ViewStyle = {
  backgroundColor: "#167F71", // Match active filter color
}

const $countText: TextStyle = {
  fontSize: 10, // Smaller font for compact badges
  fontFamily: "lexendDecaMedium", // 500 weight for count numbers
  lineHeight: 12, // Reduced line height
}

const $countTextActive: TextStyle = {
  color: "#FFFFFF",
}

const $countTextInactive: TextStyle = {
  color: "#FFFFFF",
}
