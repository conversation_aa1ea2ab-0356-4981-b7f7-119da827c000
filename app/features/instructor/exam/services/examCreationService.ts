/**
 * Exam Creation Service
 * 
 * Service for creating exams/quizzes via API
 * Transforms exam creation data to API format
 */

import { instructor<PERSON><PERSON> } from "app/services/api/api"
import { useStores } from "app/models"
import type { ExamCreationData, ExamBasicInfo, ExamCreationQuestion } from "../types"

// API Quiz Data Interface
export interface ApiQuizData {
  name: string
  description: string
  instruction: string
  quiz_type: "quiz" | "exam"
  subject_id: number
  class_id: number
  max_score: number
  passing_score: number
  time_limit: number // in minutes
  max_attempts: number
  start_date: string // ISO string
  end_date: string // ISO string
  is_randomized: boolean
  show_correct_answers: boolean
  show_result_immediately: boolean
  questions: ApiQuestionData[]
}

export interface ApiQuestionData {
  name: string
  question_type: string
  score: number
  explanation: string
  answers: ApiAnswerData[]
}

export interface ApiAnswerData {
  text: string
  is_correct: boolean
  explanation: string
}

export class ExamCreationService {
  /**
   * Transform exam creation data to API format
   */
  static transformToApiFormat(examData: ExamCreationData): ApiQuizData {
    const { examInfo, questions } = examData

    // Transform questions
    const apiQuestions: ApiQuestionData[] = questions.map((question) => ({
      name: question.question || "Untitled Question",
      question_type: this.mapQuestionType(question.type),
      score: question.points || 1,
      explanation: question.explanation || "",
      answers: this.transformAnswers(question)
    }))

    // Calculate total score
    const maxScore = questions.reduce((sum, q) => sum + (q.points || 1), 0) || 100

    // Transform exam info
    const apiData: ApiQuizData = {
      name: examInfo.title || "Untitled Exam",
      description: examInfo.description || "",
      instruction: examInfo.instructions || "",
      quiz_type: "quiz", // Default to quiz, can be changed based on exam type
      subject_id: 0, // TODO: Get from course/subject selection
      class_id: 0, // TODO: Get from class selection
      max_score: maxScore,
      passing_score: examInfo.passingScore || Math.floor(maxScore * 0.6), // 60% default
      time_limit: examInfo.duration || 60, // minutes
      max_attempts: examInfo.maxAttempts || 1,
      start_date: this.formatDateTimeForAPI(examInfo.scheduledDate || new Date()),
      end_date: this.formatDateTimeForAPI(examInfo.dueDate || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)), // 7 days from now
      is_randomized: examInfo.shuffleQuestions || false,
      show_correct_answers: examInfo.showResults || false,
      show_result_immediately: examInfo.showResults || true,
      questions: apiQuestions
    }

    return apiData
  }

  /**
   * Format datetime for API (naive datetime without timezone)
   */
  private static formatDateTimeForAPI(date: string | Date): string {
    const d = typeof date === 'string' ? new Date(date) : date
    const year = d.getFullYear()
    const month = (d.getMonth() + 1).toString().padStart(2, '0')
    const day = d.getDate().toString().padStart(2, '0')
    const hours = d.getHours().toString().padStart(2, '0')
    const minutes = d.getMinutes().toString().padStart(2, '0')
    const seconds = d.getSeconds().toString().padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  }

  /**
   * Map question type from UI to API format
   */
  private static mapQuestionType(uiType: string): string {
    switch (uiType) {
      case "multiple-choice":
        return "multiple_choice"
      case "true-false":
        return "true_false"
      case "essay":
        return "essay"
      default:
        return "multiple_choice"
    }
  }

  /**
   * Transform question answers to API format
   */
  private static transformAnswers(question: ExamCreationQuestion): ApiAnswerData[] {
    if (question.type === "essay") {
      // Essay questions don't have predefined answers
      return []
    }

    if (question.type === "true-false") {
      return [
        {
          text: "True",
          is_correct: question.correctAnswer === "true",
          explanation: ""
        },
        {
          text: "False",
          is_correct: question.correctAnswer === "false",
          explanation: ""
        }
      ]
    }

    // Multiple choice
    if (question.options && question.options.length > 0) {
      return question.options.map((option, index) => ({
        text: option,
        is_correct: question.correctAnswer === option || question.correctAnswer === index.toString(),
        explanation: ""
      }))
    }

    // Default empty answers
    return []
  }

  /**
   * Create exam via API
   */
  static async createExam(examData: ExamCreationData): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      console.log("📝 ExamCreationService: Creating exam...")
      console.log("📝 Original exam data:", examData)

      // Transform to API format
      const apiData = this.transformToApiFormat(examData)
      console.log("📝 Transformed API data:", apiData)

      // Validate required fields
      if (!apiData.name || apiData.questions.length === 0) {
        return {
          success: false,
          message: "Exam must have a title and at least one question"
        }
      }

      // Call API
      const response = await instructorApi.createQuiz(apiData)

      if (response.kind === "ok") {
        console.log("✅ Exam created successfully:", response.data)
        return {
          success: true,
          message: response.data.message || "Exam created successfully",
          data: response.data.data
        }
      } else {
        console.error("❌ Failed to create exam:", response)
        return {
          success: false,
          message: "Failed to create exam. Please try again."
        }
      }

    } catch (error) {
      console.error("💥 Error creating exam:", error)
      return {
        success: false,
        message: error instanceof Error ? error.message : "An unexpected error occurred"
      }
    }
  }

  /**
   * Validate exam data before creation
   */
  static validateExamData(examData: ExamCreationData): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // Check exam info
    if (!examData.examInfo.title?.trim()) {
      errors.push("Exam title is required")
    }

    if (!examData.examInfo.description?.trim()) {
      errors.push("Exam description is required")
    }

    if (examData.examInfo.duration <= 0) {
      errors.push("Exam duration must be greater than 0")
    }

    // Check questions
    if (examData.questions.length === 0) {
      errors.push("At least one question is required")
    }

    examData.questions.forEach((question, index) => {
      if (!question.question?.trim()) {
        errors.push(`Question ${index + 1}: Question text is required`)
      }

      if (question.points <= 0) {
        errors.push(`Question ${index + 1}: Points must be greater than 0`)
      }

      if (question.type === "multiple-choice") {
        if (!question.options || question.options.length < 2) {
          errors.push(`Question ${index + 1}: Multiple choice questions need at least 2 options`)
        }

        // Check if options have content
        const validOptions = question.options?.filter(opt => opt.trim().length > 0) || []
        if (validOptions.length < 2) {
          errors.push(`Question ${index + 1}: Multiple choice questions need at least 2 non-empty options`)
        }

        if (!question.correctAnswer) {
          errors.push(`Question ${index + 1}: Correct answer must be selected`)
        }
      }

      if (question.type === "true-false") {
        if (!question.correctAnswer) {
          errors.push(`Question ${index + 1}: Correct answer must be selected`)
        }
      }
    })

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}
