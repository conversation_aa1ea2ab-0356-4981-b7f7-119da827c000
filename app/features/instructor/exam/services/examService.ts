/**
 * Instructor Exam Service
 * 
 * Mock exam data service for instructor exam management
 * Based on student service but adapted for instructor workflow
 */

import type { InstructorExamData, InstructorExamItem, ExamAnalytics, ExamOverviewAnalytics } from "../types"

export class InstructorExamService {
  /**
   * Get instructor exam data with filters and exam management list
   */
  static async getExamData(): Promise<InstructorExamData> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    const mockInstructorExams: InstructorExamItem[] = [
      {
        id: "1",
        courseTitle: "Advanced React Development",
        subject: "Final Assessment: React Patterns and Performance",
        lesson: "Module 5",
        dateTime: "24/06/2025 - 16:30",
        status: "published",
        duration: "2 hours",
        totalQuestions: 25,
        studentCount: 45,
        completedCount: 38,
        averageScore: 87.5,
        completionRate: 84.4,
        createdAt: "2024-01-15",
        updatedAt: "2024-01-20",
        isActive: true,
        analytics: {
          totalAttempts: 42,
          averageScore: 87.5,
          highestScore: 98,
          lowestScore: 65,
          completionRate: 84.4,
          averageTimeSpent: 95, // minutes
          difficultyRating: 3.8,
          studentFeedback: 4.2,
          questionAnalytics: []
        }
      },
      {
        id: "2",
        courseTitle: "UI/UX Design Fundamentals",
        subject: "Midterm Exam: Design Principles and User Research",
        lesson: "Module 3",
        dateTime: "25/06/2025 - 14:00",
        status: "draft",
        duration: "1.5 hours",
        totalQuestions: 20,
        studentCount: 32,
        completedCount: 0,
        averageScore: 0,
        completionRate: 0,
        createdAt: "2024-01-22",
        updatedAt: "2024-01-25",
        isActive: false,
        analytics: {
          totalAttempts: 0,
          averageScore: 0,
          highestScore: 0,
          lowestScore: 0,
          completionRate: 0,
          averageTimeSpent: 0,
          difficultyRating: 0,
          studentFeedback: 0,
          questionAnalytics: []
        }
      },
      {
        id: "3",
        courseTitle: "Digital Marketing Strategy",
        subject: "Quiz: Social Media Marketing and Analytics",
        lesson: "Module 2",
        dateTime: "26/06/2025 - 10:30",
        status: "published",
        duration: "45 minutes",
        totalQuestions: 15,
        studentCount: 28,
        completedCount: 25,
        averageScore: 92.3,
        completionRate: 89.3,
        createdAt: "2024-01-10",
        updatedAt: "2024-01-18",
        isActive: true,
        analytics: {
          totalAttempts: 27,
          averageScore: 92.3,
          highestScore: 100,
          lowestScore: 78,
          completionRate: 89.3,
          averageTimeSpent: 38,
          difficultyRating: 2.5,
          studentFeedback: 4.5,
          questionAnalytics: []
        }
      },
      {
        id: "4",
        courseTitle: "JavaScript ES6+ Mastery",
        subject: "Practice Test: Async Programming and Promises",
        lesson: "Module 4",
        dateTime: "27/06/2025 - 09:00",
        status: "scheduled",
        duration: "1 hour",
        totalQuestions: 18,
        studentCount: 35,
        completedCount: 0,
        averageScore: 0,
        completionRate: 0,
        createdAt: "2024-01-25",
        updatedAt: "2024-01-26",
        isActive: true,
        analytics: {
          totalAttempts: 0,
          averageScore: 0,
          highestScore: 0,
          lowestScore: 0,
          completionRate: 0,
          averageTimeSpent: 0,
          difficultyRating: 0,
          studentFeedback: 0,
          questionAnalytics: []
        }
      },
      {
        id: "5",
        courseTitle: "Data Science with Python",
        subject: "Final Project: Machine Learning Implementation",
        lesson: "Module 6",
        dateTime: "28/06/2025 - 15:00",
        status: "archived",
        duration: "3 hours",
        totalQuestions: 30,
        studentCount: 22,
        completedCount: 20,
        averageScore: 85.7,
        completionRate: 90.9,
        createdAt: "2024-01-05",
        updatedAt: "2024-01-12",
        isActive: false,
        analytics: {
          totalAttempts: 22,
          averageScore: 85.7,
          highestScore: 96,
          lowestScore: 72,
          completionRate: 90.9,
          averageTimeSpent: 165,
          difficultyRating: 4.2,
          studentFeedback: 4.0,
          questionAnalytics: []
        }
      }
    ]

    const filters = [
      {
        id: "all",
        title: "All Exams",
        isActive: true,
        status: "all" as const,
        count: mockInstructorExams.length
      },
      {
        id: "published",
        title: "Published",
        isActive: false,
        status: "published" as const,
        count: mockInstructorExams.filter(e => e.status === "published").length
      },
      {
        id: "draft",
        title: "Draft",
        isActive: false,
        status: "draft" as const,
        count: mockInstructorExams.filter(e => e.status === "draft").length
      },
      {
        id: "scheduled",
        title: "Scheduled",
        isActive: false,
        status: "scheduled" as const,
        count: mockInstructorExams.filter(e => e.status === "scheduled").length
      },
      {
        id: "archived",
        title: "Archived",
        isActive: false,
        status: "archived" as const,
        count: mockInstructorExams.filter(e => e.status === "archived").length
      }
    ]

    return {
      filters,
      exams: mockInstructorExams
    }
  }

  /**
   * Get exam overview analytics
   */
  static async getExamOverviewAnalytics(): Promise<ExamOverviewAnalytics> {
    const examData = await this.getExamData()
    const exams = examData.exams

    return {
      totalExams: exams.length,
      publishedExams: exams.filter(e => e.status === "published").length,
      draftExams: exams.filter(e => e.status === "draft").length,
      totalStudents: exams.reduce((sum, exam) => sum + exam.studentCount, 0),
      averageCompletionRate: exams.reduce((sum, exam) => sum + exam.completionRate, 0) / exams.length,
      averageScore: exams.reduce((sum, exam) => sum + exam.averageScore, 0) / exams.length,
      totalAttempts: exams.reduce((sum, exam) => sum + exam.analytics.totalAttempts, 0)
    }
  }
}
