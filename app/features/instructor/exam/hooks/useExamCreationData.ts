/**
 * useExamCreationData Hook
 * 
 * Manages exam creation data for instructor's exam creation workflow
 * Based on useInstructorExamDetailData but adapted for creating new exams
 */

import { useState, useEffect } from "react"
import type { ExamCreationData, ExamBasicInfo, ExamCreationQuestion } from "../types"

export function useExamCreationData(courseId?: string, courseName?: string): ExamCreationData & {
  updateExamInfo: (info: Partial<ExamBasicInfo>) => void
  updateQuestion: (questionId: string, question: Partial<ExamCreationQuestion>) => void
  addQuestion: () => void
  deleteQuestion: (questionId: string) => void
  updateCurrentQuestionIndex: (index: number) => void
} {
  const [data, setData] = useState<ExamCreationData>({
    examInfo: {
      title: "",
      description: "",
      courseId: courseId || "",
      courseName: courseName || "",
      duration: 60, // Default 60 minutes
      totalPoints: 0,
      passingScore: 70, // Default 70%
      instructions: "Please read each question carefully and select the best answer.",
      allowRetakes: false,
      maxAttempts: 1,
      showResults: true,
      shuffleQuestions: false,
      shuffleAnswers: false,
    },
    questions: [],
    currentQuestionIndex: 0,
    isLoading: false,
    error: null,
    isDraft: true
  })

  useEffect(() => {
    // Initialize with a default question
    if (data.questions.length === 0) {
      addDefaultQuestion()
    }
  }, [])

  const addDefaultQuestion = () => {
    const defaultQuestion: ExamCreationQuestion = {
      id: `q_${Date.now()}`,
      type: "multiple-choice",
      question: "",
      options: ["", "", "", ""],
      correctAnswer: "",
      points: 10,
      timeLimit: 120, // 2 minutes default
      explanation: "",
      isRequired: true
    }

    setData(prev => ({
      ...prev,
      questions: [defaultQuestion],
      currentQuestionIndex: 0,
      examInfo: {
        ...prev.examInfo,
        totalPoints: 10
      }
    }))

    console.log("📝 Exam Creation: Added default question")
  }

  const updateExamInfo = (info: Partial<ExamBasicInfo>) => {
    console.log("📝 Exam Creation: Updating exam info:", info)
    setData(prev => ({
      ...prev,
      examInfo: {
        ...prev.examInfo,
        ...info
      }
    }))
  }

  const updateQuestion = (questionId: string, questionUpdate: Partial<ExamCreationQuestion>) => {
    console.log("📝 Exam Creation: Updating question:", questionId, questionUpdate)
    setData(prev => {
      const updatedQuestions = prev.questions.map(q => 
        q.id === questionId ? { ...q, ...questionUpdate } : q
      )
      
      // Recalculate total points
      const totalPoints = updatedQuestions.reduce((sum, q) => sum + q.points, 0)
      
      return {
        ...prev,
        questions: updatedQuestions,
        examInfo: {
          ...prev.examInfo,
          totalPoints
        }
      }
    })
  }

  const addQuestion = () => {
    console.log("📝 Exam Creation: Adding new question")
    const newQuestion: ExamCreationQuestion = {
      id: `q_${Date.now()}`,
      type: "multiple-choice",
      question: "",
      options: ["", "", "", ""],
      correctAnswer: "",
      points: 10,
      timeLimit: 120,
      explanation: "",
      isRequired: true
    }

    setData(prev => {
      const newQuestions = [...prev.questions, newQuestion]
      const totalPoints = newQuestions.reduce((sum, q) => sum + q.points, 0)
      
      return {
        ...prev,
        questions: newQuestions,
        currentQuestionIndex: newQuestions.length - 1, // Navigate to new question
        examInfo: {
          ...prev.examInfo,
          totalPoints
        }
      }
    })
  }

  const deleteQuestion = (questionId: string) => {
    console.log("📝 Exam Creation: Deleting question:", questionId)
    setData(prev => {
      const filteredQuestions = prev.questions.filter(q => q.id !== questionId)
      
      // Ensure at least one question exists
      if (filteredQuestions.length === 0) {
        addDefaultQuestion()
        return prev
      }
      
      const totalPoints = filteredQuestions.reduce((sum, q) => sum + q.points, 0)
      const newCurrentIndex = Math.min(prev.currentQuestionIndex, filteredQuestions.length - 1)
      
      return {
        ...prev,
        questions: filteredQuestions,
        currentQuestionIndex: newCurrentIndex,
        examInfo: {
          ...prev.examInfo,
          totalPoints
        }
      }
    })
  }

  const updateCurrentQuestionIndex = (index: number) => {
    setData(prev => ({
      ...prev,
      currentQuestionIndex: Math.max(0, Math.min(index, prev.questions.length - 1))
    }))
  }

  return {
    ...data,
    updateExamInfo,
    updateQuestion,
    addQuestion,
    deleteQuestion,
    updateCurrentQuestionIndex
  }
}
