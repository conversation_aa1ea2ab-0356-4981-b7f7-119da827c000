/**
 * useExamCreationHandlers Hook
 * 
 * Manages all interaction handlers for instructor's exam creation screen
 * Based on useInstructorExamDetailHandlers but adapted for exam creation workflow
 */

import { useCallback } from "react"
import { Alert } from "react-native"
import { ExamCreationService } from "../services/examCreationService"
import type { ExamCreationHandlers, ExamBasicInfo, ExamCreationQuestion, ExamCreationData } from "../types"

export function useExamCreationHandlers(
  navigation?: any,
  updateExamInfo?: (info: Partial<ExamBasicInfo>) => void,
  updateQuestion?: (questionId: string, question: Partial<ExamCreationQuestion>) => void,
  addQuestion?: () => void,
  deleteQuestion?: (questionId: string) => void,
  updateCurrentQuestionIndex?: (index: number) => void,
  currentQuestionIndex?: number,
  totalQuestions?: number,
  examData?: ExamCreationData
): ExamCreationHandlers {

  const handleBackPress = useCallback(() => {
    console.log("📝 Exam Creation: Back pressed")
    if (navigation) {
      navigation.goBack()
    }
  }, [navigation])

  const handleSaveDraft = useCallback(() => {
    console.log("📝 Exam Creation: Save draft pressed")
    // TODO: Implement save draft functionality
    // examCreationService.saveDraft(examData)
    
    // Show success message
    console.log("📝 Exam saved as draft successfully")
  }, [examData])

  const handlePublishExam = useCallback(async () => {
    console.log("📝 Exam Creation: Create exam pressed")

    if (!examData) {
      Alert.alert("Error", "No exam data available")
      return
    }

    try {
      // Validate exam data
      const validation = ExamCreationService.validateExamData(examData)
      if (!validation.isValid) {
        Alert.alert(
          "Validation Error",
          validation.errors.join("\n"),
          [{ text: "OK" }]
        )
        return
      }

      // Show loading state (you might want to add this to your data state)
      console.log("📝 Creating exam...")

      // Create exam via API
      const result = await ExamCreationService.createExam(examData)

      if (result.success) {
        Alert.alert(
          "Success",
          result.message,
          [
            {
              text: "OK",
              onPress: () => {
                // Navigate back to exam management
                if (navigation) {
                  navigation.navigate("ExamManagement")
                }
              }
            }
          ]
        )
      } else {
        Alert.alert("Error", result.message)
      }

    } catch (error) {
      console.error("💥 Error creating exam:", error)
      Alert.alert(
        "Error",
        "An unexpected error occurred while creating the exam. Please try again."
      )
    }
  }, [navigation, examData])

  const handlePreviewExam = useCallback(() => {
    console.log("📝 Exam Creation: Preview exam pressed")
    if (navigation) {
      navigation.navigate("ExamPreview", { examData })
    }
  }, [navigation, examData])

  const handleAddQuestion = useCallback(() => {
    console.log("📝 Exam Creation: Add question pressed")
    addQuestion?.()
  }, [addQuestion])

  const handleEditQuestion = useCallback((questionId: string) => {
    console.log("📝 Exam Creation: Edit question pressed:", questionId)
    // Navigate to question editor or show inline editor
    if (navigation) {
      navigation.navigate("QuestionEditor", { questionId, examData })
    }
  }, [navigation, examData])

  const handleDeleteQuestion = useCallback((questionId: string) => {
    console.log("📝 Exam Creation: Delete question pressed:", questionId)
    // Show confirmation dialog before deletion
    deleteQuestion?.(questionId)
  }, [deleteQuestion])

  const handlePreviousQuestion = useCallback(() => {
    console.log("📝 Exam Creation: Previous question pressed")
    if (updateCurrentQuestionIndex && currentQuestionIndex !== undefined && currentQuestionIndex > 0) {
      updateCurrentQuestionIndex(currentQuestionIndex - 1)
    }
  }, [updateCurrentQuestionIndex, currentQuestionIndex])

  const handleNextQuestion = useCallback(() => {
    console.log("📝 Exam Creation: Next question pressed")
    if (updateCurrentQuestionIndex && currentQuestionIndex !== undefined && totalQuestions !== undefined && currentQuestionIndex < totalQuestions - 1) {
      updateCurrentQuestionIndex(currentQuestionIndex + 1)
    }
  }, [updateCurrentQuestionIndex, currentQuestionIndex, totalQuestions])

  const handleUpdateExamInfo = useCallback((info: Partial<ExamBasicInfo>) => {
    console.log("📝 Exam Creation: Update exam info:", info)
    updateExamInfo?.(info)
  }, [updateExamInfo])

  const handleUpdateQuestion = useCallback((questionId: string, question: Partial<ExamCreationQuestion>) => {
    console.log("📝 Exam Creation: Update question:", questionId, question)
    updateQuestion?.(questionId, question)
  }, [updateQuestion])

  return {
    handleBackPress,
    handleSaveDraft,
    handlePublishExam,
    handlePreviewExam,
    handleAddQuestion,
    handleEditQuestion,
    handleDeleteQuestion,
    handlePreviousQuestion,
    handleNextQuestion,
    handleUpdateExamInfo,
    handleUpdateQuestion
  }
}
