/**
 * useInstructorExamDetailData Hook
 * 
 * Manages exam detail data for instructor's exam management
 * Based on student useExamDetailData but adapted for instructor workflow
 */

import { useState, useEffect } from "react"
import type { InstructorExamDetailData, InstructorExamItem, InstructorExamQuestion, ExamQuestionAnalytics } from "../types"

export function useInstructorExamDetailData(exam?: InstructorExamItem): InstructorExamDetailData & {
  updateCurrentQuestionIndex: (index: number) => void
} {
  const [data, setData] = useState<InstructorExamDetailData>({
    exam: exam || {} as InstructorExamItem,
    questions: [],
    currentQuestionIndex: 0,
    isLoading: true,
    error: null,
    analytics: []
  })

  useEffect(() => {
    if (exam) {
      loadExamQuestions(exam)
    }
  }, [exam])

  const loadExamQuestions = async (examItem: InstructorExamItem) => {
    try {
      console.log("📝 Instructor useExamDetailData loading questions for:", examItem.courseTitle)
      setData(prev => ({ ...prev, isLoading: true, error: null }))

      // Simulate API call - replace with actual service
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Mock exam questions with analytics
      const mockQuestions: InstructorExamQuestion[] = [
        {
          id: "q1",
          type: "multiple-choice",
          question: "What is the primary purpose of React hooks?",
          options: [
            "To replace class components entirely",
            "To manage state and side effects in functional components",
            "To improve performance only",
            "To handle routing in React applications"
          ],
          correctAnswer: "To manage state and side effects in functional components",
          points: 10,
          timeLimit: 120,
          analytics: {
            questionId: "q1",
            totalAttempts: 45,
            correctAnswers: 38,
            averageTime: 85,
            difficultyLevel: "medium",
            commonMistakes: ["Selected option A", "Confused with performance optimization"]
          }
        },
        {
          id: "q2",
          type: "essay",
          question: "Explain the concept of component lifecycle in React and provide examples of when you would use different lifecycle methods.",
          points: 20,
          timeLimit: 300,
          analytics: {
            questionId: "q2",
            totalAttempts: 45,
            correctAnswers: 32,
            averageTime: 245,
            difficultyLevel: "hard",
            commonMistakes: ["Incomplete lifecycle explanation", "Missing practical examples"]
          }
        },
        {
          id: "q3",
          type: "multiple-choice",
          question: "Which of the following is the correct way to handle events in React?",
          options: [
            "onClick={handleClick()}",
            "onClick={handleClick}",
            "onClick='handleClick'",
            "onClick={this.handleClick()}"
          ],
          correctAnswer: "onClick={handleClick}",
          points: 5,
          timeLimit: 60,
          analytics: {
            questionId: "q3",
            totalAttempts: 45,
            correctAnswers: 42,
            averageTime: 35,
            difficultyLevel: "easy",
            commonMistakes: ["Added parentheses to function call"]
          }
        }
      ]

      const analytics = mockQuestions.map(q => q.analytics)

      setData(prev => ({
        ...prev,
        questions: mockQuestions,
        analytics,
        isLoading: false,
        error: null
      }))

      console.log("📝 Instructor useExamDetailData loaded:", {
        questionsCount: mockQuestions.length,
        examTitle: examItem.courseTitle
      })

    } catch (error) {
      console.error("📝 Instructor useExamDetailData error:", error)
      setData(prev => ({
        ...prev,
        isLoading: false,
        error: "Failed to load exam questions"
      }))
    }
  }

  const updateCurrentQuestionIndex = (index: number) => {
    setData(prev => ({
      ...prev,
      currentQuestionIndex: Math.max(0, Math.min(index, prev.questions.length - 1))
    }))
  }

  return {
    ...data,
    updateCurrentQuestionIndex
  }
}
