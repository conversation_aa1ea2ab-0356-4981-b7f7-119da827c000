/**
 * useExamData Hook - Instructor Version
 * 
 * Manages all data for Instructor's Exam Management screen including:
 * - Exam list with management features
 * - Filter functionality for exam status
 * - Analytics and performance metrics
 * - Instructor-specific exam data
 */

import { useState, useEffect } from "react"
import { InstructorExamService } from "../services/examService"
import { instructorApi } from "app/services/api/api"
import { useStores } from "app/models"
import type { ExamScreenData, InstructorExamItem, ExamOverviewAnalytics } from "../types"

// Helper function to format date time
const formatDateTime = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    const day = date.getDate().toString().padStart(2, '0')
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const year = date.getFullYear()
    const hours = date.getHours().toString().padStart(2, '0')
    const minutes = date.getMinutes().toString().padStart(2, '0')
    return `${day}/${month}/${year} - ${hours}:${minutes}`
  } catch {
    return "Invalid date"
  }
}

// Helper function to map quiz status from API to our status
const mapQuizStatus = (apiStatus: string): "published" | "draft" | "scheduled" | "archived" => {
  switch (apiStatus?.toLowerCase()) {
    case "published":
    case "active":
      return "published"
    case "draft":
      return "draft"
    case "scheduled":
      return "scheduled"
    case "archived":
    case "inactive":
      return "archived"
    default:
      return "draft"
  }
}

export function useExamData() {
  const { authenticationStore } = useStores()

  const [data, setData] = useState<ExamScreenData>({
    activeFilter: "all",
    filteredExams: [],
    filters: [],
    loading: true,
    error: null,
    totalExams: 0,
    analytics: {
      totalExams: 0,
      publishedExams: 0,
      draftExams: 0,
      totalStudents: 0,
      averageCompletionRate: 0,
      averageScore: 0,
      totalAttempts: 0
    }
  })

  const [allExams, setAllExams] = useState<InstructorExamItem[]>([])

  useEffect(() => {
    loadExamData()
  }, [])

  const loadExamData = async () => {
    try {
      console.log("📝 Instructor useExamData loading exam data from API...")
      setData(prev => ({ ...prev, loading: true, error: null }))

      // Get access token
      const accessToken = authenticationStore.authToken
      if (!accessToken) {
        throw new Error("No access token available")
      }

      // Set auth token and call instructor quizzes API
      instructorApi.setAuthToken(accessToken)
      const response = await instructorApi.getInstructorQuizzes()

      console.log("📝 API Response:", response)

      if (response.kind !== "ok") {
        throw new Error("Failed to fetch quizzes")
      }

      if (!response.data.success) {
        throw new Error(response.data.message || "Failed to fetch quizzes")
      }

      // Transform API data to our exam format
      const apiQuizzes = response.data.data?.quizzes || []

      console.log("📝 API returned", apiQuizzes.length, "quizzes")

      // Transform API quizzes to exam format
      const transformedExams: InstructorExamItem[] = apiQuizzes.map((quiz: any) => ({
        id: quiz.id?.toString() || "",
        courseTitle: quiz.course_name || "Unknown Course",
        subject: quiz.title || "Untitled Quiz",
        lesson: quiz.lesson_name || "Unknown Lesson",
        dateTime: quiz.created_at ? formatDateTime(quiz.created_at) : "No date",
        status: mapQuizStatus(quiz.status),
        duration: quiz.duration ? `${quiz.duration} minutes` : "No duration",
        totalQuestions: quiz.total_questions || 0,
        studentCount: quiz.student_count || 0,
        completedCount: quiz.completed_count || 0,
        averageScore: quiz.average_score || 0,
        completionRate: quiz.completion_rate || 0,
        createdAt: quiz.created_at || "",
        updatedAt: quiz.updated_at || "",
        isActive: quiz.status === "published",
        analytics: {
          totalAttempts: quiz.total_attempts || 0,
          averageScore: quiz.average_score || 0,
          highestScore: quiz.highest_score || 0,
          lowestScore: quiz.lowest_score || 0,
          completionRate: quiz.completion_rate || 0,
          averageTimeSpent: quiz.average_time_spent || 0,
          difficultyRating: quiz.difficulty_rating || 0,
          studentFeedback: quiz.student_feedback || 0,
          questionAnalytics: []
        }
      }))

      // Store all exams
      setAllExams(transformedExams)

      // Create filters with counts
      const filters = [
        {
          id: "all",
          title: "All Exams",
          isActive: true,
          status: "all" as const,
          count: transformedExams.length
        },
        {
          id: "published",
          title: "Published",
          isActive: false,
          status: "published" as const,
          count: transformedExams.filter(e => e.status === "published").length
        },
        {
          id: "draft",
          title: "Draft",
          isActive: false,
          status: "draft" as const,
          count: transformedExams.filter(e => e.status === "draft").length
        },
        {
          id: "scheduled",
          title: "Scheduled",
          isActive: false,
          status: "scheduled" as const,
          count: transformedExams.filter(e => e.status === "scheduled").length
        },
        {
          id: "archived",
          title: "Archived",
          isActive: false,
          status: "archived" as const,
          count: transformedExams.filter(e => e.status === "archived").length
        }
      ]

      // Set active filter to first filter
      const activeFilter = "all"

      // Filter exams based on active filter
      const filteredExams = filterExamsByStatus(transformedExams, activeFilter)

      // Calculate analytics
      const analytics: ExamOverviewAnalytics = {
        totalExams: transformedExams.length,
        publishedExams: transformedExams.filter(e => e.status === "published").length,
        draftExams: transformedExams.filter(e => e.status === "draft").length,
        totalStudents: transformedExams.reduce((sum, exam) => sum + exam.studentCount, 0),
        averageCompletionRate: transformedExams.length > 0
          ? transformedExams.reduce((sum, exam) => sum + exam.completionRate, 0) / transformedExams.length
          : 0,
        averageScore: transformedExams.length > 0
          ? transformedExams.reduce((sum, exam) => sum + exam.averageScore, 0) / transformedExams.length
          : 0,
        totalAttempts: transformedExams.reduce((sum, exam) => sum + (exam.analytics?.totalAttempts || 0), 0)
      }

      setData({
        activeFilter,
        filteredExams,
        filters,
        loading: false,
        error: null,
        totalExams: transformedExams.length,
        analytics
      })

      console.log("📝 Instructor useExamData loaded from API:", {
        totalExams: transformedExams.length,
        filteredCount: filteredExams.length,
        activeFilter,
        analytics
      })

    } catch (error) {
      console.error("📝 Instructor useExamData error:", error)
      setData(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to load exam data"
      }))
    }
  }

  const setActiveFilter = (filterId: string, exams: InstructorExamItem[]) => {
    console.log("📝 Instructor useExamData setting active filter:", filterId)
    
    const filteredExams = filterExamsByStatus(exams, filterId)
    
    setData(prev => ({
      ...prev,
      activeFilter: filterId,
      filteredExams,
      filters: prev.filters.map(filter => ({
        ...filter,
        isActive: filter.id === filterId
      }))
    }))
  }

  const filterExamsByStatus = (exams: InstructorExamItem[], filterId: string): InstructorExamItem[] => {
    if (filterId === "all") {
      return exams
    }
    
    return exams.filter(exam => exam.status === filterId)
  }

  const refresh = () => {
    console.log("📝 Instructor useExamData refreshing...")
    loadExamData()
  }

  return {
    data,
    allExams,
    setActiveFilter,
    refresh
  }
}
