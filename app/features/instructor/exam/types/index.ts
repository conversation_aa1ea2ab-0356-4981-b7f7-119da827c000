/**
 * Instructor Exam Types
 * 
 * Types specific to instructor's exam management with analytics,
 * student performance tracking, and instructor-specific features.
 * Based on student types but adapted for instructor workflow.
 */

// Navigation Types
export interface ExamScreenProps {
  navigation?: any
}

// Instructor Exam Item (with management features)
export interface InstructorExamItem {
  id: string
  courseTitle: string
  subject: string
  lesson: string
  dateTime: string
  status: 'draft' | 'published' | 'archived' | 'scheduled'
  duration: string
  // Instructor-specific fields
  totalQuestions: number
  studentCount: number
  completedCount: number
  averageScore: number
  completionRate: number
  createdAt: string
  updatedAt: string
  isActive: boolean
  // Analytics
  analytics: ExamAnalytics
}

// Exam Analytics for Instructor
export interface ExamAnalytics {
  totalAttempts: number
  averageScore: number
  highestScore: number
  lowestScore: number
  completionRate: number
  averageTimeSpent: number // in minutes
  difficultyRating: number // 1-5 scale
  studentFeedback: number // average rating
  questionAnalytics: QuestionAnalytics[]
}

// Question Analytics
export interface QuestionAnalytics {
  questionId: string
  questionText: string
  correctAnswerRate: number
  averageTimeSpent: number
  difficultyLevel: 'easy' | 'medium' | 'hard'
  studentFeedback: string[]
}

// Instructor Exam Filter
export interface InstructorExamFilter {
  id: string
  title: string
  isActive: boolean
  status: 'all' | 'draft' | 'published' | 'archived' | 'scheduled'
  count: number
}

// Instructor Exam Data
export interface InstructorExamData {
  filters: InstructorExamFilter[]
  exams: InstructorExamItem[]
}

// Instructor Exam Screen Data
export interface ExamScreenData {
  activeFilter: string
  filteredExams: InstructorExamItem[]
  filters: InstructorExamFilter[]
  loading: boolean
  error: string | null
  totalExams: number
  analytics: ExamOverviewAnalytics
}

// Exam Overview Analytics
export interface ExamOverviewAnalytics {
  totalExams: number
  publishedExams: number
  draftExams: number
  totalStudents: number
  averageCompletionRate: number
  averageScore: number
  totalAttempts: number
}

// Instructor Exam Screen Handlers
export interface ExamScreenHandlers {
  handleFilterPress: (filterId: string) => void
  handleExamPress: (exam: InstructorExamItem) => void
  handleBackPress: () => void
  handleRefresh: () => void
  handleCreateExam: () => void
  handleExamEdit: (examId: string) => void
  handleExamDuplicate: (examId: string) => void
  handleExamArchive: (examId: string) => void
  handleViewAnalytics: (examId: string) => void
  handleViewResults: (examId: string) => void
}

// Instructor Exam Detail Types
export interface InstructorExamDetailData {
  exam: InstructorExamItem
  questions: InstructorExamQuestion[]
  currentQuestionIndex: number
  isLoading: boolean
  error: string | null
  analytics: ExamQuestionAnalytics[]
}

export interface InstructorExamQuestion {
  id: string
  type: 'multiple-choice' | 'essay' | 'true-false'
  question: string
  options?: string[]
  correctAnswer?: string
  points: number
  timeLimit?: number
  analytics: ExamQuestionAnalytics
}

export interface ExamQuestionAnalytics {
  questionId: string
  totalAttempts: number
  correctAnswers: number
  averageTime: number
  difficultyLevel: 'easy' | 'medium' | 'hard'
  commonMistakes: string[]
}

export interface InstructorExamDetailHandlers {
  handleBackPress: () => void
  handleEditQuestion: (questionId: string) => void
  handleDeleteQuestion: (questionId: string) => void
  handleAddQuestion: () => void
  handlePreviewExam: () => void
  handlePublishExam: () => void
  handleViewAnalytics: () => void
  handleViewResults: () => void
  handlePreviousQuestion: () => void
  handleNextQuestion: () => void
}

export interface ExamDetailScreenProps {
  navigation?: any
  route?: {
    params?: {
      exam?: InstructorExamItem
    }
  }
}

// Exam Creation Types
export interface ExamCreationData {
  examInfo: ExamBasicInfo
  questions: ExamCreationQuestion[]
  currentQuestionIndex: number
  isLoading: boolean
  error: string | null
  isDraft: boolean
}

export interface ExamBasicInfo {
  title: string
  description: string
  courseId: string
  courseName: string
  duration: number // in minutes
  totalPoints: number
  passingScore: number
  instructions: string
  allowRetakes: boolean
  maxAttempts: number
  showResults: boolean
  shuffleQuestions: boolean
  shuffleAnswers: boolean
  scheduledDate?: string
  dueDate?: string
}

export interface ExamCreationQuestion {
  id: string
  type: 'multiple-choice' | 'essay' | 'true-false'
  question: string
  options?: string[]
  correctAnswer?: string
  points: number
  timeLimit?: number
  explanation?: string
  isRequired: boolean
}

export interface ExamCreationHandlers {
  handleBackPress: () => void
  handleSaveDraft: () => void
  handlePublishExam: () => void
  handlePreviewExam: () => void
  handleAddQuestion: () => void
  handleEditQuestion: (questionId: string) => void
  handleDeleteQuestion: (questionId: string) => void
  handlePreviousQuestion: () => void
  handleNextQuestion: () => void
  handleUpdateExamInfo: (info: Partial<ExamBasicInfo>) => void
  handleUpdateQuestion: (questionId: string, question: Partial<ExamCreationQuestion>) => void
}

export interface ExamCreationScreenProps {
  navigation?: any
  route?: {
    params?: {
      courseId?: string
      courseName?: string
    }
  }
}

// Exam Management Actions
export type ExamAction = 
  | "edit"
  | "duplicate"
  | "archive"
  | "publish"
  | "unpublish"
  | "delete"
  | "view_analytics"
  | "view_results"
  | "export_results"

// Exam Status
export type ExamStatus = 
  | "draft"
  | "published"
  | "archived"
  | "scheduled"

// Student Performance Data
export interface StudentPerformance {
  studentId: string
  studentName: string
  studentAvatar: string
  score: number
  completionTime: number // in minutes
  attemptDate: string
  status: 'completed' | 'in_progress' | 'not_started'
  answers: StudentAnswer[]
}

// Student Answer
export interface StudentAnswer {
  questionId: string
  selectedAnswer?: string // for multiple choice
  essayAnswer?: string // for essay questions
  isCorrect: boolean
  timeSpent: number // in seconds
}

// Exam Question (Instructor perspective)
export interface InstructorExamQuestion {
  id: string
  questionNumber: number
  questionText: string
  imageUrl?: string
  type: 'multiple-choice' | 'essay'
  answers?: ExamAnswer[]
  correctAnswerId?: string
  points: number
  difficulty: 'easy' | 'medium' | 'hard'
  analytics: QuestionAnalytics
}

// Exam Answer
export interface ExamAnswer {
  id: string
  text: string
  isCorrect: boolean
}

// Exam Creation/Edit Data
export interface ExamFormData {
  title: string
  description: string
  duration: number // in minutes
  totalPoints: number
  passingScore: number
  instructions: string
  isRandomized: boolean
  allowRetakes: boolean
  maxAttempts: number
  showResults: boolean
  questions: InstructorExamQuestion[]
}

// Exam Settings
export interface ExamSettings {
  isProctored: boolean
  allowCalculator: boolean
  allowNotes: boolean
  shuffleQuestions: boolean
  shuffleAnswers: boolean
  timeLimit: number
  autoSubmit: boolean
  showProgressBar: boolean
  allowBackNavigation: boolean
}

// Bulk Operations
export interface BulkExamOperation {
  action: ExamAction
  examIds: string[]
  targetStatus?: ExamStatus
}

// Export Options
export interface ExamExportOptions {
  format: 'pdf' | 'excel' | 'csv'
  includeAnalytics: boolean
  includeStudentAnswers: boolean
  dateRange?: {
    startDate: string
    endDate: string
  }
}

// Exam Template
export interface ExamTemplate {
  id: string
  name: string
  description: string
  category: string
  questionCount: number
  estimatedDuration: number
  difficulty: 'easy' | 'medium' | 'hard'
  isPublic: boolean
  usageCount: number
  createdBy: string
  createdAt: string
}
