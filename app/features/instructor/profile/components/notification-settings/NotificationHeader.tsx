import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface NotificationHeaderProps {
  onBackPress?: () => void
}

/**
 * InstructorNotificationHeader - Header component for Instructor Notification Settings screen
 *
 * Cloned from student NotificationHeader but adapted for instructor workflow.
 * Maintains exact same design and UI components.
 *
 * Features:
 * - Back button (arrow left icon)
 * - "Notification" title
 * - Consistent styling with EditProfileHeader
 */
export function NotificationHeader({ onBackPress }: NotificationHeaderProps) {
  const { themed } = useAppTheme()

  const handleBackPress = () => {
    console.log("👨‍🏫🔔 Back pressed")
    onBackPress?.()
  }

  return (
    <View style={themed($container)}>
      <TouchableOpacity style={themed($backButton)} onPress={handleBackPress}>
        <Icon icon="caretLeft" size={20} color="#202244" />
      </TouchableOpacity>

      <Text style={themed($title)}>Notification</Text>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35,
  paddingTop: 25,
  paddingBottom: 20,
}

const $backButton: ViewStyle = {
  width: 26,
  height: 20,
  justifyContent: "center",
  alignItems: "flex-start",
  marginRight: 12,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight - for section headings per guidelines
  fontSize: 21,
  lineHeight: 30,
  color: "#202244",
  flex: 1,
}
