import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { NotificationToggleItem } from "./NotificationToggleItem"
import { NotificationSettings } from "../../types"

interface NotificationSettingsListProps {
  settings: NotificationSettings
  onSettingChange: (key: keyof NotificationSettings, value: boolean) => void
}

/**
 * NotificationSettingsList - List of notification toggle items
 * 
 * Features:
 * - All notification settings from Figma design
 * - Proper spacing between items
 * - Consistent styling
 */
export function NotificationSettingsList({ 
  settings, 
  onSettingChange 
}: NotificationSettingsListProps) {
  const { themed } = useAppTheme()

  const notificationItems = [
    { key: "specialOffers" as const, title: "Special Offers" },
    { key: "sound" as const, title: "Sound" },
    { key: "vibrate" as const, title: "Vibrate" },
    { key: "generalNotification" as const, title: "General Notification" },
    { key: "promoDiscount" as const, title: "Promo & Discount" },
    { key: "paymentOptions" as const, title: "Payment Options" },
    { key: "appUpdate" as const, title: "App Update" },
    { key: "newServiceAvailable" as const, title: "New Service Available" },
    { key: "newTipsAvailable" as const, title: "New Tips Available" },
  ]

  return (
    <View style={themed($container)}>
      {notificationItems.map((item, index) => (
        <NotificationToggleItem
          key={item.key}
          title={item.title}
          isEnabled={settings[item.key]}
          onToggle={(enabled) => onSettingChange(item.key, enabled)}
        />
      ))}
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
  paddingTop: 28, // Spacing from header
}
