import React, { useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import {
  NotificationHeader,
  NotificationSettingsList
} from "./index"
import { NotificationSettings } from "../../types"

interface NotificationScreenContentProps {
  onBackPress?: () => void
  initialSettings?: NotificationSettings
}

// Default instructor notification settings
const defaultNotificationSettings: NotificationSettings = {
  specialOffers: false, // Less relevant for instructors
  sound: true,
  vibrate: false,
  generalNotification: true,
  promoDiscount: false, // Less relevant for instructors
  paymentOptions: true, // Important for earnings
  appUpdate: true,
  newServiceAvailable: true, // Important for instructors
  newTipsAvailable: true, // Important for teaching tips
}

/**
 * InstructorNotificationScreenContent - Instructor Notification Settings screen based on Figma design
 *
 * This component implements the same Notification Settings design from Figma as student:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4323
 *
 * Cloned from student NotificationScreenContent but adapted for instructor workflow.
 * Maintains exact same design and UI components.
 *
 * Features:
 * - NotificationHeader: "Notification" title with back button
 * - NotificationSettingsList: List of toggle switches for all notification types (instructor-focused)
 * - Proper background color (#F5F9FF)
 * - Consistent spacing and styling
 */
export function NotificationScreenContent({
  onBackPress,
  initialSettings = defaultNotificationSettings
}: NotificationScreenContentProps) {
  const { themed } = useAppTheme()
  const [settings, setSettings] = useState<NotificationSettings>(initialSettings)

  const handleBackPress = () => {
    console.log("👨‍🏫🔔 Navigate back")
    onBackPress?.()
  }

  const handleSettingChange = (key: keyof NotificationSettings, value: boolean) => {
    console.log(`👨‍🏫🔔 Setting ${key} changed to:`, value)
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
    >
      <View style={themed($content)}>
        {/* Header with back button */}
        <NotificationHeader onBackPress={handleBackPress} />
        
        {/* Notification settings list */}
        <NotificationSettingsList
          settings={settings}
          onSettingChange={handleSettingChange}
        />
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  flex: 1,
}
