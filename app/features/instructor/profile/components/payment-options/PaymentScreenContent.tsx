import React, { useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import {
  PaymentHeader,
  PaymentCardsList,
  AddNewCardButton
} from "./index"
import { PaymentCardData } from "../../types"

interface PaymentScreenContentProps {
  onBackPress?: () => void
  onAddNewCard?: () => void
  initialCards?: PaymentCardData[]
}

// Default payment cards data from Figma design
const defaultPaymentCards: PaymentCardData[] = [
  {
    id: "1",
    cardNumber: undefined, // First 3 cards don't show card numbers in Figma
    isConnected: true,
  },
  {
    id: "2", 
    cardNumber: undefined,
    isConnected: true,
  },
  {
    id: "3",
    cardNumber: undefined,
    isConnected: true,
  },
  {
    id: "4",
    cardNumber: "**** ****  **76  3054", // Only the 4th card shows number
    isConnected: true,
  },
]

/**
 * InstructorPaymentScreenContent - Instructor Payment & Earnings screen based on Figma design
 *
 * This component implements the same Payment Options design from Figma as student:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4369
 *
 * Cloned from student PaymentScreenContent but adapted for instructor workflow.
 * Maintains exact same design and UI components.
 *
 * Features:
 * - PaymentHeader: "Payment & Earnings" title with back button
 * - PaymentCardsList: List of connected payment cards for earnings
 * - AddNewCardButton: Blue button to add new payment method
 * - Proper background color (#F5F9FF)
 * - Consistent spacing and styling
 */
export function PaymentScreenContent({
  onBackPress,
  onAddNewCard,
  initialCards = defaultPaymentCards
}: PaymentScreenContentProps) {
  const { themed } = useAppTheme()
  const [cards, setCards] = useState<PaymentCardData[]>(initialCards)

  const handleBackPress = () => {
    console.log("👨‍🏫💳 Navigate back")
    onBackPress?.()
  }

  const handleCardPress = (card: PaymentCardData) => {
    console.log("👨‍🏫💳 Card pressed:", card)
    // Handle card selection/editing for instructor earnings
  }

  const handleAddNewCard = () => {
    console.log("👨‍🏫💳 Add new card requested")
    onAddNewCard?.()
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
    >
      <View style={themed($content)}>
        {/* Header with back button */}
        <PaymentHeader onBackPress={handleBackPress} />
        
        {/* Payment cards list */}
        <PaymentCardsList
          cards={cards}
          onCardPress={handleCardPress}
        />
        
        {/* Add new card button */}
        <AddNewCardButton onPress={handleAddNewCard} />
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  flex: 1,
}
