import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface AddNewCardButtonProps {
  onPress?: () => void
}

/**
 * AddNewCardButton - Button to add new payment card
 *
 * Features:
 * - Blue background (#0961F5)
 * - White text and icon
 * - Plus icon in circle
 * - 30px border radius from Figma
 * - Shadow effect
 */
export function AddNewCardButton({ onPress }: AddNewCardButtonProps) {
  const { themed } = useAppTheme()

  const handlePress = () => {
    console.log("💳 Add new card pressed")
    onPress?.()
  }

  return (
    <TouchableOpacity style={themed($container)} onPress={handlePress}>
      <View style={themed($button)}>
        <Text style={themed($buttonText)}>Add New Card</Text>

        <View style={themed($iconContainer)}>
          <View style={themed($iconCircle)}>
            <Icon icon="plus" size={21} color="#0961F5" />
          </View>
        </View>
      </View>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  marginHorizontal: 39,
  marginTop: 40,
  marginBottom: 40,
}

const $button: ViewStyle = {
  backgroundColor: "#0961F5", // Blue color from Figma
  borderRadius: 30, // 30px border radius from Figma
  height: 60,
  position: "relative", // For absolute positioning of children
  // Shadow from Figma: 1px 2px 8px 0px rgba(0, 0, 0, 0.3)
  shadowColor: "#000000",
  shadowOffset: {
    width: 1,
    height: 2,
  },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8, // For Android
}

const $iconContainer: ViewStyle = {
  position: "absolute",
  right: 6, // x: 293 from Figma (350 - 48 - 6 = 296, close to 293)
  top: 6, // y: 6 from Figma
  width: 48,
  height: 48,
}

const $iconCircle: ViewStyle = {
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  justifyContent: "center",
  alignItems: "center",
}

const $buttonText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight - for button text
  fontSize: 18,
  lineHeight: 26,
  color: "#FFFFFF",
  textAlign: "center", // Center text in button
  position: "absolute",
  left: 0,
  right: 0,
  top: 17, // y position from Figma
}
