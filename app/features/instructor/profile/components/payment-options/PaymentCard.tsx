import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface PaymentCardProps {
  cardNumber?: string
  isConnected: boolean
  onPress?: () => void
}

/**
 * PaymentCard - Individual payment card component
 * 
 * Features:
 * - Card number display (masked)
 * - "Connected" status indicator
 * - White background with shadow
 * - 16px border radius from Figma
 */
export function PaymentCard({ 
  cardNumber, 
  isConnected, 
  onPress 
}: PaymentCardProps) {
  const { themed } = useAppTheme()

  const handlePress = () => {
    console.log(`💳 Payment card pressed: ${cardNumber || 'No card number'}`)
    onPress?.()
  }

  return (
    <TouchableOpacity style={themed($container)} onPress={handlePress}>
      <View style={themed($cardContent)}>
        {cardNumber && (
          <Text style={themed($cardNumber)}>{cardNumber}</Text>
        )}
        
        {isConnected && (
          <Text style={themed($connectedText)}>Connected</Text>
        )}
      </View>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  marginHorizontal: 34,
  marginBottom: 20,
}

const $cardContent: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16, // 16px border radius from Figma
  height: 60,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: 24,
  // Shadow from Figma: 0px 4px 10px 0px rgba(0, 0, 0, 0.08)
  shadowColor: "#000000",
  shadowOffset: {
    width: 0,
    height: 4,
  },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4, // For Android
}

const $cardNumber: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - per guidelines
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
  flex: 1,
}

const $connectedText: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - per guidelines
  fontSize: 14,
  lineHeight: 18,
  color: "#167F71", // Green color from Figma
  textAlign: "right",
}
