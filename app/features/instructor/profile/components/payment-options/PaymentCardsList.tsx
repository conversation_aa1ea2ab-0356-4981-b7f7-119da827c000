import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { PaymentCard } from "./PaymentCard"
import { PaymentCardData } from "../../types"

interface PaymentCardsListProps {
  cards: PaymentCardData[]
  onCardPress?: (card: PaymentCardData) => void
}

/**
 * PaymentCardsList - List of payment cards
 * 
 * Features:
 * - Display all payment cards
 * - Proper spacing between cards
 * - Handle card press events
 */
export function PaymentCardsList({ 
  cards, 
  onCardPress 
}: PaymentCardsListProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {cards.map((card, index) => (
        <PaymentCard
          key={card.id || index}
          cardNumber={card.cardNumber}
          isConnected={card.isConnected}
          onPress={() => onCardPress?.(card)}
        />
      ))}
    </View>
  )
}

const $container: ViewStyle = {
  paddingTop: 30, // Spacing from header
}
