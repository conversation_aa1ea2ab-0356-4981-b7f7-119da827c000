import React, { useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import {
  SecurityHeader,
  SecuritySettingsList,
  SecurityButtons
} from "./index"
import { SecuritySettings } from "../../types"

interface SecurityScreenContentProps {
  onBackPress?: () => void
  initialSettings?: SecuritySettings
}

// Default security settings
const defaultSecuritySettings: SecuritySettings = {
  rememberMe: true,
  biometricId: true,
  faceId: false,
}

/**
 * SecurityScreenContent - Instructor security screen based on Figma design
 *
 * This component implements the Security design from Figma:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4447
 *
 * Features:
 * - SecurityHeader: "Security" title with back button
 * - SecuritySettingsList: Toggle switches and Google Authenticator
 * - SecurityButtons: Change PIN and Change Password buttons
 * - Proper background color (#F5F9FF)
 * - Consistent spacing and styling
 */
export function SecurityScreenContent({
  onBackPress,
  initialSettings = defaultSecuritySettings
}: SecurityScreenContentProps) {
  const { themed } = useAppTheme()
  const [settings, setSettings] = useState<SecuritySettings>(initialSettings)

  const handleBackPress = () => {
    console.log("🔒 Navigate back")
    onBackPress?.()
  }

  const handleSettingChange = (key: keyof SecuritySettings, value: boolean) => {
    console.log(`🔒 Setting ${key} changed to:`, value)
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleGoogleAuthenticatorPress = () => {
    console.log("🔒 Google Authenticator pressed")
    // Handle Google Authenticator setup
  }

  const handleChangePinPress = () => {
    console.log("🔒 Change PIN pressed")
    // Handle PIN change
  }

  const handleChangePasswordPress = () => {
    console.log("🔒 Change Password pressed")
    // Handle password change
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
    >
      <View style={themed($content)}>
        {/* Header with back button */}
        <SecurityHeader onBackPress={handleBackPress} />
        
        {/* Security settings list */}
        <SecuritySettingsList
          settings={settings}
          onSettingChange={handleSettingChange}
          onGoogleAuthenticatorPress={handleGoogleAuthenticatorPress}
        />
        
        {/* Action buttons */}
        <SecurityButtons
          onChangePinPress={handleChangePinPress}
          onChangePasswordPress={handleChangePasswordPress}
        />
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  flex: 1,
}
