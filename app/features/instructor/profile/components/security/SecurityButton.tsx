import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"

interface SecurityButtonProps {
  title: string
  variant: "outline" | "filled"
  onPress?: () => void
}

/**
 * SecurityButton - Button component for security actions
 *
 * Features:
 * - Two variants: outline (Change PIN) and filled (Change Password)
 * - Filled variant has white circle with icon on the right
 * - 30px border radius from Figma
 */
export function SecurityButton({
  title,
  variant,
  onPress
}: SecurityButtonProps) {
  const { themed } = useAppTheme()

  const handlePress = () => {
    console.log(`🔒 ${title} pressed`)
    onPress?.()
  }

  const isOutline = variant === "outline"

  return (
    <TouchableOpacity style={themed($container)} onPress={handlePress}>
      <View style={themed([
        $button,
        isOutline ? $buttonOutline : $buttonFilled
      ])}>
        <Text style={themed([
          $buttonText,
          isOutline ? $buttonTextOutline : $buttonTextFilled
        ])}>
          {title}
        </Text>

        {!isOutline && (
          <View style={themed($iconContainer)}>
            <View style={themed($iconCircle)}>
              <Icon icon="lock" size={17} color="#0961F5" />
            </View>
          </View>
        )}
      </View>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  marginHorizontal: 39,
  marginVertical: 10, // Reduced from 15 to 10 for tighter spacing
}

const $button: ViewStyle = {
  borderRadius: 30, // 30px border radius from Figma
  height: 60,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
}

const $buttonOutline: ViewStyle = {
  backgroundColor: "#F5F9FF", // Background color from Figma
  borderWidth: 2,
  borderColor: "rgba(180, 189, 196, 0.3)", // Border color from Figma
}

const $buttonFilled: ViewStyle = {
  backgroundColor: "#0961F5", // Blue color from Figma
  // Shadow from Figma: 1px 2px 8px 0px rgba(0, 0, 0, 0.3)
  shadowColor: "#000000",
  shadowOffset: {
    width: 1,
    height: 2,
  },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8, // For Android
}

const $buttonText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight - for button text
  fontSize: 18,
  lineHeight: 26,
  textAlign: "center",
}

const $buttonTextOutline: TextStyle = {
  color: "#202244",
}

const $buttonTextFilled: TextStyle = {
  color: "#FFFFFF",
  position: "absolute",
  left: 0,
  right: 0,
  top: 17,
}

const $iconContainer: ViewStyle = {
  position: "absolute",
  right: 6,
  top: 6,
  width: 48,
  height: 48,
}

const $iconCircle: ViewStyle = {
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  justifyContent: "center",
  alignItems: "center",
}
