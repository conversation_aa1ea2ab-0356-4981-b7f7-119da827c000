import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { SecurityButton } from "./SecurityButton"

interface SecurityButtonsProps {
  onChangePinPress?: () => void
  onChangePasswordPress?: () => void
}

/**
 * SecurityButtons - Action buttons for security screen
 *
 * Features:
 * - Change PIN button (outline variant)
 * - Change Password button (filled variant with icon)
 * - Proper spacing between buttons
 */
export function SecurityButtons({
  onChangePinPress,
  onChangePasswordPress
}: SecurityButtonsProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Change PIN button */}
      <SecurityButton
        title="Change PIN"
        variant="outline"
        onPress={onChangePinPress}
      />

      {/* Change Password button */}
      <SecurityButton
        title="Change Password"
        variant="filled"
        onPress={onChangePasswordPress}
      />
    </View>
  )
}

const $container: ViewStyle = {
  marginTop: 80, // Reduced spacing to fit all content on screen
}
