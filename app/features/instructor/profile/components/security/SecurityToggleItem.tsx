import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface SecurityToggleItemProps {
  title: string
  isEnabled: boolean
  onToggle: (enabled: boolean) => void
}

/**
 * SecurityToggleItem - Toggle switch item for security settings
 *
 * Features:
 * - Title text on the left
 * - Custom toggle switch on the right
 * - Blue active state (#0961F5)
 * - Light blue background (#E8F1FF) with border
 */
export function SecurityToggleItem({
  title,
  isEnabled,
  onToggle
}: SecurityToggleItemProps) {
  const { themed } = useAppTheme()

  const handleToggle = () => {
    console.log(`🔒 Toggle ${title}: ${!isEnabled}`)
    onToggle(!isEnabled)
  }

  return (
    <View style={themed($container)}>
      <Text style={themed($title)}>{title}</Text>

      <TouchableOpacity
        style={themed([$toggleContainer, isEnabled && $toggleContainerActive])}
        onPress={handleToggle}
      >
        <View style={themed([$toggleCircle, isEnabled && $toggleCircleActive])} />
      </TouchableOpacity>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: 35,
  paddingVertical: 12, // Reduced from 15 to 12 for tighter spacing
}

const $title: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - per guidelines
  fontSize: 16,
  lineHeight: 20,
  color: "#202244",
  flex: 1,
}

const $toggleContainer: ViewStyle = {
  width: 50,
  height: 30,
  borderRadius: 15,
  backgroundColor: "#E8F1FF", // Light blue background from Figma
  borderWidth: 2,
  borderColor: "rgba(180, 189, 196, 0.4)", // Border color from Figma
  justifyContent: "center",
  paddingHorizontal: 4,
}

const $toggleContainerActive: ViewStyle = {
  backgroundColor: "#E8F1FF", // Same background when active
  borderColor: "rgba(180, 189, 196, 0.4)", // Same border when active
}

const $toggleCircle: ViewStyle = {
  width: 22,
  height: 20,
  borderRadius: 10,
  backgroundColor: "#FFFFFF", // White circle when inactive
  alignSelf: "flex-start",
}

const $toggleCircleActive: ViewStyle = {
  backgroundColor: "#0961F5", // Blue circle when active
  alignSelf: "flex-end",
}
