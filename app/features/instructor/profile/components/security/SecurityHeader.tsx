import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface SecurityHeaderProps {
  onBackPress?: () => void
}

/**
 * SecurityHeader - Header component for Security screen
 * 
 * Features:
 * - Back button (arrow left icon)
 * - "Security" title
 * - Consistent styling with other profile headers
 */
export function SecurityHeader({ onBackPress }: SecurityHeaderProps) {
  const { themed } = useAppTheme()

  const handleBackPress = () => {
    console.log("🔒 Back pressed")
    onBackPress?.()
  }

  return (
    <View style={themed($container)}>
      <TouchableOpacity style={themed($backButton)} onPress={handleBackPress}>
        <Icon icon="caretLeft" size={20} color="#202244" />
      </TouchableOpacity>
      
      <Text style={themed($title)}>Security</Text>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35,
  paddingTop: 25,
  paddingBottom: 20,
}

const $backButton: ViewStyle = {
  width: 26,
  height: 20,
  justifyContent: "center",
  alignItems: "flex-start",
  marginRight: 12,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight - for section headings per guidelines
  fontSize: 21,
  lineHeight: 30,
  color: "#202244",
  flex: 1,
}
