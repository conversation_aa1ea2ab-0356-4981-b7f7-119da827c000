import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { SecurityToggleItem } from "./SecurityToggleItem"
import { SecurityMenuItem } from "./SecurityMenuItem"
import { SecuritySettings } from "../../types"

interface SecuritySettingsListProps {
  settings: SecuritySettings
  onSettingChange: (key: keyof SecuritySettings, value: boolean) => void
  onGoogleAuthenticatorPress?: () => void
}

/**
 * SecuritySettingsList - List of security settings
 *
 * Features:
 * - Toggle switches for Remember Me, Biometric ID, Face ID
 * - Menu item for Google Authenticator
 * - Proper spacing between items
 */
export function SecuritySettingsList({
  settings,
  onSettingChange,
  onGoogleAuthenticatorPress
}: SecuritySettingsListProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Remember Me toggle */}
      <SecurityToggleItem
        title="Remember Me"
        isEnabled={settings.rememberMe}
        onToggle={(enabled) => onSettingChange("rememberMe", enabled)}
      />

      {/* Biometric ID toggle */}
      <SecurityToggleItem
        title="Biometric ID"
        isEnabled={settings.biometricId}
        onToggle={(enabled) => onSettingChange("biometricId", enabled)}
      />

      {/* Face ID toggle */}
      <SecurityToggleItem
        title="Face ID"
        isEnabled={settings.faceId}
        onToggle={(enabled) => onSettingChange("faceId", enabled)}
      />

      {/* Google Authenticator menu item */}
      <View style={themed($menuItemContainer)}>
        <SecurityMenuItem
          title="Google Authenticator"
          onPress={onGoogleAuthenticatorPress}
        />
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingTop: 28, // Spacing from header
}

const $menuItemContainer: ViewStyle = {
  marginTop: 20, // Reduced spacing to fit content on screen
}
