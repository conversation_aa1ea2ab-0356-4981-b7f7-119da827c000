import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface SecurityMenuItemProps {
  title: string
  onPress?: () => void
}

/**
 * SecurityMenuItem - Menu item with arrow for security options
 *
 * Features:
 * - Title text on the left
 * - Arrow icon on the right
 * - Used for Google Authenticator
 */
export function SecurityMenuItem({
  title,
  onPress
}: SecurityMenuItemProps) {
  const { themed } = useAppTheme()

  const handlePress = () => {
    console.log(`🔒 ${title} pressed`)
    onPress?.()
  }

  return (
    <TouchableOpacity style={themed($container)} onPress={handlePress}>
      <Text style={themed($title)}>{title}</Text>
      <Icon icon="caretRight" size={16} color="#1D1D1B" />
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: 35,
  paddingVertical: 12, // Reduced from 15 to 12 for tighter spacing
}

const $title: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - per guidelines
  fontSize: 16,
  lineHeight: 20,
  color: "#202244",
  flex: 1,
}
