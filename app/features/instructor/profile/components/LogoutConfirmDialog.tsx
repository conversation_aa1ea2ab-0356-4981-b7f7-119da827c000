import React from "react"
import { View, ViewStyle, TextStyle, Modal, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface LogoutConfirmDialogProps {
  visible: boolean
  onConfirm: () => void
  onCancel: () => void
}

/**
 * LogoutConfirmDialog - Confirmation dialog for logout action
 * 
 * Features:
 * - Modal overlay with fade animation
 * - Warning icon with logout confirmation message
 * - Cancel and Logout buttons
 * - Consistent styling with app design guidelines
 */
export function LogoutConfirmDialog({
  visible,
  onConfirm,
  onCancel
}: LogoutConfirmDialogProps) {
  const { themed } = useAppTheme()

  const handleConfirm = () => {
    console.log("🚪 Logout confirmed")
    onConfirm()
  }

  const handleCancel = () => {
    console.log("🚪 Logout cancelled")
    onCancel()
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <View style={themed($overlay)}>
        <View style={themed($container)}>
          {/* Warning Icon */}
          <View style={themed($iconContainer)}>
            <Icon 
              icon="x" 
              size={48} 
              color="#FF4444" 
            />
          </View>

          {/* Title */}
          <Text style={themed($title)}>Logout</Text>

          {/* Description */}
          <Text style={themed($description)}>
            Are you sure you want to logout from your account?
          </Text>

          {/* Buttons */}
          <View style={themed($buttonContainer)}>
            {/* Cancel Button */}
            <TouchableOpacity
              style={themed($cancelButton)}
              onPress={handleCancel}
              activeOpacity={0.8}
            >
              <Text style={themed($cancelButtonText)}>Cancel</Text>
            </TouchableOpacity>

            {/* Logout Button */}
            <TouchableOpacity
              style={themed($logoutButton)}
              onPress={handleConfirm}
              activeOpacity={0.8}
            >
              <Text style={themed($logoutButtonText)}>Logout</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  )
}

const $overlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
  paddingHorizontal: 20,
}

const $container: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 20,
  paddingVertical: 32,
  paddingHorizontal: 24,
  alignItems: "center",
  maxWidth: 320,
  width: "100%",
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.25,
  shadowRadius: 12,
  elevation: 8,
}

const $iconContainer: ViewStyle = {
  width: 80,
  height: 80,
  borderRadius: 40,
  backgroundColor: "#FFE8E8",
  justifyContent: "center",
  alignItems: "center",
  marginBottom: 20,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for dialog titles
  fontSize: 20,
  lineHeight: 28,
  color: "#202244",
  textAlign: "center",
  marginBottom: 12,
}

const $description: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for descriptions
  fontSize: 16,
  lineHeight: 22,
  color: "#545454",
  textAlign: "center",
  marginBottom: 32,
}

const $buttonContainer: ViewStyle = {
  flexDirection: "row",
  width: "100%",
  gap: 12,
}

const $cancelButton: ViewStyle = {
  flex: 1,
  backgroundColor: "#F5F9FF",
  borderRadius: 30,
  paddingVertical: 14,
  paddingHorizontal: 20,
  borderWidth: 1,
  borderColor: "#E8F1FF",
  alignItems: "center",
  justifyContent: "center",
  minHeight: 48,
}

const $cancelButtonText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for button text
  fontSize: 16,
  lineHeight: 20,
  color: "#202244",
  textAlign: "center",
}

const $logoutButton: ViewStyle = {
  flex: 1,
  backgroundColor: "#FF4444",
  borderRadius: 30,
  paddingVertical: 14,
  paddingHorizontal: 20,
  alignItems: "center",
  justifyContent: "center",
  minHeight: 48,
}

const $logoutButtonText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for button text
  fontSize: 16,
  lineHeight: 20,
  color: "#FFFFFF",
  textAlign: "center",
}
