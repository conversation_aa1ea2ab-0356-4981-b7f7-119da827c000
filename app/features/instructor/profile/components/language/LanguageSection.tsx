import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { LanguageItem } from "./LanguageItem"
import { LanguageSectionProps } from "../../types/language"

export function LanguageSection({ title, languages, onLanguagePress }: LanguageSectionProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Section title */}
      <Text style={themed($sectionTitle)}>{title}</Text>

      {/* Language items */}
      <View style={themed($languageList)}>
        {languages.map((language) => (
          <LanguageItem
            key={language.id}
            language={language}
            onPress={onLanguagePress}
          />
        ))}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  marginBottom: 25, // Space between sections (271 - 246 = 25px from Figma)
  paddingHorizontal: 24, // Reduced for better mobile layout (was 34px)
}

const $sectionTitle: TextStyle = {
  fontFamily: "nunitoSansBold", // 700 weight - bold for section titles per requirements
  fontSize: 18, // Exact Figma font size
  lineHeight: 26, // Exact Figma line height (18 * 1.445)
  color: "#202244", // Exact Figma text color
  marginBottom: 15, // Space before language items (41 - 26 = 15px from Figma)
}

const $languageList: ViewStyle = {
  paddingLeft: 20, // SubCategories: x: 20, All Language: x: 54 - 34 = 20
  paddingRight: 0, // Removed to achieve 24px total distance from right edge
}
