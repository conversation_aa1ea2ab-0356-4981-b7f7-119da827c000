import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { LanguageHeaderProps } from "../../types/language"

export function LanguageHeader({ onBackPress }: LanguageHeaderProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <TouchableOpacity
        style={themed($backButton)}
        onPress={onBackPress}
      >
        <Icon icon="back" size={26} color="#202244" />
      </TouchableOpacity>

      <Text style={themed($title)}>Language</Text>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35, // Match Edit Profile header
  paddingTop: 25, // Match Profile header spacing from top edge
  paddingBottom: 20, // Match Edit Profile header
}

const $backButton: ViewStyle = {
  marginRight: 12, // Match Edit Profile header
}

const $title: TextStyle = {
  fontFamily: "nunitoSansSemiBold", // Match Edit Profile header font
  fontSize: 21, // Match Edit Profile header
  lineHeight: 30, // Match Edit Profile header
  color: "#202244", // Match Edit Profile header
}
