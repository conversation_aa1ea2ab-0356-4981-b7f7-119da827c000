import React, { useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { LanguageHeader, LanguageSection } from "./index"
import { LanguageOption, LanguageSettings } from "../../types/language"

interface LanguageScreenContentProps {
  onBackPress?: () => void
  onLanguageChange?: (language: LanguageOption) => void
  initialSettings?: LanguageSettings
}

// Default language data based on Figma design
const defaultLanguageSettings: LanguageSettings = {
  selectedLanguage: "en-US",
  availableLanguages: [
    // SubCategories
    { id: "en-US-sub", name: "English (US)", code: "en-US", isSelected: true, category: "subcategory" },
    { id: "en-UK-sub", name: "English (UK)", code: "en-UK", isSelected: false, category: "subcategory" },

    // All Languages
    { id: "en-US-all", name: "English (US)", code: "en-US", isSelected: true, category: "all" },
    { id: "ar", name: "Arabic", code: "ar", isSelected: false, category: "all" },
    { id: "hi", name: "Hindi", code: "hi", isSelected: false, category: "all" },
    { id: "bn", name: "Bengali", code: "bn", isSelected: false, category: "all" },
    { id: "de", name: "Deutsch", code: "de", isSelected: false, category: "all" },
    { id: "it", name: "Italian", code: "it", isSelected: false, category: "all" },
    { id: "ko", name: "Korean", code: "ko", isSelected: false, category: "all" },
    { id: "fr", name: "Francais", code: "fr", isSelected: false, category: "all" },
    { id: "ru", name: "Russian", code: "ru", isSelected: false, category: "all" },
    { id: "pl", name: "Polish", code: "pl", isSelected: false, category: "all" },
    { id: "es", name: "Spanish", code: "es", isSelected: false, category: "all" },
    { id: "zh", name: "Mandarin", code: "zh", isSelected: false, category: "all" },
  ]
}

/**
 * LanguageScreenContent - Language settings screen based on Figma design
 *
 * This component implements the Language design from Figma:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4481
 *
 * Features:
 * - LanguageHeader: "Language" title with back button
 * - SubCategories section: English (US) and English (UK) options
 * - All Language section: Complete list of 12 languages
 * - Language selection with visual feedback
 */
export function LanguageScreenContent({
  onBackPress,
  onLanguageChange,
  initialSettings = defaultLanguageSettings
}: LanguageScreenContentProps) {
  const { themed } = useAppTheme()
  const [languageSettings, setLanguageSettings] = useState<LanguageSettings>(initialSettings)

  const handleBackPress = () => {
    console.log("🌐 Language back pressed")
    onBackPress?.()
  }

  const handleLanguagePress = (selectedLanguage: LanguageOption) => {
    console.log("🌐 Language selected:", selectedLanguage.name)

    // Update language settings
    const updatedLanguages = languageSettings.availableLanguages.map(lang => ({
      ...lang,
      isSelected: lang.code === selectedLanguage.code
    }))

    const newSettings: LanguageSettings = {
      selectedLanguage: selectedLanguage.code,
      availableLanguages: updatedLanguages
    }

    setLanguageSettings(newSettings)
    onLanguageChange?.(selectedLanguage)
  }

  // Filter languages by category
  const subCategoryLanguages = languageSettings.availableLanguages.filter(
    lang => lang.category === "subcategory"
  )

  const allLanguages = languageSettings.availableLanguages.filter(
    lang => lang.category === "all"
  )

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
    >
      <View style={themed($content)}>
        {/* Header with back button */}
        <LanguageHeader onBackPress={handleBackPress} />

        {/* SubCategories section with extra top margin */}
        <View style={themed($firstSectionContainer)}>
          <LanguageSection
            title="SubCategories:"
            languages={subCategoryLanguages}
            onLanguagePress={handleLanguagePress}
          />
        </View>

        {/* All Language section */}
        <LanguageSection
          title="All Language"
          languages={allLanguages}
          onLanguagePress={handleLanguagePress}
        />
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  flex: 1,
  paddingHorizontal: 0, // Header and sections handle their own padding
  paddingTop: 0, // No top padding - header handles its own spacing
}

const $firstSectionContainer: ViewStyle = {
  marginTop: 30, // Increased space between header and SubCategories section
}






