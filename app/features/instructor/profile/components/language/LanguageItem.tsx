import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { LanguageItemProps } from "../../types/language"

export function LanguageItem({ language, onPress }: LanguageItemProps) {
  const { themed } = useAppTheme()

  const handlePress = () => {
    onPress(language)
  }

  return (
    <TouchableOpacity
      style={themed($container)}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={themed($itemContainer)}>
        {/* Language name */}
        <Text style={themed($languageText)}>{language.name}</Text>

        {/* Checkbox - small square on the right */}
        <View style={themed([
          $checkboxContainer,
          language.isSelected ? $selectedCheckbox : $unselectedCheckbox
        ])}>
          {language.isSelected && (
            <Icon icon="check" size={13} color="#FFFFFF" />
          )}
        </View>
      </View>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  marginBottom: 24, // Reduced spacing for better mobile experience (was 48px)
}

const $itemContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  minHeight: 18, // Even smaller height for tightest spacing (reduced 2px)
  paddingVertical: 0, // No vertical padding for maximum tightness (reduced 2px)
  paddingHorizontal: 0,
  marginBottom: 0, // No margin between items for tightest spacing
  borderRadius: 8, // Exact Figma border radius
  flex: 1, // Take full available width for better balance
}

const $languageText: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - correct font family per design guidelines
  fontSize: 18, // Increased from 15px for better readability
  lineHeight: 23, // Adjusted line height (18 * 1.28)
  color: "#202244", // Exact Figma text color
  flex: 1, // Take available space, leaving room for checkbox
  textAlignVertical: "center", // Center text vertically
}

const $checkboxContainer: ViewStyle = {
  width: 28, // Exact Figma width
  height: 28, // Exact Figma height
  borderRadius: 8, // Exact Figma border radius
  justifyContent: "center",
  alignItems: "center",
  marginLeft: 12, // Space between text and checkbox for better balance
}

const $selectedCheckbox: ViewStyle = {
  backgroundColor: "#167F71", // Exact Figma background color
  borderColor: "#167F71", // No border for selected state in Figma
}

const $unselectedCheckbox: ViewStyle = {
  backgroundColor: "#E8F1FF", // Exact Figma background color
  borderColor: "#B4BDC4", // Exact Figma border color
  borderWidth: 2, // Exact Figma border width
}
