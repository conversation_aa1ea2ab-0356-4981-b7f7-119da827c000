import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { ProfileMenuItem as ProfileMenuItemType } from "../types"

interface ProfileMenuItemProps {
  item: ProfileMenuItemType
}

export function ProfileMenuItem({ item }: ProfileMenuItemProps) {
  const { themed } = useAppTheme()

  return (
    <TouchableOpacity style={themed($container)} onPress={item.onPress}>
      <View style={themed($leftSection)}>
        <View style={themed($iconContainer)}>
          <Icon 
            icon={item.icon} 
            size={20} 
            color={item.color || "#202244"} 
          />
        </View>
        <Text style={themed([$title, item.color && { color: item.color }])}>
          {item.title}
        </Text>
      </View>

      <View style={themed($rightSection)}>
        {item.value && (
          <Text style={themed($value)}>{item.value}</Text>
        )}
        {item.hasArrow && (
          <Icon 
            icon="caretRight" 
            size={16} 
            color="#8E8E93" 
          />
        )}
      </View>
    </TouchableOpacity>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingVertical: 16,
  paddingHorizontal: 20,
  backgroundColor: "#FFFFFF",
  borderBottomWidth: 1,
  borderBottomColor: "#F0F0F0",
}

const $leftSection: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
}

const $iconContainer: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 8,
  backgroundColor: "#F5F9FF",
  justifyContent: "center",
  alignItems: "center",
  marginRight: 16,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for menu items - MANDATORY
  fontSize: 16,
  lineHeight: 20,
  color: "#202244",
  flex: 1,
}

const $rightSection: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $value: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for values - MANDATORY
  fontSize: 14,
  lineHeight: 18,
  color: "#8E8E93",
  marginRight: 8,
}
