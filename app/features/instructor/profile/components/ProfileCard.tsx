import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { ProfileData } from "../types"

interface ProfileCardProps {
  profileData: ProfileData
  onEditPress?: () => void
}

export function ProfileCard({ profileData, onEditPress }: ProfileCardProps) {
  const { themed } = useAppTheme()

  console.log("👤 ProfileCard rendering with:", { profileData, hasOnEditPress: !!onEditPress })

  const handleEditPress = () => {
    console.log("👤 ProfileCard edit button pressed!")
    onEditPress?.()
  }

  return (
    <View style={themed($container)}>
      <View style={themed($card)}>
        {/* Avatar */}
        <View style={themed($avatarContainer)}>
          <View style={themed($avatar)}>
            {/* Placeholder for avatar image */}
          </View>

          {/* Edit Button */}
          <TouchableOpacity style={themed($editButton)} onPress={handleEditPress}>
            <Icon icon="settings" size={20} color="#167F71" />
          </TouchableOpacity>
        </View>

        {/* User Info */}
        <Text style={themed($name)}>{profileData.name}</Text>
        <Text style={themed($email)}>{profileData.email}</Text>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
  marginTop: 20,
}

const $card: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  paddingVertical: 40,
  paddingHorizontal: 20,
  alignItems: "center",
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4,
}

const $avatarContainer: ViewStyle = {
  position: "relative",
  marginBottom: 20,
}

const $avatar: ViewStyle = {
  width: 110,
  height: 110,
  borderRadius: 55,
  backgroundColor: "#D8D8D8",
  borderWidth: 4,
  borderColor: "#167F71",
}

const $editButton: ViewStyle = {
  position: "absolute",
  bottom: -5,
  right: -5,
  width: 36,
  height: 36,
  borderRadius: 8,
  backgroundColor: "#FFFFFF",
  borderWidth: 3,
  borderColor: "#167F71",
  justifyContent: "center",
  alignItems: "center",
}

const $name: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for names
  fontSize: 24,
  lineHeight: 35,
  color: "#202244",
  textAlign: "center",
  marginBottom: 4,
}

const $email: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for descriptions - MANDATORY
  fontSize: 13,
  lineHeight: 16,
  color: "#545454",
  textAlign: "center",
}
