import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { EditProfileFormField } from "./EditProfileFormField"
import { EditProfileData } from "../../types"

interface EditProfileFormProps {
  data: EditProfileData
  onDataChange: (field: keyof EditProfileData, value: string) => void
  onGenderPress?: () => void
}

export function EditProfileForm({ data, onDataChange, onGenderPress }: EditProfileFormProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Full Name */}
      <EditProfileFormField
        label="Full Name"
        value={data.fullName}
        onChangeText={(text) => onDataChange("fullName", text)}
        placeholder="Enter your full name"
      />

      {/* Username */}
      <EditProfileFormField
        label="Username"
        value={data.username}
        onChangeText={(text) => onDataChange("username", text)}
        placeholder="Enter your username"
        editable={false}
      />

      {/* Date of Birth */}
      <EditProfileFormField
        label="Date of Birth"
        value={data.dateOfBirth}
        onChangeText={(text) => onDataChange("dateOfBirth", text)}
        placeholder="Select date"
        icon="calendar"
      />

      {/* Email */}
      <EditProfileFormField
        label="Email"
        value={data.email}
        onChangeText={(text) => onDataChange("email", text)}
        placeholder="Enter your email"
        keyboardType="email-address"
        icon="mail"
      />

      {/* Phone Number */}
      <EditProfileFormField
        label="Phone Number"
        value={data.phoneNumber}
        onChangeText={(text) => onDataChange("phoneNumber", text)}
        placeholder="************"
        keyboardType="phone-pad"
      />

      {/* Gender */}
      <EditProfileFormField
        label="Gender"
        value={data.gender}
        onChangeText={(text) => onDataChange("gender", text as any)}
        placeholder="Select gender"
        editable={false}
        onPress={onGenderPress}
        icon="chevronDown"
      />

      {/* Role */}
      <EditProfileFormField
        label="Role"
        value={data.role}
        onChangeText={(text) => onDataChange("role", text)}
        placeholder="Instructor"
        editable={false}
      />
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
}
