import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"

interface EditProfileUpdateButtonProps {
  onPress?: () => void
  loading?: boolean
}

export function EditProfileUpdateButton({ onPress, loading = false }: EditProfileUpdateButtonProps) {
  const { themed, theme } = useAppTheme()

  return (
    <View style={themed($container)}>
      <TouchableOpacity
        style={themed($button)}
        onPress={onPress}
        activeOpacity={0.8}
        disabled={loading}
      >
        <Text style={themed($buttonText)}>Update</Text>

        <View style={themed($iconContainer)}>
          <View style={themed($iconBackground)}>
            <Icon
              icon="arrowRight"
              size={21}
              color={theme.colors.tint}
            />
          </View>
        </View>
      </TouchableOpacity>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 39,
  paddingTop: 40,
  paddingBottom: 40,
})

const $button: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  backgroundColor: colors.tint, // Main interactive color from design guidelines
  borderRadius: 30,
  height: 60,
  paddingHorizontal: 20,
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8,
})

const $buttonText: ThemedStyle<TextStyle> = ({ colors }) => ({
  fontFamily: "lexendDecaSemiBold", // 600 weight for button text
  fontSize: 18,
  lineHeight: 26,
  color: colors.palette.neutral100, // White text from design guidelines
  textAlign: "center",
  flex: 1,
})

const $iconContainer: ThemedStyle<ViewStyle> = () => ({
  marginLeft: 10,
})

const $iconBackground: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: colors.palette.neutral100, // White background from design guidelines
  justifyContent: "center",
  alignItems: "center",
})
