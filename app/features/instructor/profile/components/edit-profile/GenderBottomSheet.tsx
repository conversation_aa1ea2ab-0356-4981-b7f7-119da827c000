import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity, Modal } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface GenderBottomSheetProps {
  visible: boolean
  onClose: () => void
  onSelectGender: (gender: "Male" | "Female") => void
  selectedGender?: "Male" | "Female"
}

export function GenderBottomSheet({
  visible,
  onClose,
  onSelectGender,
  selectedGender
}: GenderBottomSheetProps) {
  const { themed } = useAppTheme()

  const genderOptions = [
    { label: "Male", value: "Male" as const },
    { label: "Female", value: "Female" as const }
  ]

  const handleSelectGender = (gender: "Male" | "Female") => {
    onSelectGender(gender)
    onClose()
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <TouchableOpacity 
        style={themed($overlay)} 
        activeOpacity={1} 
        onPress={onClose}
      >
        <View style={themed($bottomSheet)}>
          <TouchableOpacity activeOpacity={1}>
            {/* Header */}
            <View style={themed($header)}>
              <Text style={themed($title)}>Select Gender</Text>
              <TouchableOpacity onPress={onClose} style={themed($closeButton)}>
                <Icon icon="x" size={24} color="#666666" />
              </TouchableOpacity>
            </View>

            {/* Gender Options */}
            <View style={themed($optionsContainer)}>
              {genderOptions.map((option) => (
                <TouchableOpacity
                  key={option.value}
                  style={themed([
                    $optionItem,
                    selectedGender === option.value && $selectedOption
                  ])}
                  onPress={() => handleSelectGender(option.value)}
                >
                  <Text style={themed([
                    $optionText,
                    selectedGender === option.value && $selectedOptionText
                  ])}>
                    {option.label}
                  </Text>
                  {selectedGender === option.value && (
                    <Icon icon="check" size={20} color="#007AFF" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    </Modal>
  )
}

const $overlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "flex-end",
}

const $bottomSheet: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderTopLeftRadius: 20,
  borderTopRightRadius: 20,
  paddingBottom: 34, // Safe area bottom
  minHeight: 200,
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingHorizontal: 20,
  paddingVertical: 16,
  borderBottomWidth: 1,
  borderBottomColor: "#F0F0F0",
}

const $title: TextStyle = {
  fontFamily: "lexendDecaRegular", // 400 weight
  fontSize: 18,
  lineHeight: 24,
  color: "#000000",
}

const $closeButton: ViewStyle = {
  padding: 4,
}

const $optionsContainer: ViewStyle = {
  paddingHorizontal: 20,
  paddingTop: 16,
}

const $optionItem: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingVertical: 16,
  paddingHorizontal: 16,
  borderRadius: 12,
  marginBottom: 8,
  backgroundColor: "#F8F9FA",
}

const $selectedOption: ViewStyle = {
  backgroundColor: "#E3F2FD",
  borderWidth: 1,
  borderColor: "#007AFF",
}

const $optionText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight
  fontSize: 16,
  lineHeight: 20,
  color: "#333333",
}

const $selectedOptionText: TextStyle = {
  color: "#007AFF",
  fontFamily: "lexendDecaRegular", // 400 weight
}
