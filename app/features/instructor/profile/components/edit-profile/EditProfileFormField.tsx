import React from "react"
import { View, ViewStyle, TextStyle, TextInput, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { EditProfileFormFieldProps } from "../../types"

export function EditProfileFormField({
  label,
  value,
  onChangeText,
  icon,
  placeholder,
  editable = true,
  keyboardType = "default",
  onPress
}: EditProfileFormFieldProps) {
  const { themed } = useAppTheme()

  // Format gender value for display
  const formatGenderValue = (genderValue: string) => {
    if (!genderValue) return "Select Gender"

    const normalizedValue = genderValue.toLowerCase().trim()

    // Handle various possible API values
    if (normalizedValue === "male" || normalizedValue === "m" || normalizedValue === "1") {
      return "Male"
    }
    if (normalizedValue === "female" || normalizedValue === "f" || normalizedValue === "0") {
      return "Female"
    }

    // If value is already formatted correctly, return as is
    if (normalizedValue === "male" || normalizedValue === "female") {
      return genderValue.charAt(0).toUpperCase() + genderValue.slice(1).toLowerCase()
    }

    // Fallback: return original value with proper capitalization
    return genderValue.charAt(0).toUpperCase() + genderValue.slice(1).toLowerCase()
  }

  // Get icon based on field type
  const getFieldIcon = () => {
    if (icon) return icon
    
    switch (label.toLowerCase()) {
      case "full name":
      case "username":
        return "user"
      case "date of birth":
        return "calendar"
      case "email":
        return "mail"
      case "gender":
        return "chevronDown"
      default:
        return "user"
    }
  }

  const renderContent = () => {
    if (label.toLowerCase() === "gender") {
      return (
        <TouchableOpacity style={themed($genderContainer)} onPress={onPress}>
          <Text style={themed($fieldText)}>{formatGenderValue(value)}</Text>
          <Icon icon="chevronDown" size={16} color="#545454" />
        </TouchableOpacity>
      )
    }

    if (label.toLowerCase().includes("phone")) {
      return (
        <View style={themed($phoneContainer)}>
          <View style={themed($flagContainer)}>
            <Text style={themed($flagText)}>🇮🇳</Text>
          </View>
          <Text style={themed($phonePrefix)}>( +91 )</Text>
          <TextInput
            style={themed($phoneInput)}
            value={value}
            onChangeText={onChangeText}
            placeholder="************"
            placeholderTextColor="#B4BDC4"
            keyboardType="phone-pad"
            editable={editable}
          />
        </View>
      )
    }

    return (
      <View style={themed($inputContainer)}>
        {(label.toLowerCase() === "date of birth" || label.toLowerCase() === "email" || label.toLowerCase() === "username") && (
          <Icon
            icon={getFieldIcon()}
            size={18}
            color="#545454"
            style={themed($inputIcon)}
          />
        )}
        <TextInput
          style={themed([
            $input,
            !editable && $readOnlyInput
          ])}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder || `Enter ${label.toLowerCase()}`}
          placeholderTextColor="#B4BDC4"
          keyboardType={keyboardType}
          editable={editable}
        />
      </View>
    )
  }

  return (
    <View style={themed($container)}>
      <View style={themed($fieldContainer)}>
        {renderContent()}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  marginBottom: 18,
}

const $fieldContainer: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 12,
  height: 60,
  justifyContent: "center",
  paddingHorizontal: 20,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 4,
}

const $inputContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $inputIcon: ViewStyle = {
  marginRight: 8,
}

const $input: TextStyle = {
  flex: 1,
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY
  fontSize: 14,
  lineHeight: 18,
  color: "#505050",
  paddingVertical: 0,
}

const $readOnlyInput: TextStyle = {
  color: "#888888",
  backgroundColor: "transparent",
}

const $fieldText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY
  fontSize: 14,
  lineHeight: 18,
  color: "#505050",
}

const $genderContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
}

const $phoneContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $flagContainer: ViewStyle = {
  width: 24,
  height: 16,
  marginRight: 8,
  justifyContent: "center",
  alignItems: "center",
}

const $flagText: TextStyle = {
  fontSize: 12,
}

const $phonePrefix: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY
  fontSize: 14,
  lineHeight: 18,
  color: "#000000",
  marginRight: 8,
}

const $phoneInput: TextStyle = {
  flex: 1,
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY
  fontSize: 14,
  lineHeight: 18,
  color: "#000000",
  paddingVertical: 0,
}
