import React from "react"
import { View, ViewStyle, TouchableOpacity, Image, ImageStyle } from "react-native"
import { Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { EditProfileAvatarProps } from "../../types"

export function EditProfileAvatar({ avatar, onEditPress }: EditProfileAvatarProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Avatar Circle */}
      <View style={themed($avatarContainer)}>
        {avatar ? (
          <Image source={{ uri: avatar }} style={themed($avatarImage)} />
        ) : (
          <View style={themed($avatarPlaceholder)} />
        )}
      </View>

      {/* Edit Button */}
      <TouchableOpacity
        style={themed($editButton)}
        onPress={onEditPress}
        activeOpacity={0.8}
      >
        <View style={themed($editButtonBackground)}>
          <Icon icon="camera" size={17} color="#167F71" />
        </View>
      </TouchableOpacity>
    </View>
  )
}

const $container: ViewStyle = {
  alignItems: "center",
  marginBottom: 40,
}

const $avatarContainer: ViewStyle = {
  width: 90,
  height: 90,
  borderRadius: 45,
  borderWidth: 3,
  borderColor: "#167F71",
  overflow: "hidden",
  backgroundColor: "#F5F9FF",
}

const $avatarImage: ImageStyle = {
  width: "100%",
  height: "100%",
}

const $avatarPlaceholder: ViewStyle = {
  width: "100%",
  height: "100%",
  backgroundColor: "#E8F1FF",
}

const $editButton: ViewStyle = {
  position: "absolute",
  bottom: 0,
  right: "50%",
  transform: [{ translateX: 16 }],
}

const $editButtonBackground: ViewStyle = {
  width: 32,
  height: 32,
  borderRadius: 8,
  backgroundColor: "#FFFFFF",
  borderWidth: 3,
  borderColor: "#167F71",
  justifyContent: "center",
  alignItems: "center",
}
