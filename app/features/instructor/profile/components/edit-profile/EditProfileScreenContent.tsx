import React, { useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import {
  EditProfileHeader,
  EditProfileAvatar,
  EditProfileForm,
  EditProfileUpdateButton
} from "./index"
import { EditProfileData } from "../../types"
import { GenderBottomSheet } from "./GenderBottomSheet"

interface EditProfileScreenContentProps {
  onBackPress?: () => void
  onAvatarEditPress?: () => void
  onUpdatePress?: (data: EditProfileData) => void
  initialData?: EditProfileData
  loading?: boolean
}

// Default instructor profile data
const defaultProfileData: EditProfileData = {
  fullName: "<PERSON>",
  username: "alex.instructor",
  dateOfBirth: "15/08/1985",
  email: "<EMAIL>",
  phoneNumber: "************",
  gender: "Male",
  role: "Instructor",
}

/**
 * InstructorEditProfileScreenContent - Instructor Edit Profile screen based on Figma design
 *
 * This component implements the same Edit Profile design from Figma as student:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4239
 *
 * Cloned from student EditProfileScreenContent but adapted for instructor workflow.
 * Maintains exact same design and UI components.
 *
 * Features:
 * - EditProfileHeader: "Edit Profile" title with back button
 * - EditProfileAvatar: Circular avatar with edit button
 * - EditProfileForm: 7 form fields with proper styling (instructor data)
 * - EditProfileUpdateButton: Blue update button with arrow icon
 */
export function EditProfileScreenContent({
  onBackPress,
  onAvatarEditPress,
  onUpdatePress,
  initialData = defaultProfileData,
  loading = false
}: EditProfileScreenContentProps) {
  const { themed } = useAppTheme()
  const [profileData, setProfileData] = useState<EditProfileData>(initialData)
  const [showGenderBottomSheet, setShowGenderBottomSheet] = useState(false)

  console.log("👨‍🏫 InstructorEditProfileScreenContent rendering...")

  const handleBackPress = () => {
    console.log("👨‍🏫 Back pressed")
    onBackPress?.()
  }

  const handleAvatarEditPress = () => {
    console.log("👨‍🏫 Avatar edit pressed")
    onAvatarEditPress?.()
  }

  const handleDataChange = (field: keyof EditProfileData, value: string) => {
    console.log("👨‍🏫 Field changed:", field, "->", value)
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleUpdatePress = () => {
    console.log("👨‍🏫 Update instructor profile with data:", profileData)
    onUpdatePress?.(profileData)
  }

  const handleGenderPress = () => {
    console.log("👨‍🏫 Gender field pressed")
    setShowGenderBottomSheet(true)
  }

  const handleGenderSelect = (gender: "Male" | "Female") => {
    console.log("👨‍🏫 Gender selected:", gender)
    handleDataChange("gender", gender)
  }

  const handleCloseGenderBottomSheet = () => {
    setShowGenderBottomSheet(false)
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#FFFFFF"
      contentContainerStyle={{
        flexGrow: 1,
        paddingBottom: 100, // Extra space for bottom navigation - same as student
      }}
    >
      <View style={themed($content)}>
        {/* Header with back button */}
        <EditProfileHeader onBackPress={handleBackPress} />
        
        {/* Avatar with edit button */}
        <EditProfileAvatar 
          avatar={profileData.avatar}
          onEditPress={handleAvatarEditPress}
        />
        
        {/* Form fields */}
        <EditProfileForm
          data={profileData}
          onDataChange={handleDataChange}
          onGenderPress={handleGenderPress}
        />
        
        {/* Update button */}
        <EditProfileUpdateButton onPress={handleUpdatePress} loading={loading} />
      </View>

      {/* Gender Bottom Sheet */}
      <GenderBottomSheet
        visible={showGenderBottomSheet}
        onClose={handleCloseGenderBottomSheet}
        onSelectGender={handleGenderSelect}
        selectedGender={profileData.gender as "Male" | "Female"}
      />
    </Screen>
  )
}

const $content: ViewStyle = {
  // Content container - no flex to allow natural height
}
