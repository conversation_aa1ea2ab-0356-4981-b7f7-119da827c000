import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface AddNewCardButtonProps {
  onPress?: () => void
}

export function AddNewCardButton({ onPress }: AddNewCardButtonProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <TouchableOpacity style={themed($button)} onPress={onPress}>
        {/* Button background */}
        <View style={themed($buttonBackground)} />
        
        {/* Arrow icon in circle */}
        <View style={themed($iconContainer)}>
          <View style={themed($iconCircle)}>
            <Icon icon="caretRight" size={21} color="#0961F5" />
          </View>
        </View>
        
        {/* Button text */}
        <Text style={themed($buttonText)}>Add New Card</Text>
      </TouchableOpacity>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 39,
  marginTop: 40,
  marginBottom: 40,
}

const $button: ViewStyle = {
  position: "relative",
  height: 60,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
}

const $buttonBackground: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "#0961F5",
  borderRadius: 30,
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8,
}

const $iconContainer: ViewStyle = {
  position: "absolute",
  right: 6,
  top: 6,
  bottom: 6,
  width: 48,
  justifyContent: "center",
  alignItems: "center",
}

const $iconCircle: ViewStyle = {
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  justifyContent: "center",
  alignItems: "center",
}

const $buttonText: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight
  fontSize: 18,
  lineHeight: 26,
  color: "#FFFFFF",
  textAlign: "center",
}
