import React from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"

interface AddNewCardHeaderProps {
  onBackPress?: () => void
}

export function AddNewCardHeader({ onBackPress }: AddNewCardHeaderProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <TouchableOpacity 
        style={themed($backButton)}
        onPress={onBackPress}
      >
        <Icon icon="back" size={26} color="#202244" />
      </TouchableOpacity>
      
      <Text style={themed($title)}>Add New Card</Text>
    </View>
  )
}

const $container: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35,
  paddingTop: 25,
  paddingBottom: 20,
}

const $backButton: ViewStyle = {
  marginRight: 12,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight
  fontSize: 21,
  lineHeight: 30,
  color: "#202244",
}
