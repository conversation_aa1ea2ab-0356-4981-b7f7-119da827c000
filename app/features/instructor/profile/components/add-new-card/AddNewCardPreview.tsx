import React from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { AddNewCardData } from "../../types"
import { LinearGradient } from "expo-linear-gradient"

interface AddNewCardPreviewProps {
  cardData: AddNewCardData
}

export function AddNewCardPreview({ cardData }: AddNewCardPreviewProps) {
  const { themed } = useAppTheme()

  // Format card number for display
  const formatCardNumber = (number: string) => {
    if (!number) return "1234  5678  8765  0876"
    
    // Remove all non-digits
    const digits = number.replace(/\D/g, "")
    
    // Add spaces every 4 digits
    const formatted = digits.replace(/(\d{4})(?=\d)/g, "$1  ")
    
    // If less than 16 digits, pad with placeholder
    if (digits.length < 16) {
      const remaining = 16 - digits.length
      const placeholder = "0876".substring(0, remaining)
      return formatted + (formatted ? "  " : "") + placeholder
    }
    
    return formatted
  }

  // Format expiry date
  const formatExpiryDate = (date: string) => {
    if (!date) return "12/28"
    
    // Remove all non-digits
    const digits = date.replace(/\D/g, "")
    
    // Add slash after 2 digits
    if (digits.length >= 2) {
      return digits.substring(0, 2) + "/" + digits.substring(2, 4)
    }
    
    return digits
  }

  return (
    <View style={themed($container)}>
      <LinearGradient
        colors={["#0961F5", "#2E78F4"]}
        style={themed($card)}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {/* Card chip icon */}
        <View style={themed($chipContainer)}>
          <Icon icon="settings" size={28} color="#FFFFFF" />
        </View>

        {/* Card number */}
        <Text style={themed($cardNumber)}>
          {formatCardNumber(cardData.cardNumber)}
        </Text>

        {/* Valid Thru and Expiry Date */}
        <View style={themed($expiryContainer)}>
          <View style={themed($validThruContainer)}>
            <Text style={themed($validThruLabel)}>Valid</Text>
            <Text style={themed($validThruLabel)}>Thru</Text>
          </View>
          <Text style={themed($expiryDate)}>
            {formatExpiryDate(cardData.expiryDate)}
          </Text>
        </View>

        {/* Cardholder name */}
        <Text style={themed($cardholderName)}>
          {cardData.cardName || "Alex"}
        </Text>
      </LinearGradient>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
  marginTop: 20,
  marginBottom: 30,
}

const $card: ViewStyle = {
  width: 360,
  height: 180,
  borderRadius: 20,
  padding: 26,
  justifyContent: "space-between",
}

const $chipContainer: ViewStyle = {
  alignSelf: "flex-start",
}

const $cardNumber: TextStyle = {
  fontFamily: "mulishExtraBold", // 800 weight
  fontSize: 17,
  lineHeight: 21,
  color: "#FFFFFF",
  letterSpacing: 2.125, // 12.5% of 17px
  marginTop: 20,
}

const $expiryContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "flex-end",
  marginTop: 10,
}

const $validThruContainer: ViewStyle = {
  marginRight: 30,
}

const $validThruLabel: TextStyle = {
  fontFamily: "mulishExtraBold", // 800 weight
  fontSize: 8,
  lineHeight: 8,
  color: "#FFFFFF",
  textTransform: "uppercase",
}

const $expiryDate: TextStyle = {
  fontFamily: "mulishBold", // 700 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#FFFFFF",
  letterSpacing: 0.6, // 4.285714% of 14px
}

const $cardholderName: TextStyle = {
  fontFamily: "mulishExtraBold", // 800 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#FFFFFF",
  letterSpacing: 1.2, // 8.571429% of 14px
  textTransform: "uppercase",
  marginTop: 12,
}
