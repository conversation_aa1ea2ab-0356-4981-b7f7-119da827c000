import React from "react"
import { View, ViewStyle, TextStyle, TextInput } from "react-native"
import { Text } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { AddNewCardData } from "../../types"

interface AddNewCardFormProps {
  cardData: AddNewCardData
  onDataChange: (field: keyof AddNewCardData, value: string) => void
}

export function AddNewCardForm({ cardData, onDataChange }: AddNewCardFormProps) {
  const { themed } = useAppTheme()

  const handleCardNameChange = (value: string) => {
    onDataChange("cardName", value)
  }

  const handleCardNumberChange = (value: string) => {
    // Remove all non-digits and limit to 16 digits
    const digits = value.replace(/\D/g, "").substring(0, 16)
    onDataChange("cardNumber", digits)
  }

  const handleExpiryDateChange = (value: string) => {
    // Remove all non-digits and limit to 4 digits (MMYY)
    const digits = value.replace(/\D/g, "").substring(0, 4)
    onDataChange("expiryDate", digits)
  }

  const handleCvvChange = (value: string) => {
    // Remove all non-digits and limit to 3 digits
    const digits = value.replace(/\D/g, "").substring(0, 3)
    onDataChange("cvv", digits)
  }

  // Format card number for display in input
  const formatCardNumberForInput = (number: string) => {
    const digits = number.replace(/\D/g, "")
    return digits.replace(/(\d{4})(?=\d)/g, "$1  ")
  }

  // Format expiry date for display in input
  const formatExpiryForInput = (date: string) => {
    const digits = date.replace(/\D/g, "")
    if (digits.length >= 2) {
      return digits.substring(0, 2) + "/" + digits.substring(2, 4)
    }
    return digits
  }

  return (
    <View style={themed($container)}>
      {/* Card Name Field */}
      <View style={themed($fieldContainer)}>
        <Text style={themed($label)}>Card Name*</Text>
        <View style={themed($inputContainer)}>
          <TextInput
            style={themed($input)}
            value={cardData.cardName}
            onChangeText={handleCardNameChange}
            placeholder="alexia"
            placeholderTextColor="#000000"
          />
        </View>
      </View>

      {/* Card Number Field */}
      <View style={themed($fieldContainer)}>
        <Text style={themed($label)}>Card Number*</Text>
        <View style={themed($inputContainer)}>
          <TextInput
            style={themed($input)}
            value={formatCardNumberForInput(cardData.cardNumber)}
            onChangeText={handleCardNumberChange}
            placeholder="****  **65  8765  3456"
            placeholderTextColor="#000000"
            keyboardType="numeric"
            maxLength={19} // 16 digits + 3 spaces
          />
        </View>
      </View>

      {/* Expiry Date and CVV Row */}
      <View style={themed($rowContainer)}>
        {/* Expiry Date Field */}
        <View style={themed($halfFieldContainer)}>
          <Text style={themed($label)}>Expiry Date*</Text>
          <View style={themed($halfInputContainer)}>
            <TextInput
              style={themed($input)}
              value={formatExpiryForInput(cardData.expiryDate)}
              onChangeText={handleExpiryDateChange}
              placeholder="12/28"
              placeholderTextColor="#000000"
              keyboardType="numeric"
              maxLength={5} // MM/YY
            />
          </View>
        </View>

        {/* CVV Field */}
        <View style={themed($halfFieldContainer)}>
          <Text style={themed($label)}>CVV*</Text>
          <View style={themed($halfInputContainer)}>
            <TextInput
              style={themed($input)}
              value={cardData.cvv}
              onChangeText={handleCvvChange}
              placeholder="***"
              placeholderTextColor="#000000"
              keyboardType="numeric"
              maxLength={3}
              secureTextEntry
            />
          </View>
        </View>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
}

const $fieldContainer: ViewStyle = {
  marginBottom: 20,
}

const $halfFieldContainer: ViewStyle = {
  flex: 1,
}

const $rowContainer: ViewStyle = {
  flexDirection: "row",
  gap: 20,
  marginBottom: 20,
}

const $label: TextStyle = {
  fontFamily: "mulishExtraBold", // 800 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
  marginBottom: 8,
  marginLeft: 20,
}

const $inputContainer: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 12,
  height: 60,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 4,
}

const $halfInputContainer: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 12,
  height: 60,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 4,
}

const $input: TextStyle = {
  flex: 1,
  paddingHorizontal: 20,
  paddingVertical: 21,
  fontFamily: "mulishBold", // 700 weight
  fontSize: 14,
  lineHeight: 18,
  color: "#000000",
}
