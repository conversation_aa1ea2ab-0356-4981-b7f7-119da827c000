import React, { useState } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { useAppTheme } from "@/utils/useAppTheme"
import { AddNewCardHeader } from "./AddNewCardHeader"
import { AddNewCardPreview } from "./AddNewCardPreview"
import { AddNewCardForm } from "./AddNewCardForm"
import { AddNewCardButton } from "./AddNewCardButton"
import { AddNewCardData } from "../../types"

interface AddNewCardScreenContentProps {
  onBackPress?: () => void
  onAddCard?: (cardData: AddNewCardData) => void
}

// Default card data
const defaultCardData: AddNewCardData = {
  cardName: "",
  cardNumber: "",
  expiryDate: "",
  cvv: "",
}

/**
 * AddNewCardScreenContent - Add New Card screen based on Figma design
 * 
 * This component implements the Add New Card design from Figma:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4398
 * 
 * Features:
 * - AddNewCardHeader: "Add New Card" title with back button
 * - AddNewCardPreview: Credit card preview with blue gradient background
 * - AddNewCardForm: 4 form fields (Card Name, Card Number, Expiry Date, CVV)
 * - AddNewCardButton: Blue button with arrow icon
 * - Proper background color (#F5F9FF)
 * - Consistent spacing and styling
 */
export function AddNewCardScreenContent({
  onBackPress,
  onAddCard
}: AddNewCardScreenContentProps) {
  const { themed } = useAppTheme()
  const [cardData, setCardData] = useState<AddNewCardData>(defaultCardData)

  const handleBackPress = () => {
    console.log("💳 AddNewCardScreenContent back pressed")
    onBackPress?.()
  }

  const handleDataChange = (field: keyof AddNewCardData, value: string) => {
    console.log(`💳 AddNewCardScreenContent ${field} changed:`, value)
    setCardData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleAddCard = () => {
    console.log("💳 AddNewCardScreenContent add card pressed:", cardData)
    
    // Validate required fields
    if (!cardData.cardName.trim()) {
      console.log("💳 Card name is required")
      return
    }
    
    if (!cardData.cardNumber.trim()) {
      console.log("💳 Card number is required")
      return
    }
    
    if (!cardData.expiryDate.trim()) {
      console.log("💳 Expiry date is required")
      return
    }
    
    if (!cardData.cvv.trim()) {
      console.log("💳 CVV is required")
      return
    }

    onAddCard?.(cardData)
  }

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      style={themed($screen)}
    >
      <View style={themed($content)}>
        {/* Header with back button */}
        <AddNewCardHeader onBackPress={handleBackPress} />
        
        {/* Credit card preview */}
        <AddNewCardPreview cardData={cardData} />
        
        {/* Form fields */}
        <AddNewCardForm
          cardData={cardData}
          onDataChange={handleDataChange}
        />
        
        {/* Add card button */}
        <AddNewCardButton onPress={handleAddCard} />
      </View>
    </Screen>
  )
}

const $screen: ViewStyle = {
  flex: 1,
}

const $content: ViewStyle = {
  flex: 1,
}
