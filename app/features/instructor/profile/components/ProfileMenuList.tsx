import React from "react"
import { View, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { ProfileMenuItem } from "./ProfileMenuItem"
import { ProfileMenuItem as ProfileMenuItemType } from "../types"

interface ProfileMenuListProps {
  menuItems: ProfileMenuItemType[]
}

export function ProfileMenuList({ menuItems }: ProfileMenuListProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <View style={themed($menuContainer)}>
        {menuItems.map((item, index) => (
          <ProfileMenuItem 
            key={item.id} 
            item={item}
          />
        ))}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
  marginTop: 30,
  marginBottom: 100, // Increased to avoid bottom navigation overlap
  paddingBottom: 20, // Additional padding for better spacing
}

const $menuContainer: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  overflow: "hidden",
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4,
}
