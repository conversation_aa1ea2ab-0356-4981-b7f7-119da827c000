import React from "react"
import { NotificationScreenContent } from "../components/notification-settings"
import { NotificationScreenProps, NotificationSettings } from "../types"

/**
 * InstructorNotificationScreen - Instructor's notification settings screen
 *
 * This screen implements the same Notification Settings design from Figma as student:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4323
 *
 * Cloned from student NotificationScreen but adapted for instructor workflow.
 * Maintains exact same design and UI components.
 *
 * Features:
 * - NotificationHeader: "Notification" title with back button
 * - NotificationSettingsList: Toggle switches for all notification types
 * - Proper background color and spacing
 */
export function InstructorNotificationScreen({ navigation, route }: NotificationScreenProps) {
  console.log("👨‍🏫🔔 InstructorNotificationScreen rendering...")

  const initialSettings = route?.params?.settings

  const handleBackPress = () => {
    console.log("👨‍🏫🔔 Navigate back")
    if (navigation) {
      navigation.goBack()
    }
  }

  return (
    <NotificationScreenContent
      onBackPress={handleBackPress}
      initialSettings={initialSettings}
    />
  )
}
