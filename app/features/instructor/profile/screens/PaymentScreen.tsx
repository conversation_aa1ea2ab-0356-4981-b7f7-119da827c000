import React from "react"
import { PaymentScreenContent } from "../components/payment-options"
import { PaymentScreenProps, PaymentCardData } from "../types"

/**
 * InstructorPaymentScreen - Instructor's payment & earnings screen
 *
 * This screen implements the same Payment Options design from Figma as student:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4369
 *
 * Cloned from student PaymentScreen but adapted for instructor workflow.
 * Maintains exact same design and UI components.
 *
 * Features:
 * - PaymentHeader: "Payment & Earnings" title with back button
 * - PaymentCardsList: List of connected payment cards for earnings
 * - AddNewCardButton: Button to add new payment method
 * - Proper background color and spacing
 */
export function InstructorPaymentScreen({ navigation, route }: PaymentScreenProps) {
  console.log("👨‍🏫💳 InstructorPaymentScreen rendering...")

  const initialCards = route?.params?.cards

  const handleBackPress = () => {
    console.log("👨‍🏫💳 Navigate back")
    if (navigation) {
      navigation.goBack()
    }
  }

  const handleAddNewCard = () => {
    console.log("👨‍🏫💳 Navigate to Add New Card")
    if (navigation) {
      navigation.navigate("AddNewCard", {
        onCardAdded: (cardData: any) => {
          console.log("👨‍🏫💳 New card added:", cardData)
          // TODO: Update cards list or refresh data
        }
      })
    }
  }

  return (
    <PaymentScreenContent
      onBackPress={handleBackPress}
      onAddNewCard={handleAddNewCard}
      initialCards={initialCards}
    />
  )
}
