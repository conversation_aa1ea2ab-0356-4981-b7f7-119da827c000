import React from "react"
import { LanguageScreenContent } from "../components/language"
import { LanguageScreenProps, LanguageOption } from "../types/language"

/**
 * LanguageScreen - Student's language settings screen
 * 
 * This screen implements the Language design from Figma:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4481
 * 
 * Features:
 * - LanguageHeader: "Language" title with back button
 * - SubCategories section: English (US) and English (UK) options
 * - All Language section: Complete list of 12 languages with selection
 * - Language selection with visual feedback (green background + check icon)
 */
export function LanguageScreen({ navigation, route }: LanguageScreenProps) {
  console.log("🌐 LanguageScreen rendering...")

  const initialSettings = route?.params?.settings

  const handleBackPress = () => {
    console.log("🌐 Navigate back")
    if (navigation) {
      navigation.goBack()
    }
  }

  const handleLanguageChange = (language: LanguageOption) => {
    console.log("🌐 Language changed to:", language.name, language.code)
    // TODO: Implement language change API call or local storage
    // TODO: Update app language settings
    // For now, just log the change
  }

  return (
    <LanguageScreenContent
      onBackPress={handleBackPress}
      onLanguageChange={handleLanguageChange}
      initialSettings={initialSettings}
    />
  )
}
