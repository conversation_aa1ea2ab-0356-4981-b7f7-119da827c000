import React from "react"
import { EditProfileScreenContent } from "../components/edit-profile"
import { EditProfileScreenProps, EditProfileData } from "../types"
import { useInstructorEditProfile } from "../hooks/useInstructorProfile"
import { SuccessModal } from "@/components"
import { ErrorModal } from "@/components/ErrorModal"

/**
 * InstructorEditProfileScreen - Instructor's edit profile screen
 *
 * This screen implements the same Edit Profile design from Figma as student:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4239
 *
 * Cloned from student EditProfileScreen but adapted for instructor workflow.
 * Maintains exact same design and UI components.
 *
 * Features:
 * - EditProfileHeader: "Edit Profile" title with back button
 * - EditProfileAvatar: Circular avatar with edit button
 * - EditProfileForm: 7 form fields (Full Name, Nick Name, Date of Birth, Email, Phone, Gender, Role)
 * - EditProfileUpdateButton: Blue update button with arrow icon
 */
export function InstructorEditProfileScreen({ navigation, route }: EditProfileScreenProps) {
  console.log("👨‍🏫 InstructorEditProfileScreen rendering...")

  const initialData = route?.params?.profileData
  const onUpdateSuccessCallback = route?.params?.onUpdateSuccess

  // Use instructor edit profile hook
  const { updateProfile, loading, successModal, errorModal, hideErrorModal } = useInstructorEditProfile({
    onUpdateSuccess: () => {
      console.log("👨‍🏫 Profile update successful, navigating back")
      // Call the callback to refresh profile data
      if (onUpdateSuccessCallback) {
        console.log("👨‍🏫 Calling onUpdateSuccess callback")
        onUpdateSuccessCallback()
      }
      if (navigation) {
        navigation.goBack()
      }
    },
    onUpdateError: (error) => {
      console.log("👨‍🏫 Profile update error:", error)
      // Error is already shown in Alert by the hook
    },
    originalProfileData: initialData
  })

  const handleBackPress = () => {
    console.log("👨‍🏫 Navigate back")
    if (navigation) {
      navigation.goBack()
    }
  }

  const handleAvatarEditPress = () => {
    console.log("👨‍🏫 Open avatar picker")
    // TODO: Implement image picker
    // navigation?.navigate("ImagePicker")
  }

  const handleUpdatePress = async (data: EditProfileData) => {
    console.log("👨‍🏫 Update instructor profile with data:", data)

    // Call the update profile API
    const success = await updateProfile(data)

    if (success) {
      console.log("👨‍🏫 Profile updated successfully")
      // Navigation is handled in onUpdateSuccess callback
    } else {
      console.log("👨‍🏫 Profile update failed")
      // Error handling is done in the hook
    }
  }

  return (
    <>
      <EditProfileScreenContent
        onBackPress={handleBackPress}
        onAvatarEditPress={handleAvatarEditPress}
        onUpdatePress={handleUpdatePress}
        initialData={initialData}
        loading={loading}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={successModal.isVisible}
        onClose={successModal.hideSuccess}
        title={successModal.config.title}
        message={successModal.config.message}
        buttonText={successModal.config.buttonText}
        iconSource={successModal.config.iconSource}
        autoDismiss={successModal.config.autoDismiss}
        autoDismissDelay={successModal.config.autoDismissDelay}
        onButtonPress={successModal.config.onButtonPress}
      />

      {/* Error Modal */}
      <ErrorModal
        visible={errorModal.visible}
        title={errorModal.title}
        message={errorModal.message}
        onClose={hideErrorModal}
        iconSource={require("../../../../../assets/icons/congratulations.png")}
      />
    </>
  )
}
