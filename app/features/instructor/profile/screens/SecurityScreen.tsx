import React from "react"
import { SecurityScreenContent } from "../components/security"
import { SecurityScreenProps, SecuritySettings } from "../types"

/**
 * SecurityScreen - Instructor's security settings screen
 *
 * This screen implements the Security design from Figma:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4447
 *
 * Features:
 * - SecurityHeader: "Security" title with back button
 * - SecuritySettingsList: Toggle switches and Google Authenticator
 * - SecurityButtons: Change PIN and Change Password buttons
 * - Proper background color and spacing
 */
export function SecurityScreen({ navigation, route }: SecurityScreenProps) {
  console.log("🔒 SecurityScreen rendering...")

  const initialSettings = route?.params?.settings

  const handleBackPress = () => {
    console.log("🔒 Navigate back")
    if (navigation) {
      navigation.goBack()
    }
  }

  return (
    <SecurityScreenContent
      onBackPress={handleBackPress}
      initialSettings={initialSettings}
    />
  )
}
