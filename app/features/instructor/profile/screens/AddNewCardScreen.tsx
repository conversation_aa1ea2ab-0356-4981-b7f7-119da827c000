import React from "react"
import { AddNewCardScreenContent } from "../components/add-new-card"
import { AddNewCardScreenProps, AddNewCardData } from "../types"

/**
 * AddNewCardScreen - Student's add new card screen
 * 
 * This screen implements the Add New Card design from Figma:
 * https://www.figma.com/design/1CyxEsss51d7eEXFOCZOj2/lms-app?node-id=1-4398
 * 
 * Features:
 * - AddNewCardHeader: "Add New Card" title with back button
 * - Credit card preview with blue gradient background
 * - Form fields: Card Name, Card Number, Expiry Date, CVV
 * - Add New Card button with blue background and arrow icon
 * - Proper background color and spacing
 */
export function AddNewCardScreen({ navigation, route }: AddNewCardScreenProps) {
  console.log("💳 AddNewCardScreen rendering...")

  const onCardAdded = route?.params?.onCardAdded

  const handleBackPress = () => {
    console.log("💳 Navigate back")
    if (navigation) {
      navigation.goBack()
    }
  }

  const handleAddCard = (cardData: AddNewCardData) => {
    console.log("💳 Adding new card:", cardData)
    
    // Call the callback if provided
    onCardAdded?.(cardData)
    
    // Navigate back to payment options
    if (navigation) {
      navigation.goBack()
    }
  }

  return (
    <AddNewCardScreenContent
      onBackPress={handleBackPress}
      onAddCard={handleAddCard}
    />
  )
}
