import React from "react"
import { Screen } from "@/components"
import { ErrorModal } from "@/components/ErrorModal"
import { useNavigation } from "@react-navigation/native"
import {
  ProfileHeader,
  ProfileCard,
  ProfileMenuList,
  LogoutConfirmDialog
} from "../components"
import { useProfile } from "../hooks"
import { useInstructorProfile, transformInstructorProfileToEditData } from "../hooks/useInstructorProfile"

export interface ProfileScreenProps {
  /**
   * Navigation prop for screen navigation
   */
  navigation?: any
}

/**
 * InstructorProfileScreen - Instructor profile screen
 *
 * This screen follows the same pattern as student ProfileScreen:
 * - useProfile: Manages profile data and menu items (adapted for instructor)
 * - ProfileHeader: Header component with back navigation
 * - ProfileCard: User info card with avatar and edit button (instructor data)
 * - ProfileMenuList: List of profile menu options (instructor-specific)
 *
 * Cloned from student ProfileScreen but adapted for instructor workflow.
 * Maintains exact same design and UI components.
 */
export function InstructorProfileScreen({ navigation: navProp }: ProfileScreenProps) {
  console.log("👨‍🏫 InstructorProfileScreen rendering...")

  // Use navigation hook to access parent stack navigator
  const navigation = useNavigation<any>()

  // Load instructor profile from API
  const {
    profileData: apiProfileData,
    loading: apiLoading,
    refreshProfile,
    errorModal,
    hideErrorModal
  } = useInstructorProfile({
    onLoadSuccess: (profile) => {
      console.log("👨‍🏫 API profile loaded successfully:", profile.name)
    },
    onLoadError: (error) => {
      console.log("👨‍🏫 API profile load error:", error)
    }
  })

  const handleBackPress = () => {
    // Handle back navigation
    console.log("👨‍🏫 Instructor profile back pressed")
    if (navProp) {
      navProp.goBack()
    } else if (navigation) {
      navigation.goBack()
    }
  }

  const handleEditProfile = () => {
    // Navigate to edit profile screen within ProfileStack
    console.log("👤 Navigate to edit profile")
    console.log("👤 Navigation object:", navigation)

    try {
      console.log("👨‍🏫 Attempting navigation to InstructorEditProfile...")

      // Transform API profile data to edit format if available
      let editProfileData = undefined
      if (apiProfileData) {
        editProfileData = transformInstructorProfileToEditData(apiProfileData)
        console.log("👨‍🏫 Transformed profile data:", editProfileData)
      }

      navigation.navigate("InstructorEditProfile", {
        profileData: editProfileData,
        onUpdateSuccess: refreshProfile // Refresh profile after update
      })
      console.log("👨‍🏫 Navigation call completed")
    } catch (error) {
      console.log("👤 Navigation error:", error)
      console.error("👤 Full error:", error)
    }
  }

  const handleNotificationPress = () => {
    // Navigate to instructor notification settings screen
    console.log("👨‍🏫🔔 Navigate to notification settings")
    console.log("👨‍🏫🔔 Navigation object:", navigation)

    try {
      console.log("👨‍🏫🔔 Attempting navigation to InstructorNotificationSettings...")
      navigation.navigate("InstructorNotificationSettings")
      console.log("👨‍🏫🔔 Navigation call completed")
    } catch (error) {
      console.log("👨‍🏫🔔 Navigation error:", error)
      console.error("👨‍🏫🔔 Full error:", error)
    }
  }

  const handlePaymentPress = () => {
    // Navigate to instructor payment & earnings screen
    console.log("👨‍🏫💳 Navigate to payment & earnings")
    console.log("👨‍🏫💳 Navigation object:", navigation)

    try {
      console.log("👨‍🏫💳 Attempting navigation to InstructorPaymentOptions...")
      navigation.navigate("InstructorPaymentOptions")
      console.log("👨‍🏫💳 Navigation call completed")
    } catch (error) {
      console.log("👨‍🏫💳 Navigation error:", error)
      console.error("👨‍🏫💳 Full error:", error)
    }
  }

  const handleSecurityPress = () => {
    // Navigate to security screen
    console.log("🔒 Navigate to security")
    console.log("🔒 Navigation object:", navigation)

    try {
      console.log("🔒 Attempting navigation to Security...")
      navigation.navigate("Security")
      console.log("🔒 Navigation call completed")
    } catch (error) {
      console.log("🔒 Navigation error:", error)
      console.error("🔒 Full error:", error)
    }
  }

  const handleLanguagePress = () => {
    // Navigate to language settings screen
    console.log("🌐 Navigate to language settings")
    console.log("🌐 Navigation object:", navigation)

    try {
      console.log("🌐 Attempting navigation to Language...")
      navigation.navigate("Language")
      console.log("🌐 Navigation call completed")
    } catch (error) {
      console.log("🌐 Navigation error:", error)
      console.error("🌐 Full error:", error)
    }
  }

  // Get profile data from custom hook
  const {
    profileData: mockProfileData,
    loading,
    menuItems,
    showLogoutDialog,
    handleLogoutConfirm,
    handleLogoutCancel
  } = useProfile({
    onEditProfile: handleEditProfile,
    onNotificationPress: handleNotificationPress,
    onPaymentPress: handlePaymentPress,
    onSecurityPress: handleSecurityPress,
    onLanguagePress: handleLanguagePress
  })

  // Use API profile data if available, otherwise fallback to mock data
  const profileData = apiProfileData ? {
    id: apiProfileData.id.toString(),
    name: apiProfileData.name,
    email: apiProfileData.contact_info?.email || "",
    username: apiProfileData.user_name,
    avatar: undefined, // API doesn't provide avatar URL
    phone: apiProfileData.contact_info?.phone,
    bio: `${apiProfileData.employment_info?.position || "Instructor"} at VANTIS Learning Platform`,
    joinDate: apiProfileData.created_at ? new Date(apiProfileData.created_at).toISOString().split('T')[0] : undefined,
    coursesCompleted: apiProfileData.statistics?.total_courses || 0,
    totalHours: apiProfileData.statistics?.total_teaching_hours || 0,
  } : mockProfileData

  // Combine loading states
  const isLoading = loading || apiLoading

  console.log("👤 ProfileScreen data:", { profileData, loading: isLoading, menuItemsCount: menuItems.length })

  if (isLoading) {
    console.log("👤 ProfileScreen loading...")
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["top"]}
        backgroundColor="#F5F9FF"
      >
        <ProfileHeader onBackPress={handleBackPress} />
      </Screen>
    )
  }

  if (!profileData) {
    console.log("👤 ProfileScreen error: No profile data")
    return (
      <Screen
        preset="fixed"
        safeAreaEdges={["top"]}
        backgroundColor="#F5F9FF"
      >
        <ProfileHeader onBackPress={handleBackPress} />
      </Screen>
    )
  }

  console.log("👤 ProfileScreen rendering content with data:", profileData)

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor="#F5F9FF"
      contentContainerStyle={{
        flexGrow: 1,
        paddingBottom: 100, // Extra space for bottom navigation
      }}
    >
      <ProfileHeader onBackPress={handleBackPress} />

      <ProfileCard
        profileData={profileData}
        onEditPress={handleEditProfile}
      />

      <ProfileMenuList menuItems={menuItems} />

      {/* Logout Confirmation Dialog */}
      <LogoutConfirmDialog
        visible={showLogoutDialog}
        onConfirm={handleLogoutConfirm}
        onCancel={handleLogoutCancel}
      />

      {/* Error Modal */}
      <ErrorModal
        visible={errorModal.visible}
        title={errorModal.title}
        message={errorModal.message}
        onClose={hideErrorModal}
        iconSource={require("../../../../../assets/icons/congratulations.png")}
      />
    </Screen>
  )
}

