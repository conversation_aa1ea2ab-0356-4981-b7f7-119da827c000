import { useState, useEffect } from "react"
import { api } from "@/services/api"
import { InstructorProfile } from "@/services/api/instructorApi"
import { EditProfileData, ProfileMenuItem } from "../types"
import { useStores } from "@/models"
import { useSuccessModal } from "@/hooks/useSuccessModal"
import { testModalTiming } from "@/utils/testSuccessModal"

interface UseInstructorProfileProps {
  onLoadSuccess?: (profile: InstructorProfile) => void
  onLoadError?: (error: string) => void
  onEditProfile?: () => void
  onNotificationPress?: () => void
  onPaymentPress?: () => void
  onSecurityPress?: () => void
  onLanguagePress?: () => void
}

export function useInstructorProfile(props?: UseInstructorProfileProps) {
  console.log("👨‍🏫 useInstructorProfile hook initializing...")

  const { authenticationStore } = useStores()
  const [profileData, setProfileData] = useState<InstructorProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [errorModal, setErrorModal] = useState({
    visible: false,
    title: "Error",
    message: "",
  })

  const loadProfile = async () => {
    try {
      console.log("👨‍🏫 Loading instructor profile from API...")
      setLoading(true)
      setError(null)

      // Set authentication token
      const token = authenticationStore.authToken
      if (token) {
        api.instructorApi.setAuthToken(token)
        console.log("🔑 useInstructorProfile: Auth token set for API")
      } else {
        console.log("❌ useInstructorProfile: No auth token available")
        setError("Authentication required")
        setLoading(false)
        return
      }

      const result = await api.instructorApi.getProfile()

      if (result.kind === "ok") {
        console.log("✅ Instructor profile loaded successfully:", result.data.data)
        setProfileData(result.data.data)
        props?.onLoadSuccess?.(result.data.data)
      } else {
        console.log("❌ Failed to load instructor profile:", result.kind)

        let errorMessage = "Failed to load profile. Please try again."

        // Customize error message based on error type
        if (result.kind === "server") {
          errorMessage = "Server error occurred. The development team has been notified. Please try again later."
        } else if (result.kind === "unauthorized") {
          errorMessage = "Authentication expired. Please login again."
        } else if (result.kind === "timeout") {
          errorMessage = "Request timeout. Please check your internet connection and try again."
        }

        setError(errorMessage)
        props?.onLoadError?.(errorMessage)

        // Show custom error modal
        setErrorModal({
          visible: true,
          title: "Error",
          message: errorMessage,
        })
      }
    } catch (error) {
      console.error("💥 Error loading instructor profile:", error)
      const errorMessage = "An unexpected error occurred. Please try again."
      setError(errorMessage)
      props?.onLoadError?.(errorMessage)

      // Show custom error modal
      setErrorModal({
        visible: true,
        title: "Error",
        message: errorMessage,
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadProfile()
  }, [])

  const refreshProfile = () => {
    console.log("🔄 Refreshing instructor profile...")
    loadProfile()
  }

  const hideErrorModal = () => {
    setErrorModal(prev => ({ ...prev, visible: false }))
  }

  const [showLogoutDialog, setShowLogoutDialog] = useState(false)

  // Logout handlers
  const handleLogoutPress = () => {
    console.log("🚪 Logout button pressed - showing dialog")
    setShowLogoutDialog(true)
  }

  const handleLogoutConfirm = () => {
    console.log("🚪 Logout confirmed - logging out user")
    setShowLogoutDialog(false)
    authenticationStore.logout()
  }

  const handleLogoutCancel = () => {
    console.log("🚪 Logout cancelled")
    setShowLogoutDialog(false)
  }

  const getMenuItems = (): ProfileMenuItem[] => [
    {
      id: "edit-profile",
      title: "Edit Profile",
      icon: "settings",
      hasArrow: true,
      onPress: () => {
        console.log("Edit Profile pressed")
        props?.onEditProfile?.()
      },
    },
    {
      id: "payment-option",
      title: "Payment & Earnings",
      icon: "settings",
      hasArrow: true,
      onPress: () => {
        console.log("Payment & Earnings pressed")
        props?.onPaymentPress?.()
      },
    },
    {
      id: "notifications",
      title: "Notifications",
      icon: "bell",
      hasArrow: true,
      onPress: () => {
        console.log("Notifications pressed")
        if (props?.onNotificationPress) {
          props.onNotificationPress()
        }
      },
    },
    {
      id: "security",
      title: "Security",
      icon: "settings",
      hasArrow: true,
      onPress: () => {
        console.log("Security pressed")
        props?.onSecurityPress?.()
      },
    },
    {
      id: "language",
      title: "Language",
      icon: "components",
      hasArrow: true,
      value: "English (US)",
      onPress: () => {
        console.log("Language pressed")
        props?.onLanguagePress?.()
      },
    },
    {
      id: "dark-mode",
      title: "Dark Mode",
      icon: "view",
      hasArrow: true,
      onPress: () => console.log("Dark Mode pressed"),
    },
    {
      id: "terms",
      title: "Terms & Conditions",
      icon: "settings",
      hasArrow: true,
      onPress: () => console.log("Terms pressed"),
    },
    {
      id: "help",
      title: "Help Center",
      icon: "community",
      hasArrow: true,
      onPress: () => console.log("Help pressed"),
    },
    {
      id: "invite",
      title: "Invite Instructors",
      icon: "heart",
      hasArrow: true,
      onPress: () => console.log("Invite Instructors pressed"),
    },
    {
      id: "logout",
      title: "Logout",
      icon: "x",
      hasArrow: true,
      color: "#FF4444",
      onPress: handleLogoutPress,
    },
  ]

  const menuItems = getMenuItems()

  return {
    profileData,
    loading,
    error,
    refreshProfile,
    errorModal,
    hideErrorModal,
    menuItems,
    showLogoutDialog,
    handleLogoutConfirm,
    handleLogoutCancel,
  }
}

/**
 * Transform InstructorProfile to EditProfileData
 */
export function transformInstructorProfileToEditData(profile: InstructorProfile): EditProfileData {
  return {
    fullName: profile.name || "",
    username: profile.user_name || "",
    dateOfBirth: profile.birth_date || "",
    email: profile.contact_info?.email || "",
    phoneNumber: profile.contact_info?.phone || "",
    gender: profile.gender === "male" ? "Male" : profile.gender === "female" ? "Female" : "Other",
    role: profile.employment_info?.position || "",
    avatar: undefined, // API doesn't provide avatar URL in current response
  }
}

/**
 * Transform EditProfileData to InstructorProfile update payload
 */
export function transformEditDataToInstructorProfile(editData: EditProfileData): any {
  console.log("👨‍🏫 Converting edit profile data to API format:", editData)

  const apiData: any = {}

  // Map form fields to API fields
  if (editData.fullName) {
    apiData.name = editData.fullName
  }

  if (editData.dateOfBirth) {
    // Convert DD/MM/YYYY to YYYY-MM-DD format if needed
    if (editData.dateOfBirth.includes('/')) {
      const [day, month, year] = editData.dateOfBirth.split('/')
      if (day && month && year) {
        apiData.birth_date = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
      }
    } else {
      apiData.birth_date = editData.dateOfBirth
    }
  }

  if (editData.gender) {
    // Convert gender to lowercase as required by the API
    console.log("👨‍🏫 Original gender value:", editData.gender)
    apiData.gender = editData.gender.toLowerCase()
    console.log("👨‍🏫 Converted gender value:", apiData.gender)
  }

  // Contact info
  const contact_info: any = {}
  if (editData.email) {
    contact_info.email = editData.email
  }
  if (editData.phoneNumber) {
    // Remove country code prefix and format phone number
    const cleanPhone = editData.phoneNumber.replace(/^\(\+\d+\)\s*/, '').trim()
    contact_info.phone = cleanPhone
  }

  if (Object.keys(contact_info).length > 0) {
    apiData.contact_info = contact_info
  }

  console.log("👨‍🏫 Converted API data:", apiData)
  return apiData
}

interface UseInstructorEditProfileProps {
  onUpdateSuccess?: () => void
  onUpdateError?: (error: string) => void
  originalProfileData?: InstructorProfile
}

export function useInstructorEditProfile(props?: UseInstructorEditProfileProps) {
  console.log("👨‍🏫 useInstructorEditProfile hook initializing...")

  const { authenticationStore } = useStores()
  const [loading, setLoading] = useState(false)
  const successModal = useSuccessModal()
  const [errorModal, setErrorModal] = useState({
    visible: false,
    title: "Error",
    message: "",
  })

  const updateProfile = async (editData: EditProfileData): Promise<boolean> => {
    console.log("👨‍🏫 updateProfile called with data:", editData)

    if (!authenticationStore.authToken) {
      console.log("👨‍🏫 No auth token available")
      setErrorModal({
        visible: true,
        title: "Error",
        message: "Authentication required. Please login again.",
      })
      return false
    }

    setLoading(true)

    try {
      // Set auth token for API
      api.instructorApi.setAuthToken(authenticationStore.authToken)

      // Convert form data to API format
      const apiData = transformEditDataToInstructorProfile(editData)

      // Call update API
      console.log("👨‍🏫 Calling update profile API...")
      const result = await api.instructorApi.updateProfile(apiData)

      if (result.kind === "ok") {
        console.log("✅ Instructor profile updated successfully:", result.data)

        // Start timing test
        const timingTest = testModalTiming(10000)

        // Show modern success modal with auto-dismiss
        successModal.showSuccess({
          title: "Success",
          message: "Profile updated successfully!",
          buttonText: "OK",
          autoDismiss: true,
          autoDismissDelay: 10000, // 10 seconds
          onButtonPress: () => {
            // Finish timing test
            timingTest.finish()
            console.log("✅ Success modal dismissed after 10 seconds, navigating back")
            // Add a small delay to ensure modal animation completes
            setTimeout(() => {
              props?.onUpdateSuccess?.()
            }, 100)
          }
        })

        return true
      } else {
        console.log("❌ Failed to update instructor profile:", result.kind)

        let errorMessage = "Failed to update profile. Please try again."

        // Handle specific error types
        switch (result.kind) {
          case "unauthorized":
            errorMessage = "Authentication failed. Please login again."
            break
          case "bad-data":
            errorMessage = "Invalid data provided. Please check your inputs."
            break
          case "timeout":
            errorMessage = "Request timeout. Please check your connection and try again."
            break
          case "cannot-connect":
            errorMessage = "Cannot connect to server. Please check your internet connection."
            break
          default:
            errorMessage = "An unexpected error occurred. Please try again."
        }

        setErrorModal({
          visible: true,
          title: "Error",
          message: errorMessage,
        })
        props?.onUpdateError?.(errorMessage)
        return false
      }
    } catch (error) {
      console.error("👨‍🏫 Update profile error:", error)
      const errorMessage = "An unexpected error occurred. Please try again."
      setErrorModal({
        visible: true,
        title: "Error",
        message: errorMessage,
      })
      props?.onUpdateError?.(errorMessage)
      return false
    } finally {
      setLoading(false)
    }
  }

  const hideErrorModal = () => {
    setErrorModal(prev => ({ ...prev, visible: false }))
  }

  return {
    updateProfile,
    loading,
    successModal,
    errorModal,
    hideErrorModal,
  }
}
