import { useState, useEffect } from "react"
import { ProfileData, ProfileMenuItem } from "../types"
import { useStores } from "@/models"

interface UseProfileProps {
  onEditProfile?: () => void
  onNotificationPress?: () => void
  onPaymentPress?: () => void
  onSecurityPress?: () => void
  onLanguagePress?: () => void
}

export function useProfile(props?: UseProfileProps) {
  console.log("👨‍🏫 useInstructorProfile hook initializing...")

  const [profileData, setProfileData] = useState<ProfileData | null>(null)
  const [loading, setLoading] = useState(true)
  const [showLogoutDialog, setShowLogoutDialog] = useState(false)

  // Get authentication store for logout functionality
  const { authenticationStore } = useStores()

  // Mock profile data - replace with actual API call
  useEffect(() => {
    console.log("👤 useProfile loading profile data...")

    const loadProfile = async () => {
      try {
        console.log("👤 useProfile simulating API call...")
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))

        const mockProfile: ProfileData = {
          id: "1",
          name: "Alex",
          email: "<EMAIL>",
          avatar: undefined,
          phone: "****** 567 8900",
          bio: "Instructor at VANTIS Learning Platform",
          joinDate: "2023-08-15",
          coursesCompleted: 12,
          totalHours: 180,
        }

        console.log("👤 useProfile profile data loaded:", mockProfile)
        setProfileData(mockProfile)
      } catch (error) {
        console.error("👤 useProfile failed to load profile:", error)
      } finally {
        console.log("👤 useProfile loading complete")
        setLoading(false)
      }
    }

    loadProfile()
  }, [])

  // Logout handlers
  const handleLogoutPress = () => {
    console.log("🚪 Logout button pressed - showing dialog")
    setShowLogoutDialog(true)
  }

  const handleLogoutConfirm = () => {
    console.log("🚪 Logout confirmed - logging out user")
    setShowLogoutDialog(false)
    authenticationStore.logout()
  }

  const handleLogoutCancel = () => {
    console.log("🚪 Logout cancelled")
    setShowLogoutDialog(false)
  }

  const getMenuItems = (): ProfileMenuItem[] => [
    {
      id: "edit-profile",
      title: "Edit Profile",
      icon: "settings",
      hasArrow: true,
      onPress: () => {
        console.log("Edit Profile pressed")
        props?.onEditProfile?.()
      },
    },
    {
      id: "payment-option",
      title: "Payment & Earnings",
      icon: "settings",
      hasArrow: true,
      onPress: () => {
        console.log("Payment & Earnings pressed")
        props?.onPaymentPress?.()
      },
    },
    {
      id: "notifications",
      title: "Notifications",
      icon: "bell",
      hasArrow: true,
      onPress: () => {
        console.log("Notifications pressed")
        console.log("🔔 props:", props)
        console.log("🔔 onNotificationPress:", props?.onNotificationPress)
        if (props?.onNotificationPress) {
          console.log("🔔 Calling onNotificationPress...")
          props.onNotificationPress()
        } else {
          console.log("🔔 onNotificationPress not found!")
        }
      },
    },
    {
      id: "security",
      title: "Security",
      icon: "settings",
      hasArrow: true,
      onPress: () => {
        console.log("Security pressed")
        props?.onSecurityPress?.()
      },
    },
    {
      id: "language",
      title: "Language",
      icon: "components",
      hasArrow: true,
      value: "English (US)",
      onPress: () => {
        console.log("Language pressed")
        props?.onLanguagePress?.()
      },
    },
    {
      id: "dark-mode",
      title: "Dark Mode",
      icon: "view",
      hasArrow: true,
      onPress: () => console.log("Dark Mode pressed"),
    },
    {
      id: "terms",
      title: "Terms & Conditions",
      icon: "settings",
      hasArrow: true,
      onPress: () => console.log("Terms pressed"),
    },
    {
      id: "help",
      title: "Help Center",
      icon: "community",
      hasArrow: true,
      onPress: () => console.log("Help pressed"),
    },
    {
      id: "invite",
      title: "Invite Instructors",
      icon: "heart",
      hasArrow: true,
      onPress: () => console.log("Invite Instructors pressed"),
    },
    {
      id: "logout",
      title: "Logout",
      icon: "x",
      hasArrow: true,
      color: "#FF4444",
      onPress: handleLogoutPress,
    },
  ]

  const menuItems = getMenuItems()

  console.log("👤 useProfile returning data:", {
    profileData: profileData ? "loaded" : "null",
    loading,
    menuItemsCount: menuItems.length,
    showLogoutDialog
  })

  return {
    profileData,
    loading,
    menuItems,
    showLogoutDialog,
    handleLogoutConfirm,
    handleLogoutCancel,
  }
}
