/**
 * Language Settings Types
 *
 * Type definitions for the language settings feature.
 */

export interface LanguageOption {
  /**
   * Unique identifier for the language
   */
  id: string
  /**
   * Display name of the language
   */
  name: string
  /**
   * Language code (e.g., 'en-US', 'en-UK')
   */
  code: string
  /**
   * Whether this language is currently selected
   */
  isSelected: boolean
  /**
   * Category of the language (subcategory or all)
   */
  category: "subcategory" | "all"
}

export interface LanguageSettings {
  /**
   * Currently selected language
   */
  selectedLanguage: string
  /**
   * Available language options
   */
  availableLanguages: LanguageOption[]
}

export interface LanguageScreenProps {
  navigation?: any
  route?: {
    params?: {
      settings?: LanguageSettings
    }
  }
}

export interface LanguageItemProps {
  language: LanguageOption
  onPress: (language: LanguageOption) => void
}

export interface LanguageSectionProps {
  title: string
  languages: LanguageOption[]
  onLanguagePress: (language: LanguageOption) => void
}

export interface LanguageHeaderProps {
  onBackPress?: () => void
}
