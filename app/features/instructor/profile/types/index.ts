import { IconTypes } from "@/components/Icon"

// Profile types
export interface ProfileData {
  id: string
  name: string
  email: string
  username?: string
  avatar?: string
  phone?: string
  bio?: string
  joinDate?: string
  coursesCompleted?: number
  totalHours?: number
}

export interface ProfileMenuItem {
  id: string
  title: string
  icon: IconTypes
  hasArrow?: boolean
  value?: string
  color?: string
  onPress: () => void
}

export interface ProfileScreenProps {
  // Add any props needed for the profile screen
}

// Notification Settings types
export interface NotificationSettings {
  specialOffers: boolean
  sound: boolean
  vibrate: boolean
  generalNotification: boolean
  promoDiscount: boolean
  paymentOptions: boolean
  appUpdate: boolean
  newServiceAvailable: boolean
  newTipsAvailable: boolean
}

export interface NotificationScreenProps {
  navigation?: any
  route?: {
    params?: {
      settings?: NotificationSettings
    }
  }
}

// Payment Options types
export interface PaymentCardData {
  id: string
  cardNumber?: string
  isConnected: boolean
  cardType?: "visa" | "mastercard" | "amex" | "discover"
  expiryDate?: string
}

export interface PaymentScreenProps {
  navigation?: any
  route?: {
    params?: {
      cards?: PaymentCardData[]
    }
  }
}

// Add New Card types
export interface AddNewCardData {
  cardName: string
  cardNumber: string
  expiryDate: string
  cvv: string
}

export interface AddNewCardScreenProps {
  navigation?: any
  route?: {
    params?: {
      onCardAdded?: (cardData: AddNewCardData) => void
    }
  }
}

// Security types
export interface SecuritySettings {
  rememberMe: boolean
  biometricId: boolean
  faceId: boolean
}

export interface SecurityScreenProps {
  navigation?: any
  route?: {
    params?: {
      settings?: SecuritySettings
    }
  }
}

// Edit Profile types
export type {
  EditProfileData,
  EditProfileScreenProps,
  EditProfileFormFieldProps,
  EditProfileAvatarProps
} from "./edit-profile"

// Language Settings types
export type {
  LanguageOption,
  LanguageSettings,
  LanguageScreenProps,
  LanguageItemProps,
  LanguageSectionProps,
  LanguageHeaderProps
} from "./language"
