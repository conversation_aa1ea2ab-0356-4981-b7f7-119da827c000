/**
 * Edit Profile Types
 *
 * Type definitions for the edit profile feature.
 */

export interface EditProfileData {
  /**
   * User's full name
   */
  fullName: string
  /**
   * User's username
   */
  username: string
  /**
   * User's date of birth
   */
  dateOfBirth: string
  /**
   * User's email address
   */
  email: string
  /**
   * User's phone number
   */
  phoneNumber: string
  /**
   * User's gender
   */
  gender: "Male" | "Female" | "Other"
  /**
   * User's role/occupation
   */
  role: string
  /**
   * User's avatar image URL
   */
  avatar?: string
}

export interface EditProfileScreenProps {
  navigation?: any
  route?: {
    params?: {
      profileData?: EditProfileData
      onUpdateSuccess?: () => void
    }
  }
}

export interface EditProfileFormFieldProps {
  label: string
  value: string
  onChangeText: (text: string) => void
  icon?: string
  placeholder?: string
  editable?: boolean
  keyboardType?: "default" | "email-address" | "phone-pad"
  onPress?: () => void
}

export interface EditProfileAvatarProps {
  avatar?: string
  onEditPress?: () => void
}
