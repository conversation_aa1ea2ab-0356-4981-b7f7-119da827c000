# Instructor Pricing Feature

## Overview
This feature handles course pricing, promotional tools, and revenue optimization for instructors.

## Structure
- `components/` - React components (PricingManager, PromotionalTools, RevenueAnalytics, etc.)
- `hooks/` - Custom hooks for business logic (usePricing, usePromotions, etc.)
- `screens/` - Screen components (PricingScreen, PromotionsScreen, etc.)
- `services/` - API and data services (pricingService, promotionService, etc.)
- `types/` - TypeScript type definitions (PricingModel, Promotion, etc.)
- `utils/` - Utility functions (pricing calculations, promotion helpers, etc.)

## Key Components
- **PricingManager** - Course pricing setup and management
- **PromotionalTools** - Discount coupons and limited-time offers
- **RevenueAnalytics** - Revenue tracking and optimization insights
- **BundlePricing** - Package deals and course bundles
- **ReferralPrograms** - Referral and affiliate program management

## Features
- Flexible pricing models (free, one-time, subscription, tiered)
- Promotional tools (coupons, discounts, early bird pricing)
- Bundle pricing for multiple courses
- Referral and affiliate programs
- Revenue analytics and optimization insights
- A/B testing for pricing strategies

## Usage
```typescript
import { PricingManager, PromotionalTools, RevenueAnalytics } from '@/features/instructor/pricing'
```
