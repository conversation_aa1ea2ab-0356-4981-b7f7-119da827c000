/**
 * Instructor My Course Detail Types
 * 
 * Types specific to instructor's teaching course management with analytics,
 * student progress tracking, and instructor-specific features.
 * Based on student types but adapted for instructor workflow.
 */

import type { StackScreenProps } from "@react-navigation/stack"
import type { InstructorStackParamList } from "app/navigators/InstructorNavigator"

// Navigation Types
export type MyCourseDetailScreenProps = StackScreenProps<InstructorStackParamList, "CourseManagement">

// Course Performance Types (instructor perspective)
export interface CoursePerformance {
  courseId: string
  totalStudents: number
  activeStudents: number
  completionRate: number // percentage of students who completed
  averageProgress: number // average progress across all students
  totalRevenue: number
  monthlyEnrollments: number[]
  lastUpdated: string
  certificatesIssued: number
}

// Student Progress in Instructor's Course
export interface StudentProgress {
  studentId: string
  studentName: string
  studentAvatar?: string
  enrolledDate: string
  lastActivity: string
  progressPercentage: number
  completedLessons: number
  totalLessons: number
  timeSpent: number // in minutes
  status: "active" | "completed" | "inactive" | "dropped"
  certificateEarned: boolean
}

// Assignment Management (instructor perspective)
export interface Assignment {
  id: string
  title: string
  description: string
  dueDate: string
  totalSubmissions: number
  gradedSubmissions: number
  averageGrade: number
  maxGrade: number
  status: "active" | "closed" | "draft"
}

// Course Material Management
export interface CourseMaterial {
  id: string
  title: string
  type: "video" | "pdf" | "document" | "quiz" | "assignment"
  url: string
  duration?: number // for videos
  size?: string // for files
  downloadCount: number
  lastUpdated: string
  isPublished: boolean
}

// Lesson Management (instructor perspective)
export interface Lesson {
  id: string
  title: string
  description: string
  duration: number
  videoUrl: string
  materials: CourseMaterial[]
  viewCount: number
  averageWatchTime: number
  completionRate: number // percentage of students who completed this lesson
  isPublished: boolean
  lastUpdated: string
}

// Instructor's Course Detail Data
export interface MyCourseDetail {
  id: string
  title: string
  category: string
  thumbnail: string
  instructor: {
    id: string
    name: string
    avatar: string
    title: string
    rating: number
    totalCourses: number
  }
  description: string
  fullDescription: string
  lessons: Lesson[]
  assignments: Assignment[]
  materials: CourseMaterial[]
  performance: CoursePerformance
  features: string[]
  reviews: Array<{
    id: string
    student: {
      name: string
      avatar: string
    }
    rating: number
    comment: string
    date: string
    instructorReply?: string
    replyDate?: string
  }>
  createdDate: string
  lastUpdated: string
  status: "draft" | "published" | "archived"
  studentProgress: StudentProgress[]
  totalEarnings: number
  pricing: {
    price: number
    currency: string
    discountPrice?: number
  }
}

// Handler Types (instructor actions)
export interface MyCourseDetailHandlers {
  handleBackPress: () => void
  handlePreviewPress: () => void
  handleEditCourse: () => void
  handleViewAnalytics: () => void
  handleManageStudents: () => void
  handleEditLesson: (lessonId: string) => void
  handleEditAssignment: (assignmentId: string) => void
  handleUpdateMaterial: (materialId: string) => void
  handleReplyToReview: (reviewId: string) => void
  handleSeeAllReviews: () => void
  handleViewStudentProgress: () => void
  handlePublishCourse: () => void
  handleArchiveCourse: () => void
  handleViewCurriculum: () => void
  activeTab: "overview" | "lessons" | "assignments" | "materials"
  handleTabChange: (tab: "overview" | "lessons" | "assignments" | "materials") => void
}

// Data Hook Return Type
export interface MyCourseDetailData {
  courseDetail: MyCourseDetail
  isLoading: boolean
  error?: string
}

// Component Props Types
export interface MyCourseDetailHeroSectionProps {
  thumbnail: string
  performance: CoursePerformance
  onBackPress: () => void
  onPreviewPress: () => void
  onEditCourse: () => void
}

export interface MyCourseDetailInfoSectionProps {
  course: MyCourseDetail
  onViewCurriculum?: () => void
}

export interface MyCourseDetailProgressSectionProps {
  performance: CoursePerformance
  studentProgress: StudentProgress[]
  onViewAnalytics: () => void
  onManageStudents: () => void
}

export interface MyCourseDetailInstructorSectionProps {
  instructor: MyCourseDetail["instructor"]
  coInstructors?: Array<{
    id: string
    name: string
    avatar: string
    role: string
  }>
}

export interface MyCourseDetailFeaturesSection {
  features: string[]
}

export interface MyCourseDetailReviewsSectionProps {
  reviews: MyCourseDetail["reviews"]
  onReplyToReview: (reviewId: string) => void
  onSeeAllPress: () => void
}

export interface MyCourseDetailActionSectionProps {
  performance: CoursePerformance
  status: "draft" | "published" | "archived"
  onEditCourse: () => void
  onViewAnalytics: () => void
  onPublishCourse: () => void
  isLoading: boolean
}
