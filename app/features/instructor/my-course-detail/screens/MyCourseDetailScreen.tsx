/**
 * MyCourseDetailScreen - Instructor Version
 *
 * Main Course Management screen for instructor's teaching courses
 * Based on student MyCourseDetailScreen but with instructor-specific features:
 * - Course performance analytics
 * - Student progress management
 * - Course editing and publishing
 * - Revenue tracking
 *
 * Following the same pattern as student version with:
 * - useMyCourseDetailData: Manages all course data and performance
 * - useMyCourseDetailHandlers: Manages all interaction handlers
 * - MyCourseDetailScreenContent: Contains all the scrollable content sections
 *
 * Benefits of this architecture:
 * - Easier to maintain and update
 * - Better separation of concerns
 * - Reusable hooks and components
 * - Cleaner code organization
 * - Easier testing
 * - Independent from student version for future development
 */

import React from "react"
import { Screen } from "app/components"
import { colors } from "app/theme"
import { MyCourseDetailScreenContent } from "./components/MyCourseDetailScreenContent"
import { useMyCourseDetailData } from "./hooks/useMyCourseDetailData"
import { useMyCourseDetailHandlers } from "./hooks/useMyCourseDetailHandlers"
import type { MyCourseDetailScreenProps } from "../types"

/**
 * MyCourseDetailScreen - Instructor's course management screen
 * 
 * This screen implements the course management interface for instructors.
 * Identical design structure to student version but with instructor-specific content.
 * 
 * Features:
 * - Course performance overview
 * - Student progress tracking
 * - Course editing and management
 * - Revenue and analytics
 * - Student feedback management
 */
export function MyCourseDetailScreen({ navigation, route }: MyCourseDetailScreenProps) {
  console.log("👨‍🏫 Instructor MyCourseDetailScreen rendering...")

  // Get course data using the data hook
  const data = useMyCourseDetailData(route?.params?.courseId, route?.params?.course)

  // Get all handlers using the handlers hook
  const handlers = useMyCourseDetailHandlers(navigation)

  console.log("👨‍🏫 Instructor Course Data:", data)
  console.log("👨‍🏫 Instructor Course Handlers:", handlers)

  return (
    <Screen
      preset="scroll"
      safeAreaEdges={["top"]}
      backgroundColor={colors.palette.neutral100}
    >
      {/* Main Content - Scrollable sections */}
      <MyCourseDetailScreenContent
        data={data}
        handlers={handlers}
      />
    </Screen>
  )
}
