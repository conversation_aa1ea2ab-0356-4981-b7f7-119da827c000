/**
 * MyCourseDetailScreenContent - Instructor Version
 * 
 * Main content component for Instructor's Course Management screen
 * Based on student version but adapted for instructor course management workflow
 */

import React from "react"
import { View, ViewStyle, ScrollView } from "react-native"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCourseDetailData } from "../hooks/useMyCourseDetailData"
import type { MyCourseDetailHandlers } from "../hooks/useMyCourseDetailHandlers"
import {
  MyCourseDetailHeroSection,
  MyCourseDetailInfoSection,
  MyCourseDetailProgressSection,
  MyCourseDetailInstructorSection,
  MyCourseDetailFeaturesSection,
  MyCourseDetailReviewsSection,
  MyCourseDetailActionSection
} from "./sections"

interface MyCourseDetailScreenContentProps {
  data: MyCourseDetailData
  handlers: MyCourseDetailHandlers
}

/**
 * MyCourseDetailScreenContent - Instructor's course management content
 * 
 * Features:
 * - Hero section with course preview and edit options
 * - Course info with curriculum management
 * - Performance metrics and analytics
 * - Teaching team information
 * - Course features overview
 * - Student reviews with reply options
 * - Fixed action section with management buttons
 */
export function MyCourseDetailScreenContent({
  data,
  handlers
}: MyCourseDetailScreenContentProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Scrollable Content */}
      <ScrollView
        style={themed($scrollView)}
        showsVerticalScrollIndicator={false}
        bounces={false}
      >
        {/* Hero Section - Course image with performance overlay */}
        <MyCourseDetailHeroSection
          thumbnail={data.courseDetail.thumbnail}
          performance={data.courseDetail.performance}
          onBackPress={handlers.handleBackPress}
          onPreviewPress={handlers.handlePreviewPress}
          onEditCourse={handlers.handleEditCourse}
        />

        {/* Course Info Section - Title, description, curriculum management */}
        <MyCourseDetailInfoSection
          course={data.courseDetail}
          onViewCurriculum={handlers.handleViewCurriculum}
        />

        {/* Performance Section - Analytics and student management */}
        <MyCourseDetailProgressSection
          performance={data.courseDetail.performance}
          studentProgress={data.courseDetail.studentProgress}
          onViewAnalytics={handlers.handleViewAnalytics}
          onManageStudents={handlers.handleManageStudents}
        />

        {/* Teaching Team Section - Instructor info */}
        <MyCourseDetailInstructorSection
          instructor={data.courseDetail.instructor}
        />

        {/* Features Section - Course features and tools */}
        <MyCourseDetailFeaturesSection
          features={data.courseDetail.features}
        />

        {/* Reviews Section - Student feedback with reply options */}
        <MyCourseDetailReviewsSection
          reviews={data.courseDetail.reviews}
          onReplyToReview={handlers.handleReplyToReview}
          onSeeAllPress={handlers.handleSeeAllReviews}
        />

        {/* Bottom spacing for fixed action button */}
        <View style={themed($bottomSpacing)} />
      </ScrollView>

      {/* Fixed Action Section - Bottom management buttons */}
      <MyCourseDetailActionSection
        performance={data.courseDetail.performance}
        status={data.courseDetail.status}
        onEditCourse={handlers.handleEditCourse}
        onViewAnalytics={handlers.handleViewAnalytics}
        onPublishCourse={handlers.handlePublishCourse}
        isLoading={data.isLoading}
      />
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
  backgroundColor: "#F5F9FF",
}

const $scrollView: ViewStyle = {
  flex: 1,
}

const $bottomSpacing: ViewStyle = {
  height: 160, // Space for fixed action button (increased for better clearance)
}
