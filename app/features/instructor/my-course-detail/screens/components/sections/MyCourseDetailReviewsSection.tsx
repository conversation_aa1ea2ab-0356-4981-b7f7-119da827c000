/**
 * MyCourseDetailReviewsSection - Instructor Version
 * 
 * Student reviews section for instructor
 * Shows student feedback with reply options
 */

import React from "react"
import { View, ViewStyle, Text, TextStyle, TouchableOpacity, Image } from "react-native"
import { Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCourseDetailReviewsSectionProps } from "../../../types"

export function MyCourseDetailReviewsSection({
  reviews,
  onReplyToReview,
  onSeeAllPress
}: MyCourseDetailReviewsSectionProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <View style={themed($card)}>
        <View style={themed($header)}>
          <Text style={themed($title)}>Student Reviews</Text>
          <TouchableOpacity onPress={onSeeAllPress} activeOpacity={0.7}>
            <Text style={themed($seeAllText)}>See All</Text>
          </TouchableOpacity>
        </View>
        
        {reviews.slice(0, 2).map((review) => (
          <View key={review.id} style={themed($reviewItem)}>
            <View style={themed($reviewHeader)}>
              <Image source={{ uri: review.student.avatar }} style={themed($avatar)} />
              <View style={themed($reviewInfo)}>
                <Text style={themed($studentName)}>{review.student.name}</Text>
                <View style={themed($ratingRow)}>
                  {[...Array(5)].map((_, i) => (
                    <Icon
                      key={i}
                      icon="star"
                      size={12}
                      color={i < review.rating ? "#FCCB40" : "#E0E0E0"}
                    />
                  ))}
                </View>
              </View>
            </View>
            <Text style={themed($reviewText)}>{review.comment}</Text>
            {!review.instructorReply && (
              <TouchableOpacity
                style={themed($replyButton)}
                onPress={() => onReplyToReview(review.id)}
                activeOpacity={0.7}
              >
                <Text style={themed($replyButtonText)}>Reply</Text>
              </TouchableOpacity>
            )}
          </View>
        ))}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
  marginBottom: 20,
}

const $card: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  padding: 20,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4,
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 16,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for titles
  fontSize: 18,
  lineHeight: 24,
  color: "#202244",
}

const $seeAllText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for links
  fontSize: 14,
  lineHeight: 18,
  color: "#0961F5",
}

const $reviewItem: ViewStyle = {
  marginBottom: 16,
  paddingBottom: 16,
  borderBottomWidth: 1,
  borderBottomColor: "#F0F0F0",
}

const $reviewHeader: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: 8,
}

const $avatar: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 20,
  marginRight: 12,
}

const $reviewInfo: ViewStyle = {
  flex: 1,
}

const $studentName: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY
  fontSize: 14,
  lineHeight: 18,
  color: "#202244",
  marginBottom: 4,
}

const $ratingRow: ViewStyle = {
  flexDirection: "row",
  gap: 2,
}

const $reviewText: TextStyle = {
  fontFamily: "nunitoSansMedium", // 500 weight - MANDATORY
  fontSize: 14,
  lineHeight: 20,
  color: "#202244",
  marginBottom: 8,
}

const $replyButton: ViewStyle = {
  backgroundColor: "#E8F1FF",
  borderRadius: 8,
  paddingHorizontal: 12,
  paddingVertical: 6,
  alignSelf: "flex-start",
}

const $replyButtonText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for button text
  fontSize: 12,
  lineHeight: 16,
  color: "#0961F5",
}
