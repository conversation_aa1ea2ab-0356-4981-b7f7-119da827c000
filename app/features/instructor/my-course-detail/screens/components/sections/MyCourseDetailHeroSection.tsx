/**
 * MyCourseDetailHeroSection - Instructor Version
 * 
 * Hero section for Instru<PERSON>'s Course Management with performance overlay and management buttons
 * Based on student version but adapted for instructor course management workflow
 */

import React from "react"
import { View, ViewStyle, ImageBackground, TouchableOpacity, StyleSheet, Text, TextStyle } from "react-native"
import { Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCourseDetailHeroSectionProps } from "../../../types"

/**
 * MyCourseDetailHeroSection - Instructor's course hero section
 * 
 * Features:
 * - Course thumbnail with dark overlay
 * - Back button with proper touch feedback
 * - Performance metrics overlay (students, completion rate)
 * - Preview button (instead of play)
 * - Edit course button (instead of continue learning)
 * - Published status badge
 */
export function MyCourseDetailHeroSection({
  thumbnail,
  performance,
  onBackPress,
  onPreviewPress,
  onEditCourse,
}: MyCourseDetailHeroSectionProps) {
  const { themed } = useAppTheme()

  const handleBackPress = () => {
    console.log("🔙 Instructor Hero: Back pressed")
    onBackPress?.()
  }

  const handlePreviewPress = () => {
    console.log("👁️ Instructor Hero: Preview pressed")
    onPreviewPress?.()
  }

  const handleEditCourse = () => {
    console.log("✏️ Instructor Hero: Edit course pressed")
    onEditCourse?.()
  }

  return (
    <View style={themed($container)}>
      <ImageBackground
        source={{ uri: thumbnail }}
        style={themed($imageBackground)}
        resizeMode="cover"
      >
        {/* Dark overlay */}
        <View style={themed($overlay)} />

        {/* Back Button */}
        <TouchableOpacity
          style={themed($backButton)}
          onPress={handleBackPress}
          activeOpacity={0.7}
        >
          <Icon icon="back" size={20} color="#202244" />
        </TouchableOpacity>

        {/* Performance Metrics Overlay */}
        <View style={themed($performanceOverlay)}>
          <Text style={themed($performanceText)}>
            {performance.totalStudents} Students
          </Text>
          <View style={themed($completionBar)}>
            <View 
              style={[
                themed($completionFill), 
                { width: `${performance.completionRate}%` }
              ]} 
            />
          </View>
          <Text style={themed($performanceDetails)}>
            {performance.completionRate}% completion rate
          </Text>
        </View>

        {/* Preview Button */}
        <TouchableOpacity
          style={themed($previewButton)}
          onPress={handlePreviewPress}
          activeOpacity={0.8}
        >
          <View style={themed($previewButtonInner)}>
            <Icon icon="eye" size={26} color="#FFFFFF" />
          </View>
        </TouchableOpacity>

        {/* Edit Course Button */}
        <TouchableOpacity
          style={themed($editButton)}
          onPress={handleEditCourse}
          activeOpacity={0.8}
        >
          <Text style={themed($editButtonText)}>Edit Course</Text>
          <Icon icon="edit" size={16} color="#FFFFFF" />
        </TouchableOpacity>

        {/* Published Status Badge */}
        <View style={themed($statusBadge)}>
          <Icon icon="check" size={16} color="#FFFFFF" />
          <Text style={themed($statusText)}>Published</Text>
        </View>
      </ImageBackground>
    </View>
  )
}

const $container: ViewStyle = {
  height: 400,
  backgroundColor: "#000000",
}

const $imageBackground: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}

const $overlay: ViewStyle = {
  ...StyleSheet.absoluteFillObject,
  backgroundColor: "rgba(0, 0, 0, 0.3)",
}

const $backButton: ViewStyle = {
  position: "absolute",
  top: 74,
  left: 16,
  width: 26.21,
  height: 20,
  justifyContent: "center",
  alignItems: "center",
}

const $performanceOverlay: ViewStyle = {
  position: "absolute",
  top: 60,
  right: 20,
  backgroundColor: "rgba(0, 0, 0, 0.7)",
  borderRadius: 12,
  padding: 12,
  minWidth: 160,
  alignItems: "center",
}

const $performanceText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for descriptions
  fontWeight: "300",
  fontSize: 14,
  color: "#FFFFFF",
  marginBottom: 8,
}

const $completionBar: ViewStyle = {
  width: 120,
  height: 4,
  backgroundColor: "rgba(255, 255, 255, 0.3)",
  borderRadius: 2,
  marginBottom: 6,
}

const $completionFill: ViewStyle = {
  height: 4,
  backgroundColor: "#167F71",
  borderRadius: 2,
}

const $performanceDetails: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for captions
  fontWeight: "300",
  fontSize: 11,
  color: "rgba(255, 255, 255, 0.8)",
}

const $previewButton: ViewStyle = {
  width: 63,
  height: 63,
  borderRadius: 31.5,
  backgroundColor: "#0961F5",
  justifyContent: "center",
  alignItems: "center",
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 4 },
  shadowOpacity: 0.2,
  shadowRadius: 13,
  elevation: 8,
}

const $previewButtonInner: ViewStyle = {
  justifyContent: "center",
  alignItems: "center",
}

const $editButton: ViewStyle = {
  position: "absolute",
  bottom: 30,
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FF6B00",
  paddingHorizontal: 20,
  paddingVertical: 12,
  borderRadius: 25,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 8,
  elevation: 5,
}

const $editButtonText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for button text
  fontWeight: "300",
  fontSize: 16,
  color: "#FFFFFF",
  marginRight: 8,
}

const $statusBadge: ViewStyle = {
  position: "absolute",
  top: 60,
  left: 20,
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "rgba(22, 127, 113, 0.9)",
  paddingHorizontal: 12,
  paddingVertical: 6,
  borderRadius: 20,
}

const $statusText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for captions
  fontWeight: "300",
  fontSize: 12,
  color: "#FFFFFF",
  marginLeft: 6,
}
