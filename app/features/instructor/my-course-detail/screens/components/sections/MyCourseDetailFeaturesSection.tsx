/**
 * MyCourseDetailFeaturesSection - Instructor Version
 * 
 * Course features section for instructor
 * Shows what the course includes
 */

import React from "react"
import { View, ViewStyle, Text, TextStyle } from "react-native"
import { Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCourseDetailFeaturesSection } from "../../../types"

export function MyCourseDetailFeaturesSection({
  features
}: MyCourseDetailFeaturesSection) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <View style={themed($card)}>
        <Text style={themed($title)}>Course Features</Text>
        
        {features.map((feature, index) => (
          <View key={index} style={themed($featureRow)}>
            <Icon icon="check" size={16} color="#167F71" />
            <Text style={themed($featureText)}>{feature}</Text>
          </View>
        ))}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
  marginBottom: 20,
}

const $card: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  padding: 20,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for titles
  fontSize: 18,
  lineHeight: 24,
  color: "#202244",
  marginBottom: 16,
}

const $featureRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: 12,
}

const $featureText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for features
  fontSize: 14,
  lineHeight: 20,
  color: "#202244",
  marginLeft: 12,
  flex: 1,
}
