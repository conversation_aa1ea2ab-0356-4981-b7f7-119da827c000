/**
 * MyCourseDetailInstructorSection - Instructor Version
 * 
 * Instructor info section for course management
 * Shows instructor details and teaching team
 */

import React from "react"
import { View, ViewStyle, Text, TextStyle, Image } from "react-native"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCourseDetailInstructorSectionProps } from "../../../types"

export function MyCourseDetailInstructorSection({
  instructor,
  coInstructors
}: MyCourseDetailInstructorSectionProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <View style={themed($card)}>
        <Text style={themed($title)}>Teaching Team</Text>
        
        {/* Main Instructor */}
        <View style={themed($instructorRow)}>
          <Image
            source={{ uri: instructor.avatar }}
            style={themed($avatar)}
          />
          <View style={themed($instructorInfo)}>
            <Text style={themed($instructorName)}>{instructor.name}</Text>
            <Text style={themed($instructorTitle)}>{instructor.title}</Text>
            <Text style={themed($instructorStats)}>
              {instructor.totalCourses} courses • {instructor.rating}★ rating
            </Text>
          </View>
        </View>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 16,
  marginBottom: 20,
}

const $card: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  padding: 20,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for titles
  fontSize: 18,
  lineHeight: 24,
  color: "#202244",
  marginBottom: 16,
}

const $instructorRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $avatar: ViewStyle = {
  width: 60,
  height: 60,
  borderRadius: 30,
  marginRight: 16,
}

const $instructorInfo: ViewStyle = {
  flex: 1,
}

const $instructorName: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for names
  fontSize: 16,
  lineHeight: 20,
  color: "#202244",
  marginBottom: 4,
}

const $instructorTitle: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for titles
  fontSize: 14,
  lineHeight: 18,
  color: "#FF6B00",
  marginBottom: 4,
}

const $instructorStats: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for stats
  fontSize: 12,
  lineHeight: 16,
  color: "#A0A4AB",
}
