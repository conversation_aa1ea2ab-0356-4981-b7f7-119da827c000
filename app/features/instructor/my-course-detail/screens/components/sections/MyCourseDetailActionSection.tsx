/**
 * MyCourseDetailActionSection - Instructor Version
 * 
 * Action buttons for Instru<PERSON>'s Course Management with instructor-specific actions:
 * - Edit Course
 * - View Analytics
 * - Publish/Archive Course
 */

import React from "react"
import { View, ViewStyle, Text, TextStyle, TouchableOpacity, ActivityIndicator, StyleSheet } from "react-native"
import { Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCourseDetailActionSectionProps } from "../../../types"

/**
 * MyCourseDetailActionSection - Instructor's course action section
 * 
 * Features:
 * - Analytics button (instead of certificate)
 * - Edit Course button (primary action)
 * - Course performance info
 * - Consistent styling with student version
 */
export function MyCourseDetailActionSection({
  performance,
  status,
  onEditCourse,
  onViewAnalytics,
  onPublishCourse,
  isLoading,
}: MyCourseDetailActionSectionProps) {
  const { themed } = useAppTheme()

  const handleEditCourse = () => {
    console.log("✏️ Instructor Action: Edit course pressed")
    onEditCourse?.()
  }

  const handleViewAnalytics = () => {
    console.log("📊 Instructor Action: View analytics pressed")
    onViewAnalytics?.()
  }

  const handlePublishCourse = () => {
    console.log("🚀 Instructor Action: Publish course pressed")
    onPublishCourse?.()
  }

  return (
    <View style={themed($container)}>
      {/* Action Buttons Row */}
      <View style={themed($buttonsRow)}>
        {/* Analytics Button */}
        <TouchableOpacity
          style={themed($analyticsButton)}
          onPress={handleViewAnalytics}
          activeOpacity={0.8}
        >
          <Icon
            icon="chart"
            size={24}
            color="#0961F5"
          />
        </TouchableOpacity>

        {status === "published" ? (
          // Edit Course Button (for published courses)
          <TouchableOpacity
            style={themed($primaryButton)}
            onPress={handleEditCourse}
            activeOpacity={0.8}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <>
                <Text style={themed($primaryButtonText)}>
                  Edit Course
                </Text>
                <View style={themed($buttonCircle)}>
                  <Icon icon="edit" size={21} color="#FF6B00" />
                </View>
              </>
            )}
          </TouchableOpacity>
        ) : (
          // Publish Course Button (for draft courses)
          <TouchableOpacity
            style={[themed($primaryButton), themed($publishButton)]}
            onPress={handlePublishCourse}
            activeOpacity={0.8}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <>
                <View style={themed($publishIcon)}>
                  <Icon icon="upload" size={21} color="#167F71" />
                </View>
                <Text style={themed($publishButtonText)}>
                  Publish Course
                </Text>
              </>
            )}
          </TouchableOpacity>
        )}
      </View>

      {/* Performance Info */}
      <View style={themed($performanceInfo)}>
        <Text style={themed($performanceText)}>
          {performance.totalStudents} students • {performance.completionRate}% completion rate
        </Text>
        <Text style={themed($revenueText)}>
          ${performance.totalRevenue.toLocaleString()} total revenue
        </Text>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  backgroundColor: "#FFFFFF",
  paddingHorizontal: 39,
  paddingVertical: 20,
  paddingBottom: 34, // Safe area bottom
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: -2 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 10,
}

const $buttonsRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: 12, // Space between buttons
}

const $primaryButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between", // Changed for Figma layout
  backgroundColor: "#FF6B00",
  borderRadius: 30,
  width: 250, // Figma width
  height: 60, // Figma height
  paddingHorizontal: 31, // Left padding for text
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 5,
  marginBottom: 12,
}

const $publishButton: ViewStyle = {
  backgroundColor: "#167F71",
}

const $buttonCircle: ViewStyle = {
  width: 48, // Figma circle size
  height: 48, // Figma circle size
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  justifyContent: "center",
  alignItems: "center",
}

const $publishIcon: ViewStyle = {
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "rgba(22, 127, 113, 0.2)",
  justifyContent: "center",
  alignItems: "center",
  marginRight: 16,
}

const $analyticsButton: ViewStyle = {
  width: 94, // Figma width
  height: 60, // Figma height
  borderRadius: 30, // Figma border radius
  backgroundColor: "#E8F1FF", // Figma background
  borderWidth: 2, // Figma border
  borderColor: "rgba(180, 189, 196, 0.4)", // Figma border color
  justifyContent: "center",
  alignItems: "center",
  marginBottom: 12,
}

const $primaryButtonText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for button text
  fontWeight: "300",
  fontSize: 18, // Figma font size
  lineHeight: 26, // Figma line height (18 * 1.44)
  color: "#FFFFFF",
  textAlign: "left", // Left aligned per Figma
}

const $publishButtonText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for button text
  fontWeight: "300",
  fontSize: 16,
  lineHeight: 26,
  color: "#FFFFFF",
  flex: 1,
  textAlign: "center",
}

const $performanceInfo: ViewStyle = {
  alignItems: "center",
}

const $performanceText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for descriptions
  fontWeight: "300",
  fontSize: 13,
  lineHeight: 16,
  color: "#202244",
  marginBottom: 4,
}

const $revenueText: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight - MANDATORY for captions
  fontWeight: "300",
  fontSize: 11,
  lineHeight: 14,
  color: "#167F71",
}
