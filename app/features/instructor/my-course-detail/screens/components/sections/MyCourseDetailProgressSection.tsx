/**
 * MyCourseDetailProgressSection - Instructor Version
 * 
 * Course performance and analytics section for instructor
 * Shows student progress and course metrics
 */

import React from "react"
import { View, ViewStyle, Text, TextStyle, TouchableOpacity } from "react-native"
import { Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCourseDetailProgressSectionProps } from "../../../types"

export function MyCourseDetailProgressSection({
  performance,
  studentProgress,
  onViewAnalytics,
  onManageStudents
}: MyCourseDetailProgressSectionProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <View style={themed($card)}>
        <Text style={themed($title)}>Course Performance</Text>
        
        {/* Performance Metrics */}
        <View style={themed($metricsRow)}>
          <View style={themed($metric)}>
            <Text style={themed($metricValue)}>{performance.totalStudents}</Text>
            <Text style={themed($metricLabel)}>Students</Text>
          </View>
          <View style={themed($metric)}>
            <Text style={themed($metricValue)}>{performance.completionRate}%</Text>
            <Text style={themed($metricLabel)}>Completion</Text>
          </View>
          <View style={themed($metric)}>
            <Text style={themed($metricValue)}>${performance.totalRevenue}</Text>
            <Text style={themed($metricLabel)}>Revenue</Text>
          </View>
        </View>
        
        {/* Action Buttons */}
        <View style={themed($buttonsRow)}>
          <TouchableOpacity
            style={themed($actionButton)}
            onPress={onViewAnalytics}
            activeOpacity={0.7}
          >
            <Text style={themed($actionButtonText)}>View Analytics</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={themed($actionButton)}
            onPress={onManageStudents}
            activeOpacity={0.7}
          >
            <Text style={themed($actionButtonText)}>Manage Students</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
  marginBottom: 20,
}

const $card: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  padding: 20,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for titles
  fontSize: 18,
  lineHeight: 24,
  color: "#202244",
  marginBottom: 16,
}

const $metricsRow: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-around",
  marginBottom: 20,
}

const $metric: ViewStyle = {
  alignItems: "center",
}

const $metricValue: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for values
  fontSize: 24,
  lineHeight: 30,
  color: "#0961F5",
  marginBottom: 4,
}

const $metricLabel: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for labels
  fontSize: 12,
  lineHeight: 16,
  color: "#A0A4AB",
}

const $buttonsRow: ViewStyle = {
  flexDirection: "row",
  gap: 12,
}

const $actionButton: ViewStyle = {
  flex: 1,
  backgroundColor: "#E8F1FF",
  borderRadius: 12,
  padding: 16,
  alignItems: "center",
}

const $actionButtonText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for button labels
  fontSize: 14,
  lineHeight: 18,
  color: "#0961F5",
}
