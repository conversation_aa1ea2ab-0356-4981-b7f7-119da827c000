/**
 * MyCourseDetailInfoSection - Instructor Version
 * 
 * Course information section for instructor's course management
 * Based on student version but adapted for instructor workflow
 */

import React from "react"
import { View, ViewStyle, Text, TextStyle, TouchableOpacity } from "react-native"
import { Icon } from "app/components"
import { useAppTheme } from "app/utils/useAppTheme"
import type { MyCourseDetailInfoSectionProps } from "../../../types"

export function MyCourseDetailInfoSection({
  course,
  onViewCurriculum
}: MyCourseDetailInfoSectionProps) {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <View style={themed($card)}>
        {/* Course Title */}
        <Text style={themed($title)}>{course.title}</Text>
        
        {/* Course Category */}
        <Text style={themed($category)}>{course.category}</Text>
        
        {/* Course Description */}
        <Text style={themed($description)}>{course.description}</Text>
        
        {/* View Curriculum Button */}
        <TouchableOpacity
          style={themed($curriculumButton)}
          onPress={onViewCurriculum}
          activeOpacity={0.7}
        >
          <Text style={themed($curriculumButtonText)}>Manage Curriculum</Text>
          <Icon icon="caretRight" size={16} color="#0961F5" />
        </TouchableOpacity>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  paddingHorizontal: 34,
  marginTop: -50, // Overlap with hero section
  marginBottom: 20,
}

const $card: ViewStyle = {
  backgroundColor: "#FFFFFF",
  borderRadius: 16,
  padding: 20,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.08,
  shadowRadius: 10,
  elevation: 4,
}

const $title: TextStyle = {
  fontFamily: "lexendDecaSemiBold", // 600 weight for titles
  fontSize: 24,
  lineHeight: 32,
  color: "#202244",
  marginBottom: 8,
}

const $category: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for categories
  fontSize: 14,
  lineHeight: 18,
  color: "#FF6B00",
  marginBottom: 12,
}

const $description: TextStyle = {
  fontFamily: "lexendDecaLight", // 300 weight for descriptions
  fontSize: 16,
  lineHeight: 24,
  color: "#202244",
  marginBottom: 20,
}

const $curriculumButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  backgroundColor: "#E8F1FF",
  borderRadius: 12,
  padding: 16,
}

const $curriculumButtonText: TextStyle = {
  fontFamily: "lexendDecaMedium", // 500 weight for button labels
  fontSize: 16,
  lineHeight: 20,
  color: "#0961F5",
}
