/**
 * useMyCourseDetailData Hook - Instructor Version
 * 
 * Manages all data for <PERSON><PERSON><PERSON><PERSON>'s Course Management screen including:
 * - Course performance and analytics
 * - Student progress tracking
 * - Revenue and enrollment data
 * - Instructor-specific course data
 */

import { useState, useEffect } from "react"
import type { MyCourseDetailData, MyCourseDetail } from "../../types"

export function useMyCourseDetailData(
  courseId?: string,
  course?: any
): MyCourseDetailData {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string>()

  // Mock data for Instructor's Course Management (teaching course)
  const mockInstructorCourseDetail: MyCourseDetail = {
    id: courseId || "instructor-course-1",
    title: "Advanced React Development",
    category: "Web Development",
    thumbnail: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=300&fit=crop",
    instructor: {
      id: "instructor-1",
      name: "<PERSON>",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      title: "Senior React Developer",
      rating: 4.8,
      totalCourses: 12,
    },
    description: "Master advanced React concepts and build production-ready applications",
    fullDescription: "This comprehensive course covers advanced React patterns, performance optimization, testing strategies, and modern development workflows. Perfect for developers looking to take their React skills to the next level.",
    lessons: [
      {
        id: "lesson-1",
        title: "Advanced React Patterns",
        description: "Learn render props, HOCs, and compound components",
        duration: 45,
        videoUrl: "https://example.com/lesson1.mp4",
        materials: [],
        viewCount: 156,
        averageWatchTime: 38,
        completionRate: 85,
        isPublished: true,
        lastUpdated: "2024-01-15",
      },
      {
        id: "lesson-2", 
        title: "Performance Optimization",
        description: "React.memo, useMemo, useCallback, and more",
        duration: 52,
        videoUrl: "https://example.com/lesson2.mp4",
        materials: [],
        viewCount: 142,
        averageWatchTime: 45,
        completionRate: 78,
        isPublished: true,
        lastUpdated: "2024-01-18",
      },
      {
        id: "lesson-3",
        title: "Testing React Applications",
        description: "Unit testing, integration testing, and E2E testing",
        duration: 38,
        videoUrl: "https://example.com/lesson3.mp4",
        materials: [],
        viewCount: 98,
        averageWatchTime: 32,
        completionRate: 65,
        isPublished: true,
        lastUpdated: "2024-01-20",
      }
    ],
    assignments: [
      {
        id: "assignment-1",
        title: "Build a React Dashboard",
        description: "Create a responsive dashboard using advanced React patterns",
        dueDate: "2024-02-15",
        totalSubmissions: 45,
        gradedSubmissions: 38,
        averageGrade: 87,
        maxGrade: 100,
        status: "active",
      },
      {
        id: "assignment-2",
        title: "Performance Optimization Project",
        description: "Optimize a slow React application",
        dueDate: "2024-02-28",
        totalSubmissions: 32,
        gradedSubmissions: 25,
        averageGrade: 82,
        maxGrade: 100,
        status: "active",
      }
    ],
    materials: [
      {
        id: "material-1",
        title: "React Advanced Patterns Cheatsheet",
        type: "pdf",
        url: "https://example.com/cheatsheet.pdf",
        size: "2.5 MB",
        downloadCount: 234,
        lastUpdated: "2024-01-10",
        isPublished: true,
      },
      {
        id: "material-2",
        title: "Performance Testing Tools",
        type: "document",
        url: "https://example.com/tools.zip",
        size: "15 MB",
        downloadCount: 156,
        lastUpdated: "2024-01-12",
        isPublished: true,
      }
    ],
    performance: {
      courseId: courseId || "instructor-course-1",
      totalStudents: 178,
      activeStudents: 156,
      completionRate: 72,
      averageProgress: 68,
      totalRevenue: 8900,
      monthlyEnrollments: [12, 18, 25, 32, 28, 35],
      lastUpdated: "2024-01-25",
      certificatesIssued: 128,
    },
    features: [
      "8+ hours of video content",
      "Hands-on coding exercises",
      "Real-world projects",
      "Certificate of completion",
      "Lifetime access",
      "Community support"
    ],
    reviews: [
      {
        id: "review-1",
        student: {
          name: "Sarah Johnson",
          avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face",
        },
        rating: 5,
        comment: "Excellent course! The advanced patterns section was particularly helpful.",
        date: "2024-01-20",
        instructorReply: "Thank you Sarah! Glad you found the patterns section useful.",
        replyDate: "2024-01-21",
      },
      {
        id: "review-2",
        student: {
          name: "Mike Chen",
          avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face",
        },
        rating: 4,
        comment: "Great content, would love more examples on testing.",
        date: "2024-01-18",
      }
    ],
    createdDate: "2023-12-01",
    lastUpdated: "2024-01-25",
    status: "published",
    studentProgress: [
      {
        studentId: "student-1",
        studentName: "Sarah Johnson",
        studentAvatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=50&h=50&fit=crop&crop=face",
        enrolledDate: "2024-01-05",
        lastActivity: "2024-01-24",
        progressPercentage: 85,
        completedLessons: 2,
        totalLessons: 3,
        timeSpent: 180,
        status: "active",
        certificateEarned: false,
      },
      {
        studentId: "student-2",
        studentName: "Mike Chen",
        studentAvatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face",
        enrolledDate: "2024-01-08",
        lastActivity: "2024-01-23",
        progressPercentage: 100,
        completedLessons: 3,
        totalLessons: 3,
        timeSpent: 220,
        status: "completed",
        certificateEarned: true,
      }
    ],
    totalEarnings: 8900,
    pricing: {
      price: 99,
      currency: "USD",
      discountPrice: 79,
    }
  }

  // Simulate loading state
  useEffect(() => {
    if (courseId) {
      setIsLoading(true)
      const timer = setTimeout(() => {
        setIsLoading(false)
      }, 1000)
      return () => clearTimeout(timer)
    }
  }, [courseId])

  return {
    courseDetail: mockInstructorCourseDetail,
    isLoading,
    error,
  }
}
