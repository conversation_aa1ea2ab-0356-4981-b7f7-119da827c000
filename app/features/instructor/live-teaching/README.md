# Instructor Live Teaching Feature

## Overview
This feature handles virtual classroom functionality including video conferencing, interactive tools, and live session management.

## Structure
- `components/` - React components (VirtualClassroom, InteractiveTools, SessionManager, etc.)
- `hooks/` - Custom hooks for business logic (useLiveTeaching, useVideoConference, etc.)
- `screens/` - Screen components (LiveClassroomScreen, SessionSetupScreen, etc.)
- `services/` - API and data services (liveTeachingService, videoConferenceService, etc.)
- `types/` - TypeScript type definitions (LiveSession, Participant, etc.)
- `utils/` - Utility functions (streaming helpers, session management, etc.)

## Key Components
- **VirtualClassroom** - Main video conferencing interface
- **InteractiveTools** - Whiteboard, polls, breakout rooms
- **SessionManager** - Session recording and attendance tracking
- **SchedulingSystem** - Calendar integration and booking management
- **ParticipantManager** - Student management during live sessions

## Features
- HD video conferencing with multi-participant support
- Interactive tools (whiteboard, polls, breakout rooms)
- Screen and application sharing
- Session recording and automatic transcription
- Attendance tracking and session analytics
- Calendar integration and booking management

## Usage
```typescript
import { VirtualClassroom, InteractiveTools, SessionManager } from '@/features/instructor/live-teaching'
```
