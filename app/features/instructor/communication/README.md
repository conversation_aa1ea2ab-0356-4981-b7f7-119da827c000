# Instructor Communication Feature

## Overview
This feature handles comprehensive communication tools including messaging, announcements, and student interaction management.

## Structure
- `components/` - React components (MessagingSystem, AnnouncementTools, QAManager, etc.)
- `hooks/` - Custom hooks for business logic (useCommunication, useMessaging, etc.)
- `screens/` - Screen components (CommunicationScreen, MessagingScreen, etc.)
- `services/` - API and data services (communicationService, messagingService, etc.)
- `types/` - TypeScript type definitions (Message, Announcement, etc.)
- `utils/` - Utility functions (communication helpers, notification utils, etc.)

## Key Components
- **MessagingSystem** - Individual and group messaging
- **AnnouncementTools** - Broadcast announcements to students
- **QAManager** - Q&A management and discussion moderation
- **LiveInteraction** - Office hours and consultation scheduling
- **NotificationCenter** - Push notifications and email campaigns

## Features
- Individual and group messaging with students
- Announcement broadcasting to all or selected students
- Q&A management and discussion forum moderation
- Office hours scheduling and live consultations
- Email campaigns and push notification management
- Communication analytics and engagement tracking

## Usage
```typescript
import { MessagingSystem, AnnouncementTools, QAManager } from '@/features/instructor/communication'
```
