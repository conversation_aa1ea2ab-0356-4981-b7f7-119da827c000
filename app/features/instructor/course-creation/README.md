# Instructor Course Creation Feature

## Overview
This feature handles the complete course creation workflow including course setup, curriculum building, and content organization.

## Structure
- `components/` - React components (CourseWizard, CurriculumBuilder, ContentUploader, etc.)
- `hooks/` - Custom hooks for business logic (useCourseCreation, useCurriculum, etc.)
- `screens/` - Screen components (CourseCreationScreen, CurriculumScreen, etc.)
- `services/` - API and data services (courseCreationService, contentService, etc.)
- `types/` - TypeScript type definitions (CourseInfo, Curriculum, Content, etc.)
- `utils/` - Utility functions (validation, content helpers, etc.)

## Key Components
- **CourseWizard** - Step-by-step course creation wizard
- **BasicInfoForm** - Course title, description, category setup
- **CurriculumBuilder** - Section and lesson organization
- **ContentUploader** - Video, document, and resource upload
- **LearningObjectives** - Course goals and requirements setup

## Features
- Step-by-step course creation wizard
- Basic course information setup
- Curriculum structure building
- Content upload and management
- Learning objectives definition
- Course preview and validation

## Usage
```typescript
import { CourseWizard, CurriculumBuilder, ContentUploader } from '@/features/instructor/course-creation'
```
