import { FC } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
} from "react-native"
import { Text } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface OTPVerificationHeaderProps {
  phoneNumber: string
  formatPhoneNumber: (phone: string) => string
  title?: string
  logoIcon?: string
  style?: ViewStyle
}

export const OTPVerificationHeader: FC<OTPVerificationHeaderProps> = ({
  phoneNumber,
  formatPhoneNumber,
  title = "Enter Verification Code",
  logoIcon = "🔐",
  style,
}) => {
  const { themed } = useAppTheme()

  const subtitle = `We sent a 6-digit code to ${formatPhoneNumber(phoneNumber)}`

  return (
    <View style={themed([$headerSection, style])}>
      <View style={themed($logoContainer)}>
        <View style={themed($logoPlaceholder)}>
          <Text style={themed($logoText)}>{logoIcon}</Text>
        </View>
      </View>

      <Text
        text={title}
        preset="heading"
        style={themed($title)}
      />
      <Text
        text={subtitle}
        preset="subheading"
        style={themed($subtitle)}
      />
    </View>
  )
}

const $headerSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  paddingTop: spacing.xl,
  paddingBottom: spacing.xl,
})

const $logoContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $logoPlaceholder: ThemedStyle<ViewStyle> = ({ colors }) => ({
  width: 80,
  height: 80,
  borderRadius: 40,
  backgroundColor: colors.palette.neutral100,
  justifyContent: "center",
  alignItems: "center",
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 4,
})

const $logoText: ThemedStyle<TextStyle> = () => ({
  fontSize: 32,
})

const $title: ThemedStyle<TextStyle> = ({ colors, spacing, typography }) => ({
  fontSize: 28,
  fontFamily: typography.primary.bold,
  color: colors.palette.neutral100,
  textAlign: "center",
  marginBottom: spacing.xs,
})

const $subtitle: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 16,
  fontFamily: typography.primary.normal,
  color: colors.palette.neutral200,
  textAlign: "center",
  opacity: 0.9,
})
