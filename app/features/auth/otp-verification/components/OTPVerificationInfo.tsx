import { FC } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
} from "react-native"
import { Text } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface OTPVerificationInfoProps {
  infoText?: string
  style?: ViewStyle
}

export const OTPVerificationInfo: FC<OTPVerificationInfoProps> = ({
  infoText = "Didn't receive the code? Check your SMS messages or try resending.",
  style,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$infoSection, style])}>
      <Text
        text={infoText}
        style={themed($infoText)}
      />
    </View>
  )
}

const $infoSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  paddingHorizontal: spacing.md,
})

const $infoText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.palette.neutral200,
  textAlign: "center",
  opacity: 0.8,
})
