import { FC } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
} from "react-native"
import { Button, Text, OTPInput } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface OTPInputCardProps {
  otpCode: string
  error: string
  isLoading: boolean
  countdown: number
  canResend: boolean
  otpResendCount: number
  onOTPChange: (code: string) => void
  onVerifyOTP: () => void
  onResendOTP: () => void
  onBackToPhone: () => void
  style?: ViewStyle
}

export const OTPInputCard: FC<OTPInputCardProps> = ({
  otpCode,
  error,
  isLoading,
  countdown,
  canResend,
  otpResendCount,
  onOTPChange,
  onVerifyOTP,
  onResendOTP,
  onBackToPhone,
  style,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$otpCard, style])}>
      <OTPInput
        value={otpCode}
        onChangeText={onOTPChange}
        onComplete={onVerifyOTP}
        label="Verification Code"
        helper={error}
        status={error ? "error" : undefined}
        autoFocus
      />

      <Button
        text={isLoading ? "Verifying..." : "Verify Code"}
        style={themed($verifyButton)}
        preset="reversed"
        onPress={onVerifyOTP}
        disabled={isLoading || otpCode.length !== 6}
      />

      <View style={themed($resendSection)}>
        {canResend && otpResendCount < 3 ? (
          <Text
            text="Resend Code"
            style={themed($resendText)}
            onPress={onResendOTP}
          />
        ) : (
          <Text
            text={otpResendCount >= 3 ? "Maximum resend attempts reached" : `Resend in ${countdown}s`}
            style={themed($countdownText)}
          />
        )}
      </View>

      <View style={themed($backToPhone)}>
        <Text
          text="Change Phone Number"
          style={themed($backToPhoneText)}
          onPress={onBackToPhone}
        />
      </View>
    </View>
  )
}

const $otpCard: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  borderRadius: 24,
  padding: spacing.xl,
  marginHorizontal: spacing.xs,
  marginBottom: spacing.lg,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 8 },
  shadowOpacity: 0.15,
  shadowRadius: 16,
  elevation: 8,
})

const $verifyButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.lg,
  borderRadius: 16,
  paddingVertical: spacing.md,
})

const $resendSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  marginTop: spacing.lg,
})

const $resendText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 14,
  fontFamily: typography.primary.medium,
  color: colors.tint,
  textDecorationLine: "underline",
})

const $countdownText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
})

const $backToPhone: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  marginTop: spacing.md,
})

const $backToPhoneText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 14,
  fontFamily: typography.primary.medium,
  color: colors.textDim,
  textDecorationLine: "underline",
})
