import { observer } from "mobx-react-lite"
import { FC } from "react"
import { AppStackScreenProps } from "@/navigators"
import {
  OTPVerificationContainer,
  OTPVerificationHeader,
  OTPInputCard,
  OTPVerificationInfo,
} from "../components"
import { useOTPVerification } from "../hooks"

interface OTPVerificationScreenProps extends AppStackScreenProps<"OTPVerification"> {}

export const OTPVerificationScreen: FC<OTPVerificationScreenProps> = observer(function OTPVerificationScreen(props) {
  const { navigation, route } = props
  const { phoneNumber, verificationId } = route.params
  
  const {
    // State
    otpCode,
    error,
    isLoading,
    countdown,
    canResend,
    otpResendCount,
    
    // Actions
    setOTPCode,
    handleVerifyOTP,
    handleResendOTP,
    handleBackToPhone,
    formatPhoneNumber,
  } = useOTPVerification({ navigation, phoneNumber, verificationId })

  return (
    <OTPVerificationContainer>
      <OTPVerificationHeader
        phoneNumber={phoneNumber}
        formatPhoneNumber={formatPhoneNumber}
      />

      <OTPInputCard
        otpCode={otpCode}
        error={error}
        isLoading={isLoading}
        countdown={countdown}
        canResend={canResend}
        otpResendCount={otpResendCount}
        onOTPChange={setOTPCode}
        onVerifyOTP={handleVerifyOTP}
        onResendOTP={handleResendOTP}
        onBackToPhone={handleBackToPhone}
      />

      <OTPVerificationInfo />
    </OTPVerificationContainer>
  )
})
