import { useState, useEffect } from "react"
import { Alert } from "react-native"
import { useStores } from "@/models"
import { phoneAuthService } from "@/services/phoneAuth"
import type { NavigationProp } from "@react-navigation/native"
import type { AppStackParamList } from "@/navigators"

interface UseOTPVerificationProps {
  navigation: NavigationProp<AppStackParamList>
  phoneNumber: string
  verificationId: string
}

export function useOTPVerification({
  navigation,
  phoneNumber,
  verificationId
}: UseOTPVerificationProps) {
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [countdown, setCountdown] = useState(60) // 60 seconds countdown for resend
  const [canResend, setCanResend] = useState(false)

  const {
    authenticationStore: {
      otpCode,
      setOTPCode,
      otpValidationError,
      isLoading,
      setIsLoading,
      authError,
      setAuthError,
      setOTPState,
      setAuthToken,
      incrementOTPResendCount,
      otpResendCount,
    },
  } = useStores()

  // Countdown timer for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
      return undefined
    }
  }, [countdown])

  const error = isSubmitted ? otpValidationError || authError : ""

  const handleVerifyOTP = async () => {
    setIsSubmitted(true)

    if (otpValidationError) {
      return
    }

    setIsLoading(true)
    setAuthError("")
    setOTPState("verifying")

    try {
      const result = await phoneAuthService.verifyOTP(otpCode, verificationId)

      if (result.success && result.user) {
        setOTPState("verified")

        // Get ID token for API authentication
        const idToken = await phoneAuthService.getIdToken()
        if (idToken) {
          setAuthToken(idToken)
          // Navigation will be handled by the authentication state change
        } else {
          throw new Error("Failed to get authentication token")
        }
      } else {
        setOTPState("error")
        setAuthError(result.error || "Invalid OTP code")
        Alert.alert("Verification Failed", result.error || "Invalid OTP code")
      }
    } catch (error) {
      setOTPState("error")
      const errorMessage = error instanceof Error ? error.message : "Verification failed"
      setAuthError(errorMessage)
      Alert.alert("Error", errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendOTP = async () => {
    if (!canResend || otpResendCount >= 3) {
      return
    }

    setIsLoading(true)
    setAuthError("")
    setOTPState("sending")

    try {
      const result = await phoneAuthService.resendOTP(phoneNumber)

      if (result.success) {
        setOTPState("sent")
        incrementOTPResendCount()
        setCountdown(60)
        setCanResend(false)
        setOTPCode("") // Clear current OTP
        Alert.alert("Success", "Verification code sent successfully")
      } else {
        setOTPState("error")
        setAuthError(result.error || "Failed to resend OTP")
        Alert.alert("Error", result.error || "Failed to resend OTP")
      }
    } catch (error) {
      setOTPState("error")
      const errorMessage = error instanceof Error ? error.message : "Failed to resend OTP"
      setAuthError(errorMessage)
      Alert.alert("Error", errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToPhone = () => {
    navigation.goBack()
  }

  const formatPhoneNumber = (phone: string) => {
    // Format phone number for display (e.g., +84 *** *** 123)
    if (phone.length > 6) {
      const countryCode = phone.slice(0, 3)
      const lastDigits = phone.slice(-3)
      return `${countryCode} *** *** ${lastDigits}`
    }
    return phone
  }

  return {
    // State
    otpCode,
    error,
    isLoading,
    countdown,
    canResend,
    otpResendCount,

    // Actions
    setOTPCode,
    handleVerifyOTP,
    handleResendOTP,
    handleBackToPhone,
    formatPhoneNumber,
  }
}
