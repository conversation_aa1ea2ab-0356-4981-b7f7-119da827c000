import { FC, useRef, useState } from "react"
import { TextInput, ViewStyle, TextStyle, View, TouchableOpacity } from "react-native"
import { Button, TextField, Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface LoginFormProps {
  /**
   * Email value
   */
  email: string
  /**
   * Password value
   */
  password: string
  /**
   * Whether password is hidden
   */
  isPasswordHidden: boolean
  /**
   * Whether form is loading
   */
  isLoading: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Number of login attempts
   */
  attemptsCount: number
  /**
   * Email change handler
   */
  onEmailChange: (email: string) => void
  /**
   * Password change handler
   */
  onPasswordChange: (password: string) => void
  /**
   * Toggle password visibility
   */
  onTogglePasswordVisibility: () => void
  /**
   * Login submit handler
   */
  onSubmit: () => void
  /**
   * Forgot password handler
   */
  onForgotPassword?: () => void
  /**
   * Sign up handler
   */
  onSignUp?: () => void
}

export const LoginForm: FC<LoginFormProps> = ({
  email,
  password,
  isPasswordHidden,
  isLoading,
  error,
  attemptsCount,
  onEmailChange,
  onPasswordChange,
  onTogglePasswordVisibility,
  onSubmit,
  onForgotPassword,
  onSignUp,
}) => {
  const authPasswordInput = useRef<TextInput>(null)
  const [rememberMe, setRememberMe] = useState(false)
  const {
    themed,
    theme: { colors },
  } = useAppTheme()



  return (
    <View style={themed($formContainer)}>
      {attemptsCount > 2 && (
        <View style={themed($hintContainer)}>
          <Text tx="loginScreen:hint" size="sm" weight="medium" style={themed($hint)} />
        </View>
      )}

      {/* Custom Email Field to match Figma design */}
      <View style={themed($textField)}>
        <View style={themed($emailFieldContainer)}>
          <Icon
            icon="settings"
            size={19}
            color="#545454"
            style={themed($emailIcon)}
          />
          <TextInput
            value={email}
            onChangeText={onEmailChange}
            style={themed($emailInput)}
            placeholder="Email"
            placeholderTextColor="#505050"
            autoCapitalize="none"
            autoComplete="username"
            autoCorrect={false}
            keyboardType="email-address"
            onSubmitEditing={() => authPasswordInput.current?.focus()}
          />
        </View>
      </View>

      {/* Custom Password Field to match Figma design */}
      <View style={themed($lastTextField)}>
        <View style={themed($passwordFieldContainer)}>
          <Icon
            icon="lock"
            size={19}
            color="#545454"
            style={themed($passwordIcon)}
          />
          <TextInput
            ref={authPasswordInput}
            value={password}
            onChangeText={onPasswordChange}
            style={themed($passwordInput)}
            placeholder="Mật khẩu"
            placeholderTextColor="#505050"
            autoCapitalize="none"
            autoComplete="password"
            autoCorrect={false}
            secureTextEntry={isPasswordHidden}
            onSubmitEditing={onSubmit}
          />
          <TouchableOpacity
            onPress={onTogglePasswordVisibility}
            style={themed($eyeIconContainer)}
          >
            <Icon
              icon={isPasswordHidden ? "hidden" : "view"}
              size={15}
              color="#545454"
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Error Message */}
      {error && (
        <View style={themed($errorContainer)}>
          <Icon
            icon="x"
            size={16}
            color="#EF4444"
            style={themed($errorIcon)}
          />
          <Text style={themed($errorText)}>{error}</Text>
        </View>
      )}

      {/* Remember Me and Forgot Password Row - HIDDEN */}
      {/* <View style={themed($rememberForgotRow)}>
        <TouchableOpacity
          style={themed($rememberMeContainer)}
          onPress={() => setRememberMe(!rememberMe)}
        >
          <View style={themed($checkbox)}>
            {rememberMe && (
              <Icon
                icon="check"
                size={12}
                color="#167F71"
              />
            )}
          </View>
          <Text style={themed($rememberMeText)}>Ghi nhớ đăng nhập</Text>
        </TouchableOpacity>

        {onForgotPassword && (
          <TouchableOpacity onPress={onForgotPassword}>
            <Text style={themed($forgotPasswordText)}>Quên mật khẩu?</Text>
          </TouchableOpacity>
        )}
      </View> */}

      {/* Sign Up and Forgot Password Row */}
      <View style={themed($forgotPasswordSignUpRow)}>
        {onSignUp && (
          <TouchableOpacity onPress={onSignUp}>
            <Text style={themed($signUpText)}>Đăng ký tài khoản</Text>
          </TouchableOpacity>
        )}

        {onForgotPassword && (
          <TouchableOpacity onPress={onForgotPassword}>
            <Text style={themed($forgotPasswordText)}>Quên mật khẩu?</Text>
          </TouchableOpacity>
        )}
      </View>

      <TouchableOpacity
        testID="login-button"
        style={themed($loginButton)}
        onPress={onSubmit}
        disabled={isLoading}
        activeOpacity={0.8}
      >
        <Text style={themed($loginButtonText)}>
          {isLoading ? "Đang đăng nhập..." : "Đăng nhập"}
        </Text>
        <View style={themed($buttonIconContainer)}>
          <Icon
            icon="caretRight"
            size={21}
            color="#0961F5"
          />
        </View>
      </TouchableOpacity>
    </View>
  )
}

// Styles
const $formContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 24, // 24px padding left and right as requested
  alignItems: "center", // Center all form elements
})

const $hintContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "rgba(59, 130, 246, 0.1)",
  borderRadius: 12,
  padding: spacing.sm,
  marginBottom: spacing.md,
})

const $hint: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for body text - MANDATORY
  fontWeight: "500", // Medium weight for body text per design guidelines - MANDATORY
  color: colors.tint,
  textAlign: "center",
})

const $errorContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FEF2F2",
  borderColor: "#FECACA",
  borderWidth: 1,
  borderRadius: 8,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  marginTop: spacing.xs,
  marginBottom: spacing.sm,
  width: "100%",
})

const $errorIcon: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginRight: spacing.xs,
})

const $errorText: ThemedStyle<TextStyle> = ({ typography }) => ({
  flex: 1,
  fontSize: 13,
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for body text - MANDATORY
  fontWeight: "500", // Medium weight for body text per design guidelines - MANDATORY
  color: "#DC2626",
  lineHeight: 18,
})

const $textField: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: 24, // 24px space between inputs
})

const $lastTextField: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: 0, // No margin for last field to avoid double spacing
})

const $textFieldLabel: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for form labels
  fontWeight: "500", // Medium weight for form labels per design guidelines
  fontSize: 14, // Exact size from Figma
  color: "#505050", // Exact color from Figma
})

const $textFieldInput: ThemedStyle<ViewStyle> = ({ typography }) => ({
  backgroundColor: "#FFFFFF", // White background
  borderRadius: 12, // Exact border radius from Figma
  height: 60, // Exact height from Figma
  paddingHorizontal: 46, // Space for icon (20px) + padding
  paddingVertical: 0,
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 4,
  borderWidth: 0,
})

const $loginButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: "#23408B", // Brand color (royal blue)
  borderRadius: 30, // Exact border radius from Figma
  height: 60, // Exact height from Figma
  width: "100%", // Full width instead of fixed 350px
  alignSelf: "center", // Center the button
  position: "relative",
  marginBottom: 16, // 16px spacing to "Hoặc đăng nhập bằng"
})

const $loginButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontSize: 18, // Exact from Figma
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for button text - MANDATORY
  fontWeight: "500", // Medium weight for button text per design guidelines - MANDATORY
  color: "#FFFFFF", // White text
  textAlign: "center",
  lineHeight: 26, // 1.445em * 18px
  flex: 1,
})

const $buttonIconContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  position: "absolute",
  right: 6,
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  justifyContent: "center",
  alignItems: "center",
})

const $rememberForgotRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginTop: 24, // 24px spacing from password field
  marginBottom: 24, // 24px spacing to login button
  paddingHorizontal: 0, // No extra padding
  width: 360, // Match input field width
  alignSelf: "center", // Center the row itself but content aligns left/right
})

const $forgotPasswordOnlyRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "flex-end", // Align to right like before
  alignItems: "center",
  marginTop: 24, // 24px spacing from password field
  marginBottom: 24, // 24px spacing to login button
  paddingHorizontal: 0, // No extra padding
  width: 360, // Match input field width
  alignSelf: "center", // Center the row itself
})

const $forgotPasswordSignUpRow: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between", // Space between forgot password and sign up
  alignItems: "center",
  marginTop: 24, // 24px spacing from password field
  marginBottom: 24, // 24px spacing to login button
  paddingLeft: 6, // 6px extra padding from left
  paddingRight: 6, // 6px extra padding from right
  width: 360, // Match input field width
  alignSelf: "center", // Center the row itself
})

const $signUpContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
})

const $promptText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 14, // Same as SignUpPrompt
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for body text - MANDATORY
  fontWeight: "500", // Medium weight for body text per design guidelines - MANDATORY
  color: "#545454", // Same gray color
  lineHeight: 18, // Same line height
})

const $signUpText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 14, // Same size as forgot password
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for body text - MANDATORY
  fontWeight: "500", // Medium weight for body text per design guidelines - MANDATORY
  color: "#545454", // Same gray color as forgot password
  lineHeight: 18, // Same line height
  textDecorationLine: "none", // No underline
})

const $rememberMeContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
})

const $checkbox: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 18, // Exact size from Figma
  height: 18, // Exact size from Figma
  borderWidth: 2, // Exact stroke weight from Figma
  borderColor: "#167F71", // Exact color from Figma (teal)
  borderRadius: 2,
  marginRight: 8, // Space between checkbox and text (60 - 34 - 18 = 8)
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: "transparent",
})

const $rememberMeText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontSize: 13, // Exact from Figma
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for body text - MANDATORY
  fontWeight: "500", // Medium weight for body text per design guidelines - MANDATORY
  color: "#545454", // Exact color from Figma
  lineHeight: 16, // 1.255em * 13px
})

const $forgotPasswordText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontSize: 14, // Same size as sign up text
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for body text - MANDATORY
  fontWeight: "500", // Medium weight for body text per design guidelines - MANDATORY
  color: "#545454", // Same gray color as sign up text
  textAlign: "right", // RIGHT align from Figma
  lineHeight: 18, // Same line height as sign up text
})

// Email Field Styles (matching Figma design exactly)
const $emailFieldContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#FFFFFF", // White background
  borderRadius: 20, // Increased border radius for more rounded corners
  height: 60, // Exact height from Figma
  width: "100%", // Full width instead of fixed 360px
  flexDirection: "row",
  alignItems: "center",
  borderWidth: 1, // Gray border instead of shadow
  borderColor: "#E5E5E5", // Light gray border color
  paddingHorizontal: 0,
})

const $emailIcon: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginLeft: 20, // x:20 from Figma
  marginRight: 7, // Space between icon and text (46 - 20 - 19 = 7)
})

const $emailInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  flex: 1,
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for input text - MANDATORY
  fontWeight: "500", // Medium weight for input text per design guidelines - MANDATORY
  color: "#505050", // Exact color from Figma
  paddingRight: 20, // Right padding
  paddingVertical: 0,
  height: 60, // Full height
  textAlignVertical: "center",
  lineHeight: 18, // 1.255em * 14px
})

// Password Field Styles (matching Figma design exactly)
const $passwordFieldContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#FFFFFF", // White background
  borderRadius: 20, // Increased border radius for more rounded corners
  height: 60, // Exact height from Figma
  width: "100%", // Full width instead of fixed 360px
  flexDirection: "row",
  alignItems: "center",
  borderWidth: 1, // Gray border instead of shadow
  borderColor: "#E5E5E5", // Light gray border color
  paddingHorizontal: 0,
})

const $passwordIcon: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginLeft: 22, // x:22 from Figma
  marginRight: 10, // Space between icon and text (46 - 22 - 14 = 10)
})

const $passwordInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  flex: 1,
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for input text - MANDATORY
  fontWeight: "500", // Medium weight for input text per design guidelines - MANDATORY
  color: "#505050", // Exact color from Figma
  paddingVertical: 0,
  height: 60, // Full height
  textAlignVertical: "center",
  lineHeight: 18, // 1.255em * 14px
})

const $eyeIconContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginRight: 22, // x:321 from right edge (360 - 321 - 15 = 24, but we use 22 for better alignment)
  padding: 4, // Touch area
})
