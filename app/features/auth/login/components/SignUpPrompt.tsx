import { FC } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface SignUpPromptProps {
  /**
   * Sign up handler
   */
  onSignUp?: () => void
  /**
   * Sign up prompt text
   */
  promptText?: string
}

export const SignUpPrompt: FC<SignUpPromptProps> = ({
  onSignUp,
  promptText = "Chưa có tài khoản? ĐĂNG KÝ",
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($signUpPrompt)}>
      <View style={themed($signUpContainer)}>
        <Text style={themed($promptText)}>Chưa có tài khoản? </Text>
        <TouchableOpacity onPress={onSignUp}>
          <Text style={themed($signUpText)}>ĐĂNG KÝ</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

// Styles
const $signUpPrompt: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  paddingVertical: 0,
  paddingHorizontal: 24, // 24px padding left and right as requested
  marginBottom: 24, // 24px spacing from bottom
})

const $signUpContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
})

const $promptText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for body text - MANDATORY
  fontWeight: "500", // Medium weight for body text per design guidelines - MANDATORY
  color: "#545454", // Exact color from Figma (gray)
  lineHeight: 18, // 1.255em * 14px
})

const $signUpText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for body text - MANDATORY
  fontWeight: "500", // Medium weight for body text per design guidelines - MANDATORY
  color: "#23408B", // Brand color for ĐĂNG KÝ
  lineHeight: 18, // 1.255em * 14px
  textDecorationLine: "underline", // Underline for SIGN UP
})
