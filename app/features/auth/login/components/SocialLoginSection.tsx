import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text, SocialLoginButton } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface SocialLoginSectionProps {
  /**
   * Whether any login is in progress
   */
  isLoading?: boolean
  /**
   * Google login handler
   */
  onGoogleLogin: () => void
  /**
   * Phone login handler
   */
  onPhoneLogin: () => void
  /**
   * Divider text
   */
  dividerText?: string
}

export const SocialLoginSection: FC<SocialLoginSectionProps> = ({
  isLoading = false,
  onGoogleLogin,
  onPhoneLogin,
  dividerText = "Hoặc",
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($socialLoginSection)}>
      {/* Divider text hidden when no social login options */}
      {/* <Text text={dividerText} style={themed($dividerText)} /> */}

      <View style={themed($socialButtonsContainer)}>
        {/* Google login button hidden as requested */}
        {/* <SocialLoginButton
          provider="google"
          text="Đăng nhập bằng Google"
          onPress={onGoogleLogin}
          loading={isLoading}
          style={themed($googleButton)}
          textStyle={themed($googleButtonText)}
          iconOnly={false}
        /> */}

        {/* Phone login button hidden as requested */}
        {/* <SocialLoginButton
          provider="phone"
          onPress={onPhoneLogin}
          style={themed($socialButton)}
          iconOnly={true}
        /> */}
      </View>
    </View>
  )
}

// Styles
const $socialLoginSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  marginBottom: 16, // 16px spacing to sign up text
  paddingHorizontal: 24, // 24px padding left and right as requested
})

const $dividerText: ThemedStyle<TextStyle> = ({ colors, typography, spacing }) => ({
  fontSize: 12, // Reduced from 14px for smaller text
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for body text - MANDATORY
  fontWeight: "500", // Medium weight for body text - MANDATORY
  color: "#9CA3AF", // Lighter gray color (was #545454)
  textAlign: "center",
  lineHeight: 16, // Adjusted for 12px font size
  marginBottom: 16, // 16px spacing to Google button
})

const $socialButtonsContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "center",
  width: "100%",
})

const $googleButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: "100%", // Full width like login button
  height: 60, // Same height as login button
  borderRadius: 30, // Same border radius as login button
  backgroundColor: "#FFFFFF", // White background
  borderWidth: 1, // Gray border instead of shadow
  borderColor: "#E5E5E5", // Light gray border color
  justifyContent: "center",
  alignItems: "center",
})

const $googleButtonText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium, // Medium weight for descriptions - MANDATORY
  fontWeight: "500", // Medium weight (500) for descriptions - MANDATORY
  fontSize: 14, // Same as description text in login screen
  lineHeight: 18, // Same as description text in login screen
  color: "#545454", // Same color as description text in login screen
  textAlign: "center",
})

const $socialButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 48, // Exact size from Figma
  height: 48, // Exact size from Figma
  borderRadius: 24, // Perfect circle
  backgroundColor: "#FFFFFF", // White background
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 }, // Exact shadow from Figma
  shadowOpacity: 0.1, // Exact shadow opacity from Figma
  shadowRadius: 10, // Exact shadow radius from Figma
  elevation: 4,
  justifyContent: "center",
  alignItems: "center",
})
