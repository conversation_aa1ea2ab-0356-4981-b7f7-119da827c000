import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface LoginAdditionalOptionsProps {
  /**
   * Forgot password handler
   */
  onForgotPassword: () => void
  /**
   * Forgot password text
   */
  forgotPasswordText?: string
}

export const LoginAdditionalOptions: FC<LoginAdditionalOptionsProps> = ({
  onForgotPassword,
  forgotPasswordText = "Forgot your password?",
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($additionalOptions)}>
      <Text
        testID="forgot-password-link"
        text={forgotPasswordText}
        style={themed($forgotPassword)}
        onPress={onForgotPassword}
      />
    </View>
  )
}

// Styles
const $additionalOptions: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  marginTop: spacing.md,
  paddingVertical: spacing.xs,
})

const $forgotPassword: ThemedStyle<TextStyle> = ({ colors, typography, spacing }) => ({
  fontSize: 16,
  fontFamily: typography.primary.light,
  color: colors.tint,
  textDecorationLine: "underline",
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.sm,
})
