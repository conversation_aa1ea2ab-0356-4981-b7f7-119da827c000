import { FC, useRef, useEffect } from "react"
import { View, Animated, ViewStyle, TextStyle, Image, ImageStyle } from "react-native"
import { Text } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface LoginHeaderProps {
  /**
   * Optional logo source for custom logo
   */
  logoSource?: any
  /**
   * Main heading text
   */
  headingText?: string
  /**
   * Subtitle text
   */
  subtitleText?: string
  /**
   * Animation duration in milliseconds
   */
  animationDuration?: number
}

export const LoginHeader: FC<LoginHeaderProps> = ({
  logoSource,
  headingText = "Đăng Nhập VANTIS!",
  subtitleText = "Đăng nhập vào tài khoản của bạn để tiếp tục các khóa học",
  animationDuration = 800,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current
  const slideAnim = useRef(new Animated.Value(50)).current

  const { themed } = useAppTheme()

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: animationDuration,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: animationDuration * 0.75,
        useNativeDriver: true,
      }),
    ]).start()
  }, [fadeAnim, slideAnim, animationDuration])

  return (
    <Animated.View
      style={[
        themed($welcomeSection),
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <View style={themed($logoContainer)}>
        <Image
          source={require("assets/images/logo/vantis-logo-border.png")}
          style={themed($vantisLogo)}
          resizeMode="contain"
        />
      </View>

      <View style={themed($titleContainer)}>
        <Text
          testID="login-heading"
          text={headingText}
          size="xl"
          weight="bold"
          style={themed($welcomeTitle)}
        />
        <Text
          text={subtitleText}
          preset="subheading"
          style={themed($welcomeSubtitle)}
        />
      </View>
    </Animated.View>
  )
}

// Styles
const $welcomeSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  paddingTop: 85, // y:129 - status bar height (44px)
  paddingBottom: 0, // No bottom padding
  paddingHorizontal: 0, // No horizontal padding - let components handle their own
})

const $logoContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  justifyContent: "center", // Center the logo section
  marginBottom: 24, // 24px spacing between logo and title
  alignSelf: "center", // Center the container
})

const $vantisLogo: ThemedStyle<ImageStyle> = () => ({
  width: 300, // 2x current size (150 -> 300)
  height: 120, // 2x current size (60 -> 120)
  alignSelf: "center",
})



const $titleContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignSelf: "stretch", // Full width
  alignItems: "flex-start", // Left align content
  paddingHorizontal: 24, // 24px padding left and right as requested
  marginBottom: 40, // 40px spacing to form (increased for more space)
})





const $welcomeTitle: ThemedStyle<TextStyle> = ({ colors, spacing, typography }) => ({
  color: "#202244", // Exact color from Figma
  textAlign: "left", // Left aligned per Figma
  marginBottom: 0, // No margin to avoid double spacing with subtitle
})

const $welcomeSubtitle: ThemedStyle<TextStyle> = ({ colors, spacing, typography }) => ({
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.medium, // Design guidelines: Be Vietnam Pro Medium (500) for descriptions - MANDATORY
  fontWeight: "500", // Medium weight for descriptions per design guidelines - MANDATORY
  color: "#545454", // Exact color from Figma
  textAlign: "left", // Left aligned per Figma
  lineHeight: 18, // 1.255em * 14px
  marginTop: 4, // 4px spacing from title
})
