import { observer } from "mobx-react-lite"
import { FC } from "react"
import { ViewStyle, View, Animated, KeyboardAvoidingView, Platform, TouchableOpacity, Text, TextStyle } from "react-native"
import { Screen, ErrorModal } from "@/components"
import { AuthStackScreenProps } from "@/navigators/AuthNavigator"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import {
  LoginHeader,
  LoginForm,
  SocialLoginSection,
  SignUpPrompt,
} from "../components"
import { useLoginLogic } from "../hooks"

interface LoginScreenProps extends AuthStackScreenProps<"Login"> {}

export const LoginScreen: FC<LoginScreenProps> = observer(function LoginScreen(props) {
  const { themed } = useAppTheme()
  
  const {
    // Form state
    authEmail,
    authPassword,
    isAuthPasswordHidden,
    attemptsCount,
    isLoading,
    error,

    // Form handlers
    setAuthEmail,
    setAuthPassword,
    setIsAuthPasswordHidden,

    // Actions
    login,
    handleGoogleLogin,
    handlePhoneLogin,

    // Error modal
    errorModal,
    hideErrorModal,
  } = useLoginLogic(props.navigation)

  const handleForgotPassword = () => {
    props.navigation.navigate("EmailRecovery")
  }

  const handleSignUp = () => {
    props.navigation.navigate("Signup")
  }

  const handleDeveloperSettings = () => {
    props.navigation.navigate("DeveloperSettings")
  }

  const togglePasswordVisibility = () => {
    setIsAuthPasswordHidden(!isAuthPasswordHidden)
  }

  return (
    <View style={themed($container)}>
      {/* Modern Background */}
      <View style={themed($modernBackground)} />

      <KeyboardAvoidingView
        style={themed($keyboardAvoidingView)}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <Screen
          preset="scroll"
          contentContainerStyle={themed($screenContentContainer)}
          safeAreaEdges={["top", "bottom"]}
          backgroundColor="transparent"
        >
          {/* Welcome Section */}
          <LoginHeader />

          {/* Form Section */}
          <LoginForm
            email={authEmail}
            password={authPassword}
            isPasswordHidden={isAuthPasswordHidden}
            isLoading={isLoading}
            error={error}
            attemptsCount={attemptsCount}
            onEmailChange={setAuthEmail}
            onPasswordChange={setAuthPassword}
            onTogglePasswordVisibility={togglePasswordVisibility}
            onSubmit={login}
            onForgotPassword={handleForgotPassword}
            onSignUp={handleSignUp}
          />

          <SocialLoginSection
            isLoading={isLoading}
            onGoogleLogin={handleGoogleLogin}
            onPhoneLogin={handlePhoneLogin}
          />

          {/* Developer Settings Button (Development Only) - HIDDEN */}
          {false && __DEV__ && (
            <TouchableOpacity
              style={themed($developerButton)}
              onPress={handleDeveloperSettings}
            >
              <Text style={themed($developerButtonText)}>🔧 Cài đặt API</Text>
            </TouchableOpacity>
          )}
        </Screen>
      </KeyboardAvoidingView>

      {/* Error Modal */}
      <ErrorModal
        visible={errorModal.visible}
        title={errorModal.title}
        message={errorModal.message}
        onClose={hideErrorModal}
        iconSource={require("../../../../../assets/icons/congratulations.png")}
      />
    </View>
  )
})

// Modern Styles
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $modernBackground: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#FFFFFF", // White background
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexGrow: 1,
  paddingHorizontal: 0, // No horizontal padding - components handle their own
  paddingTop: 0,
  paddingBottom: 34, // Bottom padding to match home indicator
})

const $developerButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#FF6B35",
  paddingHorizontal: 20,
  paddingVertical: 12,
  borderRadius: 8,
  marginHorizontal: 34,
  marginTop: 20,
  alignItems: "center",
})

const $developerButtonText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  color: "#FFFFFF",
  fontSize: 14,
  fontFamily: typography.primary.medium,
  fontWeight: "500",
})


