import { useState, useEffect } from "react"
import { useStores } from "@/models/helpers/useStores"
import { jwtDecode } from "jwt-decode"
import { googleAuthService } from "@/services/googleAuth"
import { mockAuthService } from "@/services/mockUsers"
import { authApi } from "@/services/api/authApi"
import { studentApi } from "@/services/api/studentApi"
import { extractUserInfoFromJWT } from "@/utils/jwtUtils"

export interface UseLoginLogicReturn {
  // Form state
  authEmail: string
  authPassword: string
  isAuthPasswordHidden: boolean
  isSubmitted: boolean
  attemptsCount: number
  isLoading: boolean
  error: string

  // Form handlers
  setAuthEmail: (email: string) => void
  setAuthPassword: (password: string) => void
  setIsAuthPasswordHidden: (hidden: boolean) => void

  // Actions
  login: () => Promise<void>
  handleGoogleLogin: () => Promise<void>
  handlePhoneLogin: () => void

  // Error modal
  errorModal: {
    visible: boolean
    title: string
    message: string
  }
  hideErrorModal: () => void
}

export function useLoginLogic(navigation: any): UseLoginLogicReturn {
  const [authPassword, setAuthPassword] = useState("")
  const [isAuthPasswordHidden, setIsAuthPasswordHidden] = useState(true)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [attemptsCount, setAttemptsCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [errorModal, setErrorModal] = useState({
    visible: false,
    title: "Lỗi",
    message: "",
  })

  const {
    authenticationStore: {
      authEmail,
      setAuthEmail,
      setAuthToken,
      validationError,
      setAuthMethod,
      setGoogleUser,
      setIsLoading: setAuthStoreLoading,
      setAuthError,
      setRole,
    },
    userStore: {
      setCurrentUser,
    },
  } = useStores()

  useEffect(() => {
    // Here is where you could fetch credentials from keychain or storage
    // and pre-fill the form fields.
    setAuthEmail("<EMAIL>")
    setAuthPassword("earnbase@X2025")

    // Return a "cleanup" function that React will run when the component unmounts
    return () => {
      setAuthPassword("")
      setAuthEmail("")
    }
  }, [setAuthEmail])

  const error = isSubmitted ? validationError : ""

  async function login() {
    console.log("🚀 ===== LOGIN BUTTON CLICKED =====")
    console.log("📧 Email:", authEmail)
    console.log("🔑 Password:", authPassword ? "***PROVIDED***" : "***EMPTY***")
    console.log("🔄 Attempts count:", attemptsCount + 1)

    setIsSubmitted(true)
    setIsLoading(true)
    setAttemptsCount(attemptsCount + 1)

    if (validationError) {
      console.log("❌ Validation error:", validationError)
      setIsLoading(false)
      return
    }

    console.log("✅ Validation passed, starting authentication...")

    try {
      // First try real API authentication
      console.log("🌐 ===== ATTEMPTING REAL API AUTHENTICATION =====")
      console.log("🔗 API URL: https://lms-dev.ebill.vn/api/v1/auth/sign-in")
      console.log("📤 Request credentials:", { username: authEmail, password: "***HIDDEN***" })

      const mfaCode = "123456" // Hardcoded MFA code from curl
      const apiResult = await authApi.signIn(
        authEmail,
        authPassword,
        false,
        mfaCode
      )

      console.log("📥 ===== API RESPONSE RECEIVED =====")
      console.log("📊 API Result kind:", apiResult.kind)

      if (apiResult.kind === "ok" && apiResult.data) {
        console.log("🎉 ===== API AUTHENTICATION SUCCESSFUL =====")
        console.log("🔑 Access token received:", apiResult.data.data.access_token.substring(0, 20) + "...")
        console.log("👤 User info:", {
          user_id: apiResult.data.data.user_id,
          token_type: apiResult.data.data.token_type,
          expires_in: apiResult.data.data.expires_in,
          login_method: apiResult.data.data.login_method
        })

        // Set authentication token from API response
        console.log("💾 Setting auth token in store...")
        setAuthToken(apiResult.data.data.access_token)

        // Decode the token to get the user's role and navigate
        try {
          const decodedToken: { role?: string } = jwtDecode(apiResult.data.data.access_token)
          const userRole = decodedToken.role

          if (userRole) {
            setRole(userRole)
            console.log(`User role set to: ${userRole}`)
            // Navigate to the main authenticated screen. The RootNavigator will handle the rest.
            navigation.navigate("Main")
          } else {
            console.warn("Role not found in token, navigating to Welcome screen")
            // Fallback navigation
            navigation.navigate("Welcome")
          }
        } catch (e) {
          console.error("Failed to decode token:", e)
          // Fallback navigation on error
          navigation.navigate("Welcome")
        }

        // Set auth token for student API
        studentApi.setAuthToken(apiResult.data.data.access_token)

        // Extract user information from JWT token
        const userIdFromApi = apiResult.data.data.user_id
        console.log("👤 User ID from API:", userIdFromApi)

        // Decode JWT token to extract user info
        console.log("🔍 Decoding JWT token to extract user info...")
        console.log("🔍 JWT Token:", apiResult.data.data.access_token.substring(0, 50) + "...")

        let jwtUserInfo = null
        try {
          jwtUserInfo = extractUserInfoFromJWT(apiResult.data.data.access_token)
          console.log("🎭 JWT User Info:", jwtUserInfo)
        } catch (jwtError) {
          console.error("❌ JWT decoding error:", jwtError)
          console.log("⚠️ Using fallback user info")
        }

        // Extract name from JWT
        const userNameFromJWT = jwtUserInfo?.name || "User"
        console.log("👤 User name from JWT:", userNameFromJWT)

        // Parse first name and last name
        const nameParts = userNameFromJWT.split(" ")
        const firstName = nameParts[0] || "User"
        const lastName = nameParts.slice(1).join(" ") || ""
        console.log("👤 Parsed name - First:", firstName, "Last:", lastName)

        // Use role from JWT if available, otherwise default to student
        let userRole: "student" | "instructor" | "admin" = jwtUserInfo?.role || "student"
        console.log("🎯 Final user role:", userRole)

        if (jwtUserInfo?.role) {
          console.log("✅ Role extracted from JWT token")
        } else {
          console.log("⚠️ No role in JWT, using default: student")
        }

        // Create user data with information from JWT and API
        const userData = {
          id: apiResult.data.data.user_id.toString(),
          email: jwtUserInfo?.email || authEmail, // Use email from JWT or login form
          firstName: firstName, // From JWT token
          lastName: lastName, // From JWT token
          role: userRole,
          avatar: undefined, // Will be loaded from profile API
          phoneNumber: undefined, // Will be loaded from profile API
          isActive: true, // Assume active since login was successful
          createdAt: new Date().toISOString(), // Placeholder
          updatedAt: new Date().toISOString(), // Placeholder
        }

        console.log("👤 Created user data:", {
          id: userData.id,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName,
          fullName: `${userData.firstName} ${userData.lastName}`.trim(),
          role: userData.role
        })

        console.log("👤 Setting user data in store:", userData)
        setCurrentUser(userData)

        // Debug: Check stores state after setting data
        console.log("🔍 ===== DEBUGGING STORES STATE =====")
        console.log("🔑 Auth token set:", !!apiResult.data.data.access_token)
        console.log("👤 User data set:", userData)

        // Force a small delay to ensure MobX state updates propagate
        setTimeout(() => {
          console.log("🔍 ===== STORES STATE AFTER UPDATE =====")
          console.log("🔐 isAuthenticated:", !!apiResult.data.data.access_token)
          console.log("👤 hasUser: (will be checked by navigator)")
          console.log("🧭 Navigation should now redirect to appropriate screen based on role:", userRole)
          console.log("🎯 Expected navigation:")
          if (userRole === "student") {
            console.log("   -> Student Navigator -> StudentTabs -> StudentHome (HomeScreen)")
          } else if (userRole === "instructor") {
            console.log("   -> Instructor Navigator -> InstructorTabs -> InstructorDashboard (InstructorHomeScreen)")
          }
        }, 200)

        // Clear form and errors
        console.log("🧹 Clearing form and errors...")
        setAuthPassword("")
        setAuthEmail("")
        setAuthError("")

        console.log("🎉 ===== LOGIN COMPLETED SUCCESSFULLY WITH API =====")
        return
      }

      // If API fails, show appropriate error message based on error type
      console.log("⚠️ ===== API AUTHENTICATION FAILED =====")
      console.log("❌ API error kind:", apiResult.kind)
      console.log("❌ API error details:", apiResult)

      // Handle different API error types with user-friendly messages
      if (apiResult.kind === "server") {
        const errorMessage = "Máy chủ đang bảo trì. Vui lòng thử lại sau ít phút."
        setAuthError(errorMessage)
        setErrorModal({
          visible: true,
          title: "Lỗi máy chủ",
          message: errorMessage,
        })
        return
      } else if (apiResult.kind === "cannot-connect") {
        const errorMessage = "Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng."
        setAuthError(errorMessage)
        setErrorModal({
          visible: true,
          title: "Lỗi kết nối",
          message: errorMessage,
        })
        return
      } else if (apiResult.kind === "timeout") {
        const errorMessage = "Kết nối quá chậm. Vui lòng thử lại."
        setAuthError(errorMessage)
        setErrorModal({
          visible: true,
          title: "Lỗi kết nối",
          message: errorMessage,
        })
        return
      } else if (apiResult.kind === "unauthorized") {
        setAuthError("Email hoặc mật khẩu không đúng.")
        return
      }

      console.log("🔄 ===== TRYING MOCK FALLBACK =====")

      // Try to authenticate with mock users as fallback
      const isUsername = !authEmail.includes('@')
      console.log("📧 Is username (no @):", isUsername)
      console.log("🔍 Attempting mock authentication...")

      let authResult

      if (isUsername) {
        console.log("👤 Authenticating by username...")
        authResult = await mockAuthService.authenticate({
          username: authEmail,
          password: authPassword
        })
      } else {
        console.log("📧 Authenticating by email...")
        authResult = await mockAuthService.authenticateByEmail(authEmail, authPassword)
      }

      console.log("📥 Mock auth result:", authResult)

      if (authResult.success && authResult.user) {
        console.log("🎉 ===== MOCK AUTHENTICATION SUCCESSFUL =====")
        console.log("👤 Mock user data:", authResult.user)

        // Set authentication token (mock)
        const mockToken = String(Date.now())
        console.log("🔑 Setting mock token:", mockToken)
        setAuthToken(mockToken)

        // Set current user in UserStore
        const mockUserData = {
          id: authResult.user.id,
          email: authResult.user.email,
          firstName: authResult.user.firstName,
          lastName: authResult.user.lastName,
          role: authResult.user.role,
          avatar: authResult.user.avatar,
          phoneNumber: authResult.user.phoneNumber,
          isActive: authResult.user.isActive,
          createdAt: authResult.user.createdAt,
          updatedAt: authResult.user.updatedAt,
        }

        console.log("👤 Setting mock user data in store:", mockUserData)
        setCurrentUser(mockUserData)

        // Clear form
        console.log("🧹 Clearing form...")
        setAuthPassword("")
        setAuthEmail("")
        setAuthError("")

        console.log("🎉 ===== LOGIN COMPLETED SUCCESSFULLY WITH MOCK =====")
      } else {
        console.log("❌ ===== MOCK AUTHENTICATION FAILED =====")
        console.log("❌ Mock error:", authResult.error)
        // Show error
        setAuthError(authResult.error || "Đăng nhập thất bại")
      }
    } catch (error) {
      console.log("💥 ===== UNEXPECTED ERROR OCCURRED =====")
      console.error("❌ Login error:", error)
      console.error("❌ Error stack:", error instanceof Error ? error.stack : "No stack trace")
      setAuthError("Có lỗi xảy ra khi đăng nhập")
    } finally {
      console.log("🏁 ===== LOGIN PROCESS FINISHED =====")
      console.log("🔄 Setting loading state to false...")
      setIsSubmitted(false)
      setIsLoading(false)
      console.log("✅ Login process cleanup completed")
    }
  }

  async function handleGoogleLogin() {
    setAuthStoreLoading(true)
    setAuthError("")

    try {
      // Configure Google Sign-In if not already configured
      // You'll need to add your web client ID from Google Cloud Console
      const webClientId = "YOUR_WEB_CLIENT_ID_HERE" // Replace with actual web client ID
      await googleAuthService.configure(webClientId)

      const result = await googleAuthService.signIn()

      if (result.success && result.user) {
        setAuthMethod("google")
        setGoogleUser(result.user)

        // Get Google tokens for API authentication
        const tokens = await googleAuthService.getTokens()
        if (tokens) {
          setAuthToken(tokens.idToken || tokens.accessToken)
        } else {
          throw new Error("Failed to get authentication tokens")
        }
      } else {
        setAuthError(result.error || "Google sign-in failed")
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Google sign-in failed"
      setAuthError(errorMessage)
      console.error("Google login error:", error)
    } finally {
      setAuthStoreLoading(false)
    }
  }

  function handlePhoneLogin() {
    setAuthMethod("phone")
    navigation.navigate("PhoneLogin")
  }

  const hideErrorModal = () => {
    setErrorModal(prev => ({ ...prev, visible: false }))
  }

  return {
    // Form state
    authEmail,
    authPassword,
    isAuthPasswordHidden,
    isSubmitted,
    attemptsCount,
    isLoading,
    error,

    // Form handlers
    setAuthEmail,
    setAuthPassword,
    setIsAuthPasswordHidden,

    // Actions
    login,
    handleGoogleLogin,
    handlePhoneLogin,

    // Error modal
    errorModal,
    hideErrorModal,
  }
}
