# Login Feature

This directory contains all components, hooks, and screens related to the Login functionality of the LMS app.

## Structure

```
app/features/login/
├── components/           # Login-specific components
│   ├── LoginHeader.tsx           # Logo, heading, subtitle with animations
│   ├── LoginForm.tsx             # Email/password fields and login button
│   ├── SocialLoginSection.tsx    # Google and phone login options
│   ├── LoginAdditionalOptions.tsx # Forgot password link
│   └── index.ts                  # Component exports
├── hooks/               # Login-specific hooks
│   ├── useLoginLogic.ts          # Authentication logic and state management
│   └── index.ts                  # Hook exports
├── screens/             # Login screens
│   ├── LoginScreen.tsx           # Main login screen (refactored)
│   └── index.ts                  # Screen exports
├── index.ts            # Feature exports
└── README.md           # This file
```

## Components

### LoginHeader (~100 lines)
- Displays app logo, welcome heading, and subtitle
- Includes entrance animations (fade and slide)
- Configurable via props (logoSource, headingText, subtitleText, animationDuration)
- Reusable for other auth screens

### LoginForm (~140 lines)
- Email and password input fields
- Form validation and error display
- Login button with loading state
- Password visibility toggle
- Configurable via props (all form state and handlers)

### SocialLoginSection (~70 lines)
- Divider with "or" text
- Google and phone login buttons
- Loading state support
- Configurable via props (handlers, loading state, divider text)

### LoginAdditionalOptions (~40 lines)
- Forgot password link
- Configurable via props (handler, text)
- Simple and focused component

## Hooks

### useLoginLogic (~120 lines)
- Extracted all authentication business logic
- Form state management (email, password, loading, errors)
- Login, Google login, and phone login handlers
- Integration with MobX stores
- Returns clean interface for components

## Screens

### LoginScreen (~120 lines)
- Main layout and composition
- Uses all above components and hook
- Reduced from original 473 lines
- Clean separation of concerns
- Pure composition with minimal logic

## Usage

```typescript
// Import the entire login feature
import { LoginScreen, useLoginLogic, LoginHeader } from "@/features/login"

// Or import specific components
import { LoginForm, SocialLoginSection } from "@/features/login/components"
import { useLoginLogic } from "@/features/login/hooks"
```

## Benefits

- ✅ Each component < 200 lines (follows user preference)
- ✅ Complete separation of UI and business logic
- ✅ Custom hook can be tested independently
- ✅ Components are highly reusable
- ✅ Easier testing and maintenance
- ✅ Maximum modularity achieved (473 → 120 lines for main screen)
- ✅ Business logic completely extracted and reusable
- ✅ Clean component interfaces with comprehensive props
- ✅ Organized by feature for better scalability

## Dependencies

- Uses `@/components` for base UI components
- Uses `@/models` for MobX state management
- Uses `@/services/googleAuth` for Google authentication
- Uses `@/theme` for styling system
- Integrates with navigation system

## Migration

The original `app/screens/LoginScreen.tsx` (473 lines) has been refactored into:
- LoginHeader: 100 lines
- LoginForm: 140 lines  
- SocialLoginSection: 70 lines
- LoginAdditionalOptions: 40 lines
- useLoginLogic: 120 lines
- LoginScreen (refactored): 120 lines

Total: ~590 lines across 6 focused files vs 473 lines in 1 monolithic file.
The increase in total lines is due to better separation, reusability, and maintainability.
