import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle, Platform, KeyboardAvoidingView } from "react-native"
import { Screen } from "@/components"
import { AuthStackScreenProps } from "@/navigators"
import {
  ForgotPasswordHeader,
  EmailRecoveryForm,
} from "../components"
import { useEmailRecovery } from "../hooks"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"

interface EmailRecoveryScreenProps extends AuthStackScreenProps<"EmailRecovery"> {}

export const EmailRecoveryScreen: FC<EmailRecoveryScreenProps> = observer(
  function EmailRecoveryScreen(props) {
    const { navigation } = props
    const { themed } = useAppTheme()

    const {
      // State
      email,
      isLoading,
      error,

      // Actions
      setEmail,
      handleSendResetEmail,
      handleBackToForgotPassword,
    } = useEmailRecovery({ navigation })

    return (
      <View style={themed($container)}>
        {/* Background */}
        <View style={themed($background)} />

        <KeyboardAvoidingView
          style={themed($keyboardAvoidingView)}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <Screen
            preset="scroll"
            contentContainerStyle={themed($screenContentContainer)}
            safeAreaEdges={["top", "bottom"]}
            backgroundColor="transparent"
          >
            {/* Header */}
            <ForgotPasswordHeader onBack={handleBackToForgotPassword} />

            {/* Email Recovery Form */}
            <EmailRecoveryForm
              email={email}
              isLoading={isLoading}
              error={error}
              onEmailChange={setEmail}
              onSubmit={handleSendResetEmail}
            />
          </Screen>
        </KeyboardAvoidingView>
      </View>
    )
  }
)

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $background: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#FFFFFF", // White background
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 24, // 24px padding trái phải cho toàn bộ nội dung
  paddingBottom: 50, // Space at bottom
})
