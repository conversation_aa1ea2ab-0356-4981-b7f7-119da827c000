import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle, Platform, KeyboardAvoidingView } from "react-native"
import { Screen } from "@/components"
import { AuthStackScreenProps } from "@/navigators"
import {
  ForgotPasswordHeader,
  VerifyCodeForm,
} from "../components"
import { useVerifyCode } from "../hooks"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"

interface VerifyCodeScreenProps extends AuthStackScreenProps<"VerifyCode"> {}

export const VerifyCodeScreen: FC<VerifyCodeScreenProps> = observer(
  function VerifyCodeScreen(props) {
    const { navigation, route } = props
    const { themed } = useAppTheme()
    
    // Get params from navigation
    const { method, contact } = route.params

    const {
      // State
      code,
      isLoading,
      error,
      resendTimer,
      canResend,

      // Actions
      setCode,
      handleVerifyCode,
      handleResendCode,
      handleBackToRecovery,
    } = useVerifyCode({ navigation, method, contact })

    return (
      <View style={themed($container)}>
        {/* Background */}
        <View style={themed($background)} />

        <KeyboardAvoidingView
          style={themed($keyboardAvoidingView)}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <Screen
            preset="scroll"
            contentContainerStyle={themed($screenContentContainer)}
            safeAreaEdges={["top", "bottom"]}
            backgroundColor="transparent"
          >
            {/* Header */}
            <ForgotPasswordHeader onBack={handleBackToRecovery} />

            {/* Verify Code Form */}
            <VerifyCodeForm
              code={code}
              isLoading={isLoading}
              error={error}
              contact={contact}
              method={method}
              resendTimer={resendTimer}
              canResend={canResend}
              onCodeChange={setCode}
              onVerify={handleVerifyCode}
              onResend={handleResendCode}
            />
          </Screen>
        </KeyboardAvoidingView>
      </View>
    )
  }
)

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $background: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#F5F9FF", // Exact background color từ Figma
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 0, // Components handle their own padding
  paddingBottom: 50, // Space at bottom
})
