import { observer } from "mobx-react-lite"
import { FC, useState } from "react"
import { View, ViewStyle, Platform, KeyboardAvoidingView, TouchableOpacity, TextStyle } from "react-native"
import { Screen, TextField, Button, Text, Icon } from "@/components"
import { AuthStackScreenProps } from "@/navigators"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"
import { useForgotPassword } from "../hooks"

interface ForgotPasswordScreenProps extends AuthStackScreenProps<"ForgotPassword"> {}

export const ForgotPasswordScreen: FC<ForgotPasswordScreenProps> = observer(
  function ForgotPasswordScreen(props) {
    const { navigation } = props
    const { themed } = useAppTheme()

    const {
      // State
      selectedOption,
      isLoading,

      // Actions
      handleOptionSelect,
      handleContinue,
      handleBackToLogin,
    } = useForgotPassword({ navigation })

    return (
      <View style={themed($container)}>
        {/* Background */}
        <View style={themed($background)} />

        <KeyboardAvoidingView
          style={themed($keyboardAvoidingView)}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <Screen
            preset="scroll"
            contentContainerStyle={themed($screenContentContainer)}
            safeAreaEdges={["top", "bottom"]}
            backgroundColor="transparent"
          >
            {/* Header */}
            <ForgotPasswordHeader onBack={handleBackToLogin} />

            {/* Contact Options */}
            <ContactOptions
              onOptionSelect={handleOptionSelect}
              selectedOption={selectedOption}
            />
          </Screen>
        </KeyboardAvoidingView>

        {/* Continue Button - Fixed at bottom */}
        <ContinueButton
          onPress={handleContinue}
          disabled={!selectedOption}
          isLoading={false}
        />
      </View>
    )
  }
)

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $background: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#F5F9FF", // Exact background color từ Figma
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 0, // Components handle their own padding
  paddingBottom: 200, // Space for fixed button
})
