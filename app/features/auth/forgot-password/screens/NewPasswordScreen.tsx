import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle, Platform, KeyboardAvoidingView } from "react-native"
import { Screen } from "@/components"
import { AuthStackScreenProps } from "@/navigators"
import {
  NewPasswordHeader,
  NewPasswordForm,
  AlertModal,
} from "../components"
import { useNewPassword } from "../hooks"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"

interface NewPasswordScreenProps extends AuthStackScreenProps<"NewPassword"> {}

export const NewPasswordScreen: FC<NewPasswordScreenProps> = observer(
  function NewPasswordScreen(props) {
    const { navigation, route } = props
    const { themed } = useAppTheme()
    
    // Get params from navigation
    const { method, contact } = route.params

    const {
      // State
      password,
      confirmPassword,
      isLoading,
      error,
      showPassword,
      showConfirmPassword,
      showAlertModal,

      // Actions
      setPassword,
      setConfirmPassword,
      toggleShowPassword,
      toggleShowConfirmPassword,
      handleCreatePassword,
      handleBackToVerifyCode,
      handleAlertModalOk,
    } = useNewPassword({ navigation, method, contact })

    return (
      <View style={themed($container)}>
        {/* Background */}
        <View style={themed($background)} />

        <KeyboardAvoidingView
          style={themed($keyboardAvoidingView)}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <Screen
            preset="scroll"
            contentContainerStyle={themed($screenContentContainer)}
            safeAreaEdges={["top", "bottom"]}
            backgroundColor="transparent"
          >
            {/* Header */}
            <NewPasswordHeader onBack={handleBackToVerifyCode} />

            {/* New Password Form */}
            <NewPasswordForm
              password={password}
              confirmPassword={confirmPassword}
              isLoading={isLoading}
              error={error}
              showPassword={showPassword}
              showConfirmPassword={showConfirmPassword}
              onPasswordChange={setPassword}
              onConfirmPasswordChange={setConfirmPassword}
              onToggleShowPassword={toggleShowPassword}
              onToggleShowConfirmPassword={toggleShowConfirmPassword}
              onSubmit={handleCreatePassword}
            />
          </Screen>
        </KeyboardAvoidingView>

        {/* Alert Modal */}
        <AlertModal
          visible={showAlertModal}
          title="Congratulations"
          message="Password reset successful! Redirecting to login..."
          okText="Back to Sign In"
          onOk={handleAlertModalOk}
        />
      </View>
    )
  }
)

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $background: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#F5F9FF", // Exact background color từ Figma
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 0, // Components handle their own padding
  paddingBottom: 50, // Space at bottom
})
