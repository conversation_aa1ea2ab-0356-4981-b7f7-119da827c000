import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle, Platform, KeyboardAvoidingView } from "react-native"
import { Screen } from "@/components"
import { AuthStackScreenProps } from "@/navigators"
import {
  ForgotPasswordHeader,
  PhoneRecoveryForm,
} from "../components"
import { usePhoneRecovery } from "../hooks"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"

interface PhoneRecoveryScreenProps extends AuthStackScreenProps<"PhoneRecovery"> {}

export const PhoneRecoveryScreen: FC<PhoneRecoveryScreenProps> = observer(
  function PhoneRecoveryScreen(props) {
    const { navigation } = props
    const { themed } = useAppTheme()

    const {
      // State
      phoneNumber,
      isLoading,
      error,

      // Actions
      setPhoneNumber,
      handleSendResetSMS,
      handleBackToForgotPassword,
    } = usePhoneRecovery({ navigation })

    return (
      <View style={themed($container)}>
        {/* Background */}
        <View style={themed($background)} />

        <KeyboardAvoidingView
          style={themed($keyboardAvoidingView)}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <Screen
            preset="scroll"
            contentContainerStyle={themed($screenContentContainer)}
            safeAreaEdges={["top", "bottom"]}
            backgroundColor="transparent"
          >
            {/* Header */}
            <ForgotPasswordHeader onBack={handleBackToForgotPassword} />

            {/* Phone Recovery Form */}
            <PhoneRecoveryForm
              phoneNumber={phoneNumber}
              isLoading={isLoading}
              error={error}
              onPhoneNumberChange={setPhoneNumber}
              onSubmit={handleSendResetSMS}
            />
          </Screen>
        </KeyboardAvoidingView>
      </View>
    )
  }
)

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $background: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#F5F9FF", // Exact background color từ Figma
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 0, // Components handle their own padding
  paddingBottom: 50, // Space at bottom
})
