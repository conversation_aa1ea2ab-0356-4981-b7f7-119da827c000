import { FC, useRef, useCallback } from "react"
import { TextInput, ViewStyle, TextStyle, View, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface VerifyCodeFormProps {
  /**
   * Verification code value
   */
  code: string
  /**
   * Whether the form is loading
   */
  isLoading: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Contact info (email or phone)
   */
  contact: string
  /**
   * Recovery method (email or sms)
   */
  method: "email" | "sms"
  /**
   * Resend timer countdown
   */
  resendTimer: number
  /**
   * Whether can resend code
   */
  canResend: boolean
  /**
   * Code change handler
   */
  onCodeChange: (code: string) => void
  /**
   * Verify handler
   */
  onVerify: () => void
  /**
   * Resend code handler
   */
  onResend: () => void
}

export const VerifyCodeForm: FC<VerifyCodeFormProps> = ({
  code,
  isLoading,
  error,
  contact,
  method,
  resendTimer,
  canResend,
  onCodeChange,
  onVerify,
  onResend,
}) => {
  const codeInputs = useRef<(TextInput | null)[]>([])
  const { themed } = useAppTheme()

  // Split code into individual digits
  const codeDigits = code.padEnd(4, ' ').split('').slice(0, 4)

  const handleCodeChange = useCallback((text: string, index: number) => {
    const newCode = [...codeDigits]
    newCode[index] = text
    const updatedCode = newCode.join('').replace(/\s/g, '')
    onCodeChange(updatedCode)

    // Auto focus next input
    if (text && index < 3) {
      codeInputs.current[index + 1]?.focus()
    }
  }, [codeDigits, onCodeChange])

  const handleKeyPress = useCallback((key: string, index: number) => {
    if (key === 'Backspace' && !codeDigits[index] && index > 0) {
      codeInputs.current[index - 1]?.focus()
    }
  }, [codeDigits])

  const maskContact = (contact: string, method: "email" | "sms"): string => {
    if (method === "email") {
      const [name, domain] = contact.split('@')
      if (name && domain) {
        const maskedName = name.charAt(0) + '*'.repeat(Math.max(0, name.length - 2)) + name.charAt(name.length - 1)
        return `${maskedName}@${domain}`
      }
    } else {
      // Phone number masking
      if (contact.length >= 4) {
        return contact.slice(0, -4).replace(/\d/g, '*') + contact.slice(-4)
      }
    }
    return contact
  }

  return (
    <View style={themed($formContainer)}>
      {/* Description Text */}
      <Text style={themed($descriptionText)}>
        Code has been sent to {maskContact(contact, method)}
      </Text>

      {/* Code Input Fields */}
      <View style={themed($codeContainer)}>
        {codeDigits.map((digit, index) => (
          <View key={index} style={themed($codeInputContainer)}>
            <TextInput
              ref={(ref) => (codeInputs.current[index] = ref)}
              value={digit.trim()}
              onChangeText={(text) => handleCodeChange(text, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              style={themed($codeInput)}
              maxLength={1}
              keyboardType="numeric"
              textAlign="center"
              secureTextEntry={digit.trim() !== '' && digit !== ' '}
              placeholder=""
            />
            {digit.trim() !== '' && digit !== ' ' && (
              <Text style={themed($codeDisplayText)}>*</Text>
            )}
          </View>
        ))}
      </View>

      {/* Error Message */}
      {error && (
        <View style={themed($errorContainer)}>
          <Text style={themed($errorText)}>{error}</Text>
        </View>
      )}

      {/* Resend Code */}
      <TouchableOpacity
        style={themed($resendContainer)}
        onPress={onResend}
        disabled={!canResend}
        activeOpacity={0.8}
      >
        <Text style={themed($resendText)}>
          {canResend ? "Resend Code" : `Resend Code in ${resendTimer}s`}
        </Text>
      </TouchableOpacity>

      {/* Verify Button */}
      <TouchableOpacity
        testID="verify-button"
        style={themed($verifyButton)}
        onPress={onVerify}
        disabled={isLoading || code.length !== 4}
        activeOpacity={0.8}
      >
        <Text style={themed($verifyButtonText)}>
          {isLoading ? "Verifying..." : "Verify"}
        </Text>
        <View style={themed($buttonIconContainer)}>
          <Icon
            icon="caretRight"
            size={21}
            color="#0961F5"
          />
        </View>
      </TouchableOpacity>
    </View>
  )
}

// Styles theo Figma design
const $formContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Exact padding from Figma (x:34)
  alignItems: "center", // Center all form elements
})

const $descriptionText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for descriptions - MANDATORY
  fontWeight: "300", // Light weight for descriptions - MANDATORY
  fontSize: 14, // Exact size từ Figma
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Exact color từ Figma
  textAlign: "center",
  marginTop: 50, // Reduced spacing to fit on screen
  marginBottom: 40, // Space before code inputs
  paddingHorizontal: 10, // Padding for text
})

const $codeContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  justifyContent: "space-between",
  width: "100%", // Full width
  marginBottom: 30, // Space before resend
})

const $codeInputContainer: ThemedStyle<ViewStyle> = () => ({
  position: "relative",
  width: 75, // Exact width from Figma
  height: 60, // Exact height from Figma
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 12, // Exact border radius from Figma
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5, // Android shadow
  alignItems: "center",
  justifyContent: "center",
})

const $codeInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  position: "absolute",
  width: "100%",
  height: "100%",
  fontSize: 22, // Exact from Figma
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for input text - MANDATORY
  fontWeight: "800", // Bold weight for code digits
  color: "transparent", // Hide actual input text
  textAlign: "center",
  textAlignVertical: "center",
})

const $codeDisplayText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontSize: 30, // Exact from Figma for asterisk
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for input text - MANDATORY
  fontWeight: "800", // Bold weight for asterisk
  color: "#505050", // Exact color from Figma
  textAlign: "center",
  position: "absolute",
})

const $errorContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 20,
  paddingHorizontal: 16,
})

const $errorText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for helper text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  color: colors.error,
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  textAlign: "center",
})

const $resendContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 40, // Space before verify button
})

const $resendText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for descriptions - MANDATORY
  fontWeight: "700", // Bold weight for resend text
  fontSize: 14, // Exact size từ Figma
  lineHeight: 18, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Exact color từ Figma
  textAlign: "center",
})

const $verifyButton: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  backgroundColor: "#0961F5", // Primary blue from Figma
  borderRadius: 30, // Exact border radius from Figma
  paddingHorizontal: 30,
  height: 60, // Exact height from Figma
  width: "100%", // Full width
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8, // Android shadow
})

const $verifyButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for button text - MANDATORY
  fontWeight: "600", // Semi-bold weight for button text
  fontSize: 18,
  lineHeight: 20, // Design guidelines button text line height
  letterSpacing: 0.2, // Design guidelines button text letter spacing
  color: "#FFFFFF",
  flex: 1,
  textAlign: "center",
})

const $buttonIconContainer: ThemedStyle<ViewStyle> = () => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  alignItems: "center",
  justifyContent: "center",
})
