import { FC, useRef, useCallback } from "react"
import { TextInput, ViewStyle, TextStyle, View, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface PhoneRecoveryFormProps {
  /**
   * Phone number value
   */
  phoneNumber: string
  /**
   * Whether the form is loading
   */
  isLoading: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Phone number change handler
   */
  onPhoneNumberChange: (phoneNumber: string) => void
  /**
   * Form submit handler
   */
  onSubmit: () => void
}

export const PhoneRecoveryForm: FC<PhoneRecoveryFormProps> = ({
  phoneNumber,
  isLoading,
  error,
  onPhoneNumberChange,
  onSubmit,
}) => {
  const phoneInput = useRef<TextInput>(null)
  const { themed } = useAppTheme()

  // Wrap onPhoneNumberChange để đảm bảo re-render
  const handlePhoneNumberChange = useCallback((text: string) => {
    onPhoneNumberChange(text)
  }, [onPhoneNumberChange])

  return (
    <View style={themed($formContainer)}>
      {/* Description Text */}
      <Text style={themed($descriptionText)}>
        Enter your phone number to receive a verification code for password reset
      </Text>

      {/* Phone Input Field */}
      <View style={themed($textField)}>
        <View style={themed($phoneFieldContainer)}>
          <Icon
            icon="phone" // Phone icon
            size={19}
            color="#545454"
            style={themed($phoneIcon)}
          />
          <TextInput
            ref={phoneInput}
            value={phoneNumber}
            onChangeText={handlePhoneNumberChange}
            style={themed($phoneInput)}
            placeholder="Phone Number"
            placeholderTextColor="#505050"
            autoCapitalize="none"
            autoComplete="tel"
            autoCorrect={false}
            keyboardType="phone-pad"
            onSubmitEditing={onSubmit}
          />
        </View>
      </View>

      {/* Error Message */}
      {error && (
        <View style={themed($errorContainer)}>
          <Text style={themed($errorText)}>{error}</Text>
        </View>
      )}

      {/* Continue Button */}
      <TouchableOpacity
        testID="continue-button"
        style={themed($continueButton)}
        onPress={onSubmit}
        disabled={isLoading || !phoneNumber.trim()}
        activeOpacity={0.8}
      >
        <Text style={themed($continueButtonText)}>
          {isLoading ? "Sending..." : "Continue"}
        </Text>
        <View style={themed($buttonIconContainer)}>
          <Icon
            icon="caretRight"
            size={21}
            color="#0961F5"
          />
        </View>
      </TouchableOpacity>
    </View>
  )
}

// Styles theo Figma design và pattern của EmailRecoveryForm
const $formContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Exact padding from Figma (x:34)
  alignItems: "center", // Center all form elements
})

const $descriptionText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for descriptions - MANDATORY
  fontWeight: "300", // Light weight for descriptions - MANDATORY
  fontSize: 14, // Exact size từ Figma
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Exact color từ Figma
  textAlign: "center",
  marginTop: 50, // Reduced spacing to fit on screen
  marginBottom: 30, // Space before input
  paddingHorizontal: 10, // Padding for text
})

const $textField: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  marginBottom: 20, // Exact spacing from Figma
})

const $phoneFieldContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 12, // Exact border radius from Figma
  paddingHorizontal: 20, // Exact padding from Figma
  height: 60, // Exact height from Figma
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5, // Android shadow
})

const $phoneIcon: ThemedStyle<ViewStyle> = () => ({
  marginRight: 12, // Space between icon and input
})

const $phoneInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  flex: 1,
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for input text - MANDATORY
  fontWeight: "300", // Light weight for input text - MANDATORY
  color: "#505050", // Exact color from Figma
  paddingRight: 20, // Right padding
  paddingVertical: 0,
  height: 60, // Full height
  textAlignVertical: "center",
  lineHeight: 18, // 1.255em * 14px
})

const $errorContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 20,
  paddingHorizontal: 16,
})

const $errorText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for helper text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  color: colors.error,
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  textAlign: "center",
})

const $continueButton: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  backgroundColor: "#0961F5", // Primary blue from Figma
  borderRadius: 30, // Exact border radius from Figma
  paddingHorizontal: 30,
  height: 60, // Exact height from Figma
  width: "100%", // Full width
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8, // Android shadow
  marginTop: 40, // Space from input field
})

const $continueButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for button text - MANDATORY
  fontWeight: "300", // Light weight for button text - MANDATORY
  fontSize: 18,
  lineHeight: 20, // Design guidelines button text line height
  letterSpacing: 0.2, // Design guidelines button text letter spacing
  color: "#FFFFFF",
  flex: 1,
  textAlign: "center",
})

const $buttonIconContainer: ThemedStyle<ViewStyle> = () => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  alignItems: "center",
  justifyContent: "center",
})
