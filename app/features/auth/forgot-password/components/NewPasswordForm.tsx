import { FC, useRef, useCallback } from "react"
import { TextInput, ViewStyle, TextStyle, View, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface NewPasswordFormProps {
  /**
   * Password value
   */
  password: string
  /**
   * Confirm password value
   */
  confirmPassword: string
  /**
   * Whether the form is loading
   */
  isLoading: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Whether to show password
   */
  showPassword: boolean
  /**
   * Whether to show confirm password
   */
  showConfirmPassword: boolean
  /**
   * Password change handler
   */
  onPasswordChange: (password: string) => void
  /**
   * Confirm password change handler
   */
  onConfirmPasswordChange: (confirmPassword: string) => void
  /**
   * Toggle show password handler
   */
  onToggleShowPassword: () => void
  /**
   * Toggle show confirm password handler
   */
  onToggleShowConfirmPassword: () => void
  /**
   * Form submit handler
   */
  onSubmit: () => void
}

export const NewPasswordForm: FC<NewPasswordFormProps> = ({
  password,
  confirmPassword,
  isLoading,
  error,
  showPassword,
  showConfirmPassword,
  onPasswordChange,
  onConfirmPasswordChange,
  onToggleShowPassword,
  onToggleShowConfirmPassword,
  onSubmit,
}) => {
  const passwordInput = useRef<TextInput>(null)
  const confirmPasswordInput = useRef<TextInput>(null)
  const { themed } = useAppTheme()

  // Wrap handlers để đảm bảo re-render
  const handlePasswordChange = useCallback((text: string) => {
    onPasswordChange(text)
  }, [onPasswordChange])

  const handleConfirmPasswordChange = useCallback((text: string) => {
    onConfirmPasswordChange(text)
  }, [onConfirmPasswordChange])

  return (
    <View style={themed($formContainer)}>
      {/* Title Text */}
      <Text style={themed($titleText)}>
        Create Your New Password
      </Text>

      {/* Password Input Field */}
      <View style={themed($textField)}>
        <View style={themed($passwordFieldContainer)}>
          <Icon
            icon="lock" // Lock icon
            size={19}
            color="#545454"
            style={themed($passwordIcon)}
          />
          <TextInput
            ref={passwordInput}
            value={password}
            onChangeText={handlePasswordChange}
            style={themed($passwordInput)}
            placeholder="Password"
            placeholderTextColor="#505050"
            autoCapitalize="none"
            autoComplete="new-password"
            autoCorrect={false}
            secureTextEntry={!showPassword}
            onSubmitEditing={() => confirmPasswordInput.current?.focus()}
            returnKeyType="next"
          />
          <TouchableOpacity
            onPress={onToggleShowPassword}
            style={themed($eyeButton)}
            activeOpacity={0.8}
          >
            <Icon
              icon={showPassword ? "view" : "hidden"}
              size={15}
              color="#545454"
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Confirm Password Input Field */}
      <View style={themed($textField)}>
        <View style={themed($passwordFieldContainer)}>
          <Icon
            icon="lock" // Lock icon
            size={19}
            color="#545454"
            style={themed($passwordIcon)}
          />
          <TextInput
            ref={confirmPasswordInput}
            value={confirmPassword}
            onChangeText={handleConfirmPasswordChange}
            style={themed($passwordInput)}
            placeholder="Confirm Password"
            placeholderTextColor="#505050"
            autoCapitalize="none"
            autoComplete="new-password"
            autoCorrect={false}
            secureTextEntry={!showConfirmPassword}
            onSubmitEditing={onSubmit}
            returnKeyType="done"
          />
          <TouchableOpacity
            onPress={onToggleShowConfirmPassword}
            style={themed($eyeButton)}
            activeOpacity={0.8}
          >
            <Icon
              icon={showConfirmPassword ? "view" : "hidden"}
              size={15}
              color="#545454"
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Error Message */}
      {error && (
        <View style={themed($errorContainer)}>
          <Text style={themed($errorText)}>{error}</Text>
        </View>
      )}

      {/* Continue Button */}
      <TouchableOpacity
        testID="continue-button"
        style={themed($continueButton)}
        onPress={onSubmit}
        disabled={isLoading || !password.trim() || !confirmPassword.trim()}
        activeOpacity={0.8}
      >
        <Text style={themed($continueButtonText)}>
          {isLoading ? "Creating..." : "Continue"}
        </Text>
        <View style={themed($buttonIconContainer)}>
          <Icon
            icon="caretRight"
            size={21}
            color="#0961F5"
          />
        </View>
      </TouchableOpacity>
    </View>
  )
}

// Styles theo Figma design
const $formContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Exact padding from Figma (x:34)
  alignItems: "center", // Center all form elements
})

const $titleText: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for titles
  fontWeight: "600", // Semi-bold weight for titles
  fontSize: 19, // Exact size từ Figma
  lineHeight: 27, // Design guidelines title line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: colors.text, // Primary text color from updated design guidelines
  textAlign: "left",
  alignSelf: "flex-start", // Align to left
  marginTop: 50, // Reduced spacing to fit on screen
  marginBottom: 30, // Space before inputs
})

const $textField: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  marginBottom: 20, // Exact spacing from Figma
})

const $passwordFieldContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 12, // Exact border radius from Figma
  paddingHorizontal: 20, // Exact padding from Figma
  height: 60, // Exact height from Figma
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5, // Android shadow
})

const $passwordIcon: ThemedStyle<ViewStyle> = () => ({
  marginRight: 12, // Space between icon and input
})

const $passwordInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  flex: 1,
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for input text - MANDATORY
  fontWeight: "700", // Bold weight for password text
  color: "#505050", // Exact color from Figma
  paddingRight: 12, // Space before eye icon
  paddingVertical: 0,
  height: 60, // Full height
  textAlignVertical: "center",
  lineHeight: 18, // 1.255em * 14px
})

const $eyeButton: ThemedStyle<ViewStyle> = () => ({
  width: 30, // Touch area
  height: 30, // Touch area
  alignItems: "center",
  justifyContent: "center",
})

const $errorContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 20,
  paddingHorizontal: 16,
})

const $errorText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for helper text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  color: colors.error,
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  textAlign: "center",
})

const $continueButton: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  backgroundColor: "#0961F5", // Primary blue from Figma
  borderRadius: 30, // Exact border radius from Figma
  paddingHorizontal: 30,
  height: 60, // Exact height from Figma
  width: "100%", // Full width
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8, // Android shadow
  marginTop: 40, // Space from input field
})

const $continueButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for button text - MANDATORY
  fontWeight: "600", // Semi-bold weight for button text
  fontSize: 18,
  lineHeight: 20, // Design guidelines button text line height
  letterSpacing: 0.2, // Design guidelines button text letter spacing
  color: "#FFFFFF",
  flex: 1,
  textAlign: "center",
})

const $buttonIconContainer: ThemedStyle<ViewStyle> = () => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  alignItems: "center",
  justifyContent: "center",
})
