import { FC } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
} from "react-native"
import { Button, TextField } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ForgotPasswordFormProps {
  email: string
  isLoading: boolean
  onEmailChange: (email: string) => void
  onSendResetEmail: () => void
  onBackToLogin: () => void
  style?: ViewStyle
}

export const ForgotPasswordForm: FC<ForgotPasswordFormProps> = ({
  email,
  isLoading,
  onEmailChange,
  onSendResetEmail,
  onBackToLogin,
  style,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$resetCard, style])}>
      <TextField
        value={email}
        onChangeText={onEmailChange}
        containerStyle={themed($textField)}
        autoCapitalize="none"
        autoComplete="email"
        autoCorrect={false}
        keyboardType="email-address"
        label="Email Address"
        placeholder="Enter your email address"
        helper=""
        status={undefined}
        LabelTextProps={{ style: themed($textFieldLabel) }}
        inputWrapperStyle={themed($textFieldInput)}
      />

      <Button
        testID="reset-button"
        text="Send Reset Link"
        style={themed($resetButton)}
        textStyle={themed($resetButtonText)}
        preset="reversed"
        onPress={onSendResetEmail}
        disabled={isLoading}
      />

      <Button
        testID="back-to-login-button"
        text="Back to Login"
        style={themed($backButton)}
        textStyle={themed($backButtonText)}
        preset="default"
        onPress={onBackToLogin}
        disabled={isLoading}
      />
    </View>
  )
}

const $resetCard: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  padding: spacing.md,
  marginHorizontal: spacing.xs,
  marginTop: spacing.sm,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 8 },
  shadowOpacity: 0.15,
  shadowRadius: 16,
  elevation: 8,
})

const $textField: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $textFieldLabel: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light,
})

const $textFieldInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light,
})

const $resetButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.xs,
  marginBottom: spacing.sm,
  borderRadius: 16,
  paddingVertical: spacing.md,
})

const $resetButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light,
})

const $backButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  borderRadius: 16,
  paddingVertical: spacing.md,
})

const $backButtonText: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.light,
  color: colors.tint,
})
