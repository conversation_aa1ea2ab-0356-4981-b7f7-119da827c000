import { FC, useRef, useCallback } from "react"
import { TextInput, ViewStyle, TextStyle, View, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface EmailRecoveryFormProps {
  /**
   * Email value
   */
  email: string
  /**
   * Whether the form is loading
   */
  isLoading: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Email change handler
   */
  onEmailChange: (email: string) => void
  /**
   * Form submit handler
   */
  onSubmit: () => void
}

export const EmailRecoveryForm: FC<EmailRecoveryFormProps> = ({
  email,
  isLoading,
  error,
  onEmailChange,
  onSubmit,
}) => {
  const emailInput = useRef<TextInput>(null)
  const { themed } = useAppTheme()

  // Wrap onEmailChange để đảm bảo re-render
  const handleEmailChange = useCallback((text: string) => {
    onEmailChange(text)
  }, [onEmailChange])

  return (
    <View style={themed($formContainer)}>
      {/* Description Text */}
      <Text style={themed($descriptionText)}>
        Nhập địa chỉ email của bạn để nhận mã xác minh đặt lại mật khẩu
      </Text>

      {/* Email Input Field */}
      <View style={themed($textField)}>
        <View style={themed($emailFieldContainer)}>
          <Icon
            icon="settings" // Email icon placeholder
            size={19}
            color="#545454"
            style={themed($emailIcon)}
          />
          <TextInput
            ref={emailInput}
            value={email}
            onChangeText={handleEmailChange}
            style={themed($emailInput)}
            placeholder="Email"
            placeholderTextColor="#505050"
            autoCapitalize="none"
            autoComplete="email"
            autoCorrect={false}
            keyboardType="email-address"
            onSubmitEditing={onSubmit}
          />
        </View>
      </View>

      {/* Error Message */}
      {error && (
        <View style={themed($errorContainer)}>
          <Text style={themed($errorText)}>{error}</Text>
        </View>
      )}

      {/* Continue Button */}
      <TouchableOpacity
        testID="continue-button"
        style={themed($continueButton)}
        onPress={onSubmit}
        disabled={isLoading || !email.trim()}
        activeOpacity={0.8}
      >
        <Text style={themed($continueButtonText)}>
          {isLoading ? "Đang gửi..." : "Gửi mã xác minh"}
        </Text>
      </TouchableOpacity>
    </View>
  )
}

// Styles theo Figma design và pattern của Signup
const $formContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 0, // Không cần padding vì màn hình chính đã có 24px
  alignItems: "center", // Center all form elements
})

const $descriptionText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for descriptions - MANDATORY
  fontWeight: "500", // Medium weight for descriptions - MANDATORY
  fontSize: 14, // Exact size từ Figma
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Exact color từ Figma
  textAlign: "center",
  marginTop: 50, // Reduced spacing to fit on screen
  marginBottom: 24, // 24px space before input
  paddingHorizontal: 10, // Padding for text
})

const $textField: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  marginBottom: 24, // 24px space after input field
})

const $emailFieldContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 12, // Exact border radius from Figma
  paddingHorizontal: 20, // Exact padding from Figma
  height: 60, // Exact height from Figma
  borderWidth: 1, // Thêm border line
  borderColor: "#E0E0E0", // Gray border color
})

const $emailIcon: ThemedStyle<ViewStyle> = () => ({
  marginRight: 12, // Space between icon and input
})

const $emailInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  flex: 1,
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for input text - MANDATORY
  fontWeight: "500", // Medium weight for input text - MANDATORY
  color: "#505050", // Exact color from Figma
  paddingRight: 20, // Right padding
  paddingVertical: 0,
  height: 60, // Full height
  textAlignVertical: "center",
  lineHeight: 18, // 1.255em * 14px
})

const $errorContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 20,
  paddingHorizontal: 16,
})

const $errorText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for helper text - MANDATORY
  fontWeight: "500", // Medium weight - MANDATORY
  color: colors.error,
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  textAlign: "center",
})

const $continueButton: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center", // Center text vì không còn icon
  backgroundColor: "#23408B", // Brand color RGB(35, 64, 139)
  borderRadius: 30, // Exact border radius from Figma
  paddingHorizontal: 30,
  height: 60, // Exact height from Figma
  width: "100%", // Full width
  marginTop: 0, // Không cần margin vì textField đã có marginBottom 24px
})

const $continueButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.bold, // Bold font cho button text
  fontWeight: "700", // Bold weight cho button text
  fontSize: 16, // Giảm từ 18 xuống 16 để phù hợp hơn
  lineHeight: 20, // Design guidelines button text line height
  letterSpacing: 0.2, // Design guidelines button text letter spacing
  color: "#FFFFFF",
  textAlign: "center", // Bỏ flex: 1 vì không còn icon
})


