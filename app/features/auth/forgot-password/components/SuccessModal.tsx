import { FC, useEffect } from "react"
import { View, ViewStyle, TextStyle, Modal, Dimensions, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface SuccessModalProps {
  /**
   * Whether the modal is visible
   */
  visible: boolean
  /**
   * Modal close handler (called after auto-close timer)
   */
  onClose: () => void
}

export const SuccessModal: FC<SuccessModalProps> = ({
  visible,
  onClose,
}) => {
  const { themed } = useAppTheme()

  // Auto-close timer - temporarily disabled to fix React warning
  // useEffect(() => {
  //   if (!visible) return
  //   const timer = setTimeout(onClose, 3000)
  //   return () => clearTimeout(timer)
  // }, [visible, onClose])

  if (!visible) return null

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <View style={themed($modalOverlay)}>
        <View style={themed($modalContainer)}>
          {/* Decorative Elements */}
          <View style={themed($decorativeElements)}>
            {/* Top left star */}
            <View style={[themed($star), themed($starTopLeft)]} />
            
            {/* Top right triangle */}
            <View style={[themed($triangle), themed($triangleTopRight)]} />
            
            {/* Left circle */}
            <View style={[themed($circle), themed($circleLeft)]} />
            
            {/* Bottom left star */}
            <View style={[themed($star), themed($starBottomLeft)]} />
            
            {/* Bottom triangle */}
            <View style={[themed($triangle), themed($triangleBottom)]} />
            
            {/* Small circles */}
            <View style={[themed($smallCircle), themed($smallCircleTop)]} />
            <View style={[themed($smallCircle), themed($smallCircleBottom)]} />
          </View>

          {/* Main Illustration */}
          <View style={themed($illustrationContainer)}>
            <Icon
              icon="checkmark"
              size={80}
              color="#5AB3A0"
            />
          </View>

          {/* Title */}
          <Text style={themed($titleText)}>
            Congratulations
          </Text>

          {/* Description */}
          <Text style={themed($descriptionText)}>
            Your Account is Ready to Use. You will be redirected to the Home Page in a Few Seconds.
          </Text>

          {/* Continue Button */}
          <TouchableOpacity style={themed($continueButton)} onPress={onClose}>
            <Text style={themed($continueButtonText)}>Continue</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  )
}

const { width: screenWidth, height: screenHeight } = Dimensions.get("window")

// Styles theo Figma design
const $modalOverlay: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent overlay
  alignItems: "center",
  justifyContent: "center",
  paddingHorizontal: 34, // Same as screen padding
})

const $modalContainer: ThemedStyle<ViewStyle> = () => ({
  width: 360, // Exact width from Figma
  height: 460, // Exact height from Figma
  backgroundColor: "#F5F9FF", // Exact background color from Figma
  borderRadius: 40, // Exact border radius from Figma
  alignItems: "center",
  justifyContent: "center",
  position: "relative",
  overflow: "hidden",
})

const $decorativeElements: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  width: "100%",
  height: "100%",
})

const $star: ThemedStyle<ViewStyle> = () => ({
  width: 18,
  height: 18,
  backgroundColor: "#FCC202", // Yellow star from Figma
  position: "absolute",
  // Star shape approximation with border radius
  borderRadius: 2,
  transform: [{ rotate: "45deg" }],
})

const $starTopLeft: ThemedStyle<ViewStyle> = () => ({
  top: 37,
  left: 244,
})

const $starBottomLeft: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#FF001E", // Red star from Figma
  top: 158,
  left: 78,
})

const $triangle: ThemedStyle<ViewStyle> = () => ({
  width: 14,
  height: 14,
  backgroundColor: "#167F71", // Green triangle from Figma
  position: "absolute",
  borderRadius: 2,
  transform: [{ rotate: "45deg" }],
})

const $triangleTopRight: ThemedStyle<ViewStyle> = () => ({
  top: 108,
  right: 70, // Calculated from right edge
})

const $triangleBottom: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#332DA1", // Purple triangle from Figma
  bottom: 245,
  left: 110,
})

const $circle: ThemedStyle<ViewStyle> = () => ({
  width: 12,
  height: 12,
  backgroundColor: "#472D2D", // Dark circle from Figma
  borderRadius: 6,
  position: "absolute",
})

const $circleLeft: ThemedStyle<ViewStyle> = () => ({
  top: 88,
  left: 236,
})

const $smallCircle: ThemedStyle<ViewStyle> = () => ({
  width: 8,
  height: 8,
  borderRadius: 4,
  position: "absolute",
})

const $smallCircleTop: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#FF6B00", // Orange circle from Figma
  top: 78,
  left: 92,
})

const $smallCircleBottom: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#167F71", // Green circle from Figma
  top: 180,
  left: 111,
})

const $illustrationContainer: ThemedStyle<ViewStyle> = () => ({
  marginTop: 20,
  marginBottom: 30,
  alignItems: "center",
  justifyContent: "center",
})

const $titleText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for titles
  fontWeight: "600", // Semi-bold weight from Figma
  fontSize: 24, // Exact size from Figma
  lineHeight: 35, // 1.445em * 24px
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#202244", // Exact color from Figma
  textAlign: "center",
  marginBottom: 12, // Space before description
})

const $descriptionText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for description - MANDATORY
  fontWeight: "700", // Bold weight from Figma
  fontSize: 14, // Exact size from Figma
  lineHeight: 18, // 1.255em * 14px
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Exact color from Figma
  textAlign: "center",
  paddingHorizontal: 40, // Exact padding from Figma (40px from each side)
  marginBottom: 25, // Space before loading indicator
})

const $loadingContainer: ThemedStyle<ViewStyle> = () => ({
  alignItems: "center",
  justifyContent: "center",
})

const $loadingDots: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
})

const $dot: ThemedStyle<ViewStyle> = () => ({
  width: 8,
  height: 8,
  borderRadius: 4,
  backgroundColor: "#E0E0E0", // Inactive dot color
  marginHorizontal: 4,
})

const $dotActive: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#202244", // Active dot color (same as title)
})

const $continueButton: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#167F71", // Primary button color
  borderRadius: 25,
  paddingVertical: 15,
  paddingHorizontal: 40,
  marginTop: 20,
})

const $continueButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium,
  fontWeight: "600",
  fontSize: 16,
  color: "#FFFFFF",
  textAlign: "center",
})
