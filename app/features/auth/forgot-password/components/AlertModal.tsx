import { FC } from "react"
import { View, ViewStyle, TextStyle, Modal, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface AlertModalProps {
  /**
   * Whether the modal is visible
   */
  visible: boolean
  /**
   * Title of the alert
   */
  title?: string
  /**
   * Message content of the alert
   */
  message: string
  /**
   * Text for the OK button
   */
  okText?: string
  /**
   * Callback when OK button is pressed
   */
  onOk: () => void
}

export const AlertModal: FC<AlertModalProps> = ({
  visible,
  title = "Congratulations",
  message,
  okText = "OK",
  onOk,
}) => {
  const { themed } = useAppTheme()

  if (!visible) return null

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <View style={themed($modalOverlay)}>
        <View style={themed($modalContainer)}>
          {/* Congratulations Icon */}
          <View style={themed($iconContainer)}>
            <Icon
              icon="congratulations"
              size={180}
              style={{ tintColor: undefined }}
            />
          </View>

          {/* Title */}
          <Text style={themed($titleText)}>
            {title}
          </Text>

          {/* Message */}
          <Text style={themed($messageText)}>
            {message}
          </Text>

          {/* OK Button */}
          <TouchableOpacity style={themed($okButton)} onPress={onOk}>
            <Text style={themed($okButtonText)}>{okText}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  )
}

// Styles theo Figma design
const $modalOverlay: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.4)", // Semi-transparent overlay from Figma
  alignItems: "center",
  justifyContent: "center",
  paddingHorizontal: 34, // Same as screen padding
})

const $modalContainer: ThemedStyle<ViewStyle> = () => ({
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 30, // Large rounded corners from Figma
  paddingTop: 40,
  paddingBottom: 30,
  paddingHorizontal: 40,
  alignItems: "center",
  width: 320, // Fixed width from Figma
  shadowColor: "#000",
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5,
})

const $iconContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 25,
  alignItems: "center",
  justifyContent: "center",
})

const $titleText: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.semiBold, // Design guidelines: SemiBold (600) for titles
  fontWeight: "600", // Semi-bold weight from design guidelines
  fontSize: 24, // Larger title size from Figma
  lineHeight: 32,
  letterSpacing: 0,
  color: colors.text, // Primary text color from updated design guidelines
  textAlign: "center",
  marginBottom: 16,
})

const $messageText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for description - MANDATORY
  fontWeight: "500", // Medium weight for description - MANDATORY per design guidelines
  fontSize: 16, // Message size from Figma
  lineHeight: 24,
  letterSpacing: 0,
  color: "#8B8B8B", // Lighter gray text color from Figma
  textAlign: "center",
  marginBottom: 30,
  paddingHorizontal: 10, // Extra padding for better text layout
})

const $okButton: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  backgroundColor: "#0961F5", // Exact color from Sign In button
  borderRadius: 30, // Exact border radius from Sign In button
  height: 60, // Exact height from Sign In button
  width: 280, // Slightly smaller than Sign In button for modal
  alignSelf: "center",
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 }, // Exact shadow from Sign In button
  shadowOpacity: 0.3, // Exact shadow opacity from Sign In button
  shadowRadius: 8, // Exact shadow radius from Sign In button
  elevation: 4,
  marginTop: 10, // Reduced from 20 to 10 for closer spacing
})

const $okButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontSize: 18, // Exact from Sign In button
  fontFamily: typography.primary.bold, // Bold font family for emphasis
  fontWeight: "700", // Bold weight for button text
  color: "#FFFFFF", // White text
  textAlign: "center",
  lineHeight: 26, // 1.445em * 18px
  flex: 1,
})
