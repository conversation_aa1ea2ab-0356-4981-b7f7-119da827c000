import { FC, ReactNode } from "react"
import {
  View,
  ViewStyle,
  KeyboardAvoidingView,
  Platform,
} from "react-native"
import { Screen } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ForgotPasswordContainerProps {
  children: ReactNode
  style?: ViewStyle
}

export const ForgotPasswordContainer: FC<ForgotPasswordContainerProps> = ({
  children,
  style,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$container, style])}>
      {/* Blue background as requested */}
      <View style={themed($modernBackground)} />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={themed($keyboardAvoidingView)}
      >
        <Screen
          preset="scroll"
          contentContainerStyle={themed($screenContentContainer)}
          safeAreaEdges={["bottom"]}
          backgroundColor="transparent"
        >
          {children}
        </Screen>
      </KeyboardAvoidingView>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $modernBackground: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: colors.tint, // Use blue background as requested
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexGrow: 1,
  paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
  paddingTop: spacing.sm,
  paddingBottom: spacing.lg,
  justifyContent: "center", // Center content vertically
})
