import { FC, useState } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ContactOptionsProps {
  onOptionSelect: (option: "email" | "sms") => void
  selectedOption?: "email" | "sms"
}

export const ContactOptions: FC<ContactOptionsProps> = ({
  onOptionSelect,
  selectedOption,
}) => {
  const { themed } = useAppTheme()

  const handleEmailPress = () => {
    console.log("Email option pressed")
    onOptionSelect("email")
  }

  const handleSMSPress = () => {
    console.log("SMS option pressed")
    onOptionSelect("sms")
  }

  return (
    <View style={themed($container)}>
      {/* Description Text */}
      <Text style={themed($descriptionText)}>
        Select which contact details should we use to Reset Your Password
      </Text>

      {/* Email Option */}
      <TouchableOpacity
        style={[
          themed($optionCard),
          selectedOption === "email" && themed($selectedCard)
        ]}
        onPress={handleEmailPress}
        activeOpacity={0.8}
      >
        <View style={themed($iconContainer)}>
          <View style={themed($iconCircle)}>
            <Icon
              icon="settings" // Placeholder - cần icon email
              size={18}
              color="#167F71"
            />
          </View>
        </View>
        <View style={themed($textContainer)}>
          <Text style={themed($optionTitle)}>Via Email</Text>
          <Text style={themed($optionSubtitle)}>Choose password recovery method via email</Text>
        </View>
      </TouchableOpacity>

      {/* SMS Option */}
      <TouchableOpacity
        style={[
          themed($optionCard),
          selectedOption === "sms" && themed($selectedCard)
        ]}
        onPress={handleSMSPress}
        activeOpacity={0.8}
      >
        <View style={themed($iconContainer)}>
          <View style={themed($iconCircle)}>
            <Icon
              icon="phone"
              size={18}
              color="#167F71"
            />
          </View>
        </View>
        <View style={themed($textContainer)}>
          <Text style={themed($optionTitle)}>Via SMS</Text>
          <Text style={themed($optionSubtitle)}>Choose password recovery method via phone number</Text>
        </View>
      </TouchableOpacity>
    </View>
  )
}

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Consistent với design
  alignItems: "center",
})

const $descriptionText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for descriptions - MANDATORY
  fontWeight: "300", // Light weight for descriptions - MANDATORY
  fontSize: 14, // Exact size từ Figma
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Exact color từ Figma
  textAlign: "center",
  marginTop: 50, // Reduced spacing to fit on screen
  marginBottom: 30, // Space before options
  paddingHorizontal: 10, // Padding for text
})

const $optionCard: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF",
  borderRadius: 12, // Exact từ Figma
  width: 360, // Exact width từ Figma
  height: 80, // Exact height từ Figma
  marginBottom: 20, // Space between cards
  paddingHorizontal: 21, // x:21 từ Figma
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5, // Android shadow
})

const $selectedCard: ThemedStyle<ViewStyle> = () => ({
  borderWidth: 2,
  borderColor: "#167F71",
  backgroundColor: "#F0F8FF",
})

const $iconContainer: ThemedStyle<ViewStyle> = () => ({
  marginRight: 15, // Space between icon and text
})

const $iconCircle: ThemedStyle<ViewStyle> = () => ({
  width: 36, // Exact size từ Figma
  height: 36, // Exact size từ Figma
  borderRadius: 18, // Perfect circle
  backgroundColor: "#E8F1FF", // Exact background từ Figma
  borderWidth: 2,
  borderColor: "#167F71", // Exact border color từ Figma
  alignItems: "center",
  justifyContent: "center",
})

const $textContainer: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $optionTitle: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for labels
  fontWeight: "500", // Medium weight for labels
  fontSize: 12, // Exact size từ Figma
  lineHeight: 16, // Design guidelines caption line height
  letterSpacing: 0.1, // Design guidelines caption letter spacing
  color: "#505050", // Exact color từ Figma
  marginBottom: 5, // Space between title and subtitle
})

const $optionSubtitle: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for descriptions - MANDATORY
  fontWeight: "300", // Light weight for descriptions - MANDATORY
  fontSize: 14, // Exact size từ Figma
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#202244", // Exact color từ Figma
})
