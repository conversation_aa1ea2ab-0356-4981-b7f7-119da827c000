import { FC } from "react"
import { View, ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface NewPasswordHeaderProps {
  /**
   * Back button press handler
   */
  onBack: () => void
}

export const NewPasswordHeader: FC<NewPasswordHeaderProps> = ({
  onBack,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($headerContainer)}>
      {/* Back Button */}
      <TouchableOpacity
        onPress={onBack}
        style={themed($backButton)}
        activeOpacity={0.8}
      >
        <Icon
          icon="back"
          size={26}
          color="#202244"
        />
      </TouchableOpacity>

      {/* Title */}
      <Text style={themed($titleText)}>Create New Password</Text>
    </View>
  )
}

// Styles theo Figma design
const $headerContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35, // Exact padding from Figma (x:35)
  paddingTop: 25, // Space from top
  paddingBottom: 20, // Space before content
  width: "100%",
})

const $backButton: ThemedStyle<ViewStyle> = () => ({
  width: 30, // Touch area
  height: 30, // Touch area
  alignItems: "center",
  justifyContent: "center",
  marginRight: 12, // Space between back button and title
})

const $titleText: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for headers
  fontWeight: "600", // Semi-bold weight for headers
  fontSize: 21, // Exact size từ Figma
  lineHeight: 30, // Design guidelines header line height
  letterSpacing: 0, // Design guidelines header letter spacing
  color: colors.text, // Primary text color from updated design guidelines
  flex: 1, // Take remaining space
})
