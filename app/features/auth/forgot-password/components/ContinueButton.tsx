import { FC } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ContinueButtonProps {
  onPress: () => void
  disabled?: boolean
  isLoading?: boolean
}

export const ContinueButton: FC<ContinueButtonProps> = ({
  onPress,
  disabled = false,
  isLoading = false,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      <TouchableOpacity
        style={themed($button)}
        onPress={onPress}
        disabled={disabled || isLoading}
        activeOpacity={0.8}
      >
        <Text style={themed($buttonText)}>
          {isLoading ? "Processing..." : "Continue"}
        </Text>
        <View style={themed($iconContainer)}>
          <Icon
            icon="caretRight"
            size={17}
            color="#0961F5"
          />
        </View>
      </TouchableOpacity>
    </View>
  )
}

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 39, // x:39 từ Figma
  position: "absolute",
  bottom: 120, // y:743 từ bottom
})

const $button: ThemedStyle<ViewStyle> = ({ colors }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  backgroundColor: colors.tint, // Main brand color from design guidelines (primary500)
  borderRadius: 30, // Exact border radius từ Figma
  width: 350, // Exact width từ Figma
  height: 60, // Exact height từ Figma
  paddingHorizontal: 30,
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8, // Android shadow
})

const $buttonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for button text - MANDATORY
  fontWeight: "300", // Light weight for button text - MANDATORY
  fontSize: 18, // Exact size từ Figma
  lineHeight: 20, // Design guidelines button text line height
  letterSpacing: 0.2, // Design guidelines button text letter spacing
  color: "#FFFFFF",
  flex: 1,
  textAlign: "center",
})

const $iconContainer: ThemedStyle<ViewStyle> = () => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  alignItems: "center",
  justifyContent: "center",
})
