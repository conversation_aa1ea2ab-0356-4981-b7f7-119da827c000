import { FC } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface ForgotPasswordHeaderProps {
  onBack?: () => void
}

export const ForgotPasswordHeader: FC<ForgotPasswordHeaderProps> = ({
  onBack,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($headerContainer)}>
      {/* Back Button */}
      <TouchableOpacity
        onPress={onBack}
        style={themed($backButton)}
        activeOpacity={0.7}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Icon
          icon="back"
          size={20}
          color="#202244"
        />
      </TouchableOpacity>

      {/* Title */}
      <Text style={themed($headerTitle)}><PERSON>u<PERSON><PERSON></Text>
    </View>
  )
}

// Styles theo Figma design
const $headerContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 0, // Không cần padding vì màn hình chính đã có 24px
  paddingTop: 25, // y:69 - y:44 (status bar)
  paddingBottom: 20,
  width: "100%",
})

const $backButton: ThemedStyle<ViewStyle> = () => ({
  padding: 5, // Touch area
  marginRight: 12, // Space between back button and title
})

const $headerTitle: ThemedStyle<TextStyle> = ({ typography, colors }) => ({
  fontFamily: typography.primary.semiBold, // Design guidelines: SemiBold (600) for headings
  fontWeight: "600", // SemiBold weight for headings
  fontSize: 21, // Exact size từ Figma
  lineHeight: 30, // 1.445em * 21px
  color: colors.text, // Primary text color from updated design guidelines
  textAlign: "left",
})
