import { useState } from "react"
import { Al<PERSON> } from "react-native"
import type { NavigationProp } from "@react-navigation/native"
import type { AppStackParamList } from "@/navigators"

interface UseForgotPasswordProps {
  navigation: NavigationProp<AppStackParamList>
}

export function useForgotPassword({ navigation }: UseForgotPasswordProps) {
  const [selectedOption, setSelectedOption] = useState<"email" | "sms" | undefined>(undefined)
  const [isLoading, setIsLoading] = useState(false)

  const handleOptionSelect = (option: "email" | "sms") => {
    setSelectedOption(option)
  }

  const handleContinue = () => {
    if (!selectedOption) {
      Alert.alert("Error", "Please select a recovery method")
      return
    }

    // Navigate to input screen based on selected option
    if (selectedOption === "email") {
      navigation.navigate("EmailRecovery")
    } else {
      navigation.navigate("PhoneRecovery")
    }
  }

  const handleBackToLogin = () => {
    navigation.goBack()
  }

  return {
    // State
    selectedOption,
    isLoading,

    // Actions
    handleOptionSelect,
    handleContinue,
    handleBackToLogin,
  }
}
