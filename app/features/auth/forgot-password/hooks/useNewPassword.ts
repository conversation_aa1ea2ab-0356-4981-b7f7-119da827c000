import { useState } from "react"
import type { NavigationProp } from "@react-navigation/native"
import type { AuthStackParamList } from "@/navigators"

interface UseNewPasswordProps {
  navigation: NavigationProp<AuthStackParamList>
  method: "email" | "sms"
  contact: string
}

export function useNewPassword({ navigation, method, contact }: UseNewPasswordProps) {
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [showAlertModal, setShowAlertModal] = useState(false)

  const validatePassword = (password: string, confirmPassword: string): boolean => {
    if (!password.trim()) {
      setError("Please enter a password")
      return false
    }

    if (password.length < 8) {
      setError("Password must be at least 8 characters long")
      return false
    }

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(password)) {
      setError("Password must contain at least one uppercase letter")
      return false
    }

    // Check for at least one lowercase letter
    if (!/[a-z]/.test(password)) {
      setError("Password must contain at least one lowercase letter")
      return false
    }

    // Check for at least one number
    if (!/\d/.test(password)) {
      setError("Password must contain at least one number")
      return false
    }

    // Check for at least one special character
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      setError("Password must contain at least one special character")
      return false
    }

    if (!confirmPassword.trim()) {
      setError("Please confirm your password")
      return false
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match")
      return false
    }

    setError("")
    return true
  }

  const handleCreatePassword = async () => {
    if (!validatePassword(password, confirmPassword)) {
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // TODO: Implement actual password reset logic here
      // For now, just simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Show success alert modal
      setShowAlertModal(true)
    } catch (error) {
      setError("Failed to reset password. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const toggleShowPassword = () => {
    setShowPassword(!showPassword)
  }

  const toggleShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword)
  }

  const handleBackToVerifyCode = () => {
    navigation.goBack()
  }

  const handleAlertModalOk = () => {
    setShowAlertModal(false)
    // Navigate to Login after modal closes
    navigation.navigate("Login")
  }



  return {
    // State
    password,
    confirmPassword,
    isLoading,
    error,
    showPassword,
    showConfirmPassword,
    showAlertModal,

    // Actions
    setPassword,
    setConfirmPassword,
    toggleShowPassword,
    toggleShowConfirmPassword,
    handleCreatePassword,
    handleBackToVerifyCode,
    handleAlertModalOk,
  }
}
