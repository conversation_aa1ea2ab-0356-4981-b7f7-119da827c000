import { useState, useEffect } from "react"
import { Alert } from "react-native"
import type { NavigationProp } from "@react-navigation/native"
import type { AuthStackParamList } from "@/navigators"

interface UseVerifyCodeProps {
  navigation: NavigationProp<AuthStackParamList>
  method: "email" | "sms"
  contact: string
}

export function useVerifyCode({ navigation, method, contact }: UseVerifyCodeProps) {
  const [code, setCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [resendTimer, setResendTimer] = useState(60)
  const [canResend, setCanResend] = useState(false)

  // Countdown timer for resend
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
    }
  }, [resendTimer])

  const validateCode = (code: string): boolean => {
    if (!code.trim()) {
      setError("Please enter the verification code")
      return false
    }

    if (code.length !== 4) {
      setError("Please enter a 4-digit verification code")
      return false
    }

    // Check if code contains only numbers
    if (!/^\d{4}$/.test(code)) {
      setError("Verification code must contain only numbers")
      return false
    }

    setError("")
    return true
  }

  const handleVerifyCode = async () => {
    if (!validateCode(code)) {
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // TODO: Implement actual verification logic here
      // For now, just simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Simulate verification success/failure
      if (code === "1234") {
        // Navigate to new password screen
        navigation.navigate("NewPassword", {
          method,
          contact
        })
      } else {
        setError("Invalid verification code. Please try again.")
      }
    } catch (error) {
      setError("Failed to verify code. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendCode = async () => {
    if (!canResend) {
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // TODO: Implement actual resend logic here
      // For now, just simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      Alert.alert(
        "Code Resent",
        `A new verification code has been sent to your ${method === "email" ? "email" : "phone number"}.`,
        [{ text: "OK" }]
      )

      // Reset timer
      setResendTimer(60)
      setCanResend(false)
      setCode("") // Clear current code
    } catch (error) {
      setError("Failed to resend code. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToRecovery = () => {
    navigation.goBack()
  }

  return {
    // State
    code,
    isLoading,
    error,
    resendTimer,
    canResend,
    
    // Actions
    setCode,
    handleVerifyCode,
    handleResendCode,
    handleBackToRecovery,
  }
}
