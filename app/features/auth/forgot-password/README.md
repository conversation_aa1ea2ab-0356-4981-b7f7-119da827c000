# Forgot Password Feature

This directory contains all components and screens related to the Forgot Password functionality of the LMS app.

## Structure

```
app/features/forgot-password/
├── components/           # Forgot password specific components
│   ├── ForgotPasswordContainer.tsx   # Container with background and keyboard handling (60 lines)
│   ├── ForgotPasswordHeader.tsx      # Logo, title, subtitle (80 lines)
│   ├── ForgotPasswordForm.tsx        # Email input, reset button, back button (105 lines)
│   └── index.ts                      # Component exports
├── screens/             # Forgot password screens
│   ├── ForgotPasswordScreen.tsx      # Main forgot password screen (40 lines)
│   └── index.ts                      # Screen exports
├── hooks/               # Forgot password hooks
│   ├── useForgotPassword.ts          # Forgot password business logic (65 lines)
│   └── index.ts                      # Hook exports
├── index.ts            # Feature exports
└── README.md           # This file
```

## Components

### ForgotPasswordContainer
- Container with blue background and keyboard handling
- Provides consistent layout structure
- 60 lines (well under 200 line limit)
- Reusable for other password-related screens

### ForgotPasswordHeader
- Displays logo, title, and subtitle
- Configurable via props (title, subtitle, logoText)
- 80 lines (well under 200 line limit)
- Clean, focused component

### ForgotPasswordForm
- Email input field with validation
- Reset button and back to login button
- 105 lines (well under 200 line limit)
- Handles all form interactions

## Hooks

### useForgotPassword
- Contains all business logic for password reset
- Email validation and API simulation
- Navigation handling
- 65 lines (well under 200 line limit)
- Clean separation of business logic from UI

## Screens

### ForgotPasswordScreen
- Main layout and component orchestration
- 40 lines (significantly reduced from original 277 lines)
- Uses all above components and hooks
- Clean separation of concerns

## Usage

```typescript
// Import the entire forgot password feature
import { 
  ForgotPasswordScreen, 
  ForgotPasswordContainer,
  ForgotPasswordHeader,
  ForgotPasswordForm,
  useForgotPassword 
} from "@/features/forgot-password"

// Or import specific components
import { ForgotPasswordForm } from "@/features/forgot-password/components"
import { useForgotPassword } from "@/features/forgot-password/hooks"
```

## Benefits of This Structure

1. **Modularity**: Each component has a single responsibility
2. **Reusability**: Components can be reused in other password flows
3. **Maintainability**: Small files are easier to understand and modify
4. **Testability**: Each component can be tested independently
5. **Performance**: Better code splitting and lazy loading potential
6. **Consistency**: Follows the same pattern as other features in the app

## File Size Reduction

- Original file: 277 lines
- New main screen: 40 lines (86% reduction)
- All components combined: ~310 lines across 6 files
- Average component size: ~52 lines per file
- All files are well under the 200-line limit

## Features

- Email validation with regex
- Loading states during API calls
- Error handling with alerts
- Success feedback with navigation
- Consistent typography (font-weight 300)
- Blue background design as requested
- Proper keyboard handling
