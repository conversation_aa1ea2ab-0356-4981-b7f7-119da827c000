import { useState } from "react"
import { Alert } from "react-native"
import type { NavigationProp } from "@react-navigation/native"
import type { AuthStackParamList } from "@/navigators"

interface UsePhoneLoginFormProps {
  navigation: NavigationProp<AuthStackParamList>
}

export function usePhoneLoginForm({ navigation }: UsePhoneLoginFormProps) {
  const [phoneNumber, setPhoneNumber] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const validatePhoneNumber = (phone: string): boolean => {
    if (!phone.trim()) {
      setError("Please enter your phone number")
      return false
    }

    // Basic phone validation - remove spaces and check if it's numeric
    const cleanPhone = phone.replace(/\s+/g, '')
    if (cleanPhone.length < 10) {
      setError("Please enter a valid phone number")
      return false
    }

    // Check if it contains only numbers, +, -, (, ), and spaces
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]+$/
    if (!phoneRegex.test(phone)) {
      setError("Please enter a valid phone number")
      return false
    }

    setError("")
    return true
  }

  const handlePhoneLogin = async () => {
    if (!validatePhoneNumber(phoneNumber)) {
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // TODO: Implement actual phone login logic here
      // For now, just simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Navigate to phone login PIN verification screen
      navigation.navigate("PhoneLoginPin", {
        phoneNumber: phoneNumber
      })
    } catch (error) {
      setError("Failed to send verification code. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToLogin = () => {
    navigation.goBack()
  }

  return {
    // State
    phoneNumber,
    isLoading,
    error,
    
    // Actions
    setPhoneNumber,
    handlePhoneLogin,
    handleBackToLogin,
  }
}
