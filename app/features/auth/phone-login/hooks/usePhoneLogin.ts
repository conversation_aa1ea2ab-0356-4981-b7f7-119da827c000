import { useState } from "react"
import { Alert } from "react-native"
import { useStores } from "@/models"
import { phoneAuthService } from "@/services/phoneAuth"
import { AppStackScreenProps } from "@/navigators"

interface UsePhoneLoginProps {
  navigation: AppStackScreenProps<"PhoneLogin">["navigation"]
}

export const usePhoneLogin = ({ navigation }: UsePhoneLoginProps) => {
  const [isSubmitted, setIsSubmitted] = useState(false)

  const {
    authenticationStore: {
      phoneNumber,
      setPhoneNumber,
      phoneValidationError,
      isLoading,
      setIsLoading,
      authError,
      setAuthError,
      setOTPState,
      setOTPExpiresAt,
    },
  } = useStores()

  const error = isSubmitted ? phoneValidationError || authError : ""

  const handleSendOTP = async () => {
    setIsSubmitted(true)

    if (phoneValidationError) {
      return
    }

    setIsLoading(true)
    setAuthError("")
    setOTPState("sending")

    try {
      const result = await phoneAuthService.sendOTP(phoneNumber)

      if (result.success && result.verificationId) {
        setOTPState("sent")
        // Set OTP expiration time (5 minutes from now)
        setOTPExpiresAt(Date.now() + 5 * 60 * 1000)

        // Navigate to OTP verification screen
        navigation.navigate("OTPVerification", {
          phoneNumber,
          verificationId: result.verificationId,
        })
      } else {
        setOTPState("error")
        setAuthError(result.error || "Failed to send OTP")
        Alert.alert("Error", result.error || "Failed to send OTP")
      }
    } catch (error) {
      setOTPState("error")
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred"
      setAuthError(errorMessage)
      Alert.alert("Error", errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToLogin = () => {
    navigation.goBack()
  }

  return {
    // State
    phoneNumber,
    error,
    isLoading,

    // Actions
    setPhoneNumber,
    handleSendOTP,
    handleBackToLogin,
  }
}
