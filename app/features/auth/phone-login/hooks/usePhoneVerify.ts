import { useState } from "react"
import { Alert } from "react-native"
import type { NavigationProp } from "@react-navigation/native"
import type { AuthStackParamList } from "@/navigators"

interface UsePhoneVerifyProps {
  navigation: NavigationProp<AuthStackParamList>
  phoneNumber: string
  isLogin: boolean
}

export function usePhoneVerify({ navigation, phoneNumber, isLogin }: UsePhoneVerifyProps) {
  const [verificationCode, setVerificationCode] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const validateVerificationCode = (code: string): boolean => {
    if (!code.trim()) {
      setError("Please enter the verification code")
      return false
    }

    if (code.length !== 6) {
      setError("Please enter a valid 6-digit code")
      return false
    }

    // Check if it contains only numbers
    const codeRegex = /^[0-9]{6}$/
    if (!codeRegex.test(code)) {
      setError("Please enter a valid 6-digit code")
      return false
    }

    setError("")
    return true
  }

  const handleVerifyCode = async () => {
    if (!validateVerificationCode(verificationCode)) {
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // TODO: Implement actual verification logic here
      // For now, just simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Simulate verification success
      if (verificationCode === "123456") {
        // Navigate to main app (Home screen)
        navigation.reset({
          index: 0,
          routes: [{ name: "Main" }],
        })
      } else {
        setError("Invalid verification code. Please try again.")
      }
    } catch (error) {
      setError("Failed to verify code. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendCode = async () => {
    setIsLoading(true)
    setError("")

    try {
      // TODO: Implement actual resend logic here
      // For now, just simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      Alert.alert(
        "Code Sent",
        `A new verification code has been sent to ${phoneNumber}`,
        [{ text: "OK" }]
      )
    } catch (error) {
      setError("Failed to resend code. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToPhoneLogin = () => {
    navigation.goBack()
  }

  return {
    // State
    verificationCode,
    isLoading,
    error,
    
    // Actions
    setVerificationCode,
    handleVerifyCode,
    handleBackToPhoneLogin,
    handleResendCode,
  }
}
