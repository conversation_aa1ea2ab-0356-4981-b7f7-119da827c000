import { FC, ReactNode } from "react"
import { View, ViewStyle, KeyboardAvoidingView, Platform } from "react-native"
import { Screen } from "@/components"
import { type ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface PhoneLoginContainerProps {
  /**
   * Children components to render inside the container
   */
  children: ReactNode
  /**
   * Optional custom styles for the container
   */
  style?: ViewStyle
}

export const PhoneLoginContainer: FC<PhoneLoginContainerProps> = ({
  children,
  style,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$container, style])}>
      {/* Modern Background */}
      <View style={themed($modernBackground)} />

      <KeyboardAvoidingView
        style={themed($keyboardAvoidingView)}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <Screen
          preset="scroll"
          contentContainerStyle={themed($screenContentContainer)}
          safeAreaEdges={["top", "bottom"]}
          backgroundColor="transparent"
        >
          {children}
        </Screen>
      </KeyboardAvoidingView>
    </View>
  )
}

const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $modernBackground: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: colors.tint,
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexGrow: 1,
  paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
  paddingTop: spacing.lg, // Reduced from xl for better spacing
  paddingBottom: spacing.lg,
})
