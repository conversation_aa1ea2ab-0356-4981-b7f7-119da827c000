import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Text } from "@/components"
import { type ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface PhoneLoginInfoProps {
  /**
   * Main info text
   */
  infoText?: string
  /**
   * Disclaimer text
   */
  disclaimerText?: string
  /**
   * Optional custom styles for the container
   */
  style?: ViewStyle
}

export const PhoneLoginInfo: FC<PhoneLoginInfoProps> = ({
  infoText = "We'll send you a 6-digit verification code via SMS",
  disclaimerText = "Standard message and data rates may apply",
  style,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$infoSection, style])}>
      <Text
        text={infoText}
        style={themed($infoText)}
      />
      <Text
        text={disclaimerText}
        style={themed($disclaimerText)}
      />
    </View>
  )
}

const $infoSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding
})

const $infoText: ThemedStyle<TextStyle> = ({ colors, typography, spacing }) => ({
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.palette.neutral200,
  textAlign: "center",
  marginBottom: spacing.xs,
  opacity: 0.8,
})

const $disclaimerText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 12,
  fontFamily: typography.primary.normal,
  color: colors.palette.neutral300,
  textAlign: "center",
  opacity: 0.7,
})
