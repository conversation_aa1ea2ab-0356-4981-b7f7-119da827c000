import { FC, useRef, useCallback } from "react"
import { TextInput, ViewStyle, TextStyle, View, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface PhoneVerifyFormProps {
  /**
   * Phone number being verified
   */
  phoneNumber: string
  /**
   * Verification code value
   */
  verificationCode: string
  /**
   * Whether the form is loading
   */
  isLoading: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Verification code change handler
   */
  onVerificationCodeChange: (code: string) => void
  /**
   * Form submit handler
   */
  onSubmit: () => void
  /**
   * Resend code handler
   */
  onResendCode: () => void
}

export const PhoneVerifyForm: FC<PhoneVerifyFormProps> = ({
  phoneNumber,
  verificationCode,
  isLoading,
  error,
  onVerificationCodeChange,
  onSubmit,
  onResendCode,
}) => {
  const codeInput = useRef<TextInput>(null)
  const { themed } = useAppTheme()

  // Wrap onVerificationCodeChange để đảm bảo re-render
  const handleCodeChange = useCallback((text: string) => {
    // Only allow numbers and limit to 6 digits
    const numericText = text.replace(/[^0-9]/g, '').slice(0, 6)
    onVerificationCodeChange(numericText)
  }, [onVerificationCodeChange])

  return (
    <View style={themed($formContainer)}>
      {/* Description Text */}
      <Text style={themed($descriptionText)}>
        We've sent a verification code to {phoneNumber}. Please enter the code below to sign in.
      </Text>

      {/* Verification Code Input Field */}
      <View style={themed($textField)}>
        <View style={themed($codeFieldContainer)}>
          <Icon
            icon="lock" // Lock icon for security
            size={19}
            color="#545454"
            style={themed($codeIcon)}
          />
          <TextInput
            ref={codeInput}
            value={verificationCode}
            onChangeText={handleCodeChange}
            style={themed($codeInput)}
            placeholder="Enter 6-digit code"
            placeholderTextColor="#505050"
            autoCapitalize="none"
            autoComplete="sms-otp"
            autoCorrect={false}
            keyboardType="number-pad"
            maxLength={6}
            onSubmitEditing={onSubmit}
          />
        </View>
      </View>

      {/* Error Message */}
      {error && (
        <View style={themed($errorContainer)}>
          <Text style={themed($errorText)}>{error}</Text>
        </View>
      )}

      {/* Resend Code Link */}
      <TouchableOpacity
        style={themed($resendContainer)}
        onPress={onResendCode}
        disabled={isLoading}
      >
        <Text style={themed($resendText)}>
          Didn't receive the code? Resend
        </Text>
      </TouchableOpacity>

      {/* Verify Button */}
      <TouchableOpacity
        testID="verify-button"
        style={themed($verifyButton)}
        onPress={onSubmit}
        disabled={isLoading || verificationCode.length !== 6}
        activeOpacity={0.8}
      >
        <Text style={themed($verifyButtonText)}>
          {isLoading ? "Verifying..." : "Verify & Sign In"}
        </Text>
        <View style={themed($buttonIconContainer)}>
          <Icon
            icon="caretRight"
            size={21}
            color="#0961F5"
          />
        </View>
      </TouchableOpacity>
    </View>
  )
}

// Styles theo Figma design và pattern của VerifyCodeForm
const $formContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Exact padding from Figma (x:34)
  alignItems: "center", // Center all form elements
})

const $descriptionText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for descriptions - MANDATORY
  fontWeight: "300", // Light weight for descriptions - MANDATORY
  fontSize: 14, // Exact size từ Figma
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Exact color từ Figma
  textAlign: "center",
  marginTop: 50, // Reduced spacing to fit on screen
  marginBottom: 30, // Space before input
  paddingHorizontal: 10, // Padding for text
})

const $textField: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  marginBottom: 20, // Exact spacing from Figma
})

const $codeFieldContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 12, // Exact border radius from Figma
  paddingHorizontal: 20, // Exact padding from Figma
  height: 60, // Exact height from Figma
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5, // Android shadow
})

const $codeIcon: ThemedStyle<ViewStyle> = () => ({
  marginRight: 12, // Space between icon and input
})

const $codeInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  flex: 1,
  fontSize: 16, // Slightly larger for code input
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for input text - MANDATORY
  fontWeight: "300", // Light weight for input text - MANDATORY
  color: "#505050", // Exact color from Figma
  paddingRight: 20, // Right padding
  paddingVertical: 0,
  height: 60, // Full height
  textAlignVertical: "center",
  lineHeight: 20,
  letterSpacing: 2, // Space between digits for better readability
})

const $errorContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 20,
  paddingHorizontal: 16,
})

const $errorText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for helper text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  color: colors.error,
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  textAlign: "center",
})

const $resendContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 30,
})

const $resendText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for body text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  fontSize: 14,
  lineHeight: 20,
  color: "#0961F5", // Primary blue color
  textAlign: "center",
  textDecorationLine: "underline",
})

const $verifyButton: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  backgroundColor: "#0961F5", // Primary blue from Figma
  borderRadius: 30, // Exact border radius from Figma
  paddingHorizontal: 30,
  height: 60, // Exact height from Figma
  width: "100%", // Full width
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8, // Android shadow
})

const $verifyButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.bold, // Bold font family for emphasis
  fontWeight: "700", // Bold weight for button text
  fontSize: 18,
  lineHeight: 20, // Design guidelines button text line height
  letterSpacing: 0.2, // Design guidelines button text letter spacing
  color: "#FFFFFF",
  flex: 1,
  textAlign: "center",
})

const $buttonIconContainer: ThemedStyle<ViewStyle> = () => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  alignItems: "center",
  justifyContent: "center",
})
