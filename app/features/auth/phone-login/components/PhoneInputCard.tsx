import { FC } from "react"
import { View, ViewStyle, TextStyle } from "react-native"
import { Button, Text, PhoneNumberInput } from "@/components"
import { type ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface PhoneInputCardProps {
  /**
   * Current phone number value
   */
  phoneNumber: string
  /**
   * Callback when phone number changes
   */
  onPhoneNumberChange: (phone: string) => void
  /**
   * Error message to display
   */
  error?: string
  /**
   * Whether the send button is loading
   */
  isLoading?: boolean
  /**
   * Callback when send OTP button is pressed
   */
  onSendOTP: () => void
  /**
   * Callback when back to login is pressed
   */
  onBackToLogin: () => void
  /**
   * Send button text
   */
  sendButtonText?: string
  /**
   * Loading button text
   */
  loadingButtonText?: string
  /**
   * Back to login text
   */
  backToLoginText?: string
  /**
   * Phone input label
   */
  phoneInputLabel?: string
  /**
   * Phone input placeholder
   */
  phoneInputPlaceholder?: string
  /**
   * Optional custom styles for the container
   */
  style?: ViewStyle
}

export const PhoneInputCard: FC<PhoneInputCardProps> = ({
  phoneNumber,
  onPhoneNumberChange,
  error,
  isLoading = false,
  onSendOTP,
  onBackToLogin,
  sendButtonText = "Send Verification Code",
  loadingButtonText = "Sending...",
  backToLoginText = "Back to Login",
  phoneInputLabel = "Phone Number",
  phoneInputPlaceholder = "Enter your phone number",
  style,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$phoneCard, style])}>
      <PhoneNumberInput
        value={phoneNumber}
        onChangeText={onPhoneNumberChange}
        label={phoneInputLabel}
        placeholder={phoneInputPlaceholder}
        helper={error}
        status={error ? "error" : undefined}
        containerStyle={themed($phoneInput)}
        autoFocus
      />

      <Button
        text={isLoading ? loadingButtonText : sendButtonText}
        style={themed($sendButton)}
        preset="reversed"
        onPress={onSendOTP}
        disabled={isLoading}
      />

      {/* Back to Login */}
      <View style={themed($backToLogin)}>
        <Text
          text={backToLoginText}
          style={themed($backToLoginText)}
          onPress={onBackToLogin}
        />
      </View>
    </View>
  )
}

const $phoneCard: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100,
  borderRadius: 24,
  padding: spacing.lg, // Reduced from xl for better spacing
  marginHorizontal: spacing.xs,
  marginBottom: spacing.lg,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 8 },
  shadowOpacity: 0.15,
  shadowRadius: 16,
  elevation: 8,
})

const $phoneInput: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.lg,
})

const $sendButton: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.sm,
  borderRadius: 16,
  paddingVertical: spacing.md,
})

const $backToLogin: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  marginTop: spacing.lg,
})

const $backToLoginText: ThemedStyle<TextStyle> = ({ colors, typography, spacing }) => ({
  fontSize: 16,
  fontFamily: typography.primary.light,
  color: colors.tint,
  textDecorationLine: "underline",
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.sm,
})
