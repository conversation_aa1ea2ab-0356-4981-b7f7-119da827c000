import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle, Platform, KeyboardAvoidingView } from "react-native"
import { Screen } from "@/components"
import { AuthStackScreenProps } from "@/navigators"
import {
  PhoneLoginHeader,
  PhoneVerifyForm,
} from "../components"
import { usePhoneVerify } from "../hooks"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"

interface PhoneVerifyScreenProps extends AuthStackScreenProps<"PhoneVerify"> {}

export const PhoneVerifyScreen: FC<PhoneVerifyScreenProps> = observer(
  function PhoneVerifyScreen(props) {
    const { navigation, route } = props
    const { phoneNumber, isLogin } = route.params
    const { themed } = useAppTheme()

    const {
      // State
      verificationCode,
      isLoading,
      error,

      // Actions
      setVerificationCode,
      handleVerifyCode,
      handleBackToPhoneLogin,
      handleResendCode,
    } = usePhoneVerify({ navigation, phoneNumber, isLogin })

    return (
      <View style={themed($container)}>
        {/* Background */}
        <View style={themed($background)} />

        <KeyboardAvoidingView
          style={themed($keyboardAvoidingView)}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <Screen
            preset="scroll"
            contentContainerStyle={themed($screenContentContainer)}
            safeAreaEdges={["top", "bottom"]}
            backgroundColor="transparent"
          >
            {/* Header */}
            <PhoneLoginHeader 
              title="Verify Phone Number"
              onBack={handleBackToPhoneLogin} 
            />

            {/* Phone Verification Form */}
            <PhoneVerifyForm
              phoneNumber={phoneNumber}
              verificationCode={verificationCode}
              isLoading={isLoading}
              error={error}
              onVerificationCodeChange={setVerificationCode}
              onSubmit={handleVerifyCode}
              onResendCode={handleResendCode}
            />
          </Screen>
        </KeyboardAvoidingView>
      </View>
    )
  }
)

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $background: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#F5F9FF", // Exact background color từ Figma
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 0, // Components handle their own padding
  paddingBottom: 50, // Space at bottom
})
