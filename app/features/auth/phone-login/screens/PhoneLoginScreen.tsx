import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle, Platform, KeyboardAvoidingView } from "react-native"
import { Screen } from "@/components"
import { AuthStackScreenProps } from "@/navigators"
import {
  PhoneLoginHeader,
  PhoneLoginForm,
} from "../components"
import { usePhoneLoginForm } from "../hooks"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"

interface PhoneLoginScreenProps extends AuthStackScreenProps<"PhoneLogin"> {}

export const PhoneLoginScreen: FC<PhoneLoginScreenProps> = observer(function PhoneLoginScreen(props) {
  const { navigation } = props
  const { themed } = useAppTheme()

  const {
    phoneNumber,
    error,
    isLoading,
    setPhoneNumber,
    handlePhoneLogin,
    handleBackToLogin,
  } = usePhoneLoginForm({ navigation })

  return (
    <View style={themed($container)}>
      {/* Background */}
      <View style={themed($background)} />

      <KeyboardAvoidingView
        style={themed($keyboardAvoidingView)}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <Screen
          preset="scroll"
          contentContainerStyle={themed($screenContentContainer)}
          safeAreaEdges={["top", "bottom"]}
          backgroundColor="transparent"
        >
          {/* Header */}
          <PhoneLoginHeader
            title="Phone Sign In"
            onBack={handleBackToLogin}
          />

          {/* Phone Login Form */}
          <PhoneLoginForm
            phoneNumber={phoneNumber}
            isLoading={isLoading}
            error={error}
            onPhoneNumberChange={setPhoneNumber}
            onSubmit={handlePhoneLogin}
          />
        </Screen>
      </KeyboardAvoidingView>
    </View>
  )
})

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $background: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#F5F9FF", // Exact background color từ Figma
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 0, // Components handle their own padding
  paddingBottom: 50, // Space at bottom
})




