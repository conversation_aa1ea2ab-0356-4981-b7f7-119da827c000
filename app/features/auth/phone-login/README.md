# Phone Login Feature

This directory contains all components, screens, and hooks related to the Phone Login functionality of the LMS app.

## Structure

```
app/features/phone-login/
├── components/           # Phone login specific components
│   ├── PhoneLoginHeader.tsx      # Logo, title, subtitle (78 lines)
│   ├── PhoneInputCard.tsx        # Phone input, send button, back link (105 lines)
│   ├── PhoneLoginInfo.tsx        # Info text and disclaimer (53 lines)
│   ├── PhoneLoginContainer.tsx   # Main container with background and keyboard handling (85 lines)
│   └── index.ts                  # Component exports
├── screens/             # Phone login screens
│   ├── PhoneLoginScreen.tsx      # Main phone login screen (49 lines)
│   └── index.ts                  # Screen exports
├── hooks/               # Phone login hooks
│   ├── usePhoneLogin.ts          # Phone login business logic (84 lines)
│   └── index.ts                  # Hook exports
├── index.ts            # Feature exports
└── README.md           # This file
```

## Components

### PhoneLoginHeader
- Displays logo icon, title, and subtitle
- Configurable via props (title, subtitle, logoIcon)
- 78 lines (well under 200 line limit)
- Reusable for other auth screens

### PhoneInputCard
- Phone input field with country code
- Send verification button with loading state
- Back to login link
- Comprehensive error handling
- 105 lines (well under 200 line limit)
- Separates UI from business logic

### PhoneLoginInfo
- Information text about SMS verification
- Disclaimer about message rates
- 53 lines (well under 200 line limit)
- Simple and focused component

### PhoneLoginContainer
- Main container with modern background
- Keyboard avoiding behavior
- Screen wrapper with safe area handling
- 85 lines (well under 200 line limit)
- Reusable container pattern

## Screens

### PhoneLoginScreen
- Main layout and navigation logic
- 49 lines (reduced from original larger file)
- Uses above components
- Clean separation of concerns
- Integrates with usePhoneLogin hook

## Hooks

### usePhoneLogin
- Handles phone number validation
- OTP sending logic
- Error handling and loading states
- Navigation between screens
- 84 lines (well under 200 line limit)
- Separates business logic from UI

## Usage

```typescript
// Import the entire phone login feature
import { PhoneLoginScreen, usePhoneLogin } from "@/features/phone-login"

// Or import specific components
import { PhoneLoginHeader, PhoneInputCard } from "@/features/phone-login/components"
import { usePhoneLogin } from "@/features/phone-login/hooks"
```

## Dependencies

- Uses `@/components/PhoneNumberInput` for phone input
- Uses `@/services/phoneAuth` for OTP sending
- Uses `@/models` for state management
- Integrates with navigation system

## Benefits

- ✅ Each component < 200 lines (follows user preference)
- ✅ Better separation of concerns
- ✅ Easier testing and maintenance
- ✅ Reusable components
- ✅ Clean business logic separation
- ✅ Organized by feature for better scalability
- ✅ Comprehensive error handling
- ✅ Modern UI patterns
