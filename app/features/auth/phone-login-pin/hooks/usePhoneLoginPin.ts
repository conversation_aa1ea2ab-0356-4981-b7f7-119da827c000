import { useState, useEffect } from "react"
import { Alert } from "react-native"
import type { NavigationProp } from "@react-navigation/native"
import type { AuthStackParamList } from "@/navigators"
import { useStores } from "@/models"

interface UsePhoneLoginPinProps {
  navigation: NavigationProp<AuthStackParamList>
  phoneNumber: string
}

export function usePhoneLoginPin({ navigation, phoneNumber }: UsePhoneLoginPinProps) {
  const [pin, setPin] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [resendTimer, setResendTimer] = useState(60)
  const [canResend, setCanResend] = useState(false)

  const {
    authenticationStore: { setAuthToken, setAuthMethod },
    userStore: { setCurrentUser },
  } = useStores()

  // Countdown timer for resend
  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer(resendTimer - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else {
      setCanResend(true)
    }
  }, [resendTimer])

  const validatePin = (pin: string): boolean => {
    if (!pin.trim()) {
      setError("Please enter the PIN code")
      return false
    }

    if (pin.length !== 6) {
      setError("Please enter a 6-digit PIN code")
      return false
    }

    // Check if PIN contains only numbers
    if (!/^\d{6}$/.test(pin)) {
      setError("PIN code must contain only numbers")
      return false
    }

    setError("")
    return true
  }

  const handleVerifyPin = async () => {
    if (!validatePin(pin)) {
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // TODO: Implement actual phone login PIN verification logic here
      // This should call your phone login API endpoint
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Simulate PIN verification success/failure
      if (pin === "123456") {
        // Mock successful phone login - create a demo user
        const mockUser = {
          id: "phone-user-" + Date.now(),
          email: `user-${phoneNumber}@example.com`,
          firstName: "Phone",
          lastName: "User",
          role: "student" as const, // Default to student, can be changed based on API response
          avatar: undefined,
          phoneNumber: phoneNumber,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }

        // Set authentication token
        setAuthToken(String(Date.now()))

        // Set authentication method
        setAuthMethod("phone")

        // Set current user in UserStore
        setCurrentUser(mockUser)

        // RootNavigator will automatically navigate to Student/Instructor navigator
        // based on user role and authentication state
      } else {
        setError("Invalid PIN code. Please try again.")
      }
    } catch (error) {
      setError("Failed to verify PIN. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleResendPin = async () => {
    if (!canResend) {
      return
    }

    setIsLoading(true)
    setError("")

    try {
      // TODO: Implement actual resend PIN logic here
      // This should call your phone login resend PIN API endpoint
      await new Promise(resolve => setTimeout(resolve, 1000))

      Alert.alert(
        "PIN Resent",
        `A new 6-digit PIN has been sent to your phone number ${phoneNumber}.`,
        [{ text: "OK" }]
      )

      // Reset timer
      setResendTimer(60)
      setCanResend(false)
      setPin("") // Clear current PIN
    } catch (error) {
      setError("Failed to resend PIN. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToPhoneLogin = () => {
    navigation.goBack()
  }

  return {
    // State
    pin,
    isLoading,
    error,
    resendTimer,
    canResend,
    
    // Actions
    setPin,
    handleVerifyPin,
    handleResendPin,
    handleBackToPhoneLogin,
  }
}
