/**
 * Phone Login PIN Verification Types
 */

export interface PhoneLoginPinData {
  /**
   * Phone number being verified
   */
  phoneNumber: string
  /**
   * 6-digit PIN code
   */
  pin: string
}

export interface PhoneLoginPinVerificationRequest {
  /**
   * Phone number
   */
  phoneNumber: string
  /**
   * 6-digit PIN code
   */
  pin: string
}

export interface PhoneLoginPinVerificationResponse {
  /**
   * Whether verification was successful
   */
  success: boolean
  /**
   * User data if verification successful
   */
  user?: {
    id: string
    name: string
    email?: string
    phoneNumber: string
    role: "student" | "instructor"
  }
  /**
   * Error message if verification failed
   */
  error?: string
  /**
   * Authentication token if verification successful
   */
  token?: string
}

export interface PhoneLoginPinResendRequest {
  /**
   * Phone number to resend PIN to
   */
  phoneNumber: string
}

export interface PhoneLoginPinResendResponse {
  /**
   * Whether resend was successful
   */
  success: boolean
  /**
   * Error message if resend failed
   */
  error?: string
  /**
   * Time until next resend allowed (in seconds)
   */
  nextResendIn?: number
}

export interface PhoneLoginPinFormData {
  /**
   * Current PIN input value
   */
  pin: string
  /**
   * Whether form is in loading state
   */
  isLoading: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Phone number being verified
   */
  phoneNumber: string
  /**
   * Resend timer countdown
   */
  resendTimer: number
  /**
   * Whether can resend PIN
   */
  canResend: boolean
}

export interface PhoneLoginPinScreenParams {
  /**
   * Phone number that was used for login
   */
  phoneNumber: string
  /**
   * Optional user role hint
   */
  userRole?: "student" | "instructor"
}
