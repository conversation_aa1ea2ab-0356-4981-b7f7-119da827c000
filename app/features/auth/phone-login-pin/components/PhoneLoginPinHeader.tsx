import { FC } from "react"
import {
  View,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface PhoneLoginPinHeaderProps {
  onBack?: () => void
}

export const PhoneLoginPinHeader: FC<PhoneLoginPinHeaderProps> = ({
  onBack,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($headerContainer)}>
      {/* Back Button */}
      <TouchableOpacity
        onPress={onBack}
        style={themed($backButton)}
        activeOpacity={0.7}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Icon
          icon="back"
          size={20}
          color="#202244"
        />
      </TouchableOpacity>

      {/* Title */}
      <Text style={themed($headerTitle)}>Verify PIN</Text>
    </View>
  )
}

// Styles theo Figma design
const $headerContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: 35, // x:35 từ Figma
  paddingTop: 25, // y:69 - y:44 (status bar)
  paddingBottom: 20,
  width: "100%",
})

const $backButton: ThemedStyle<ViewStyle> = () => ({
  padding: 5, // Touch area
  marginRight: 12, // Space between back button and title
})

const $headerTitle: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.semiBold, // Design guidelines: SemiBold (600) for headings
  fontWeight: "600", // SemiBold weight for headings
  fontSize: 21, // Exact size từ Figma
  lineHeight: 30, // 1.445em * 21px
  color: "#202244", // Exact color từ Figma
  textAlign: "left",
})
