import { FC, useRef, useCallback } from "react"
import { TextInput, ViewStyle, TextStyle, View, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface PhoneLoginPinFormProps {
  /**
   * PIN code value (6 digits for phone login)
   */
  pin: string
  /**
   * Whether the form is loading
   */
  isLoading: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Phone number being verified
   */
  phoneNumber: string
  /**
   * Resend timer countdown
   */
  resendTimer: number
  /**
   * Whether can resend PIN
   */
  canResend: boolean
  /**
   * PIN change handler
   */
  onPinChange: (pin: string) => void
  /**
   * Verify PIN handler
   */
  onVerify: () => void
  /**
   * Resend PIN handler
   */
  onResend: () => void
}

export const PhoneLoginPinForm: FC<PhoneLoginPinFormProps> = ({
  pin,
  isLoading,
  error,
  phoneNumber,
  resendTimer,
  canResend,
  onPinChange,
  onVerify,
  onResend,
}) => {
  const pinInputs = useRef<(TextInput | null)[]>([])
  const { themed } = useAppTheme()

  // Split PIN into individual digits (6 digits for phone login)
  const pinDigits = pin.padEnd(6, ' ').split('').slice(0, 6)

  const handlePinChange = useCallback((text: string, index: number) => {
    const newPin = [...pinDigits]
    newPin[index] = text
    const updatedPin = newPin.join('').replace(/\s/g, '')
    onPinChange(updatedPin)

    // Auto focus next input
    if (text && index < 5) {
      pinInputs.current[index + 1]?.focus()
    }
  }, [pinDigits, onPinChange])

  const handleKeyPress = useCallback((key: string, index: number) => {
    if (key === 'Backspace' && !pinDigits[index] && index > 0) {
      pinInputs.current[index - 1]?.focus()
    }
  }, [pinDigits])

  const maskPhoneNumber = (phone: string): string => {
    // Phone number masking for display
    if (phone.length >= 4) {
      return phone.slice(0, -4).replace(/\d/g, '*') + phone.slice(-4)
    }
    return phone
  }

  return (
    <View style={themed($formContainer)}>
      {/* Description Text */}
      <Text style={themed($descriptionText)}>
        Enter the 6-digit PIN sent to {maskPhoneNumber(phoneNumber)}
      </Text>

      {/* PIN Input Fields */}
      <View style={themed($pinContainer)}>
        {pinDigits.map((digit, index) => (
          <View key={index} style={themed($pinInputContainer)}>
            <TextInput
              ref={(ref) => (pinInputs.current[index] = ref)}
              value={digit.trim()}
              onChangeText={(text) => handlePinChange(text, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              style={themed($pinInput)}
              maxLength={1}
              keyboardType="numeric"
              textAlign="center"
              secureTextEntry={digit.trim() !== '' && digit !== ' '}
              placeholder=""
            />
            {digit.trim() !== '' && digit !== ' ' && (
              <Text style={themed($pinDisplayText)}>*</Text>
            )}
          </View>
        ))}
      </View>

      {/* Error Message */}
      {error && (
        <View style={themed($errorContainer)}>
          <Text style={themed($errorText)}>{error}</Text>
        </View>
      )}

      {/* Resend PIN */}
      <TouchableOpacity
        style={themed($resendContainer)}
        onPress={onResend}
        disabled={!canResend}
        activeOpacity={0.8}
      >
        <Text style={themed($resendText)}>
          {canResend ? "Resend PIN" : `Resend PIN in ${resendTimer}s`}
        </Text>
      </TouchableOpacity>

      {/* Verify Button */}
      <TouchableOpacity
        testID="verify-pin-button"
        style={themed($verifyButton)}
        onPress={onVerify}
        disabled={isLoading || pin.length !== 6}
        activeOpacity={0.8}
      >
        <Text style={themed($verifyButtonText)}>
          {isLoading ? "Verifying..." : "Verify PIN"}
        </Text>
        <View style={themed($buttonIconContainer)}>
          <Icon
            icon="caretRight"
            size={21}
            color="#0961F5"
          />
        </View>
      </TouchableOpacity>
    </View>
  )
}

// Styles theo Figma design
const $formContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Exact padding from Figma (x:34)
  alignItems: "center", // Center all form elements
})

const $descriptionText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for descriptions - MANDATORY
  fontWeight: "300", // Light weight for descriptions - MANDATORY
  fontSize: 14, // Exact size từ Figma
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Exact color từ Figma
  textAlign: "center",
  marginTop: 50, // Reduced spacing to fit on screen
  marginBottom: 40, // Space before PIN inputs
  paddingHorizontal: 10, // Padding for text
})

const $pinContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  justifyContent: "space-between",
  width: "100%", // Full width
  marginBottom: 30, // Space before resend
})

const $pinInputContainer: ThemedStyle<ViewStyle> = () => ({
  position: "relative",
  width: 50, // Smaller width for 6 digits
  height: 60, // Exact height from Figma
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 12, // Exact border radius from Figma
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5, // Android shadow
  alignItems: "center",
  justifyContent: "center",
})

const $pinInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  position: "absolute",
  width: "100%",
  height: "100%",
  fontSize: 22, // Exact from Figma
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for input text - MANDATORY
  fontWeight: "800", // Bold weight for PIN digits
  color: "transparent", // Hide actual input text
  textAlign: "center",
  textAlignVertical: "center",
})

const $pinDisplayText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontSize: 30, // Exact from Figma for asterisk
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for input text - MANDATORY
  fontWeight: "800", // Bold weight for asterisk
  color: "#505050", // Exact color from Figma
  textAlign: "center",
  position: "absolute",
})

const $errorContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 20,
  paddingHorizontal: 16,
})

const $errorText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for helper text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  color: colors.error,
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  textAlign: "center",
})

const $resendContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 40, // Space before verify button
})

const $resendText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for descriptions - MANDATORY
  fontWeight: "700", // Bold weight for resend text
  fontSize: 14, // Exact size từ Figma
  lineHeight: 18, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Exact color từ Figma
  textAlign: "center",
})

const $verifyButton: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  backgroundColor: "#0961F5", // Primary blue from Figma
  borderRadius: 30, // Exact border radius from Figma
  paddingHorizontal: 30,
  height: 60, // Exact height from Figma
  width: "100%", // Full width
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8, // Android shadow
})

const $verifyButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for button text - MANDATORY
  fontWeight: "600", // Semi-bold weight for button text
  fontSize: 18,
  lineHeight: 20, // Design guidelines button text line height
  letterSpacing: 0.2, // Design guidelines button text letter spacing
  color: "#FFFFFF",
  flex: 1,
  textAlign: "center",
})

const $buttonIconContainer: ThemedStyle<ViewStyle> = () => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  alignItems: "center",
  justifyContent: "center",
})
