import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle, Platform, KeyboardAvoidingView } from "react-native"
import { Screen } from "@/components"
import { AuthStackScreenProps } from "@/navigators"
import { PhoneLoginPinHeader, PhoneLoginPinForm } from "../components"
import { usePhoneLoginPin } from "../hooks/usePhoneLoginPin"
import { useAppTheme, ThemedStyle } from "@/utils/useAppTheme"

interface PhoneLoginPinScreenProps extends AuthStackScreenProps<"PhoneLoginPin"> {}

export const PhoneLoginPinScreen: FC<PhoneLoginPinScreenProps> = observer(
  function PhoneLoginPinScreen(props) {
    const { navigation, route } = props
    const { themed } = useAppTheme()
    
    // Get params from navigation
    const { phoneNumber } = route.params

    const {
      // State
      pin,
      isLoading,
      error,
      resendTimer,
      canResend,

      // Actions
      setPin,
      handleVerifyPin,
      handleResendPin,
      handleBackToPhoneLogin,
    } = usePhoneLoginPin({ navigation, phoneNumber })

    return (
      <View style={themed($container)}>
        {/* Background */}
        <View style={themed($background)} />

        <KeyboardAvoidingView
          style={themed($keyboardAvoidingView)}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <Screen
            preset="scroll"
            contentContainerStyle={themed($screenContentContainer)}
            safeAreaEdges={["top", "bottom"]}
            backgroundColor="transparent"
          >
            {/* Header */}
            <PhoneLoginPinHeader onBack={handleBackToPhoneLogin} />

            {/* Phone Login PIN Form */}
            <PhoneLoginPinForm
              pin={pin}
              isLoading={isLoading}
              error={error}
              phoneNumber={phoneNumber}
              resendTimer={resendTimer}
              canResend={canResend}
              onPinChange={setPin}
              onVerify={handleVerifyPin}
              onResend={handleResendPin}
            />
          </Screen>
        </KeyboardAvoidingView>
      </View>
    )
  }
)

// Styles theo Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $background: ThemedStyle<ViewStyle> = () => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#F5F9FF", // Exact background color từ Figma
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 0, // Components handle their own padding
  paddingBottom: 50, // Space at bottom
})


