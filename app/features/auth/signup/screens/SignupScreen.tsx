import { observer } from "mobx-react-lite"
import { FC } from "react"
import { ViewStyle, View, KeyboardAvoidingView, Platform } from "react-native"
import { Screen } from "@/components"
import { AuthStackScreenProps } from "@/navigators/AuthNavigator"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import {
  SignupHeader,
  SignupForm,
  SocialSignupSection,
  SignInPrompt,
} from "../components"
import { useSignupLogic } from "../hooks"

interface SignupScreenProps extends AuthStackScreenProps<"Signup"> {}

export const SignupScreen: FC<SignupScreenProps> = observer(function SignupScreen(props) {
  const { themed } = useAppTheme()
  
  const {
    // Form state
    authEmail,
    authPassword,
    isAuthPasswordHidden,
    attemptsCount,
    isLoading,
    error,
    agreeToTerms,
    
    // Form handlers
    setAuthEmail,
    setAuthPassword,
    setIsAuthPasswordHidden,
    setAgreeToTerms,
    
    // Actions
    signup,
    handleGoogleSignup,
    handlePhoneSignup,
  } = useSignupLogic(props.navigation)

  const handleSignIn = () => {
    props.navigation.navigate("Login")
  }

  const togglePasswordVisibility = () => {
    setIsAuthPasswordHidden(!isAuthPasswordHidden)
  }

  return (
    <View style={themed($container)}>
      {/* Modern Background */}
      <View style={themed($modernBackground)} />

      <KeyboardAvoidingView
        style={themed($keyboardAvoidingView)}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <Screen
          preset="scroll"
          contentContainerStyle={themed($screenContentContainer)}
          safeAreaEdges={["top", "bottom"]}
          backgroundColor="transparent"
        >
          {/* Welcome Section */}
          <SignupHeader />

          {/* Form Section */}
          <SignupForm
            email={authEmail}
            password={authPassword}
            isPasswordHidden={isAuthPasswordHidden}
            isLoading={isLoading}
            error={error}
            attemptsCount={attemptsCount}
            agreeToTerms={agreeToTerms}
            onEmailChange={setAuthEmail}
            onPasswordChange={setAuthPassword}
            onTogglePasswordVisibility={togglePasswordVisibility}
            onAgreeToTermsChange={setAgreeToTerms}
            onSubmit={signup}
          />

          <SocialSignupSection
            isLoading={isLoading}
            onGoogleSignup={handleGoogleSignup}
            onPhoneSignup={handlePhoneSignup}
          />

          <SignInPrompt onSignIn={handleSignIn} />
        </Screen>
      </KeyboardAvoidingView>
    </View>
  )
})

// Modern Styles
const $container: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $modernBackground: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  left: 0,
  right: 0,
  top: 0,
  bottom: 0,
  backgroundColor: "#F5F9FF", // Exact background color from Figma
})

const $keyboardAvoidingView: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
})

const $screenContentContainer: ThemedStyle<ViewStyle> = () => ({
  paddingHorizontal: 0, // Remove default padding since components handle their own
  paddingBottom: 20,
})
