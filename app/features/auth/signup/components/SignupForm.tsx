import { FC, useRef, useCallback } from "react"
import { TextInput, ViewStyle, TextStyle, View, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface SignupFormProps {
  /**
   * Email value
   */
  email: string
  /**
   * Password value
   */
  password: string
  /**
   * Whether password is hidden
   */
  isPasswordHidden: boolean
  /**
   * Whether form is loading
   */
  isLoading: boolean
  /**
   * Error message to display
   */
  error?: string
  /**
   * Number of signup attempts
   */
  attemptsCount: number
  /**
   * Terms agreement state
   */
  agreeToTerms: boolean
  /**
   * Email change handler
   */
  onEmailChange: (email: string) => void
  /**
   * Password change handler
   */
  onPasswordChange: (password: string) => void
  /**
   * Toggle password visibility
   */
  onTogglePasswordVisibility: () => void
  /**
   * Terms agreement change handler
   */
  onAgreeToTermsChange: (agree: boolean) => void
  /**
   * Signup submit handler
   */
  onSubmit: () => void
}

export const SignupForm: FC<SignupFormProps> = ({
  email,
  password,
  isPasswordHidden,
  isLoading,
  error,
  attemptsCount,
  agreeToTerms,
  onEmailChange,
  onPasswordChange,
  onTogglePasswordVisibility,
  onAgreeToTermsChange,
  onSubmit,
}) => {
  const authPasswordInput = useRef<TextInput>(null)
  const {
    themed,
    theme: { colors },
  } = useAppTheme()

  // Wrap onPasswordChange để đảm bảo re-render
  const handlePasswordChange = useCallback((text: string) => {
    onPasswordChange(text)
  }, [onPasswordChange])

  return (
    <View style={themed($formContainer)}>
      {attemptsCount > 2 && (
        <View style={themed($hintContainer)}>
          <Text tx="signupScreen:hint" size="sm" weight="light" style={themed($hint)} />
        </View>
      )}

      {/* Custom Email Field to match Figma design */}
      <View style={themed($textField)}>
        <View style={themed($emailFieldContainer)}>
          <Icon
            icon="settings"
            size={19}
            color="#545454"
            style={themed($emailIcon)}
          />
          <TextInput
            value={email}
            onChangeText={onEmailChange}
            style={themed($emailInput)}
            placeholder="Email"
            placeholderTextColor="#505050"
            autoCapitalize="none"
            autoComplete="username"
            autoCorrect={false}
            keyboardType="email-address"
            onSubmitEditing={() => authPasswordInput.current?.focus()}
          />
        </View>
      </View>

      {/* Custom Password Field to match Figma design */}
      <View style={themed($textField)}>
        <View style={themed($passwordFieldContainer)}>
          <Icon
            icon="lock"
            size={19}
            color="#545454"
            style={themed($passwordIcon)}
          />
          <TextInput
            ref={authPasswordInput}
            value={password}
            onChangeText={handlePasswordChange}
            style={themed($passwordInput)}
            placeholder="Password"
            placeholderTextColor="#505050"
            autoCapitalize="none"
            autoComplete="password"
            autoCorrect={false}
            secureTextEntry={isPasswordHidden}
            onSubmitEditing={onSubmit}
          />
          {/* Icon ẩn/hiện password - luôn luôn hiển thị */}
          <TouchableOpacity
            onPress={onTogglePasswordVisibility}
            style={themed($eyeIconContainer)}
            activeOpacity={0.7}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <Icon
              icon={isPasswordHidden ? "hidden" : "view"}
              size={18}
              color="#545454"
            />
          </TouchableOpacity>
        </View>
      </View>

      {/* Terms and Conditions Checkbox */}
      <View style={themed($termsContainer)}>
        <TouchableOpacity
          onPress={() => onAgreeToTermsChange(!agreeToTerms)}
          style={themed($checkboxContainer)}
        >
          <View style={themed($checkbox)}>
            {agreeToTerms && (
              <Icon
                icon="check"
                size={16}
                color="#50B748"
              />
            )}
          </View>
        </TouchableOpacity>
        <Text style={themed($termsText)}>Agree to Terms & Conditions</Text>
      </View>

      {/* Error Message */}
      {error && (
        <View style={themed($errorContainer)}>
          <Text style={themed($errorText)}>{error}</Text>
        </View>
      )}

      {/* Sign Up Button */}
      <TouchableOpacity
        testID="signup-button"
        style={themed($signupButton)}
        onPress={onSubmit}
        disabled={isLoading || !agreeToTerms}
        activeOpacity={0.8}
      >
        <Text style={themed($signupButtonText)}>
          {isLoading ? "Creating Account..." : "Sign Up"}
        </Text>
        <View style={themed($buttonIconContainer)}>
          <Icon
            icon="caretRight"
            size={21}
            color="#0961F5"
          />
        </View>
      </TouchableOpacity>
    </View>
  )
}

// Styles following Figma design
const $formContainer: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Exact padding from Figma (x:34)
  alignItems: "center", // Center all form elements
})

const $hintContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "rgba(59, 130, 246, 0.1)",
  borderRadius: 12,
  padding: spacing.sm,
  marginBottom: spacing.md,
})

const $hint: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for helper text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  color: colors.tint,
})

const $textField: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  marginBottom: 20, // Exact spacing from Figma
})

const $emailFieldContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 12, // Exact border radius from Figma
  paddingHorizontal: 20, // Exact padding from Figma
  height: 60, // Exact height from Figma
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5, // Android shadow
})

const $passwordFieldContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "#FFFFFF", // White background from Figma
  borderRadius: 12, // Exact border radius from Figma
  paddingHorizontal: 20, // Exact padding from Figma
  height: 60, // Exact height from Figma
  shadowColor: "#000000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5, // Android shadow
})

const $emailIcon: ThemedStyle<ViewStyle> = () => ({
  marginRight: 12, // Space between icon and input
})

const $passwordIcon: ThemedStyle<ViewStyle> = () => ({
  marginRight: 12, // Space between icon and input
})

const $emailInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  flex: 1,
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.medium, // 500 weight - MANDATORY
  color: "#505050", // Exact color from Figma
  paddingRight: 20, // Right padding
  paddingVertical: 0,
  height: 60, // Full height
  textAlignVertical: "center",
  lineHeight: 18, // 1.255em * 14px
})

const $passwordInput: ThemedStyle<TextStyle> = ({ typography }) => ({
  flex: 1,
  fontSize: 14, // Exact from Figma
  fontFamily: typography.primary.medium, // 500 weight - MANDATORY
  color: "#505050", // Exact color from Figma
  paddingRight: 8, // Right padding - space for potential eye icon
  paddingVertical: 0,
  height: 60, // Full height
  textAlignVertical: "center",
  lineHeight: 18, // 1.255em * 14px
})

const $eyeIconContainer: ThemedStyle<ViewStyle> = () => ({
  padding: 8, // Touch target
  alignItems: "center",
  justifyContent: "center",
  minWidth: 32, // Minimum touch target width
  minHeight: 32, // Minimum touch target height
})

const $termsContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "flex-start", // Căn trái
  marginBottom: 40, // Space before button
  paddingHorizontal: 2, // Slight padding for alignment
  width: "100%", // Full width để có thể căn trái
})

const $checkboxContainer: ThemedStyle<ViewStyle> = () => ({
  marginRight: 8,
})

const $checkbox: ThemedStyle<ViewStyle> = () => ({
  width: 24,
  height: 24,
  borderRadius: 4,
  borderWidth: 2,
  borderColor: "#50B748",
  backgroundColor: "#FFFFFF",
  alignItems: "center",
  justifyContent: "center",
})

const $termsText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for body text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  fontSize: 13,
  lineHeight: 16, // Design guidelines caption line height
  letterSpacing: 0.1, // Design guidelines caption letter spacing
  color: "#545454", // Gray from Figma
  textAlign: "left", // Căn trái
  flex: 1, // Cho phép text wrap nếu cần
})

const $errorContainer: ThemedStyle<ViewStyle> = () => ({
  marginBottom: 20,
  paddingHorizontal: 16,
})

const $errorText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for helper text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  color: colors.error,
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  textAlign: "center",
})

const $signupButton: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  backgroundColor: "#0961F5", // Primary blue from Figma
  borderRadius: 30, // Exact border radius from Figma
  paddingHorizontal: 30,
  height: 60, // Exact height from Figma
  width: "100%", // Full width
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.3,
  shadowRadius: 8,
  elevation: 8, // Android shadow
})

const $signupButtonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // 500 weight - MANDATORY
  fontSize: 18,
  lineHeight: 20, // Design guidelines button text line height
  letterSpacing: 0.2, // Design guidelines button text letter spacing
  color: "#FFFFFF",
  flex: 1,
  textAlign: "center",
})

const $buttonIconContainer: ThemedStyle<ViewStyle> = () => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#FFFFFF",
  alignItems: "center",
  justifyContent: "center",
})
