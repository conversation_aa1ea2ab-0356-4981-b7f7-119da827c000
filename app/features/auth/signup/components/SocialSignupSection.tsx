import { FC } from "react"
import { ViewStyle, TextStyle, ImageStyle, View, TouchableOpacity } from "react-native"
import { Text, Icon } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface SocialSignupSectionProps {
  /**
   * Whether the form is loading
   */
  isLoading: boolean
  /**
   * Google signup handler
   */
  onGoogleSignup: () => void
  /**
   * Phone signup handler
   */
  onPhoneSignup: () => void
}

export const SocialSignupSection: FC<SocialSignupSectionProps> = ({
  isLoading,
  onGoogleSignup,
  onPhoneSignup,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($container)}>
      {/* Divider with text */}
      <View style={themed($dividerContainer)}>
        <View style={themed($dividerLine)} />
        <Text style={themed($dividerText)}>Or Continue With</Text>
        <View style={themed($dividerLine)} />
      </View>

      {/* Social Media Buttons */}
      <View style={themed($socialButtonsContainer)}>
        {/* Google Button */}
        <TouchableOpacity
          style={themed($socialButton)}
          onPress={onGoogleSignup}
          disabled={isLoading}
          activeOpacity={0.8}
        >
          <View style={themed($googleIconContainer)}>
            <Icon
              icon="google"
              size={20}
              style={themed($googleIconStyle)}
            />
          </View>
        </TouchableOpacity>

        {/* Phone Button */}
        <TouchableOpacity
          style={themed($socialButton)}
          onPress={onPhoneSignup}
          disabled={isLoading}
          activeOpacity={0.8}
        >
          <View style={themed($phoneIconContainer)}>
            <Icon
              icon="phone"
              size={20}
              style={themed($phoneIconStyle)}
            />
          </View>
        </TouchableOpacity>
      </View>
    </View>
  )
}

// Styles following Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  width: "100%",
  paddingHorizontal: 34, // Consistent with form padding
  alignItems: "center",
  marginTop: 25, // Space after signup button
  marginBottom: 25, // Same as SocialLoginSection in LoginScreen
})

const $dividerContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
  width: "100%",
  marginBottom: 25, // Space before social buttons
})

const $dividerLine: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  height: 1,
  backgroundColor: "#E2E6EA", // Light gray from design
})

const $dividerText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for body text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Gray from Figma
  paddingHorizontal: 20, // Space around text
  textAlign: "center",
})

const $socialButtonsContainer: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: 50, // Space between buttons - from Figma (98px between centers, 48px button width = 50px gap)
})

const $socialButton: ThemedStyle<ViewStyle> = () => ({
  width: 48, // Exact size from Figma
  height: 48, // Exact size from Figma
  borderRadius: 24, // Perfect circle
  backgroundColor: "#FFFFFF",
  alignItems: "center",
  justifyContent: "center",
  shadowColor: "#000000",
  shadowOffset: { width: 1, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 10,
  elevation: 5, // Android shadow
})

const $googleIconContainer: ThemedStyle<ViewStyle> = () => ({
  width: 20,
  height: 20,
  alignItems: "center",
  justifyContent: "center",
})



const $googleIconStyle: ThemedStyle<ImageStyle> = () => ({
  tintColor: null, // Không áp dụng tint color để giữ màu gốc của icon Google
})

const $phoneIconContainer: ThemedStyle<ViewStyle> = () => ({
  width: 20,
  height: 20,
  alignItems: "center",
  justifyContent: "center",
})

const $phoneIconStyle: ThemedStyle<ImageStyle> = () => ({
  tintColor: null, // Không áp dụng tint color để giữ màu gốc của icon phone
})
