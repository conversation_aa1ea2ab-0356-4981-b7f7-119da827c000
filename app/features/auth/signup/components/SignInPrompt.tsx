import { FC } from "react"
import { ViewStyle, TextStyle, TouchableOpacity } from "react-native"
import { Text } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface SignInPromptProps {
  /**
   * Sign in handler
   */
  onSignIn: () => void
}

export const SignInPrompt: FC<SignInPromptProps> = ({ onSignIn }) => {
  const { themed } = useAppTheme()

  return (
    <TouchableOpacity
      style={themed($container)}
      onPress={onSignIn}
      activeOpacity={0.8}
    >
      <Text style={themed($promptText)}>
        Already have an Account?{" "}
        <Text style={themed($signInText)}>SIGN IN</Text>
      </Text>
    </TouchableOpacity>
  )
}

// Styles following Figma design
const $container: ThemedStyle<ViewStyle> = () => ({
  alignItems: "center",
  justifyContent: "center",
  paddingHorizontal: 34, // Consistent with other components
  paddingVertical: 0, // No vertical padding, match LoginScreen
  marginBottom: 40, // Same as SignUpPrompt marginBottom in LoginScreen
})

const $promptText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // 500 weight - MANDATORY
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Gray from Figma
  textAlign: "center",
})

const $signInText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light, // Design guidelines: Light (300) for body text - MANDATORY
  fontWeight: "300", // Light weight - MANDATORY
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#0961F5", // Primary blue from Figma
  textDecorationLine: "underline",
  textDecorationColor: "#0961F5",
})
