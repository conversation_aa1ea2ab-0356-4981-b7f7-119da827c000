import { FC, useRef, useEffect } from "react"
import { ViewStyle, TextStyle, View, Animated, ImageSourcePropType, Image, ImageStyle } from "react-native"
import { Text } from "@/components"
import type { ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface SignupHeaderProps {
  /**
   * Optional logo source
   */
  logoSource?: ImageSourcePropType
  /**
   * Main heading text
   */
  headingText?: string
  /**
   * Subtitle text
   */
  subtitleText?: string
  /**
   * Animation duration in milliseconds
   */
  animationDuration?: number
}

export const SignupHeader: FC<SignupHeaderProps> = ({
  logoSource,
  headingText = "Getting Started.!",
  subtitleText = "Create an Account to Continue your allCourses",
  animationDuration = 800,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current
  const slideAnim = useRef(new Animated.Value(50)).current

  const { themed } = useAppTheme()

  useEffect(() => {
    // Entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: animationDuration,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: animationDuration,
        useNativeDriver: true,
      }),
    ]).start()
  }, [fadeAnim, slideAnim, animationDuration])

  return (
    <Animated.View
      style={[
        themed($headerContainer),
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      {/* Logo Section - Vantis Logo */}
      <View style={themed($logoSection)}>
        <Image
          source={require("assets/images/logo/vantis-logo.png")}
          style={themed($vantisLogo)}
          resizeMode="contain"
        />
      </View>

      {/* Welcome Text */}
      <View style={themed($textContainer)}>
        <Text style={themed($headingText)}>{headingText}</Text>
        <Text style={themed($subtitleText)}>{subtitleText}</Text>
      </View>
    </Animated.View>
  )
}

// Styles following Figma design
const $headerContainer: ThemedStyle<ViewStyle> = () => ({
  alignItems: "center",
  paddingTop: 85, // Adjusted for status bar + spacing
  paddingBottom: 40,
})

const $logoSection: ThemedStyle<ViewStyle> = () => ({
  alignItems: "center",
  marginBottom: 60, // Space between logo and text
})

const $vantisLogo: ThemedStyle<ImageStyle> = () => ({
  width: 200, // Appropriate width for Vantis logo
  height: 80, // Appropriate height for Vantis logo
  alignSelf: "center",
})



const $textContainer: ThemedStyle<ViewStyle> = () => ({
  alignItems: "flex-start", // Left align as per Figma
  paddingHorizontal: 34, // Exact padding from Figma
  width: "100%",
})

const $headingText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.semiBold, // Design guidelines: SemiBold (600) for section headings
  fontWeight: "600",
  fontSize: 24,
  lineHeight: 26, // Design guidelines section heading line height
  letterSpacing: -0.3, // Design guidelines letter spacing
  color: "#202244", // Dark blue from Figma
  marginBottom: 10,
  textAlign: "left",
})

const $subtitleText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // Design guidelines: Medium (500) for descriptions - MANDATORY
  fontWeight: "500", // Medium weight for descriptions - MANDATORY
  fontSize: 14,
  lineHeight: 20, // Design guidelines description line height
  letterSpacing: 0, // Design guidelines letter spacing
  color: "#545454", // Gray from Figma
  textAlign: "left",
  maxWidth: 312, // Exact width from Figma
})
