import { useEffect, useState } from "react"
import { useStores } from "@/models"
import { NavigationProp } from "@react-navigation/native"
import { AuthStackParamList } from "@/navigators/AuthNavigator"

export function useSignupLogic(navigation: NavigationProp<AuthStackParamList>) {
  const {
    authenticationStore: {
      authEmail,
      authMethod,
      setAuthEmail,
      setAuthMethod,
      setIsLoading,
      setAuthError,
      isLoading,
      authError,
      validationError,
    },
    userStore: { setCurrentUser },
  } = useStores()

  const [authPassword, setAuthPassword] = useState("")
  const [isAuthPasswordHidden, setIsAuthPasswordHidden] = useState(true)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [attemptsCount, setAttemptsCount] = useState(0)
  const [agreeToTerms, setAgreeToTerms] = useState(false)

  useEffect(() => {
    // Clear form on mount
    setAuthEmail("")
    setAuthPassword("")
    setAgreeToTerms(false)

    // Return a "cleanup" function that <PERSON>act will run when the component unmounts
    return () => {
      setAuthPassword("")
      setAuthEmail("")
      setAgreeToTerms(false)
    }
  }, [setAuthEmail])

  const error = isSubmitted ? validationError || authError : ""

  async function signup() {
    setIsSubmitted(true)
    setIsLoading(true)
    setAttemptsCount(attemptsCount + 1)

    if (validationError) {
      setIsLoading(false)
      return
    }

    if (!agreeToTerms) {
      setAuthError("You must agree to the Terms & Conditions")
      setIsLoading(false)
      return
    }

    if (authPassword.length < 6) {
      setAuthError("Password must be at least 6 characters")
      setIsLoading(false)
      return
    }

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Mock successful signup
      const mockUser = {
        id: "new-user-" + Date.now(),
        username: authEmail.includes('@') ? authEmail.split('@')[0] : authEmail,
        email: authEmail.includes('@') ? authEmail : `${authEmail}@example.com`,
        name: authEmail.includes('@') ? authEmail.split('@')[0] : authEmail,
        role: "student" as const,
        avatar: null,
        bio: "",
        phone: "",
        address: "",
        dateOfBirth: "",
        enrolledCourses: [],
        completedCourses: [],
        certificates: [],
        preferences: {
          notifications: true,
          darkMode: false,
          language: "en",
        },
        stats: {
          totalCourses: 0,
          completedCourses: 0,
          totalHours: 0,
          certificates: 0,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // Set user data - RootNavigator will automatically switch to appropriate navigator
      setCurrentUser(mockUser)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Signup failed"
      setAuthError(errorMessage)
      console.error("Signup error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  async function handleGoogleSignup() {
    setAuthMethod("google")
    setIsLoading(true)
    setAuthError("")

    try {
      // Simulate Google signup
      await new Promise((resolve) => setTimeout(resolve, 1500))

      // Mock successful Google signup
      const mockUser = {
        id: "google-user-" + Date.now(),
        username: "googleuser",
        email: "<EMAIL>",
        name: "Google User",
        role: "student" as const,
        avatar: null,
        bio: "",
        phone: "",
        address: "",
        dateOfBirth: "",
        enrolledCourses: [],
        completedCourses: [],
        certificates: [],
        preferences: {
          notifications: true,
          darkMode: false,
          language: "en",
        },
        stats: {
          totalCourses: 0,
          completedCourses: 0,
          totalHours: 0,
          certificates: 0,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // Set user data - RootNavigator will automatically switch to appropriate navigator
      setCurrentUser(mockUser)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Google sign-up failed"
      setAuthError(errorMessage)
      console.error("Google signup error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  function handlePhoneSignup() {
    setAuthMethod("phone")
    navigation.navigate("PhoneLogin")
  }

  return {
    // Form state
    authEmail,
    authPassword,
    isAuthPasswordHidden,
    isSubmitted,
    attemptsCount,
    isLoading,
    error,
    agreeToTerms,

    // Form handlers
    setAuthEmail,
    setAuthPassword,
    setIsAuthPasswordHidden,
    setAgreeToTerms,

    // Actions
    signup,
    handleGoogleSignup,
    handlePhoneSignup,
  }
}
