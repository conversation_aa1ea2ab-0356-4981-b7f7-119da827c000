import { FC } from "react"
import { TextStyle, View, ViewStyle } from "react-native"
import { Button } from "@/components"
import { type ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

interface WelcomeActionButtonsProps {
  /**
   * Translation key for the primary button text
   */
  primaryButtonTx?: string
  /**
   * Translation key for the secondary button text
   */
  secondaryButtonTx?: string
  /**
   * Callback for primary button press
   */
  onPrimaryPress?: () => void
  /**
   * Callback for secondary button press
   */
  onSecondaryPress?: () => void
  /**
   * Test ID for primary button
   */
  primaryButtonTestID?: string
  /**
   * Test ID for secondary button
   */
  secondaryButtonTestID?: string
  /**
   * Optional custom styles for the container
   */
  style?: ViewStyle
  /**
   * Whether to show loading state on primary button
   */
  primaryLoading?: boolean
  /**
   * Whether to show loading state on secondary button
   */
  secondaryLoading?: boolean
}

export const WelcomeActionButtons: FC<WelcomeActionButtonsProps> = ({
  primaryButtonTx = "welcomeScreen:letsGo",
  secondaryButtonTx = "welcomeScreen:exploreCourses",
  onPrimaryPress,
  onSecondaryPress,
  primaryButtonTestID = "start-learning-button",
  secondaryButtonTestID = "explore-courses-button",
  style,
  primaryLoading = false,
  secondaryLoading = false,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$buttonContainer, style])}>
      <Button
        testID={primaryButtonTestID}
        preset="filled"
        tx={primaryButtonTx}
        onPress={onPrimaryPress}
        style={themed($primaryButton)}
        textStyle={themed($buttonText)}
        loading={primaryLoading}
      />

      <Button
        testID={secondaryButtonTestID}
        preset="default"
        tx={secondaryButtonTx}
        onPress={onSecondaryPress}
        style={themed($secondaryButton)}
        textStyle={themed($secondaryButtonText)}
        loading={secondaryLoading}
      />
    </View>
  )
}

const $buttonContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.md, // 16px spacing between buttons
})

const $primaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.primary500, // Royal blue
  borderRadius: 16, // Same as login screen buttons for consistency
  height: 44, // Standard button height from guidelines
  minHeight: 44, // Ensure consistent height with secondary button
  shadowColor: colors.palette.primary700,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.2,
  shadowRadius: 4,
  elevation: 3,
})

const $secondaryButton: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.background, // White background
  borderColor: colors.palette.neutral300, // Light gray border like "Continue with Phone"
  borderWidth: 1, // Thin border
  borderRadius: 16, // Same as login screen buttons and primary button for consistency
  height: 44, // Standard button height from guidelines - same as primary
  minHeight: 44, // Ensure consistent height
  shadowColor: colors.palette.neutral400,
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.1,
  shadowRadius: 2,
  elevation: 1, // Subtle shadow like the reference
})

const $buttonText: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.medium, // Font weight 500 - MANDATORY for all text
  fontSize: 16,
})

const $secondaryButtonText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium, // Font weight 500 - MANDATORY for all text
  fontSize: 16,
  color: colors.text, // Dark text color like "Continue with Phone" button
})
