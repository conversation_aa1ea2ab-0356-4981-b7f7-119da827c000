import { FC } from "react"
import { Image, ImageStyle, TextStyle, View, ViewStyle } from "react-native"
import { Text } from "@/components"
import { $styles, type ThemedStyle } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"

const welcomeLogo = require("../../../../../assets/images/logo.png")

interface WelcomeHeroSectionProps {
  /**
   * Optional custom logo source. If not provided, uses default logo.
   */
  logoSource?: any
  /**
   * Translation key for the main heading text
   */
  headingTx?: string
  /**
   * Translation key for the subtitle text
   */
  subtitleTx?: string
  /**
   * Translation key for the description text
   */
  descriptionTx?: string
  /**
   * Optional custom styles for the container
   */
  style?: ViewStyle
}

export const WelcomeHeroSection: FC<WelcomeHeroSectionProps> = ({
  logoSource = welcomeLogo,
  headingTx = "welcomeScreen:readyForLaunch",
  subtitleTx = "welcomeScreen:exciting",
  descriptionTx = "welcomeScreen:postscript",
  style,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed([$heroSection, style])}>
      <Image style={themed($welcomeLogo)} source={logoSource} resizeMode="contain" />

      <Text
        testID="welcome-heading"
        style={themed($welcomeHeading)}
        tx={headingTx}
        preset="heading"
      />

      <Text
        testID="welcome-subtitle"
        style={themed($welcomeSubtitle)}
        tx={subtitleTx}
        preset="subheading"
      />

      <Text
        preset="description"
        tx={descriptionTx}
        style={themed($descriptionText)}
      />
    </View>
  )
}

const $heroSection: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  alignItems: "center",
  flex: 1,
  justifyContent: "center",
  paddingHorizontal: spacing.xs, // 8px additional internal padding (total: 12+8=20px from edges)
  gap: spacing.md, // 16px spacing between elements
})

const $welcomeLogo: ThemedStyle<ImageStyle> = () => ({
  height: 80,
  width: "100%",
  tintColor: undefined, // Remove tint to show original logo colors
})

const $welcomeHeading: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 28,
  fontFamily: typography.primary.bold, // Bold for main title
  color: colors.palette.primary700, // Deep navy
  textAlign: "center",
})

const $welcomeSubtitle: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 18,
  fontFamily: typography.primary.medium, // Medium for subtitle
  color: colors.palette.primary500, // Royal blue
  textAlign: "center",
})

const $descriptionText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontSize: 16,
  fontFamily: typography.primary.medium, // Font weight 500 - MANDATORY for descriptions
  color: colors.palette.primary600,
  textAlign: "center",
  lineHeight: 24,
})
