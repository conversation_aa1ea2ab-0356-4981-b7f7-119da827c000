# Welcome Feature

This directory contains all components and screens related to the Welcome functionality of the LMS app.

## Structure

```
app/features/welcome/
├── components/           # Welcome-specific components
│   ├── WelcomeHeroSection.tsx    # Logo, heading, subtitle, description
│   ├── WelcomeActionButtons.tsx  # Primary and secondary action buttons
│   └── index.ts                  # Component exports
├── screens/             # Welcome screens
│   ├── WelcomeScreen.tsx        # Main welcome screen
│   └── index.ts                 # Screen exports
├── index.ts            # Feature exports
└── README.md           # This file
```

## Components

### WelcomeHeroSection
- Displays app logo, heading, subtitle, and description
- Configurable via props (logoSource, headingTx, subtitleTx, descriptionTx)
- 85 lines (well under 200 line limit)
- Reusable for other welcome screens

### WelcomeActionButtons
- Primary and secondary action buttons
- Configurable via props (button texts, callbacks, loading states)
- 95 lines (well under 200 line limit)
- Follows design system guidelines

## Screens

### WelcomeScreen
- Main layout and navigation logic
- 74 lines (reduced from original 185 lines)
- Uses above components
- Clean separation of concerns

## Usage

```typescript
// Import the entire welcome feature
import { WelcomeScreen, WelcomeHeroSection, WelcomeActionButtons } from "@/features/welcome"

// Or import specific components
import { WelcomeHeroSection } from "@/features/welcome/components"
import { WelcomeScreen } from "@/features/welcome/screens"
```

## Benefits

- ✅ Each component < 200 lines (follows user preference)
- ✅ Better separation of concerns
- ✅ Easier testing and maintenance
- ✅ Reusable components
- ✅ Reduced complexity in main screen file
- ✅ Organized by feature for better scalability
