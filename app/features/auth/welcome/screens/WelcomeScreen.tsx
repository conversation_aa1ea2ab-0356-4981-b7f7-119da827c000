import { observer } from "mobx-react-lite"
import { FC } from "react"
import { View, ViewStyle } from "react-native"
import { Screen } from "@/components"
import { WelcomeHeroSection, WelcomeActionButtons } from "../components"
import { useStores } from "@/models"
import { AuthStackScreenProps } from "@/navigators/AuthNavigator"
import { $styles, type ThemedStyle } from "@/theme"
import { useHeader } from "@/utils/useHeader"
import { useSafeAreaInsetsStyle } from "@/utils/useSafeAreaInsetsStyle"
import { useAppTheme } from "@/utils/useAppTheme"

interface WelcomeScreenProps extends AuthStackScreenProps<"Welcome"> {}

export const WelcomeScreen: FC<WelcomeScreenProps> = observer(function WelcomeScreen(_props) {
  const { themed } = useAppTheme()

  const { navigation } = _props
  const {
    authenticationStore: { logout },
  } = useStores()

  function goNext() {
    navigation.navigate("Demo", { screen: "DemoShowroom", params: {} })
  }

  function goToLMSDemo() {
    navigation.navigate("LMSDemo")
  }

  useHeader(
    {
      rightTx: "common:logOut",
      onRightPress: logout,
    },
    [logout],
  )

  const $bottomContainerInsets = useSafeAreaInsetsStyle(["bottom"])

  return (
    <Screen preset="fixed" contentContainerStyle={$styles.flex1}>
      <View style={themed($topContainer)}>
        <WelcomeHeroSection logoSource={require("assets/images/logo/vantis-logo.png")} />
      </View>

      <View style={themed([$bottomContainer, $bottomContainerInsets])}>
        <WelcomeActionButtons
          onPrimaryPress={goNext}
          onSecondaryPress={goToLMSDemo}
        />
      </View>
    </Screen>
  )
})

const $topContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexShrink: 1,
  flexGrow: 1,
  flexBasis: "70%",
  justifyContent: "center",
  paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding from screen edges
  paddingVertical: spacing.lg, // 24px vertical spacing
})

const $bottomContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexShrink: 1,
  flexGrow: 0,
  flexBasis: "30%",
  backgroundColor: colors.background, // White background - MANDATORY
  paddingHorizontal: spacing.sm, // 12px - MANDATORY horizontal padding from screen edges
  paddingVertical: spacing.lg, // 24px vertical spacing
  justifyContent: "center",
})
