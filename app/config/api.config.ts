/**
 * API Configuration
 * This file contains API configurations, including base URLs and endpoints
 * for the EarnBase LMS application
 */

import { Platform } from "react-native"

// Environment types
export type Environment = 'development' | 'staging' | 'production'

// API Base URLs for each environment
const API_ENDPOINTS = {
  development: 'https://lms-dev.ebill.vn/api/v1',
  staging: 'https://lms-staging.ebill.vn/api/v1', 
  production: 'https://lms.ebill.vn/api/v1',
}

// Determine current environment
const getEnvironment = (): Environment => {
  // Check if we're in development mode
  if (__DEV__) {
    return 'development'
  }
  
  // For production builds, you can set environment via build configuration
  // This can be overridden by build scripts or environment variables
  return 'production'
}

// Get API Base URL based on environment
export const getApiBaseUrl = (): string => {
  return API_ENDPOINTS[getEnvironment()]
}

// API Base URL (can be changed dynamically)
export let API_BASE_URL = getApiBaseUrl()

// Function to update API base URL at runtime
export const updateApiBaseUrl = (newUrl: string): void => {
  API_BASE_URL = newUrl
  console.log(`API Base URL updated to: ${API_BASE_URL}`)
}

// Function to switch environment
export const switchEnvironment = (env: Environment): void => {
  API_BASE_URL = API_ENDPOINTS[env]
  console.log(`Environment switched to: ${env}, API URL: ${API_BASE_URL}`)
}

// Get current environment
export const getCurrentEnvironment = (): Environment => {
  return getEnvironment()
}

// API Paths - All endpoints organized by feature
export const API_PATHS = {
  // Core API Info
  CORE: {
    INFO: '/',
    HEALTH: '/health',
    MODULES: '/modules',
  },

  // Authentication
  AUTH: {
    SIGN_IN: '/auth/sign-in',
    ME: '/auth/me',
    REFRESH: '/auth/refresh',
    VERIFY: '/auth/verify',
    SIGN_OUT: '/auth/sign-out',
    TOKENS: '/auth/tokens',
    REVOKE: '/auth/revoke',
    DEVICE_REGISTER: '/auth/device/register',
  },

  // Public APIs
  PUBLIC: {
    COURSES: '/public/courses',
    COURSES_SEARCH: '/public/courses/search',
    COURSES_CATEGORIES: '/public/courses/categories',
    EVENTS: '/public/events',
    EVENT_DETAIL: (eventId: string | number) => `/public/events/${eventId}`,
    EVENT_REGISTRATIONS: (eventId: string | number) => `/public/events/${eventId}/registrations`,
    REGISTRATIONS: '/public/registrations',
    WORKSHOP_TYPES: '/public/workshop-types',
  },

  // Student APIs
  STUDENT: {
    PROFILE: '/students/profile/',  // Added trailing slash
    COURSES: '/students/courses/',
    COURSE_DETAIL: (courseId: string | number) => `/students/courses/${courseId}/`,
    SCHEDULE: '/students/schedule/',
    SCHEDULE_WEEK: '/students/schedule/week/',
    PROGRESS: '/students/progress/',
    LESSON_DETAIL: (lessonId: string | number) => `/students/lessons/${lessonId}/`,
    LESSON_ATTENDANCE: (lessonId: string | number) => `/students/lessons/${lessonId}/attendance/`,
  },

  // Instructor APIs
  INSTRUCTOR: {
    PROFILE: '/instructors/profile/',  // Added trailing slash
    COURSES: '/instructors/courses/',
    COURSE_DETAIL: (courseId: string | number) => `/instructors/courses/${courseId}/`,
    SCHEDULE: '/instructors/schedule/',
    SCHEDULE_WEEK: '/instructors/schedule/week/',
    CLASS_STUDENTS: (classId: string | number) => `/instructors/classes/${classId}/students/`,
    LESSON_DETAIL: (lessonId: string | number) => `/instructors/lessons/${lessonId}/`,
    LESSON_UPDATE: (lessonId: string | number) => `/instructors/lessons/${lessonId}/`,
    LESSON_ATTENDANCE: (lessonId: string | number) => `/instructors/lessons/${lessonId}/attendance/`,
  },

  // Course Management
  COURSES: {
    LIST: '/courses',
    DETAIL: (courseId: string | number) => `/courses/${courseId}`,
    LESSONS: (courseId: string | number) => `/courses/${courseId}/lessons`,
    STUDENTS: (courseId: string | number) => `/courses/${courseId}/students`,
  },

  // Lesson Management
  LESSONS: {
    DETAIL: (lessonId: string | number) => `/lessons/${lessonId}`,
    MATERIALS: (lessonId: string | number) => `/lessons/${lessonId}/materials`,
  },

  // File Management
  FILES: {
    UPLOAD: '/files/',
    GET: (accessToken: string) => `/files/${accessToken}`,
  },

  // Chatbot APIs
  CHATBOT: {
    CREATE_SESSION: '/public/chatbot/sessions',
    GET_SESSION: (sessionId: string) => `/public/chatbot/sessions/${sessionId}`,
    DELETE_SESSION: (sessionId: string) => `/public/chatbot/sessions/${sessionId}`,
    SEND_MESSAGE: (sessionId: string) => `/public/chatbot/sessions/${sessionId}/messages`,
    GET_MESSAGES: (sessionId: string) => `/public/chatbot/sessions/${sessionId}/messages`,
    MESSAGE_FEEDBACK: (messageId: string) => `/public/chatbot/messages/${messageId}/feedback`,
    CONFIG: '/public/chatbot/config',
  },
}

// Utility function to create full URL
export const getApiUrl = (path: string): string => {
  return `${API_BASE_URL}${path}`
}

// Default headers for API requests
export const DEFAULT_HEADERS = {
  'Accept': 'application/json',
  'Content-Type': 'application/json',
  'Accept-Language': 'vi-VN,vi;q=0.9,en;q=0.8',
}

// Default timeout for API requests (30 seconds)
export const DEFAULT_TIMEOUT = 30000

// Request configuration
export const REQUEST_CONFIG = {
  timeout: DEFAULT_TIMEOUT,
  headers: DEFAULT_HEADERS,
}

// Export all configuration
export default {
  API_BASE_URL,
  API_PATHS,
  getApiUrl,
  DEFAULT_HEADERS,
  DEFAULT_TIMEOUT,
  REQUEST_CONFIG,
  updateApiBaseUrl,
  switchEnvironment,
  getCurrentEnvironment,
  getApiBaseUrl,
}
