/**
 * SuccessModal Component
 * 
 * A modern success modal component that follows the app's design system.
 * Features smooth animations, consistent styling, and accessibility support.
 */

import React, { useEffect, useState } from "react"
import {
  Modal,
  View,
  StyleSheet,
  Pressable,
  Dimensions,
  Platform,
  Image,
} from "react-native"
import { Text } from "./Text"
import { Button } from "./Button"
import { colors, spacing, typography } from "../theme"

const { width: screenWidth, height: screenHeight } = Dimensions.get("window")

export interface SuccessModalProps {
  /**
   * Whether the modal is visible
   */
  visible: boolean
  /**
   * Function called when modal should be closed
   */
  onClose: () => void
  /**
   * Title text for the modal
   */
  title?: string
  /**
   * Message text for the modal
   */
  message?: string
  /**
   * Text for the action button
   */
  buttonText?: string
  /**
   * Custom icon source (optional) - will use congratulations.png by default
   */
  iconSource?: any
  /**
   * Whether to show the backdrop overlay
   */
  showBackdrop?: boolean
  /**
   * Custom onPress for the button (optional)
   */
  onButtonPress?: () => void
  /**
   * Auto-dismiss the modal after specified time
   */
  autoDismiss?: boolean
  /**
   * Auto-dismiss delay in milliseconds
   */
  autoDismissDelay?: number
}

/**
 * Modern success modal with smooth animations and consistent styling
 */
export const SuccessModal: React.FC<SuccessModalProps> = ({
  visible,
  onClose,
  title = "Success",
  message = "Operation completed successfully!",
  buttonText = "OK",
  iconSource = require("../../assets/icons/congratulations.png"),
  showBackdrop = true,
  onButtonPress,
  autoDismiss = false,
  autoDismissDelay = 10000,
}) => {
  const [countdown, setCountdown] = useState<number>(0)

  // Auto-dismiss countdown effect
  useEffect(() => {
    if (visible && autoDismiss) {
      const totalSeconds = Math.ceil(autoDismissDelay / 1000)
      console.log(`🕐 SuccessModal: Starting countdown from ${totalSeconds} seconds (${autoDismissDelay}ms)`)
      setCountdown(totalSeconds)

      const interval = setInterval(() => {
        setCountdown((prev) => {
          const newCount = prev <= 1 ? 0 : prev - 1
          console.log(`🕐 SuccessModal: Countdown ${newCount}`)
          if (newCount === 0) {
            clearInterval(interval)
            console.log(`🕐 SuccessModal: Countdown finished`)
          }
          return newCount
        })
      }, 1000)

      return () => {
        console.log(`🕐 SuccessModal: Cleaning up countdown interval`)
        clearInterval(interval)
      }
    } else {
      setCountdown(0)
    }
  }, [visible, autoDismiss, autoDismissDelay])

  const handleButtonPress = () => {
    if (onButtonPress) {
      onButtonPress()
    } else {
      onClose()
    }
  }

  const handleBackdropPress = () => {
    if (showBackdrop) {
      onClose()
    }
  }

  // Display button text with countdown if auto-dismiss is enabled
  const displayButtonText = autoDismiss && countdown > 0
    ? `${buttonText} (${countdown}s)`
    : buttonText

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      {/* Backdrop */}
      <Pressable style={styles.backdrop} onPress={handleBackdropPress}>
        <View style={styles.container}>
          {/* Modal Content */}
          <Pressable style={styles.modal} onPress={(e) => e.stopPropagation()}>
            {/* Success Icon */}
            <View style={styles.iconContainer}>
              <Image
                source={iconSource}
                style={styles.congratulationsIcon}
                resizeMode="contain"
              />
            </View>

            {/* Title */}
            <Text
              preset="subheading"
              weight="semiBold"
              size="lg"
              text={title}
              style={styles.title}
            />

            {/* Message */}
            <Text
              preset="description"
              weight="light"
              size="sm"
              text={message}
              style={styles.message}
            />

            {/* Action Button */}
            <Button
              text={displayButtonText}
              preset="filled"
              style={styles.button}
              textStyle={styles.buttonText}
              onPress={handleButtonPress}
            />
          </Pressable>
        </View>
      </Pressable>
    </Modal>
  )
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.4)", // Semi-transparent overlay from design guidelines
  },
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 34, // Same as screen padding from design guidelines
  },
  modal: {
    backgroundColor: "#FFFFFF", // White background from design guidelines
    borderRadius: 30, // Large rounded corners from design guidelines
    paddingTop: 40,
    paddingBottom: 30,
    paddingHorizontal: 40,
    alignItems: "center",
    width: 320, // Fixed width from design guidelines
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  iconContainer: {
    marginBottom: 25, // Consistent spacing from design guidelines
    alignItems: "center",
    justifyContent: "center",
  },
  congratulationsIcon: {
    width: 120,
    height: 120,
  },
  title: {
    textAlign: "center",
    marginBottom: 16, // Consistent spacing from design guidelines
    color: "#202244", // Dark text color from design guidelines
    fontFamily: typography.primary.semiBold, // Montserrat SemiBold (600)
    fontWeight: "600", // Semi-bold weight for titles
    fontSize: 24, // Larger title size for modal importance
    lineHeight: 32,
    letterSpacing: 0,
  },
  message: {
    textAlign: "center",
    marginBottom: 30, // Consistent spacing from design guidelines
    color: "#8B8B8B", // Lighter gray text color from design guidelines
    fontFamily: typography.primary.light, // Montserrat Light (300) - MANDATORY for descriptions
    fontWeight: "300", // Light weight for description - MANDATORY per design guidelines
    fontSize: 16,
    lineHeight: 24,
    letterSpacing: 0,
    paddingHorizontal: 10, // Extra padding for better text layout
  },
  button: {
    minWidth: 120,
    backgroundColor: "#0961F5", // Exact color from design guidelines
    borderRadius: 30, // Rounded corners from design guidelines
    height: 50, // Standard button height
    paddingHorizontal: spacing.lg,
    alignItems: "center",
    justifyContent: "center",
    shadowColor: "#000000",
    shadowOffset: { width: 1, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  buttonText: {
    color: "#FFFFFF", // White text on primary button
    fontFamily: typography.primary.light, // Lexend Deca Light (300) - MANDATORY for button text
    fontWeight: "300", // Light weight for button text - MANDATORY per design guidelines
    fontSize: 18,
    lineHeight: 26,
    textAlign: "center",
  },
})
