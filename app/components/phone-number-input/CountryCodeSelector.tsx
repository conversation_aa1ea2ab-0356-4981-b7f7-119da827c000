/**
 * Country Code Selector Component
 * 
 * Displays country code selector with dropdown
 */

import React from "react"
import { Pressable } from "react-native"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { CountryCodeSelectorProps } from "./types"
import { $countryCodeContainer, $countryCodeText, $dropdownIcon } from "./styles"

export const CountryCodeSelector: React.FC<CountryCodeSelectorProps> = ({
  countryCode,
  onCountryCodeChange,
  disabled = false,
}) => {
  const { themed } = useAppTheme()

  const handlePress = () => {
    if (disabled) return
    
    // TODO: Implement country code selector modal
    console.log("Country code selector pressed")
    // For now, just log the action
    // In a real implementation, you would open a modal or dropdown
    // with the list of available country codes
  }

  return (
    <Pressable
      style={themed($countryCodeContainer)}
      onPress={handlePress}
      disabled={disabled}
      accessibilityRole="button"
      accessibilityLabel={`Country code ${countryCode}`}
      accessibilityHint="Tap to change country code"
    >
      <Text text={countryCode} style={themed($countryCodeText)} />
      <Text text="▼" style={themed($dropdownIcon)} />
    </Pressable>
  )
}
