/**
 * Phone Number Input Field Component
 * 
 * Core input field with formatting and country code selector
 */

import React, { forwardRef } from "react"
import { TextInput, View } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { PhoneNumberInputFieldProps } from "./types"
import { CountryCodeSelector } from "./CountryCodeSelector"
import { usePhoneNumberFormatting } from "./usePhoneNumberFormatting"
import { $inputContainer, $input, $errorInput, $disabledInput } from "./styles"
import { DEFAULT_PLACEHOLDER } from "./constants"

export const PhoneNumberInputField = forwardRef<TextInput, PhoneNumberInputFieldProps>(
  function PhoneNumberInputField(props, ref) {
    const {
      value,
      onChangeText,
      placeholder = DEFAULT_PLACEHOLDER,
      status,
      inputStyle: $inputStyleOverride,
      showCountrySelector,
      ...TextInputProps
    } = props

    const { themed, theme: { colors } } = useAppTheme()
    const { handlePhoneNumberChange, formatDisplayValue } = usePhoneNumberFormatting(onChangeText)

    const isError = status === "error"
    const isDisabled = status === "disabled"

    const $inputStyles = [
      themed($input),
      isError && themed($errorInput),
      isDisabled && themed($disabledInput),
      $inputStyleOverride,
    ]

    return (
      <View style={themed($inputContainer)}>
        {showCountrySelector && (
          <CountryCodeSelector
            countryCode="+84" // Default country code - could be made configurable
            disabled={isDisabled}
          />
        )}

        <TextInput
          ref={ref}
          style={$inputStyles}
          value={formatDisplayValue(value)}
          onChangeText={handlePhoneNumberChange}
          placeholder={placeholder}
          placeholderTextColor={colors.inputPlaceholder}
          keyboardType="phone-pad"
          autoComplete="tel"
          textContentType="telephoneNumber"
          editable={!isDisabled}
          {...TextInputProps}
        />
      </View>
    )
  }
)
