/**
 * Phone Number Label Component
 * 
 * Displays the label for phone number input
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { PhoneNumberLabelProps } from "./types"
import { $label, $errorLabel } from "./styles"

export const PhoneNumberLabel: React.FC<PhoneNumberLabelProps> = ({
  label,
  status,
}) => {
  const { themed } = useAppTheme()

  if (!label) return null

  const isError = status === "error"

  return (
    <Text
      text={label}
      preset="formLabel"
      style={[themed($label), isError && themed($errorLabel)]}
    />
  )
}
