/**
 * Styles for PhoneNumberInput components
 */

import type { TextStyle, ViewStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

// Container styles
export const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

export const $inputContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  borderWidth: 1,
  borderColor: colors.border,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral100,
  paddingHorizontal: spacing.sm,
})

// Label styles
export const $label: ThemedStyle<TextStyle> = ({ colors, spacing, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 14,
  color: colors.text,
  marginBottom: spacing.xs,
})

// Country code selector styles
export const $countryCodeContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingVertical: spacing.sm,
  paddingRight: spacing.sm,
  borderRightWidth: 1,
  borderRightColor: colors.border,
  marginRight: spacing.sm,
})

export const $countryCodeText: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 16,
  color: colors.text,
  marginRight: 4,
})

export const $dropdownIcon: ThemedStyle<TextStyle> = ({ colors }) => ({
  fontSize: 10,
  color: colors.textDim,
})

// Input styles
export const $input: ThemedStyle<TextStyle> = ({ colors, spacing, typography }) => ({
  flex: 1,
  fontFamily: typography.primary.normal,
  fontSize: 16,
  color: colors.text,
  paddingVertical: spacing.sm,
  minHeight: 48,
})

// Helper styles
export const $helper: ThemedStyle<TextStyle> = ({ colors, spacing, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 12,
  color: colors.textDim,
  marginTop: spacing.xs,
})

// Error states
export const $errorContainer: ThemedStyle<ViewStyle> = () => ({})

export const $errorLabel: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.error,
})

export const $errorInput: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.error,
})

export const $errorHelper: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.error,
})

// Disabled states
export const $disabledContainer: ThemedStyle<ViewStyle> = () => ({
  opacity: 0.6,
})

export const $disabledInput: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.textDim,
})
