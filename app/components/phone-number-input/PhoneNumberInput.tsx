/**
 * Phone Number Input Component - Refactored
 * 
 * Main phone number input component using smaller, focused sub-components.
 * Reduced from 295 lines to ~80 lines for better maintainability.
 */

import React, { forwardRef, useImperativeHandle, useRef } from "react"
import { TextInput, View } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { PhoneNumberInputProps, PhoneNumberInputRef } from "./types"
import { PhoneNumberLabel } from "./PhoneNumberLabel"
import { PhoneNumberInputField } from "./PhoneNumberInputField"
import { PhoneNumberHelper } from "./PhoneNumberHelper"
import { $container, $errorContainer, $disabledContainer } from "./styles"
import { DEFAULT_COUNTRY_CODE, DEFAULT_PLACEHOLDER } from "./constants"

export const PhoneNumberInput = forwardRef<PhoneNumberInputRef, PhoneNumberInputProps>(
  function PhoneNumberInput(props, ref) {
    const {
      value = "",
      onChangeText,
      countryCode = DEFAULT_COUNTRY_CODE,
      onCountryCodeChange,
      label,
      placeholder = DEFAULT_PLACEHOLDER,
      helper,
      status,
      containerStyle: $containerStyleOverride,
      inputStyle: $inputStyleOverride,
      showCountrySelector = true,
      ...TextInputProps
    } = props

    const { themed } = useAppTheme()
    const input = useRef<TextInput>(null)

    useImperativeHandle(ref, () => ({
      focus: () => input.current?.focus(),
      blur: () => input.current?.blur(),
      clear: () => input.current?.clear(),
    }))

    const isError = status === "error"
    const isDisabled = status === "disabled"

    const $containerStyles = [
      themed($container),
      isError && themed($errorContainer),
      isDisabled && themed($disabledContainer),
      $containerStyleOverride,
    ]

    return (
      <View style={$containerStyles}>
        <PhoneNumberLabel
          label={label}
          status={status}
        />

        <PhoneNumberInputField
          ref={input}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          status={status}
          inputStyle={$inputStyleOverride}
          showCountrySelector={showCountrySelector}
          {...TextInputProps}
        />

        <PhoneNumberHelper
          helper={helper}
          status={status}
        />
      </View>
    )
  }
)
