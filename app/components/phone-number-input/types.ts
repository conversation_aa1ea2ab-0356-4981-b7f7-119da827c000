/**
 * Types for PhoneNumberInput components
 */

import type { TextInputProps, ViewStyle, TextStyle } from "react-native"

export interface PhoneNumberInputProps extends Omit<TextInputProps, "value" | "onChangeText"> {
  /**
   * Phone number value
   */
  value?: string

  /**
   * Called when phone number changes
   */
  onChangeText?: (phoneNumber: string) => void

  /**
   * Country code (default: +84 for Vietnam)
   */
  countryCode?: string

  /**
   * Called when country code changes
   */
  onCountryCodeChange?: (countryCode: string) => void

  /**
   * Label text
   */
  label?: string

  /**
   * Placeholder text
   */
  placeholder?: string

  /**
   * Helper text
   */
  helper?: string

  /**
   * Error status
   */
  status?: "error" | "disabled"

  /**
   * Container style override
   */
  containerStyle?: ViewStyle

  /**
   * Input style override
   */
  inputStyle?: TextStyle

  /**
   * Whether to show country code selector
   */
  showCountrySelector?: boolean
}

export interface PhoneNumberInputRef {
  focus: () => void
  blur: () => void
  clear: () => void
}

export interface PhoneNumberLabelProps {
  label?: string
  status?: "error" | "disabled"
}

export interface PhoneNumberHelperProps {
  helper?: string
  status?: "error" | "disabled"
}

export interface CountryCodeSelectorProps {
  countryCode: string
  onCountryCodeChange?: (countryCode: string) => void
  disabled?: boolean
}

export interface PhoneNumberInputFieldProps extends Omit<TextInputProps, "value" | "onChangeText"> {
  value: string
  onChangeText: (phoneNumber: string) => void
  placeholder?: string
  status?: "error" | "disabled"
  inputStyle?: TextStyle
  showCountrySelector: boolean
}

export interface UsePhoneNumberFormattingResult {
  handlePhoneNumberChange: (text: string) => void
  formatDisplayValue: (phoneNumber: string) => string
}
