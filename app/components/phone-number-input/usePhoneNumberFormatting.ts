/**
 * usePhoneNumberFormatting Hook
 * 
 * Custom hook that handles phone number formatting and validation
 */

import { useCallback } from "react"
import type { UsePhoneNumberFormattingResult } from "./types"

export function usePhoneNumberFormatting(
  onChangeText?: (phoneNumber: string) => void
): UsePhoneNumberFormattingResult {
  
  const handlePhoneNumberChange = useCallback((text: string) => {
    // Remove all non-digit characters
    const cleaned = text.replace(/\D/g, "")

    // Format the number with spaces for better readability
    let formatted = cleaned
    if (cleaned.length > 3) {
      formatted = cleaned.replace(/(\d{3})(\d{3})(\d{4})/, "$1 $2 $3")
    } else if (cleaned.length > 6) {
      formatted = cleaned.replace(/(\d{3})(\d{3})/, "$1 $2")
    }

    onChangeText?.(cleaned)
  }, [onChangeText])

  const formatDisplayValue = useCallback((phoneNumber: string) => {
    if (phoneNumber.length <= 3) return phoneNumber
    if (phoneNumber.length <= 6) {
      return phoneNumber.replace(/(\d{3})(\d{1,3})/, "$1 $2")
    }
    return phoneNumber.replace(/(\d{3})(\d{3})(\d{1,4})/, "$1 $2 $3")
  }, [])

  return {
    handlePhoneNumberChange,
    formatDisplayValue,
  }
}
