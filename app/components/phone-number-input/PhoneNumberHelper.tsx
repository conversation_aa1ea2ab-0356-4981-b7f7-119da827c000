/**
 * Phone Number Helper Component
 * 
 * Displays helper text for phone number input
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { PhoneNumberHelperProps } from "./types"
import { $helper, $errorHelper } from "./styles"

export const PhoneNumberHelper: React.FC<PhoneNumberHelperProps> = ({
  helper,
  status,
}) => {
  const { themed } = useAppTheme()

  if (!helper) return null

  const isError = status === "error"

  return (
    <Text
      text={helper}
      preset="formHelper"
      style={[themed($helper), isError && themed($errorHelper)]}
    />
  )
}
