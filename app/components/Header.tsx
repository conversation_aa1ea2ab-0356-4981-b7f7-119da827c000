/**
 * Header Components
 *
 * Refactored from a single 330-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the header module
export {
  Header,
  HeaderAction,
  HeaderTitle,
  HeaderContainer,
  type HeaderProps,
  type HeaderActionProps,
  type HeaderTitleProps,
  type HeaderContainerProps,
} from "./header"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
