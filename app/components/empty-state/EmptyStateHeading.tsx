/**
 * Empty State Heading Component
 * 
 * Displays the heading text for empty states
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { EmptyStateHeadingProps } from "./types"
import { $heading } from "./styles"

export const EmptyStateHeading: React.FC<EmptyStateHeadingProps> = ({
  heading,
  headingTx,
  headingTxOptions,
  headingStyle: $headingStyleOverride,
  HeadingTextProps,
  hasImage,
  hasOtherContent,
}) => {
  const { themed, theme: { spacing } } = useAppTheme()

  if (!(heading || headingTx)) return null

  const $headingStyles = [
    themed($heading),
    hasImage && { marginTop: spacing.xxxs },
    hasOtherContent && { marginBottom: spacing.xxxs },
    $headingStyleOverride,
    HeadingTextProps?.style,
  ]

  return (
    <Text
      preset="subheading"
      text={heading}
      tx={headingTx}
      txOptions={headingTxOptions}
      {...HeadingTextProps}
      style={$headingStyles}
    />
  )
}
