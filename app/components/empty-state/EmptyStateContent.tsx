/**
 * Empty State Content Component
 * 
 * Displays the content text for empty states
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { EmptyStateContentProps } from "./types"
import { $content } from "./styles"

export const EmptyStateContent: React.FC<EmptyStateContentProps> = ({
  content,
  contentTx,
  contentTxOptions,
  contentStyle: $contentStyleOverride,
  ContentTextProps,
  hasImageOrHeading,
  hasButton,
}) => {
  const { themed, theme: { spacing } } = useAppTheme()

  if (!(content || contentTx)) return null

  const $contentStyles = [
    themed($content),
    hasImageOrHeading && { marginTop: spacing.xxxs },
    hasButton && { marginBottom: spacing.xxxs },
    $contentStyleOverride,
    ContentTextProps?.style,
  ]

  return (
    <Text
      preset="description"
      text={content}
      tx={contentTx}
      txOptions={contentTxOptions}
      {...ContentTextProps}
      style={$contentStyles}
    />
  )
}
