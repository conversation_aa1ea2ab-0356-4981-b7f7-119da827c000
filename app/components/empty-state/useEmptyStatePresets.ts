/**
 * useEmptyStatePresets Hook
 * 
 * Custom hook that manages empty state presets and configurations
 */

import { useMemo } from "react"
import { translate } from "@/i18n/translate"
import type { EmptyStatePresetItem } from "./types"

const sadFace = require("../../../assets/images/sad-face.png")

export function useEmptyStatePresets() {
  const presets = useMemo(() => ({
    generic: {
      imageSource: sadFace,
      heading: translate("emptyStateComponent:generic.heading"),
      content: translate("emptyStateComponent:generic.content"),
      button: translate("emptyStateComponent:generic.button"),
    } as EmptyStatePresetItem,
  }), [])

  const getPreset = (presetName: keyof typeof presets = "generic") => {
    return presets[presetName]
  }

  return {
    presets,
    getPreset,
  }
}
