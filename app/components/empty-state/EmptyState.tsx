/**
 * Empty State Component - Refactored
 * 
 * Main empty state component using smaller, focused sub-components.
 * Reduced from 249 lines to ~80 lines for better maintainability.
 */

import React from "react"
import { View } from "react-native"
import type { EmptyStateProps } from "./types"
import { EmptyStateImage } from "./EmptyStateImage"
import { EmptyStateHeading } from "./EmptyStateHeading"
import { EmptyStateContent } from "./EmptyStateContent"
import { EmptyStateButton } from "./EmptyStateButton"
import { useEmptyStatePresets } from "./useEmptyStatePresets"

/**
 * A component to use when there is no data to display. It can be utilized to direct the user what to do next.
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/EmptyState/}
 * @param {EmptyStateProps} props - The props for the `EmptyState` component.
 * @returns {JSX.Element} The rendered `EmptyState` component.
 */
export const EmptyState: React.FC<EmptyStateProps> = (props) => {
  const { getPreset } = useEmptyStatePresets()
  const preset = getPreset(props.preset)

  const {
    button = preset.button,
    buttonTx,
    buttonOnPress,
    buttonTxOptions,
    content = preset.content,
    contentTx,
    contentTxOptions,
    heading = preset.heading,
    headingTx,
    headingTxOptions,
    imageSource = preset.imageSource,
    style: $containerStyleOverride,
    buttonStyle: $buttonStyleOverride,
    buttonTextStyle: $buttonTextStyleOverride,
    contentStyle: $contentStyleOverride,
    headingStyle: $headingStyleOverride,
    imageStyle: $imageStyleOverride,
    ButtonProps,
    ContentTextProps,
    HeadingTextProps,
    ImageProps,
  } = props

  const isImagePresent = !!imageSource
  const isHeadingPresent = !!(heading || headingTx)
  const isContentPresent = !!(content || contentTx)
  const isButtonPresent = !!(button || buttonTx)

  const hasOtherContentForImage = isHeadingPresent || isContentPresent || isButtonPresent
  const hasOtherContentForHeading = isContentPresent || isButtonPresent
  const hasImageOrHeadingForContent = isImagePresent || isHeadingPresent
  const hasOtherContentForButton = isImagePresent || isHeadingPresent || isContentPresent

  const $containerStyles = [$containerStyleOverride]

  return (
    <View style={$containerStyles}>
      <EmptyStateImage
        imageSource={imageSource}
        imageStyle={$imageStyleOverride}
        ImageProps={ImageProps}
        hasOtherContent={hasOtherContentForImage}
      />

      <EmptyStateHeading
        heading={heading}
        headingTx={headingTx}
        headingTxOptions={headingTxOptions}
        headingStyle={$headingStyleOverride}
        HeadingTextProps={HeadingTextProps}
        hasImage={isImagePresent}
        hasOtherContent={hasOtherContentForHeading}
      />

      <EmptyStateContent
        content={content}
        contentTx={contentTx}
        contentTxOptions={contentTxOptions}
        contentStyle={$contentStyleOverride}
        ContentTextProps={ContentTextProps}
        hasImageOrHeading={hasImageOrHeadingForContent}
        hasButton={isButtonPresent}
      />

      <EmptyStateButton
        button={button}
        buttonTx={buttonTx}
        buttonTxOptions={buttonTxOptions}
        buttonStyle={$buttonStyleOverride}
        buttonTextStyle={$buttonTextStyleOverride}
        buttonOnPress={buttonOnPress}
        ButtonProps={ButtonProps}
        hasOtherContent={hasOtherContentForButton}
      />
    </View>
  )
}
