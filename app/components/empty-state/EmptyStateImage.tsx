/**
 * Empty State Image Component
 * 
 * Displays the image for empty states
 */

import React from "react"
import { Image } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { EmptyStateImageProps } from "./types"
import { $image } from "./styles"

export const EmptyStateImage: React.FC<EmptyStateImageProps> = ({
  imageSource,
  imageStyle: $imageStyleOverride,
  ImageProps,
  hasOtherContent,
}) => {
  const { theme, theme: { spacing } } = useAppTheme()

  if (!imageSource) return null

  const $imageStyles = [
    $image,
    hasOtherContent && { marginBottom: spacing.xxxs },
    $imageStyleOverride,
    ImageProps?.style,
  ]

  return (
    <Image
      source={imageSource}
      {...ImageProps}
      style={$imageStyles}
      tintColor={theme.colors.palette.neutral900}
    />
  )
}
