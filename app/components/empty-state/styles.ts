/**
 * Styles for EmptyState components
 */

import type { ImageStyle, TextStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

export const $image: ImageStyle = { 
  alignSelf: "center" 
}

export const $heading: ThemedStyle<TextStyle> = ({ spacing }) => ({
  textAlign: "center",
  paddingHorizontal: spacing.lg,
})

export const $content: ThemedStyle<TextStyle> = ({ spacing }) => ({
  textAlign: "center",
  paddingHorizontal: spacing.lg,
})
