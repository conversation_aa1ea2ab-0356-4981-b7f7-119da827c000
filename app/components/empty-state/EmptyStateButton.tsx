/**
 * Empty State Button Component
 * 
 * Displays the action button for empty states
 */

import React from "react"
import { Button } from "../Button"
import { useAppTheme } from "@/utils/useAppTheme"
import type { EmptyStateButtonProps } from "./types"

export const EmptyStateButton: React.FC<EmptyStateButtonProps> = ({
  button,
  buttonTx,
  buttonTxOptions,
  buttonStyle: $buttonStyleOverride,
  buttonTextStyle: $buttonTextStyleOverride,
  buttonOnPress,
  ButtonProps,
  hasOtherContent,
}) => {
  const { theme: { spacing } } = useAppTheme()

  if (!(button || buttonTx)) return null

  const $buttonStyles = [
    hasOtherContent && { marginTop: spacing.xl },
    $buttonStyleOverride,
    ButtonProps?.style,
  ]

  return (
    <Button
      onPress={buttonOnPress}
      text={button}
      tx={buttonTx}
      txOptions={buttonTxOptions}
      textStyle={$buttonTextStyleOverride}
      {...ButtonProps}
      style={$buttonStyles}
    />
  )
}
