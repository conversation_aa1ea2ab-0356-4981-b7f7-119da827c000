import React from "react"
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  StatusBar,
  Image,
} from "react-native"
import { colors, spacing, typography } from "@/theme"

interface ErrorModalProps {
  visible: boolean
  title?: string
  message: string
  onClose: () => void
  buttonText?: string
  iconSource?: any
}

const { width: screenWidth } = Dimensions.get("window")

export function ErrorModal({
  visible,
  title = "Error",
  message,
  onClose,
  buttonText = "OK",
  iconSource,
}: ErrorModalProps) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
      onRequestClose={onClose}
    >
      <StatusBar backgroundColor="rgba(0, 0, 0, 0.5)" barStyle="light-content" />
      
      {/* Backdrop */}
      <View style={styles.backdrop}>
        <TouchableOpacity 
          style={styles.backdropTouchable} 
          activeOpacity={1} 
          onPress={onClose}
        />
        
        {/* Modal Content */}
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {/* Icon */}
            {iconSource && (
              <Image source={iconSource} style={styles.icon} />
            )}

            {/* Title */}
            <Text style={styles.title}>{title}</Text>

            {/* Message */}
            <Text style={styles.message}>{message}</Text>
            
            {/* Button */}
            <TouchableOpacity 
              style={styles.button} 
              onPress={onClose}
              activeOpacity={0.8}
            >
              <Text style={styles.buttonText}>{buttonText}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  backdropTouchable: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  modalContainer: {
    width: screenWidth - spacing.lg * 2,
    maxWidth: 400,
    paddingHorizontal: spacing.md,
  },
  modalContent: {
    backgroundColor: colors.background,
    borderRadius: 16,
    padding: spacing.xl,
    alignItems: "center",
    shadowColor: colors.palette.neutral800,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 16,
  },
  icon: {
    width: 60,
    height: 60,
    marginBottom: spacing.md,
    resizeMode: "contain",
  },
  title: {
    fontSize: 20,
    fontWeight: "600",
    color: colors.text, // Deep navy #0F1B2A - primary text color
    marginBottom: spacing.md,
    textAlign: "center",
    fontFamily: typography.primary.semiBold, // 600 weight for headings
    lineHeight: 26,
    letterSpacing: -0.3,
  },
  message: {
    fontSize: 16,
    color: "#8B8B8B", // Gray color consistent with other descriptions in app
    textAlign: "center",
    lineHeight: 22,
    marginBottom: spacing.xl,
    fontFamily: typography.primary.light, // 300 weight - MANDATORY for descriptions
    letterSpacing: 0,
  },
  button: {
    backgroundColor: colors.palette.primary500,
    paddingHorizontal: spacing.xxl, // Increased horizontal padding for wider button
    paddingVertical: spacing.sm, // Reduced vertical padding for shorter height
    borderRadius: 12,
    minWidth: 140, // Increased minimum width
    alignItems: "center",
  },
  buttonText: {
    color: colors.palette.neutral100,
    fontSize: 16,
    fontWeight: "300", // 300 weight - MANDATORY for button text
    fontFamily: typography.primary.light, // Light weight for button text
    lineHeight: 20,
    letterSpacing: 0.2,
  },
})
