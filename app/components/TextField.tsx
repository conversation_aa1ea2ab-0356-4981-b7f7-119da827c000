/**
 * TextField Components
 *
 * Refactored from a single 303-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the text-field module
export {
  TextField,
  TextFieldLabel,
  TextFieldInput,
  TextFieldHelper,
  TextFieldAccessory,
  useTextFieldState,
  type TextFieldProps,
  type TextFieldAccessoryProps,
  type TextFieldLabelProps,
  type TextFieldHelperProps,
  type TextFieldInputProps,
  type UseTextFieldStateResult,
} from "./text-field"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
