/**
 * Video Lesson Item Components
 *
 * Refactored from a single 285-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the video-lesson-item module
export {
  VideoLessonItem,
  VideoLessonPlayButton,
  VideoLessonContent,
  VideoLessonActions,
  VideoLessonList,
  type VideoLessonItemProps,
  type VideoLessonPlayButtonProps,
  type VideoLessonContentProps,
  type VideoLessonActionsProps,
  type VideoLessonListProps,
} from "./video-lesson-item"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
