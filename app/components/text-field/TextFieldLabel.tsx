/**
 * TextField Label Component
 * 
 * Displays the label text for text fields
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { TextFieldLabelProps } from "./types"
import { $labelStyle } from "./styles"

export const TextFieldLabel: React.FC<TextFieldLabelProps> = ({
  label,
  labelTx,
  labelTxOptions,
  LabelTextProps,
}) => {
  const { themed } = useAppTheme()

  if (!(label || labelTx)) return null

  const $labelStyles = [$labelStyle, LabelTextProps?.style]

  return (
    <Text
      preset="formLabel"
      text={label}
      tx={labelTx}
      txOptions={labelTxOptions}
      {...LabelTextProps}
      style={themed($labelStyles)}
    />
  )
}
