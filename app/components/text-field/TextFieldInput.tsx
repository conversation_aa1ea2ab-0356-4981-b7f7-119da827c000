/**
 * TextField Input Component
 * 
 * Core input component with wrapper and accessories
 */

import React from "react"
import { TextInput, View } from "react-native"
import { isRTL, translate } from "@/i18n"
import { $styles } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyleArray } from "@/theme"
import type { TextStyle } from "react-native"
import type { TextFieldInputProps } from "./types"
import { TextFieldAccessory } from "./TextFieldAccessory"
import { $inputWrapperStyle, $inputStyle } from "./styles"

export const TextFieldInput: React.FC<TextFieldInputProps> = ({
  status,
  placeholder,
  placeholderTx,
  placeholderTxOptions,
  style: $inputStyleOverride,
  inputWrapperStyle: $inputWrapperStyleOverride,
  LeftAccessory,
  RightAccessory,
  isFocused,
  onFocusChange,
  inputRef,
  ...TextInputProps
}) => {
  const { themed, theme: { colors } } = useAppTheme()

  const disabled = TextInputProps.editable === false || status === "disabled"

  const placeholderContent = placeholderTx
    ? translate(placeholderTx, placeholderTxOptions)
    : placeholder

  const $inputWrapperStyles = [
    $styles.row,
    $inputWrapperStyle,
    status === "error" && { borderColor: colors.inputError }, // Light red for errors
    isFocused && { borderColor: colors.inputBorderFocus }, // Orange focus border
    TextInputProps.multiline && { minHeight: 112 },
    LeftAccessory && { paddingStart: 0 },
    RightAccessory && { paddingEnd: 0 },
    $inputWrapperStyleOverride,
  ]

  const $inputStyles: ThemedStyleArray<TextStyle> = [
    $inputStyle,
    disabled && { color: colors.textDim },
    isRTL && { textAlign: "right" as TextStyle["textAlign"] },
    TextInputProps.multiline && { height: "auto" },
    $inputStyleOverride,
  ]

  return (
    <View style={themed($inputWrapperStyles)}>
      <TextFieldAccessory
        Accessory={LeftAccessory}
        position="left"
        status={status}
        editable={!disabled}
        multiline={TextInputProps.multiline ?? false}
      />

      <TextInput
        ref={inputRef}
        underlineColorAndroid={colors.transparent}
        textAlignVertical="top"
        placeholder={placeholderContent}
        placeholderTextColor={colors.inputPlaceholder} // Light gray placeholder
        onFocus={(e) => {
          onFocusChange(true)
          TextInputProps.onFocus?.(e)
        }}
        onBlur={(e) => {
          onFocusChange(false)
          TextInputProps.onBlur?.(e)
        }}
        {...TextInputProps}
        editable={!disabled}
        style={themed($inputStyles)}
      />

      <TextFieldAccessory
        Accessory={RightAccessory}
        position="right"
        status={status}
        editable={!disabled}
        multiline={TextInputProps.multiline ?? false}
      />
    </View>
  )
}
