/**
 * Styles for TextField components
 */

import type { TextStyle, ViewStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

export const $labelStyle: ThemedStyle<TextStyle> = ({ spacing, colors }) => ({
  marginBottom: spacing.xs,
  color: colors.inputLabel, // Medium gray for labels (#5F6368)
})

export const $inputWrapperStyle: ThemedStyle<ViewStyle> = ({ colors }) => ({
  alignItems: "flex-start",
  borderWidth: 1,
  borderRadius: 8, // Modern border radius per guideline
  backgroundColor: colors.inputBackground, // Pure white background - MANDATORY
  borderColor: colors.inputBorder, // Very light gray border (#F1F3F4)
  overflow: "hidden",
})

export const $inputStyle: ThemedStyle<ViewStyle> = ({ colors, typography, spacing }) => ({
  flex: 1,
  alignSelf: "stretch",
  fontFamily: typography.primary.normal,
  color: colors.inputText, // Dark text for good contrast (#202124)
  fontSize: 16,
  height: 24,
  // https://github.com/facebook/react-native/issues/21720#issuecomment-532642093
  paddingVertical: 0,
  paddingHorizontal: 0,
  marginVertical: spacing.sm, // 8px vertical margin
  marginHorizontal: spacing.md, // 16px horizontal margin per guideline
})

export const $helperStyle: ThemedStyle<TextStyle> = ({ spacing, colors }) => ({
  marginTop: spacing.xs,
  color: colors.inputLabel, // Medium gray for helper text (#5F6368)
  fontSize: 14,
})

export const $rightAccessoryStyle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginEnd: spacing.xs,
  height: 40,
  justifyContent: "center",
  alignItems: "center",
})

export const $leftAccessoryStyle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginStart: spacing.xs,
  height: 40,
  justifyContent: "center",
  alignItems: "center",
})
