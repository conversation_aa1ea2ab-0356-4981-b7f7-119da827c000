/**
 * TextField Component - Refactored
 * 
 * Main text field component using smaller, focused sub-components.
 * Reduced from 303 lines to ~80 lines for better maintainability.
 */

import React, { forwardRef, Ref, useImperativeHandle, useRef } from "react"
import { TextInput, TouchableOpacity } from "react-native"
import type { TextFieldProps } from "./types"
import { TextFieldLabel } from "./TextFieldLabel"
import { TextFieldInput } from "./TextFieldInput"
import { TextFieldHelper } from "./TextFieldHelper"
import { useTextFieldState } from "./useTextFieldState"

/**
 * A component that allows for the entering and editing of text.
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/TextField/}
 * @param {TextFieldProps} props - The props for the `TextField` component.
 * @returns {JSX.Element} The rendered `TextField` component.
 */
export const TextField = forwardRef<TextInput, TextFieldProps>(function TextField(props, ref) {
  const {
    labelTx,
    label,
    labelTxOptions,
    placeholderTx,
    placeholder,
    placeholderTxOptions,
    helper,
    helperTx,
    helperTxOptions,
    status,
    RightAccessory,
    LeftAccessory,
    HelperTextProps,
    LabelTextProps,
    style: $inputStyleOverride,
    containerStyle: $containerStyleOverride,
    inputWrapperStyle: $inputWrapperStyleOverride,
    ...TextInputProps
  } = props

  const input = useRef<TextInput>(null)
  const { isFocused, setIsFocused, disabled } = useTextFieldState(props)

  const $containerStyles = [$containerStyleOverride]

  /**
   * Focus the input when container is pressed
   */
  function focusInput() {
    if (disabled) return
    input.current?.focus()
  }

  useImperativeHandle(ref, () => input.current as TextInput)

  return (
    <TouchableOpacity
      activeOpacity={1}
      style={$containerStyles}
      onPress={focusInput}
      accessibilityState={{ disabled }}
    >
      <TextFieldLabel
        label={label}
        labelTx={labelTx}
        labelTxOptions={labelTxOptions}
        LabelTextProps={LabelTextProps}
      />

      <TextFieldInput
        {...TextInputProps}
        status={status}
        placeholder={placeholder}
        placeholderTx={placeholderTx}
        placeholderTxOptions={placeholderTxOptions}
        style={$inputStyleOverride}
        inputWrapperStyle={$inputWrapperStyleOverride}
        LeftAccessory={LeftAccessory}
        RightAccessory={RightAccessory}
        isFocused={isFocused}
        onFocusChange={setIsFocused}
        inputRef={input}
      />

      <TextFieldHelper
        helper={helper}
        helperTx={helperTx}
        helperTxOptions={helperTxOptions}
        HelperTextProps={HelperTextProps}
        status={status}
      />
    </TouchableOpacity>
  )
})
