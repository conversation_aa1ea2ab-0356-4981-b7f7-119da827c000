/**
 * useTextFieldState Hook
 * 
 * Custom hook that manages TextField state and derived values
 */

import { useState } from "react"
import { translate } from "@/i18n"
import type { TextFieldProps, UseTextFieldStateResult } from "./types"

export function useTextFieldState(props: TextFieldProps): UseTextFieldStateResult {
  const {
    status,
    editable,
    placeholderTx,
    placeholder,
    placeholderTxOptions,
  } = props

  const [isFocused, setIsFocused] = useState(false)

  const disabled = editable === false || status === "disabled"

  const placeholderContent = placeholderTx
    ? translate(placeholderTx, placeholderTxOptions)
    : placeholder

  return {
    isFocused,
    setIsFocused,
    disabled,
    placeholderContent,
  }
}
