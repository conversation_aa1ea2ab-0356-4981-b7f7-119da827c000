/**
 * TextField Accessory Component
 * 
 * Wrapper for left and right accessories in text fields
 */

import React from "react"
import { useAppTheme } from "@/utils/useAppTheme"
import type { TextFieldAccessoryProps } from "./types"
import { $leftAccessoryStyle, $rightAccessoryStyle } from "./styles"

interface TextFieldAccessoryWrapperProps {
  Accessory?: React.ComponentType<TextFieldAccessoryProps>
  position: "left" | "right"
  status?: "error" | "disabled"
  editable: boolean
  multiline: boolean
}

export const TextFieldAccessory: React.FC<TextFieldAccessoryWrapperProps> = ({
  Accessory,
  position,
  status,
  editable,
  multiline,
}) => {
  const { themed } = useAppTheme()

  if (!Accessory) return null

  const accessoryStyle = position === "left" ? $leftAccessoryStyle : $rightAccessoryStyle

  return (
    <Accessory
      style={themed(accessoryStyle)}
      status={status}
      editable={editable}
      multiline={multiline}
    />
  )
}
