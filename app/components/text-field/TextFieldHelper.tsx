/**
 * TextField Helper Component
 * 
 * Displays helper text for text fields with error state support
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { TextFieldHelperProps } from "./types"
import { $helperStyle } from "./styles"

export const TextFieldHelper: React.FC<TextFieldHelperProps> = ({
  helper,
  helperTx,
  helperTxOptions,
  HelperTextProps,
  status,
}) => {
  const { themed, theme: { colors } } = useAppTheme()

  if (!(helper || helperTx)) return null

  const $helperStyles = [
    $helperStyle,
    status === "error" && { color: colors.inputError }, // Light red for error text
    HelperTextProps?.style,
  ]

  return (
    <Text
      preset="formHelper"
      text={helper}
      tx={helperTx}
      txOptions={helperTxOptions}
      {...HelperTextProps}
      style={themed($helperStyles)}
    />
  )
}
