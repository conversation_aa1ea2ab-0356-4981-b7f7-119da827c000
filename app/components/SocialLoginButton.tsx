/**
 * Social Login Button Components
 *
 * Refactored from a single 219-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the social-login-button module
export {
  SocialLoginButton,
  SocialLoginIcon,
  useSocialLoginConfig,
  useSocialLoginStyles,
  type SocialLoginButtonProps,
  type SocialLoginIconProps,
  type SocialProvider,
  type ProviderConfig,
  PROVIDER_CONFIG,
} from "./SocialLoginButton/social-login-button"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
