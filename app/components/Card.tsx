/**
 * Card Components
 *
 * Refactored from a single 314-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the card module
export {
  Card,
  CardHeader,
  CardContent,
  CardFooter,
  CardWrapper,
  type CardProps,
  type CardHeaderProps,
  type CardContentProps,
  type CardFooterProps,
  type CardWrapperProps,
  type CardPresets,
  type CardVerticalAlignment,
} from "./card"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
