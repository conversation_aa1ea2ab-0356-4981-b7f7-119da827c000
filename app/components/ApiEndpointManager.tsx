/**
 * API Endpoint Manager Component
 * Provides UI for switching API endpoints and environments
 */

import React, { useState } from "react"
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  StyleSheet,
  ScrollView,
} from "react-native"
import { observer } from "mobx-react-lite"
import { 
  apiManager, 
  switchToCommonEndpoints,
  type Environment 
} from "../services/api/api-manager"

interface ApiEndpointManagerProps {
  onClose?: () => void
}

export const ApiEndpointManager = observer(function ApiEndpointManager({
  onClose,
}: ApiEndpointManagerProps) {
  const [customUrl, setCustomUrl] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const apiStatus = apiManager.getApiStatus()

  const handleEnvironmentSwitch = async (env: Environment) => {
    setIsLoading(true)
    try {
      apiManager.switchEnvironment(env)
      
      // Test connection
      const isConnected = await apiManager.testConnection()
      if (isConnected) {
        Alert.alert("Success", `Switched to ${env} environment successfully!`)
      } else {
        Alert.alert("Warning", `Switched to ${env} environment, but connection test failed.`)
      }
    } catch (error) {
      Alert.alert("Error", `Failed to switch to ${env} environment`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCustomUrlUpdate = async () => {
    if (!customUrl.trim()) {
      Alert.alert("Error", "Please enter a valid URL")
      return
    }

    setIsLoading(true)
    try {
      // Validate URL format
      new URL(customUrl)
      
      apiManager.updateBaseUrl(customUrl.trim())
      
      // Test connection
      const isConnected = await apiManager.testConnection()
      if (isConnected) {
        Alert.alert("Success", "Custom API URL updated successfully!")
        setCustomUrl("")
      } else {
        Alert.alert("Warning", "API URL updated, but connection test failed.")
      }
    } catch (error) {
      Alert.alert("Error", "Invalid URL format")
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestConnection = async () => {
    setIsLoading(true)
    try {
      const isConnected = await apiManager.testConnection()
      Alert.alert(
        isConnected ? "Success" : "Failed",
        isConnected ? "API connection is working!" : "Failed to connect to API"
      )
    } catch (error) {
      Alert.alert("Error", "Connection test failed")
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    Alert.alert(
      "Reset API Configuration",
      "This will reset to default settings. Continue?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Reset",
          style: "destructive",
          onPress: () => {
            apiManager.resetToDefault()
            Alert.alert("Success", "API configuration reset to default")
          },
        },
      ]
    )
  }

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>API Endpoint Manager</Text>
        {onClose && (
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Current Status */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Status</Text>
        <View style={styles.statusCard}>
          <Text style={styles.statusText}>Environment: {apiStatus.environment}</Text>
          <Text style={styles.statusText}>Base URL: {apiStatus.baseUrl}</Text>
          <Text style={styles.statusText}>
            Auth Token: {apiStatus.hasAuthToken ? "✅ Set" : "❌ Not Set"}
          </Text>
        </View>
      </View>

      {/* Environment Switcher */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Environment Switch</Text>
        <View style={styles.buttonRow}>
          <TouchableOpacity
            style={[
              styles.envButton,
              apiStatus.environment === "development" && styles.activeEnvButton,
            ]}
            onPress={() => handleEnvironmentSwitch("development")}
            disabled={isLoading}
          >
            <Text style={styles.envButtonText}>Development</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.envButton,
              apiStatus.environment === "staging" && styles.activeEnvButton,
            ]}
            onPress={() => handleEnvironmentSwitch("staging")}
            disabled={isLoading}
          >
            <Text style={styles.envButtonText}>Staging</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.envButton,
              apiStatus.environment === "production" && styles.activeEnvButton,
            ]}
            onPress={() => handleEnvironmentSwitch("production")}
            disabled={isLoading}
          >
            <Text style={styles.envButtonText}>Production</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Custom URL */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Custom API URL</Text>
        <TextInput
          style={styles.textInput}
          value={customUrl}
          onChangeText={setCustomUrl}
          placeholder="https://your-api.com/api/v1"
          autoCapitalize="none"
          autoCorrect={false}
        />
        <TouchableOpacity
          style={styles.updateButton}
          onPress={handleCustomUrlUpdate}
          disabled={isLoading || !customUrl.trim()}
        >
          <Text style={styles.updateButtonText}>Update URL</Text>
        </TouchableOpacity>
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => switchToCommonEndpoints.local()}
          disabled={isLoading}
        >
          <Text style={styles.actionButtonText}>Switch to Local (localhost:3000)</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleTestConnection}
          disabled={isLoading}
        >
          <Text style={styles.actionButtonText}>Test Connection</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.resetButton]}
          onPress={handleReset}
          disabled={isLoading}
        >
          <Text style={[styles.actionButtonText, styles.resetButtonText]}>
            Reset to Default
          </Text>
        </TouchableOpacity>
      </View>

      {isLoading && (
        <View style={styles.loadingOverlay}>
          <Text style={styles.loadingText}>Processing...</Text>
        </View>
      )}
    </ScrollView>
  )
})

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    padding: 16,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: "#ddd",
    justifyContent: "center",
    alignItems: "center",
  },
  closeButtonText: {
    fontSize: 16,
    color: "#666",
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 12,
  },
  statusCard: {
    backgroundColor: "#fff",
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  statusText: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
    fontFamily: "monospace",
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  envButton: {
    flex: 1,
    backgroundColor: "#fff",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    marginHorizontal: 4,
    alignItems: "center",
  },
  activeEnvButton: {
    backgroundColor: "#007AFF",
    borderColor: "#007AFF",
  },
  envButtonText: {
    fontSize: 14,
    color: "#333",
    fontWeight: "500",
  },
  textInput: {
    backgroundColor: "#fff",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    fontSize: 14,
    marginBottom: 12,
  },
  updateButton: {
    backgroundColor: "#007AFF",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  updateButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  actionButton: {
    backgroundColor: "#fff",
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#ddd",
    alignItems: "center",
    marginBottom: 8,
  },
  actionButtonText: {
    fontSize: 14,
    color: "#333",
    fontWeight: "500",
  },
  resetButton: {
    borderColor: "#FF3B30",
  },
  resetButtonText: {
    color: "#FF3B30",
  },
  loadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    color: "#fff",
    fontSize: 16,
  },
})
