/**
 * Progress Stats Card Component - Refactored
 * 
 * Main progress stats card component using smaller, focused sub-components.
 * Reduced from 216 lines to ~50 lines for better maintainability.
 */

import React from "react"
import { View } from "react-native"
import { Text } from "../../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ProgressStatsCardProps } from "./types"
import { useProgressStatsColors } from "./useProgressStatsColors"
import { $container, $sizeVariants, $valueText, $labelText } from "./styles"

/**
 * Progress Statistics Card Component
 * 
 * Displays numerical progress data with visual indicators according to LMS UI guidelines.
 * Features colored backgrounds with white text and light weight (300) labels.
 */
export function ProgressStatsCard(props: ProgressStatsCardProps) {
  const {
    label,
    value,
    backgroundColor,
    textColor,
    size = "medium",
    style: $styleOverride,
  } = props

  const { themed } = useAppTheme()
  const { finalBackgroundColor, finalTextColor } = useProgressStatsColors(
    label,
    backgroundColor,
    textColor
  )

  return (
    <View
      style={[
        themed($container),
        themed($sizeVariants[size]),
        { backgroundColor: finalBackgroundColor },
        $styleOverride,
      ]}
    >
      {/* Statistics Value */}
      <Text
        text={String(value)}
        weight="bold" // Bold weight for numbers per guideline
        size={size === "small" ? "md" : size === "large" ? "xxl" : "xl"}
        style={[themed($valueText), { color: finalTextColor }]}
        numberOfLines={1}
        adjustsFontSizeToFit
      />

      {/* Statistics Label */}
      <Text
        text={label}
        preset="description" // MANDATORY: Light weight (300) for labels
        size={size === "small" ? "xxs" : "xs"}
        style={[themed($labelText), { color: finalTextColor }]}
        numberOfLines={2}
        textAlign="center"
      />
    </View>
  )
}
