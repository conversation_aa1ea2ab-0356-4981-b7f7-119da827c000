/**
 * Progress Stats Grid Component
 * 
 * Displays multiple statistics in a grid layout
 */

import React from "react"
import { View } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ProgressStatsGridProps } from "./types"
import { ProgressStatsCard } from "./ProgressStatsCard"
import { $gridContainer, $gridItem } from "./styles"

export function ProgressStatsGrid(props: ProgressStatsGridProps) {
  const { stats, columns = 3, size = "medium", style: $styleOverride } = props
  const { themed } = useAppTheme()

  return (
    <View style={[themed($gridContainer), $styleOverride]}>
      {stats.map((stat, index) => (
        <View
          key={index}
          style={[
            themed($gridItem),
            { width: `${100 / columns - 2}%` }, // Account for gap
          ]}
        >
          <ProgressStatsCard
            label={stat.label}
            value={stat.value}
            backgroundColor={stat.backgroundColor}
            textColor={stat.textColor}
            size={size}
          />
        </View>
      ))}
    </View>
  )
}
