/**
 * Styles for ProgressStatsCard components
 */

import type { ViewStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

// Container styles
export const $container: ThemedStyle<ViewStyle> = () => ({
  borderRadius: 8, // Medium border radius per guideline
  alignItems: "center",
  justifyContent: "center",
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.05,
  shadowRadius: 2,
  elevation: 1, // Subtle elevation
})

// Size variants
export const $sizeVariants = {
  small: ({ spacing }: { spacing: any }) => ({
    padding: spacing.xs, // 8px for small
    minHeight: 60,
  }),
  medium: ({ spacing }: { spacing: any }) => ({
    padding: spacing.sm, // 12px for medium
    minHeight: 80,
  }),
  large: ({ spacing }: { spacing: any }) => ({
    padding: spacing.md, // 16px for large
    minHeight: 100,
  }),
} satisfies Record<string, ThemedStyle<ViewStyle>>

// Text styles
export const $valueText: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xxs, // 4px to label
  textAlign: "center",
})

export const $labelText: ThemedStyle<ViewStyle> = () => ({
  textAlign: "center",
  opacity: 0.9, // Slightly transparent for hierarchy
})

// Grid styles
export const $gridContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  flexWrap: "wrap",
  gap: spacing.sm, // 8px gap between items
  justifyContent: "space-between",
})

export const $gridItem: ThemedStyle<ViewStyle> = () => ({
  // Width is set dynamically based on columns
})
