/**
 * useProgressStatsColors Hook
 * 
 * Custom hook that calculates background and text colors for progress stats cards
 */

import { useMemo } from "react"

export function useProgressStatsColors(
  label: string,
  backgroundColor?: string,
  textColor?: string
) {
  const finalBackgroundColor = useMemo(() => {
    if (backgroundColor) return backgroundColor
    
    // Default colors based on common LMS statistics
    if (label.toLowerCase().includes("course")) return "#132339" // Dark blue
    if (label.toLowerCase().includes("progress")) return "#FFBB50" // Orange
    if (label.toLowerCase().includes("complete")) return "#F4F2F1" // Light gray
    
    return "#132339" // Default to dark blue
  }, [backgroundColor, label])

  const finalTextColor = useMemo(() => {
    if (textColor) return textColor
    
    // Use white text on dark backgrounds, dark text on light backgrounds
    return finalBackgroundColor === "#F4F2F1" ? "#132339" : "#FFFFFF"
  }, [textColor, finalBackgroundColor])

  return {
    finalBackgroundColor,
    finalTextColor,
  }
}
