/**
 * Types for ProgressStatsCard components
 */

import type { StyleProp, ViewStyle } from "react-native"

export interface ProgressStatsCardProps {
  /**
   * Statistics label
   */
  label: string
  /**
   * Statistics value (number or string)
   */
  value: number | string
  /**
   * Background color for the card
   */
  backgroundColor?: string
  /**
   * Text color override
   */
  textColor?: string
  /**
   * Card size variant
   */
  size?: "small" | "medium" | "large"
  /**
   * Optional icon name
   */
  icon?: string
  /**
   * Optional style override
   */
  style?: StyleProp<ViewStyle>
}

export interface ProgressStatsGridProps {
  /**
   * Array of statistics to display
   */
  stats: Array<{
    label: string
    value: number | string
    backgroundColor?: string
    textColor?: string
    icon?: string
  }>
  /**
   * Number of columns in the grid
   */
  columns?: number
  /**
   * Size of individual stat cards
   */
  size?: "small" | "medium" | "large"
  /**
   * Optional style override
   */
  style?: StyleProp<ViewStyle>
}

export type ProgressStatsSize = "small" | "medium" | "large"
