/**
 * Video Lesson Actions Component
 * 
 * Displays additional action buttons for video lessons
 */

import React from "react"
import { View, Pressable } from "react-native"
import { Icon } from "../Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { VideoLessonActionsProps } from "./types"
import { $actions, $moreButton } from "./styles"

export const VideoLessonActions: React.FC<VideoLessonActionsProps> = ({
  isLocked,
  onMorePress,
}) => {
  const { themed } = useAppTheme()

  const handleMorePress = () => {
    if (onMorePress) {
      onMorePress()
    }
  }

  return (
    <View style={themed($actions)}>
      {!isLocked && (
        <Pressable
          onPress={handleMorePress}
          style={themed($moreButton)}
          accessibilityLabel="More options"
        >
          <Icon
            icon="moreVertical"
            size={20}
            color="#978F8A" // Gray for secondary actions
          />
        </Pressable>
      )}
    </View>
  )
}
