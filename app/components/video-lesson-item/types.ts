/**
 * Types for VideoLessonItem components
 */

import type { StyleProp, ViewStyle } from "react-native"

export interface VideoLessonItemProps {
  /**
   * Lesson title
   */
  title: string
  /**
   * Lesson duration (e.g., "15:30")
   */
  duration: string
  /**
   * Whether the lesson is completed
   */
  isCompleted?: boolean
  /**
   * Whether the lesson is locked
   */
  isLocked?: boolean
  /**
   * Lesson thumbnail URL
   */
  thumbnail?: string
  /**
   * Callback when play button is pressed
   */
  onPlay?: () => void
  /**
   * Callback when lesson item is pressed
   */
  onPress?: () => void
  /**
   * Optional style override
   */
  style?: StyleProp<ViewStyle>
}

export interface VideoLessonPlayButtonProps {
  isLocked: boolean
  onPlay?: () => void
}

export interface VideoLessonContentProps {
  title: string
  duration: string
  isCompleted: boolean
}

export interface VideoLessonActionsProps {
  isLocked: boolean
  onMorePress?: () => void
}

export interface VideoLessonListProps {
  /**
   * Array of lessons to display
   */
  lessons: Array<{
    id: string
    title: string
    duration: string
    isCompleted?: boolean
    isLocked?: boolean
    thumbnail?: string
  }>
  /**
   * Callback when a lesson's play button is pressed
   */
  onLessonPlay?: (lessonId: string) => void
  /**
   * Callback when a lesson item is pressed
   */
  onLessonPress?: (lessonId: string) => void
  /**
   * Optional style override
   */
  style?: StyleProp<ViewStyle>
}
