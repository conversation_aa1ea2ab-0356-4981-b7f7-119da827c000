/**
 * Styles for VideoLessonItem components
 */

import type { ViewStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

// Main container styles
export const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  paddingVertical: spacing.sm, // 8px vertical
  paddingHorizontal: spacing.md, // 16px horizontal
  backgroundColor: "#FFFFFF", // White background
  borderRadius: 8, // Medium border radius
  gap: spacing.sm, // 8px between elements
})

// Play button styles
export const $playButtonContainer: ThemedStyle<ViewStyle> = () => ({
  // Play button container - fixed width for alignment
  width: 40,
  height: 40,
  justifyContent: "center",
  alignItems: "center",
})

export const $lockedButton: ThemedStyle<ViewStyle> = () => ({
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "#F4F2F1", // Light gray background
  justifyContent: "center",
  alignItems: "center",
})

// Content styles
export const $content: ThemedStyle<ViewStyle> = () => ({
  flex: 1, // Take remaining space
})

export const $titleContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  marginBottom: spacing.xxs, // 4px to duration
})

export const $title: ThemedStyle<ViewStyle> = () => ({
  flex: 1, // Take remaining space
})

export const $statusIcon: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginLeft: spacing.xs, // 8px from title
})

export const $duration: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.textDim, // Secondary text color
})

// Actions styles
export const $actions: ThemedStyle<ViewStyle> = () => ({
  // Actions container - fixed width for alignment
  width: 32,
  alignItems: "center",
})

export const $moreButton: ThemedStyle<ViewStyle> = () => ({
  width: 32,
  height: 32,
  justifyContent: "center",
  alignItems: "center",
  borderRadius: 16,
})

// List styles
export const $listContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.md, // 16px vertical spacing between items
})

export const $listItemMargin: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md, // 16px margin between items
})
