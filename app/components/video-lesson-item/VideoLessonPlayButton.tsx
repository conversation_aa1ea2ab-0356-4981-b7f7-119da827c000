/**
 * Video Lesson Play Button Component
 * 
 * Displays play button or lock icon based on lesson status
 */

import React from "react"
import { View } from "react-native"
import { Button } from "../Button"
import { Icon } from "../Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { VideoLessonPlayButtonProps } from "./types"
import { $playButtonContainer, $lockedButton } from "./styles"

export const VideoLessonPlayButton: React.FC<VideoLessonPlayButtonProps> = ({
  isLocked,
  onPlay,
}) => {
  const { themed } = useAppTheme()

  const handlePlayPress = () => {
    if (!isLocked && onPlay) {
      onPlay()
    }
  }

  return (
    <View style={themed($playButtonContainer)}>
      {isLocked ? (
        <View style={themed($lockedButton)}>
          <Icon
            icon="lock"
            size={20}
            color="#978F8A" // Gray for locked
          />
        </View>
      ) : (
        <Button
          preset="play"
          onPress={handlePlayPress}
          accessibilityLabel="Play lesson"
        >
          <Icon
            icon="play"
            size={20}
            color="#FFFFFF"
          />
        </Button>
      )}
    </View>
  )
}
