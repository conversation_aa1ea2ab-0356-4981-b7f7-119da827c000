/**
 * Video Lesson List Component
 * 
 * Displays a list of video lessons with consistent spacing
 */

import React from "react"
import { View } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { VideoLessonListProps } from "./types"
import { VideoLessonItem } from "./VideoLessonItem"
import { $listContainer, $listItemMargin } from "./styles"

export const VideoLessonList: React.FC<VideoLessonListProps> = ({
  lessons,
  onLessonPlay,
  onLessonPress,
  style: $styleOverride,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={[themed($listContainer), $styleOverride]}>
      {lessons.map((lesson, index) => (
        <VideoLessonItem
          key={lesson.id}
          title={lesson.title}
          duration={lesson.duration}
          isCompleted={lesson.isCompleted}
          isLocked={lesson.isLocked}
          thumbnail={lesson.thumbnail}
          onPlay={() => onLessonPlay?.(lesson.id)}
          onPress={() => onLessonPress?.(lesson.id)}
          style={index < lessons.length - 1 ? themed($listItemMargin) : undefined}
        />
      ))}
    </View>
  )
}
