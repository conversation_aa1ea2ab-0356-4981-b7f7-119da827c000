/**
 * Video Lesson Content Component
 * 
 * Displays lesson title, duration, and completion status
 */

import React from "react"
import { View } from "react-native"
import { Text } from "../Text"
import { Icon } from "../Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { VideoLessonContentProps } from "./types"
import { $content, $titleContainer, $title, $statusIcon, $duration } from "./styles"

export const VideoLessonContent: React.FC<VideoLessonContentProps> = ({
  title,
  duration,
  isCompleted,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($content)}>
      <View style={themed($titleContainer)}>
        <Text
          text={title}
          preset="default"
          weight="normal" // Regular weight for readability
          size="sm"
          style={themed($title)}
          numberOfLines={2}
        />
        
        {/* Status Icon */}
        {isCompleted && (
          <Icon
            icon="checkCircle"
            size={20}
            color="#10B981" // Green for completed
            style={themed($statusIcon)}
          />
        )}
      </View>

      <Text
        text={duration}
        preset="description" // MANDATORY: Light weight (300) for duration
        size="xs"
        style={themed($duration)}
      />
    </View>
  )
}
