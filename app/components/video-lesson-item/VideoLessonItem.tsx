/**
 * Video Lesson Item Component - Refactored
 * 
 * Main video lesson item component using smaller, focused sub-components.
 * Reduced from 285 lines to ~80 lines for better maintainability.
 */

import React from "react"
import { Pressable } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { VideoLessonItemProps } from "./types"
import { VideoLessonPlayButton } from "./VideoLessonPlayButton"
import { VideoLessonContent } from "./VideoLessonContent"
import { VideoLessonActions } from "./VideoLessonActions"
import { $container } from "./styles"

/**
 * Video Lesson Item Component
 * 
 * Displays individual video lessons with play functionality according to LMS UI guidelines.
 * Features orange play buttons, light weight (300) duration text, and status indicators.
 */
export const VideoLessonItem: React.FC<VideoLessonItemProps> = (props) => {
  const {
    title,
    duration,
    isCompleted = false,
    isLocked = false,
    onPlay,
    onPress,
    style: $styleOverride,
  } = props

  const { themed } = useAppTheme()

  const handleItemPress = () => {
    if (!isLocked && onPress) {
      onPress()
    }
  }

  const handleMorePress = () => {
    // Handle more options
    console.log("More options pressed for:", title)
  }

  return (
    <Pressable
      style={[themed($container), $styleOverride]}
      onPress={handleItemPress}
      disabled={isLocked}
      accessibilityRole="button"
      accessibilityLabel={`Lesson: ${title}, Duration: ${duration}`}
      accessibilityState={{ disabled: isLocked }}
    >
      <VideoLessonPlayButton
        isLocked={isLocked}
        onPlay={onPlay}
      />

      <VideoLessonContent
        title={title}
        duration={duration}
        isCompleted={isCompleted}
      />

      <VideoLessonActions
        isLocked={isLocked}
        onMorePress={handleMorePress}
      />
    </Pressable>
  )
}
