/**
 * Social Login Icon Component
 *
 * Displays the appropriate icon for each social login provider
 */

import React from "react"
import { ActivityIndicator, Image } from "react-native"
import { Text } from "../../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { SocialLoginIconProps } from "./types"
import { useSocialLoginConfig } from "./useSocialLoginConfig"
import { $loadingIndicator, $iconImage, $icon } from "./styles"

export const SocialLoginIcon: React.FC<SocialLoginIconProps> = ({
  provider,
  loading,
  textColor,
}) => {
  const { themed } = useAppTheme()
  const { config } = useSocialLoginConfig(provider)

  if (loading) {
    return (
      <ActivityIndicator
        size="small"
        color={textColor}
        style={themed($loadingIndicator)}
      />
    )
  }

  if (provider === "google") {
    return (
      <Image
        source={require("../../../../assets/icons/demo/2702602-min.png")}
        style={themed($iconImage)}
      />
    )
  }

  if (provider === "phone") {
    return (
      <Image
        source={require("../../../../assets/icons/demo/9840541-min.png")}
        style={themed($iconImage)}
      />
    )
  }

  return (
    <Text style={themed($icon)}>
      {config.icon}
    </Text>
  )
}
