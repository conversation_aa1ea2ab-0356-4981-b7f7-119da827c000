/**
 * Social Login Button Component - Refactored
 * 
 * Main social login button component using smaller, focused sub-components.
 * Reduced from 219 lines to ~50 lines for better maintainability.
 */

import React from "react"
import { Pressable, View } from "react-native"
import { Text } from "../../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { SocialLoginButtonProps } from "./types"
import { SocialLoginIcon } from "./SocialLoginIcon"
import { useSocialLoginConfig } from "./useSocialLoginConfig"
import { useSocialLoginStyles } from "./useSocialLoginStyles"
import { $content, $pressed } from "./styles"

/**
 * Social Login Button Component
 * 
 * A customizable button for social login providers (Google, Facebook, Apple, Phone).
 * Features loading states, disabled states, and provider-specific styling.
 */
export function SocialLoginButton(props: SocialLoginButtonProps) {
  const {
    provider,
    text,
    loading = false,
    disabled = false,
    onPress,
    iconOnly = false, // New prop for icon-only mode
  } = props

  const { themed } = useAppTheme()
  const { config, displayText } = useSocialLoginConfig(provider, text)
  const isDisabled = disabled || loading
  const { $containerStyle, $textStyle } = useSocialLoginStyles(props, config, isDisabled)

  return (
    <Pressable
      style={({ pressed }) => [
        $containerStyle,
        pressed && !isDisabled && themed($pressed),
      ]}
      onPress={onPress}
      disabled={isDisabled}
      accessibilityRole="button"
      accessibilityLabel={displayText}
      accessibilityState={{ disabled: isDisabled }}
    >
      <View style={themed($content)}>
        <SocialLoginIcon
          provider={provider}
          loading={loading}
          textColor={config.textColor}
        />

        {!iconOnly && (
          <Text
            text={displayText}
            style={$textStyle}
            numberOfLines={1}
          />
        )}
      </View>
    </Pressable>
  )
}
