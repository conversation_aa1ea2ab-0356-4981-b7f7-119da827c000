/**
 * Styles for SocialLoginButton components
 */

import type { ViewStyle, TextStyle, ImageStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

// Container styles
export const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  borderRadius: 12,
  borderWidth: 1,
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  marginVertical: 0,
  minHeight: 44,
  justifyContent: "center",
  alignItems: "center",
})

export const $content: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: spacing.xs,
})

// Text styles
export const $text: ThemedStyle<TextStyle> = ({ typography }) => ({
  fontFamily: typography.primary.light,
  fontSize: 16,
  textAlign: "center",
})

// Icon styles
export const $icon: ThemedStyle<TextStyle> = () => ({
  fontSize: 20,
})

export const $iconImage: ThemedStyle<ImageStyle> = () => ({
  width: 20,
  height: 20,
  resizeMode: "contain",
})

// Loading styles
export const $loadingIndicator: ThemedStyle<ViewStyle> = () => ({
  marginRight: 0,
})

// State styles
export const $pressed: ThemedStyle<ViewStyle> = () => ({
  opacity: 0.8,
  transform: [{ scale: 0.98 }],
})

export const $disabled: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral200,
  borderColor: colors.palette.neutral300,
})

export const $disabledText: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.palette.neutral500,
})

// Icon-only mode styles
export const $iconOnlyContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  paddingHorizontal: 0,
  paddingVertical: 0,
  minHeight: 48,
  justifyContent: "center",
  alignItems: "center",
})
