/**
 * Constants for SocialLoginButton components
 */

import type { SocialProvider, ProviderConfig } from "./types"

export const PROVIDER_CONFIG: Record<SocialProvider, ProviderConfig> = {
  google: {
    text: "Continue with Google",
    backgroundColor: "#ffffff",
    textColor: "#1f2937",
    borderColor: "#d1d5db",
    icon: "G", // Will be replaced with actual Google icon image
  },
  facebook: {
    text: "Continue with Facebook",
    backgroundColor: "#1877f2",
    textColor: "#ffffff",
    borderColor: "#1877f2",
    icon: "📘", // You can replace with actual Facebook icon
  },
  apple: {
    text: "Continue with Apple",
    backgroundColor: "#000000",
    textColor: "#ffffff",
    borderColor: "#000000",
    icon: "🍎", // You can replace with actual Apple icon
  },
  phone: {
    text: "Continue with Phone",
    backgroundColor: "#ffffff",
    textColor: "#1f2937",
    borderColor: "#d1d5db",
    icon: "📱", // Will be replaced with actual Phone icon image
  },
}
