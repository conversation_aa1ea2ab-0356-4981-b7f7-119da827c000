/**
 * useSocialLoginStyles Hook
 * 
 * Custom hook that calculates social login button styles based on props and theme
 */

import { useAppTheme } from "@/utils/useAppTheme"
import type { SocialLoginButtonProps, ProviderConfig } from "./types"
import { $container, $text, $disabled, $disabledText, $iconOnlyContainer } from "./styles"

export function useSocialLoginStyles(
  props: SocialLoginButtonProps,
  config: ProviderConfig,
  isDisabled: boolean
) {
  const {
    style: $styleOverride,
    textStyle: $textStyleOverride,
    iconOnly = false,
  } = props

  const { themed } = useAppTheme()

  const $containerStyle = [
    themed(iconOnly ? $iconOnlyContainer : $container),
    {
      backgroundColor: config.backgroundColor,
      borderColor: config.borderColor,
    },
    isDisabled && themed($disabled),
    $styleOverride,
  ]

  const $textStyle = [
    themed($text),
    {
      color: config.textColor,
    },
    isDisabled && themed($disabledText),
    $textStyleOverride,
  ]

  return {
    $containerStyle,
    $textStyle,
  }
}
