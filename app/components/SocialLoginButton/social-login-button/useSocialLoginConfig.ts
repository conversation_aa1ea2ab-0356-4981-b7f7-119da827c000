/**
 * useSocialLoginConfig Hook
 * 
 * Custom hook that provides social login provider configuration
 */

import { useMemo } from "react"
import type { SocialProvider, ProviderConfig } from "./types"
import { PROVIDER_CONFIG } from "./constants"

export function useSocialLoginConfig(provider: SocialProvider, text?: string) {
  const config = useMemo(() => PROVIDER_CONFIG[provider], [provider])
  
  const displayText = useMemo(() => text || config.text, [text, config.text])

  return {
    config,
    displayText,
  }
}
