/**
 * Types for SocialLoginButton components
 */

import type { ViewStyle, TextStyle } from "react-native"

export type SocialProvider = "google" | "facebook" | "apple" | "phone"

export interface SocialLoginButtonProps {
  /**
   * The social provider type
   */
  provider: SocialProvider

  /**
   * Button text override
   */
  text?: string

  /**
   * Loading state
   */
  loading?: boolean

  /**
   * Disabled state
   */
  disabled?: boolean

  /**
   * Button press handler
   */
  onPress?: () => void

  /**
   * Container style override
   */
  style?: ViewStyle

  /**
   * Text style override
   */
  textStyle?: TextStyle

  /**
   * Show only icon without text
   */
  iconOnly?: boolean
}

export interface SocialLoginIconProps {
  provider: SocialProvider
  loading: boolean
  textColor: string
}

export interface ProviderConfig {
  text: string
  backgroundColor: string
  textColor: string
  borderColor: string
  icon: string
}
