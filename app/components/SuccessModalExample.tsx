/**
 * SuccessModalExample Component
 * 
 * Example component demonstrating how to use the SuccessModal
 * with the useSuccessModal hook.
 */

import React from "react"
import { View, StyleSheet } from "react-native"
import { Button } from "./Button"
import { SuccessModal } from "./SuccessModal"
import { useSuccessModal } from "../hooks/useSuccessModal"
import { colors, spacing } from "../theme"

/**
 * Example component showing different ways to use SuccessModal
 */
export const SuccessModalExample: React.FC = () => {
  const { isVisible, showSuccess, hideSuccess, config } = useSuccessModal()

  const handleBasicSuccess = () => {
    showSuccess({
      title: "Success",
      message: "Profile updated successfully!",
      buttonText: "OK",
    })
  }

  const handleCustomSuccess = () => {
    showSuccess({
      title: "Payment Complete",
      message: "Your payment has been processed successfully. You will receive a confirmation email shortly.",
      buttonText: "Continue",
      iconName: "card",
      onButtonPress: () => {
        console.log("Custom action performed")
        hideSuccess()
      },
    })
  }

  const handleSimpleSuccess = () => {
    showSuccess() // Uses default values
  }

  return (
    <View style={styles.container}>
      <Button
        text="Show Basic Success"
        preset="filled"
        style={styles.button}
        onPress={handleBasicSuccess}
      />

      <Button
        text="Show Custom Success"
        preset="filled"
        style={styles.button}
        onPress={handleCustomSuccess}
      />

      <Button
        text="Show Simple Success"
        preset="filled"
        style={styles.button}
        onPress={handleSimpleSuccess}
      />

      <SuccessModal
        visible={isVisible}
        onClose={hideSuccess}
        title={config.title}
        message={config.message}
        buttonText={config.buttonText}
        iconName={config.iconName}
        onButtonPress={config.onButtonPress}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: spacing.lg,
    backgroundColor: colors.background,
  },
  button: {
    marginBottom: spacing.md,
    minWidth: 200,
  },
})
