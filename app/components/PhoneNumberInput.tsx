/**
 * Phone Number Input Components
 *
 * Refactored from a single 295-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the phone-number-input module
export {
  PhoneNumberInput,
  PhoneNumberLabel,
  PhoneNumberInputField,
  CountryCodeSelector,
  PhoneNumberHelper,
  usePhoneNumberFormatting,
  type PhoneNumberInputProps,
  type PhoneNumberInputRef,
  type PhoneNumberLabelProps,
  type PhoneNumberHelperProps,
  type CountryCodeSelectorProps,
  type PhoneNumberInputFieldProps,
  type UsePhoneNumberFormattingResult,
  type CountryCode,
  COUNTRY_CODES,
  DEFAULT_COUNTRY_CODE,
  DEFAULT_PLACEHOLDER,
} from "./phone-number-input"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
