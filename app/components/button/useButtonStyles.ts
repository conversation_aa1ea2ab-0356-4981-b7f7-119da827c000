/**
 * useButtonStyles Hook
 * 
 * Custom hook that calculates button styles based on state and props
 */

import { useMemo } from "react"
import type { PressableStateCallbackType, StyleProp, TextStyle, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ButtonProps, UseButtonStylesResult } from "./types"
import {
  $viewPresets,
  $textPresets,
  $pressedViewPresets,
  $pressedTextPresets,
} from "./presets"

export function useButtonStyles(props: ButtonProps): UseButtonStylesResult {
  const {
    preset = "default",
    style: $viewStyleOverride,
    pressedStyle: $pressedViewStyleOverride,
    disabled,
    disabledStyle: $disabledViewStyleOverride,
    textStyle: $textStyleOverride,
    pressedTextStyle: $pressedTextStyleOverride,
    disabledTextStyle: $disabledTextStyleOverride,
  } = props

  const { themed } = useAppTheme()

  const viewStyle = useMemo(
    () => (state: PressableStateCallbackType): StyleProp<ViewStyle> => {
      const isPressed = state.pressed

      return [
        themed($viewPresets[preset]),
        $viewStyleOverride,
        isPressed && [themed($pressedViewPresets[preset]), $pressedViewStyleOverride],
        disabled && $disabledViewStyleOverride,
      ]
    },
    [
      themed,
      preset,
      $viewStyleOverride,
      $pressedViewStyleOverride,
      disabled,
      $disabledViewStyleOverride,
    ]
  )

  const textStyle = useMemo(
    () => (state: PressableStateCallbackType): StyleProp<TextStyle> => {
      const isPressed = state.pressed

      return [
        themed($textPresets[preset]),
        $textStyleOverride,
        isPressed && [themed($pressedTextPresets[preset]), $pressedTextStyleOverride],
        disabled && $disabledTextStyleOverride,
      ]
    },
    [
      themed,
      preset,
      $textStyleOverride,
      $pressedTextStyleOverride,
      disabled,
      $disabledTextStyleOverride,
    ]
  )

  return {
    viewStyle,
    textStyle,
  }
}
