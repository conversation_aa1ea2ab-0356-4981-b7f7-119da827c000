/**
 * Button Component - Refactored
 * 
 * Main button component using smaller, focused sub-components.
 * Reduced from 294 lines to ~80 lines for better maintainability.
 */

import React from "react"
import { Pressable } from "react-native"
import type { ButtonProps } from "./types"
import { ButtonContent } from "./ButtonContent"
import { ButtonAccessory } from "./ButtonAccessory"
import { useButtonStyles } from "./useButtonStyles"

/**
 * A component that allows users to take actions and make choices.
 * Wraps the Text component with a Pressable component.
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Button/}
 * @param {ButtonProps} props - The props for the `Button` component.
 * @returns {JSX.Element} The rendered `Button` component.
 */
export const Button: React.FC<ButtonProps> = (props) => {
  const {
    tx,
    text,
    txOptions,
    children,
    RightAccessory,
    LeftAccessory,
    disabled,
    ...rest
  } = props

  const { viewStyle, textStyle } = useButtonStyles(props)

  return (
    <Pressable
      style={viewStyle}
      accessibilityRole="button"
      accessibilityState={{ disabled }}
      {...rest}
      disabled={disabled}
    >
      {(state) => (
        <>
          <ButtonAccessory
            Accessory={LeftAccessory}
            position="left"
            state={state}
            disabled={disabled}
          />

          <ButtonContent
            tx={tx}
            text={text}
            txOptions={txOptions}
            textStyle={textStyle}
            state={state}
          >
            {children}
          </ButtonContent>

          <ButtonAccessory
            Accessory={RightAccessory}
            position="right"
            state={state}
            disabled={disabled}
          />
        </>
      )}
    </Pressable>
  )
}
