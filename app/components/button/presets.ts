/**
 * Button presets and style configurations
 */

import type { TextStyle, ViewStyle } from "react-native"
import type { ThemedStyle, ThemedStyleArray } from "@/theme"
import { $styles } from "../../theme"
import type { ButtonPresets } from "./types"
import { $baseViewStyle, $baseTextStyle } from "./styles"

export const $viewPresets: Record<ButtonPresets, ThemedStyleArray<ViewStyle>> = {
  // Default button - secondary style with border
  default: [
    $styles.row,
    $baseViewStyle,
    ({ colors }) => ({
      borderWidth: 1,
      borderColor: colors.border,
      backgroundColor: colors.backgroundCard,
    }),
  ],
  // Filled button - primary style with blue background
  filled: [
    $styles.row,
    $baseViewStyle,
    ({ colors }) => ({
      backgroundColor: colors.tint, // Royal blue background
      borderWidth: 0,
    }),
  ],
  // Secondary button - sky blue background per guideline
  secondary: [
    $styles.row,
    $baseViewStyle,
    ({ colors }) => ({
      backgroundColor: colors.tintSecondary, // Sky blue background
      borderWidth: 0,
    }),
  ],
  // Play button - circular orange button per guideline
  play: [
    $baseViewStyle,
    ({ colors }) => ({
      backgroundColor: colors.playButton, // Orange background - MANDATORY
      borderRadius: 20, // Circular (40px diameter)
      width: 40,
      height: 40,
      minHeight: 40,
      paddingVertical: 0,
      paddingHorizontal: 0,
    }),
  ],
  // Reversed button - dark style for light backgrounds
  reversed: [
    $styles.row,
    $baseViewStyle,
    ({ colors }) => ({
      backgroundColor: colors.navigationPrimary, // Deep navy background
      borderWidth: 0,
    }),
  ],
}

export const $textPresets: Record<ButtonPresets, ThemedStyleArray<TextStyle>> = {
  // Default button text - primary text color
  default: [$baseTextStyle, ({ colors }) => ({ color: colors.text })],
  // Filled button text - white text on blue background
  filled: [$baseTextStyle, ({ colors }) => ({ color: colors.textOnPrimary })],
  // Secondary button text - dark text on light blue background
  secondary: [$baseTextStyle, ({ colors }) => ({ color: colors.text })],
  // Play button text - white text on orange background
  play: [$baseTextStyle, ({ colors }) => ({ color: "#FFFFFF", fontSize: 20 })],
  // Reversed button text - light blue text on dark background
  reversed: [$baseTextStyle, ({ colors }) => ({ color: colors.navigationText })],
}

export const $pressedViewPresets: Record<ButtonPresets, ThemedStyle<ViewStyle>> = {
  // Default button pressed - light blue background
  default: ({ colors }) => ({ backgroundColor: colors.backgroundAccent }),
  // Filled button pressed - dark blue background
  filled: ({ colors }) => ({ backgroundColor: colors.tintPressed }),
  // Secondary button pressed - darker sky blue
  secondary: ({ colors }) => ({ backgroundColor: colors.tint }),
  // Play button pressed - darker orange
  play: () => ({ backgroundColor: "#E6A545", transform: [{ scale: 0.96 }] }),
  // Reversed button pressed - darker navy background
  reversed: ({ colors }) => ({ backgroundColor: colors.navigationSecondary }),
}

export const $pressedTextPresets: Record<ButtonPresets, ThemedStyle<TextStyle>> = {
  default: () => ({ opacity: 0.9 }),
  filled: () => ({ opacity: 0.9 }),
  secondary: () => ({ opacity: 0.9 }),
  play: () => ({ opacity: 0.9 }),
  reversed: () => ({ opacity: 0.9 }),
}
