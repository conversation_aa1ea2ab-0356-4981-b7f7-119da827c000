/**
 * Button Accessory Component
 * 
 * Wrapper for left and right accessories in buttons
 */

import React from "react"
import type { ComponentType } from "react"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ButtonAccessoryProps } from "./types"
import { $leftAccessoryStyle, $rightAccessoryStyle } from "./styles"

interface ButtonAccessoryWrapperProps {
  Accessory?: ComponentType<ButtonAccessoryProps>
  position: "left" | "right"
  state: Parameters<ButtonAccessoryProps["style"]>[0]
  disabled?: boolean
}

export const ButtonAccessory: React.FC<ButtonAccessoryWrapperProps> = ({
  Accessory,
  position,
  state,
  disabled,
}) => {
  const { themed } = useAppTheme()

  if (!Accessory) return null

  const accessoryStyle = position === "left" ? $leftAccessoryStyle : $rightAccessoryStyle

  return (
    <Accessory
      style={themed(accessoryStyle)}
      pressableState={state}
      disabled={disabled}
    />
  )
}
