/**
 * Button Content Component
 * 
 * Handles text rendering and children for buttons
 */

import React from "react"
import { Text } from "../Text"
import type { ButtonContentProps } from "./types"

export const ButtonContent: React.FC<ButtonContentProps> = ({
  tx,
  text,
  txOptions,
  children,
  textStyle,
  state,
}) => {
  const content = tx || text || children

  if (!content) return null

  if (typeof content === "string" || tx) {
    return (
      <Text
        tx={tx}
        text={text}
        txOptions={txOptions}
        style={textStyle(state)}
        weight="light" // MANDATORY: Light weight (300) for all button text
      />
    )
  }

  return <>{children}</>
}
