/**
 * OTP Input Components
 *
 * Refactored from a single 290-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the otp-input module
export {
  OTPInput,
  OTPLabel,
  OTPInputField,
  OTPInputContainer,
  OTPHelper,
  useOTPLogic,
  type OTPInputProps,
  type OTPLabelProps,
  type OTPHelperProps,
  type OTPInputFieldProps,
  type OTPInputContainerProps,
  type UseOTPLogicResult,
} from "./otp-input"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
