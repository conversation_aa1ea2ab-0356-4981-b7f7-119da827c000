/**
 * Utility functions for Screen components
 */

import { Platform, ViewStyle } from "react-native"
import type { ScreenPreset } from "./types"

export const DEFAULT_BOTTOM_OFFSET = 50

export const isIos = Platform.OS === "ios"

/**
 * @param {ScreenPreset?} preset - The preset to check.
 * @returns {boolean} - Whether the preset is non-scrolling.
 */
export function isNonScrolling(preset?: ScreenPreset): boolean {
  return !preset || preset === "fixed"
}

// Screen styles
export const $containerStyle: ViewStyle = {
  flex: 1,
  height: "100%",
  width: "100%",
}

export const $outerStyle: ViewStyle = {
  flex: 1,
  height: "100%",
  width: "100%",
}

export const $justifyFlexEnd: ViewStyle = {
  justifyContent: "flex-end",
}

export const $innerStyle: ViewStyle = {
  justifyContent: "flex-start",
  alignItems: "stretch",
}
