/**
 * Screen Without Scrolling Component
 * 
 * Fixed preset screen that doesn't scroll
 */

import React from "react"
import { View } from "react-native"
import type { ScreenWithoutScrollingProps } from "./types"
import { $outerStyle, $innerStyle, $justifyFlexEnd } from "./utils"

export const ScreenWithoutScrolling: React.FC<ScreenWithoutScrollingProps> = ({
  style,
  contentContainerStyle,
  children,
  preset,
}) => {
  return (
    <View style={[$outerStyle, style]}>
      <View style={[$innerStyle, preset === "fixed" && $justifyFlexEnd, contentContainerStyle]}>
        {children}
      </View>
    </View>
  )
}
