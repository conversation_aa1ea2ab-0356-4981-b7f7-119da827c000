/**
 * Screen Component - Refactored
 * 
 * Main screen component using smaller, focused sub-components.
 * Reduced from 305 lines to ~60 lines for better maintainability.
 */

import React from "react"
import type { ScreenProps } from "./types"
import { ScreenContainer } from "./ScreenContainer"
import { ScreenWithoutScrolling } from "./ScreenWithoutScrolling"
import { ScreenWithScrolling } from "./ScreenWithScrolling"
import { isNonScrolling } from "./utils"

/**
 * Represents a screen component that provides a consistent layout and behavior for different screen presets.
 * The `Screen` component can be used with different presets such as "fixed", "scroll", or "auto".
 * It handles safe area insets, status bar settings, keyboard avoiding behavior, and scrollability based on the preset.
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Screen/}
 * @param {ScreenProps} props - The props for the `Screen` component.
 * @returns {JSX.Element} The rendered `Screen` component.
 */
export const Screen: React.FC<ScreenProps> = (props) => {
  const {
    backgroundColor,
    KeyboardAvoidingViewProps,
    keyboardOffset = 0,
    safeAreaEdges,
    StatusBarProps,
    statusBarStyle,
  } = props

  return (
    <ScreenContainer
      backgroundColor={backgroundColor}
      safeAreaEdges={safeAreaEdges}
      statusBarStyle={statusBarStyle}
      keyboardOffset={keyboardOffset}
      StatusBarProps={StatusBarProps}
      KeyboardAvoidingViewProps={KeyboardAvoidingViewProps}
    >
      {isNonScrolling(props.preset) ? (
        <ScreenWithoutScrolling {...props} />
      ) : (
        <ScreenWithScrolling {...props} />
      )}
    </ScreenContainer>
  )
}
