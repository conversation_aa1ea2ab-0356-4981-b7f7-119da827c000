/**
 * Screen Container Component
 * 
 * Provides the main container with safe area, status bar, and keyboard avoiding behavior
 */

import React from "react"
import { View, KeyboardAvoidingView } from "react-native"
import { StatusBar } from "expo-status-bar"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "../../utils/useSafeAreaInsetsStyle"
import { $styles } from "../../theme"
import type { ScreenContainerProps } from "./types"
import { $containerStyle, isIos } from "./utils"

export const ScreenContainer: React.FC<ScreenContainerProps> = ({
  backgroundColor,
  safeAreaEdges,
  statusBarStyle,
  keyboardOffset = 0,
  StatusBarProps,
  KeyboardAvoidingViewProps,
  children,
}) => {
  const {
    theme: { colors },
    themeContext,
  } = useAppTheme()

  const $containerInsets = useSafeAreaInsetsStyle(safeAreaEdges)

  return (
    <View
      style={[
        $containerStyle,
        { backgroundColor: backgroundColor || colors.background },
        $containerInsets,
      ]}
    >
      <StatusBar
        style={statusBarStyle || (themeContext === "dark" ? "light" : "dark")}
        {...StatusBarProps}
      />

      <KeyboardAvoidingView
        behavior={isIos ? "padding" : "height"}
        keyboardVerticalOffset={keyboardOffset}
        {...KeyboardAvoidingViewProps}
        style={[$styles.flex1, KeyboardAvoidingViewProps?.style]}
      >
        {children}
      </KeyboardAvoidingView>
    </View>
  )
}
