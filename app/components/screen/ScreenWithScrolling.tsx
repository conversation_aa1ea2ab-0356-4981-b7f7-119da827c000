/**
 * Screen With Scrolling Component
 * 
 * <PERSON>roll and auto preset screen with scrolling capability
 */

import React, { useRef } from "react"
import { ScrollView } from "react-native"
import { useScrollToTop } from "@react-navigation/native"
import { KeyboardAwareScrollView } from "react-native-keyboard-controller"
import type { ScreenWithScrollingProps, AutoScreenProps } from "./types"
import { useAutoPreset } from "./useAutoPreset"
import { $outerStyle, $innerStyle, DEFAULT_BOTTOM_OFFSET } from "./utils"

export const ScreenWithScrolling: React.FC<ScreenWithScrollingProps> = (props) => {
  const {
    children,
    keyboardShouldPersistTaps = "handled",
    keyboardBottomOffset = DEFAULT_BOTTOM_OFFSET,
    contentContainerStyle,
    ScrollViewProps,
    style,
  } = props

  const ref = useRef<ScrollView>(null)

  const { scrollEnabled, onContentSizeChange, onLayout } = useAutoPreset(props as AutoScreenProps)

  // Add native behavior of pressing the active tab to scroll to the top of the content
  // More info at: https://reactnavigation.org/docs/use-scroll-to-top/
  useScrollToTop(ref)

  return (
    <KeyboardAwareScrollView
      bottomOffset={keyboardBottomOffset}
      {...{ keyboardShouldPersistTaps, scrollEnabled, ref }}
      {...ScrollViewProps}
      onLayout={(e) => {
        onLayout(e)
        ScrollViewProps?.onLayout?.(e)
      }}
      onContentSizeChange={(w: number, h: number) => {
        onContentSizeChange(w, h)
        ScrollViewProps?.onContentSizeChange?.(w, h)
      }}
      style={[$outerStyle, ScrollViewProps?.style, style]}
      contentContainerStyle={[
        $innerStyle,
        ScrollViewProps?.contentContainerStyle,
        contentContainerStyle,
      ]}
    >
      {children}
    </KeyboardAwareScrollView>
  )
}
