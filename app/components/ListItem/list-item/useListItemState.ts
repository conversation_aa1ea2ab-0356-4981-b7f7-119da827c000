/**
 * useListItemState Hook
 * 
 * Custom hook that manages list item state and interactions
 */

import { useMemo, ComponentType } from "react"
import { TouchableOpacity, TouchableOpacityProps, View } from "react-native"
import type { ListItemProps } from "./types"

export function useListItemState(props: ListItemProps) {
  const {
    onPress,
    onPressIn,
    onPressOut,
    onLongPress,
  } = props

  const isTouchable = useMemo(() => {
    return (
      onPress !== undefined ||
      onPressIn !== undefined ||
      onPressOut !== undefined ||
      onLongPress !== undefined
    )
  }, [onPress, onPressIn, onPressOut, onLongPress])

  const Wrapper: ComponentType<TouchableOpacityProps> = useMemo(
    () => (isTouchable ? TouchableOpacity : View),
    [isTouchable],
  )

  return {
    isTouchable,
    Wrapper,
  }
}
