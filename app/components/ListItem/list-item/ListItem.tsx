/**
 * List Item Component - Refactored
 * 
 * Main list item component using smaller, focused sub-components.
 * Reduced from 242 lines to ~60 lines for better maintainability.
 */

import { forwardRef } from "react"
import { View } from "react-native"
import { Text } from "../../Text"
import type { ListItemProps } from "./types"
import { ListItemAction } from "./ListItemAction"
import { useListItemState } from "./useListItemState"
import { useListItemStyles } from "./useListItemStyles"

/**
 * List Item Component
 * 
 * A styled row component that can be used in FlatList, SectionList, or by itself.
 * Features customizable icons, text, separators, and touch interactions.
 */
export const ListItem = forwardRef<View, ListItemProps>(function ListItem(
  props: ListItemProps,
  ref,
) {
  const {
    children,
    height = 56,
    LeftComponent,
    leftIcon,
    leftIconColor,
    RightComponent,
    rightIcon,
    rightIconColor,
    text,
    TextProps,
    tx,
    txOptions,
    ...TouchableOpacityProps
  } = props

  const { Wrapper } = useListItemState(props)
  const { $textStyles, $containerStyles, $touchableStyles } = useListItemStyles(props)

  return (
    <View ref={ref} style={$containerStyles}>
      <Wrapper {...TouchableOpacityProps} style={$touchableStyles}>
        <ListItemAction
          side="left"
          size={height}
          icon={leftIcon}
          iconColor={leftIconColor}
          Component={LeftComponent}
        />

        <Text {...TextProps} tx={tx} text={text} txOptions={txOptions} style={$textStyles}>
          {children}
        </Text>

        <ListItemAction
          side="right"
          size={height}
          icon={rightIcon}
          iconColor={rightIconColor}
          Component={RightComponent}
        />
      </Wrapper>
    </View>
  )
})
