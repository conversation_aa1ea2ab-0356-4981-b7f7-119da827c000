/**
 * List Item Action Component
 * 
 * Displays action icons or custom components on the left or right side of list items
 */

import React from "react"
import { useAppTheme } from "@/utils/useAppTheme"
import { Icon } from "../../Icon"
import type { ListItemActionProps } from "./types"
import { $iconContainer, $iconContainerLeft, $iconContainerRight } from "./styles"

/**
 * List Item Action Component
 * 
 * Renders an icon or custom component for list item actions.
 * Supports left and right positioning with proper spacing.
 */
export function ListItemAction(props: ListItemActionProps) {
  const { icon, Component, iconColor, size, side } = props
  const { themed } = useAppTheme()

  const $iconContainerStyles = [
    $iconContainer,
    side === "left" && $iconContainerLeft,
    side === "right" && $iconContainerRight,
    { height: size },
  ]

  if (Component) return Component

  if (icon !== undefined) {
    return (
      <Icon
        size={24}
        icon={icon}
        color={iconColor}
        containerStyle={themed($iconContainerStyles)}
      />
    )
  }

  return null
}
