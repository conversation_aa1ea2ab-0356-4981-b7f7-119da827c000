/**
 * useListItemStyles Hook
 * 
 * Custom hook that calculates list item styles based on props and theme
 */

import { useAppTheme } from "@/utils/useAppTheme"
import { $styles } from "../../../theme"
import type { ListItemProps } from "./types"
import { $separatorTop, $separatorBottom, $textStyle, $touchableStyle } from "./styles"

export function useListItemStyles(props: ListItemProps) {
  const {
    topSeparator,
    bottomSeparator,
    height = 56,
    style,
    textStyle: $textStyleOverride,
    containerStyle: $containerStyleOverride,
    TextProps,
  } = props

  const { themed } = useAppTheme()

  const $textStyles = [
    $textStyle,
    $textStyleOverride,
    TextProps?.style
  ]

  const $containerStyles = [
    topSeparator && $separatorTop,
    bottomSeparator && $separatorBottom,
    $containerStyleOverride,
  ]

  const $touchableStyles = [
    $styles.row,
    $touchableStyle,
    { minHeight: height },
    style
  ]

  return {
    $textStyles: themed($textStyles),
    $containerStyles: themed($containerStyles),
    $touchableStyles,
  }
}
