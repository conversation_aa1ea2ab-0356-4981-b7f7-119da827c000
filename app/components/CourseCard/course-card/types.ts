/**
 * Types for CourseCard components
 */

import type { StyleProp, ViewStyle } from "react-native"

export interface CourseCardProps {
  /**
   * Course title
   */
  title: string
  /**
   * Course description - will use light weight (300) per guideline
   */
  description?: string
  /**
   * Course instructor name
   */
  instructor?: string
  /**
   * Number of participants/students
   */
  participants?: number
  /**
   * Number of lessons in the course
   */
  lessons?: number
  /**
   * Course duration (e.g., "2h 30m")
   */
  duration?: string
  /**
   * Progress percentage (0-100)
   */
  progress?: number
  /**
   * Course thumbnail URL
   */
  thumbnail?: string
  /**
   * Callback when card is pressed
   */
  onPress?: () => void
  /**
   * Card variant
   */
  variant?: "grid" | "list"
  /**
   * Optional style override
   */
  style?: StyleProp<ViewStyle>
}

export interface CourseCardHeaderProps {
  title: string
  description?: string
  variant: "grid" | "list"
}

export interface CourseCardMetadataProps {
  instructor?: string
  participants?: number
  lessons?: number
  duration?: string
}

export interface CourseCardProgressProps {
  progress: number
}
