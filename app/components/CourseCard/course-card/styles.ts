/**
 * Styles for CourseCard components
 */

import type { ViewStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

// Container styles
export const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  backgroundColor: "#FFFFFF", // MANDATORY: White background
  borderRadius: 12, // Large border radius per guideline
  padding: spacing.md, // 16px internal padding - STANDARD
  marginBottom: spacing.md, // 16px margin between cards
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 3, // Android shadow
})

export const $listVariant: ThemedStyle<ViewStyle> = () => ({
  flexDirection: "row",
  alignItems: "center",
})

// Header styles
export const $header: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm, // 8px to metadata
})

export const $title: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xxs, // 4px to description
})

export const $description: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginTop: spacing.xxs, // 4px from title
})

// Metadata styles
export const $metadata: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm, // 8px to progress
})

export const $instructor: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xxs, // 4px to stats
})

export const $stats: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  gap: spacing.sm, // 8px between stat items
  flexWrap: "wrap",
})

export const $statText: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.textDim, // Secondary text color
})

// Progress styles
export const $progressContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  gap: spacing.xs, // 8px between bar and text
})

export const $progressBackground: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  height: 6, // Progress bar height per guideline
  backgroundColor: "#F4F2F1", // Light gray background
  borderRadius: 3, // Half of height for rounded ends
  overflow: "hidden",
})

export const $progressFill: ThemedStyle<ViewStyle> = () => ({
  height: "100%",
  backgroundColor: "#FFBB50", // Orange progress - MANDATORY
  borderRadius: 3,
})

export const $progressText: ThemedStyle<ViewStyle> = ({ colors }) => ({
  color: colors.textDim,
  minWidth: 32, // Consistent width for percentage
  textAlign: "right",
})
