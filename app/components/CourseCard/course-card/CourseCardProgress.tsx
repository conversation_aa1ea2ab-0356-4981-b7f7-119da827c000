/**
 * Course Card Progress Component
 * 
 * Displays the progress bar with percentage
 */

import React from "react"
import { View } from "react-native"
import { Text } from "../../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { CourseCardProgressProps } from "./types"
import { $progressContainer, $progressBackground, $progressFill, $progressText } from "./styles"

export const CourseCardProgress: React.FC<CourseCardProgressProps> = ({
  progress,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($progressContainer)}>
      <View style={themed($progressBackground)}>
        <View
          style={[
            themed($progressFill),
            { width: `${Math.max(0, Math.min(100, progress))}%` },
          ]}
        />
      </View>
      <Text
        text={`${Math.round(progress)}%`}
        preset="description"
        size="xxs"
        style={themed($progressText)}
      />
    </View>
  )
}
