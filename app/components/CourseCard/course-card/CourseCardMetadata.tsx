/**
 * Course Card Metadata Component
 * 
 * Displays instructor name and course statistics
 */

import React from "react"
import { View } from "react-native"
import { Text } from "../../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { CourseCardMetadataProps } from "./types"
import { $metadata, $instructor, $stats, $statText } from "./styles"

export const CourseCardMetadata: React.FC<CourseCardMetadataProps> = ({
  instructor,
  participants,
  lessons,
  duration,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($metadata)}>
      {instructor && (
        <Text
          text={`By ${instructor}`}
          preset="description" // Light weight (300)
          size="xxs"
          style={themed($instructor)}
        />
      )}
      
      <View style={themed($stats)}>
        {participants && (
          <Text
            text={`${participants} students`}
            preset="description"
            size="xxs"
            style={themed($statText)}
          />
        )}
        {lessons && (
          <Text
            text={`${lessons} lessons`}
            preset="description"
            size="xxs"
            style={themed($statText)}
          />
        )}
        {duration && (
          <Text
            text={duration}
            preset="description"
            size="xxs"
            style={themed($statText)}
          />
        )}
      </View>
    </View>
  )
}
