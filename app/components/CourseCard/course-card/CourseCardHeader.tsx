/**
 * Course Card Header Component
 * 
 * Displays the course title and description
 */

import React from "react"
import { View } from "react-native"
import { Text } from "../../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { CourseCardHeaderProps } from "./types"
import { $header, $title, $description } from "./styles"

export const CourseCardHeader: React.FC<CourseCardHeaderProps> = ({
  title,
  description,
  variant,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($header)}>
      <Text
        text={title}
        preset="default"
        weight="semiBold"
        size="md"
        style={themed($title)}
        numberOfLines={2}
      />
      {description && (
        <Text
          text={description}
          preset="description" // MANDATORY: Uses light weight (300)
          size="xs"
          style={themed($description)}
          numberOfLines={variant === "grid" ? 2 : 1}
        />
      )}
    </View>
  )
}
