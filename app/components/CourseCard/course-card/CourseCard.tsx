/**
 * Course Card Component - Refactored
 * 
 * Main course card component using smaller, focused sub-components.
 * Reduced from 239 lines to ~50 lines for better maintainability.
 */

import React from "react"
import { Pressable } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { CourseCardProps } from "./types"
import { CourseCardHeader } from "./CourseCardHeader"
import { CourseCardMetadata } from "./CourseCardMetadata"
import { CourseCardProgress } from "./CourseCardProgress"
import { $container, $listVariant } from "./styles"

/**
 * Course Card Component
 * 
 * Displays course information with progress and statistics according to LMS UI guidelines.
 * Features white background, 12px border radius, and orange progress indicators.
 */
export function CourseCard(props: CourseCardProps) {
  const {
    title,
    description,
    instructor,
    participants,
    lessons,
    duration,
    progress,
    onPress,
    variant = "grid",
    style: $styleOverride,
  } = props

  const { themed } = useAppTheme()

  return (
    <Pressable
      style={[themed($container), variant === "list" && themed($listVariant), $styleOverride]}
      onPress={onPress}
      accessibilityRole="button"
      accessibilityLabel={`Course: ${title}`}
    >
      <CourseCardHeader
        title={title}
        description={description}
        variant={variant}
      />

      <CourseCardMetadata
        instructor={instructor}
        participants={participants}
        lessons={lessons}
        duration={duration}
      />

      {progress !== undefined && (
        <CourseCardProgress progress={progress} />
      )}
    </Pressable>
  )
}
