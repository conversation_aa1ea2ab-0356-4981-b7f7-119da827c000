/**
 * Screen Components
 *
 * Refactored from a single 305-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the screen module
export {
  Screen,
  ScreenContainer,
  ScreenWithoutScrolling,
  ScreenWithScrolling,
  useAutoPreset,
  type ScreenProps,
  type BaseScreenProps,
  type FixedScreenProps,
  type ScrollScreenProps,
  type AutoScreenProps,
  type ScreenPreset,
  type ScreenContainerProps,
  type UseAutoPresetResult,
  DEFAULT_BOTTOM_OFFSET,
  isNonScrolling,
} from "./screen"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
