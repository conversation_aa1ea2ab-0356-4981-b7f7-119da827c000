/**
 * Empty State Components
 *
 * Refactored from a single 249-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the empty-state module
export {
  EmptyState,
  EmptyStateImage,
  EmptyStateHeading,
  EmptyStateContent,
  EmptyStateButton,
  useEmptyStatePresets,
  type EmptyStateProps,
  type EmptyStatePresetItem,
  type EmptyStateImageProps,
  type EmptyStateHeadingProps,
  type EmptyStateContentProps,
  type EmptyStateButtonProps,
} from "./empty-state"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
