/**
 * User Profile Header Component
 * 
 * Displays user information with avatar and greeting according to LMS UI guidelines.
 * Features 48px circular avatar, light weight (300) greeting text, and space-between layout.
 */

import React from "react"
import { View, Pressable, Image } from "react-native"
import { Text } from "../Text"
import { Icon } from "../Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { UserProfileHeaderProps } from "./types"

export const UserProfileHeader: React.FC<UserProfileHeaderProps> = ({
  name,
  username,
  avatar,
  greeting = "Let's Learning to smart",
  showSearch = true,
  onAvatarPress,
  onSearchPress,
  style: $styleOverride,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={[themed($container), $styleOverride]}>
      {/* Left Section - Avatar and User Info */}
      <View style={themed($leftSection)}>
        <Pressable
          onPress={onAvatarPress}
          style={themed($avatarContainer)}
          accessibilityRole="button"
          accessibilityLabel="User profile"
        >
          {avatar ? (
            <Image
              source={{ uri: avatar }}
              style={themed($avatar)}
              resizeMode="cover"
            />
          ) : (
            <View style={themed($avatarFallback)}>
              <Icon
                icon="user"
                size={24}
                color="#978F8A" // Gray for fallback
              />
            </View>
          )}
        </Pressable>

        <View style={themed($userInfo)}>
          {greeting && (
            <Text
              text={greeting}
              preset="description" // MANDATORY: Light weight (300) for greeting
              size="xs"
              style={themed($greeting)}
            />
          )}
          <Text
            text={name}
            preset="default"
            weight="semiBold" // SemiBold weight for prominence
            size="md"
            style={themed($name)}
          />
          {username && (
            <Text
              text={`@${username}`}
              preset="description" // Light weight (300)
              size="xxs"
              style={themed($username)}
            />
          )}
        </View>
      </View>

      {/* Right Section - Actions */}
      <View style={themed($rightSection)}>
        {showSearch && (
          <Pressable
            onPress={onSearchPress}
            style={themed($searchButton)}
            accessibilityRole="button"
            accessibilityLabel="Search"
          >
            <Icon
              icon="search"
              size={24}
              color="#132339" // Dark blue for primary actions
            />
          </Pressable>
        )}
      </View>
    </View>
  )
}

// Themed styles following LMS UI guidelines
const $container: ThemedStyle = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingHorizontal: spacing.md, // 16px horizontal
  paddingVertical: spacing.sm, // 8px vertical
  backgroundColor: "#FFFFFF", // White background
})

const $leftSection: ThemedStyle = ({ spacing }) => ({
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
  gap: spacing.sm, // 8px between avatar and text
})

const $avatarContainer: ThemedStyle = () => ({
  // Avatar container for touch handling
})

const $avatar: ThemedStyle = () => ({
  width: 48, // Medium avatar size per guideline
  height: 48,
  borderRadius: 24,
})

const $avatarFallback: ThemedStyle = () => ({
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: "#F4F2F1", // Light gray background
  justifyContent: "center",
  alignItems: "center",
})

const $userInfo: ThemedStyle = () => ({
  flex: 1,
})

const $greeting: ThemedStyle = ({ colors }) => ({
  color: colors.textDim, // Secondary text color
})

const $name: ThemedStyle = ({ spacing }) => ({
  marginTop: spacing.xxxs, // 2px from greeting
})

const $username: ThemedStyle = ({ colors, spacing }) => ({
  color: colors.textDim,
  marginTop: spacing.xxxs, // 2px from name
})

const $rightSection: ThemedStyle = () => ({
  // Right section for actions
})

const $searchButton: ThemedStyle = () => ({
  width: 44, // Minimum touch target
  height: 44,
  justifyContent: "center",
  alignItems: "center",
  borderRadius: 22,
})
