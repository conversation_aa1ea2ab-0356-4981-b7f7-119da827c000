/**
 * Student Avatar Group Component
 * 
 * Displays multiple student avatars in an overlapping row with count indicator
 */

import React from "react"
import { View } from "react-native"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { StudentAvatarGroupProps } from "./types"
import { UserAvatar } from "./UserAvatar"

export const StudentAvatarGroup: React.FC<StudentAvatarGroupProps> = ({
  students,
  maxVisible = 4,
  size = 32,
  onPress,
  style: $styleOverride,
}) => {
  const { themed } = useAppTheme()

  const visibleStudents = students.slice(0, maxVisible)
  const remainingCount = Math.max(0, students.length - maxVisible)

  return (
    <View style={[themed($groupContainer), $styleOverride]}>
      {visibleStudents.map((student, index) => (
        <View
          key={student.id}
          style={[
            themed($groupAvatar),
            {
              marginLeft: index > 0 ? -8 : 0, // -8px overlap for stacked effect
              zIndex: visibleStudents.length - index, // Higher z-index for front avatars
            },
          ]}
        >
          <UserAvatar
            source={student.avatar}
            size="small"
            onPress={() => onPress?.(student.id)}
          />
        </View>
      ))}
      
      {remainingCount > 0 && (
        <View
          style={[
            themed($countIndicator),
            {
              width: size,
              height: size,
              borderRadius: size / 2,
              marginLeft: -8,
            },
          ]}
        >
          <Text
            text={`+${remainingCount}`}
            preset="default"
            weight="medium"
            size="xxs"
            style={themed($countText)}
          />
        </View>
      )}
    </View>
  )
}

// Themed styles following design guidelines
const $groupContainer: ThemedStyle = () => ({
  flexDirection: "row",
  alignItems: "center",
})

const $groupAvatar: ThemedStyle = () => ({
  // Individual avatar in group
})

const $countIndicator: ThemedStyle = () => ({
  backgroundColor: "#132339", // Dark blue background
  justifyContent: "center",
  alignItems: "center",
  borderWidth: 2,
  borderColor: "#FFFFFF", // White border
})

const $countText: ThemedStyle = () => ({
  color: "#FFFFFF", // White text on dark background
})
