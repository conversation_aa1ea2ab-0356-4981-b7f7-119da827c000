/**
 * User Avatar Component
 * 
 * Reusable avatar component with different sizes and fallback support
 */

import React from "react"
import { View, Pressable, Image } from "react-native"
import { Icon } from "../Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"
import type { UserAvatarProps } from "./types"

export const UserAvatar: React.FC<UserAvatarProps> = ({
  source,
  size = "medium",
  borderColor = "#FFFFFF",
  borderWidth = 2,
  onPress,
  style: $styleOverride,
}) => {
  const { themed } = useAppTheme()

  const avatarSize = {
    small: 32,   // Small avatars for groups, lists
    medium: 48,  // Medium avatars for headers, cards
    large: 64,   // Large avatars for profiles
  }[size]

  const Component = onPress ? Pressable : View

  return (
    <Component
      onPress={onPress}
      style={[
        themed($avatarBase),
        {
          width: avatarSize,
          height: avatarSize,
          borderRadius: avatarSize / 2,
          borderColor,
          borderWidth,
        },
        $styleOverride,
      ]}
      accessibilityRole={onPress ? "button" : undefined}
      accessibilityLabel={onPress ? "User avatar" : undefined}
    >
      {source ? (
        <Image
          source={{ uri: source }}
          style={{
            width: avatarSize - borderWidth * 2,
            height: avatarSize - borderWidth * 2,
            borderRadius: (avatarSize - borderWidth * 2) / 2,
          }}
          resizeMode="cover"
        />
      ) : (
        <Icon
          icon="user"
          size={avatarSize * 0.5}
          color="#978F8A"
        />
      )}
    </Component>
  )
}

// Themed styles following design guidelines
const $avatarBase: ThemedStyle = () => ({
  justifyContent: "center",
  alignItems: "center",
  backgroundColor: "#F4F2F1", // Light gray fallback
})
