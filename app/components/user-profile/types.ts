/**
 * Types for User Profile Components
 */

import type { StyleProp, ViewStyle } from "react-native"

export interface UserProfileHeaderProps {
  /**
   * User's display name
   */
  name: string
  /**
   * Optional username
   */
  username?: string
  /**
   * User avatar URL
   */
  avatar?: string
  /**
   * Greeting message
   */
  greeting?: string
  /**
   * Whether to show search button
   */
  showSearch?: boolean
  /**
   * Callback when avatar is pressed
   */
  onAvatarPress?: () => void
  /**
   * Callback when search button is pressed
   */
  onSearchPress?: () => void
  /**
   * Optional style override
   */
  style?: StyleProp<ViewStyle>
}

export interface UserAvatarProps {
  /**
   * Avatar image URL
   */
  source?: string
  /**
   * Avatar size
   */
  size?: "small" | "medium" | "large"
  /**
   * Border color
   */
  borderColor?: string
  /**
   * Border width
   */
  borderWidth?: number
  /**
   * Callback when avatar is pressed
   */
  onPress?: () => void
  /**
   * Optional style override
   */
  style?: StyleProp<ViewStyle>
}

export interface StudentAvatarGroupProps {
  /**
   * Array of student data
   */
  students: Array<{
    id: string
    name: string
    avatar?: string
  }>
  /**
   * Maximum number of avatars to show before "+X"
   */
  maxVisible?: number
  /**
   * Size of individual avatars
   */
  size?: number
  /**
   * Callback when avatar group is pressed
   */
  onPress?: (studentId: string) => void
  /**
   * Optional style override
   */
  style?: StyleProp<ViewStyle>
}
