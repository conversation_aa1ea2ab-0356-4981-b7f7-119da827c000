/**
 * Card Content Component
 * 
 * Displays the content section of a card
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { CardContentProps } from "./types"
import { $contentPresets } from "./presets"

export const CardContent: React.FC<CardContentProps> = ({
  content,
  contentTx,
  contentTxOptions,
  contentStyle: $contentStyleOverride,
  ContentTextProps,
  ContentComponent,
  preset = "default",
  hasHeading = false,
  hasFooter = false,
}) => {
  const {
    themed,
    theme: { spacing },
  } = useAppTheme()

  const isContentPresent = !!(ContentComponent || content || contentTx)

  if (!isContentPresent) return null

  const $contentStyle = [
    themed($contentPresets[preset]),
    hasHeading && { marginTop: spacing.xxxs },
    hasFooter && { marginBottom: spacing.xxxs },
    $contentStyleOverride,
    ContentTextProps?.style,
  ]

  if (ContentComponent) return ContentComponent

  return (
    <Text
      preset="description"
      text={content}
      tx={contentTx}
      txOptions={contentTxOptions}
      {...ContentTextProps}
      style={$contentStyle}
    />
  )
}
