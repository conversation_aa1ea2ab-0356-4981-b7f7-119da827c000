/**
 * Card Wrapper Component
 * 
 * Provides the main container and layout logic for card components
 */

import React, { ComponentType } from "react"
import { TouchableOpacity, View, ViewProps, TouchableOpacityProps, StyleProp, ViewStyle } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { CardWrapperProps } from "./types"
import { $containerPresets, $alignmentWrapper, $alignmentWrapperFlexOptions } from "./presets"

export const CardWrapper: React.FC<CardWrapperProps> = ({
  preset = "default",
  verticalAlignment = "top",
  LeftComponent,
  RightComponent,
  style: $containerStyleOverride,
  children,
  ...WrapperProps
}) => {
  const {
    themed,
    theme: { spacing },
  } = useAppTheme()

  const isPressable = !!WrapperProps.onPress
  const Wrapper = (isPressable ? TouchableOpacity : View) as ComponentType<
    TouchableOpacityProps | ViewProps
  >

  const $containerStyle: StyleProp<ViewStyle> = [
    themed($containerPresets[preset]),
    $containerStyleOverride,
  ]

  const $alignmentWrapperStyle = [
    $alignmentWrapper,
    { justifyContent: $alignmentWrapperFlexOptions[verticalAlignment] },
    LeftComponent && { marginStart: spacing.md },
    RightComponent && { marginEnd: spacing.md },
  ]

  return (
    <Wrapper
      style={$containerStyle}
      activeOpacity={0.8}
      accessibilityRole={isPressable ? "button" : undefined}
      {...WrapperProps}
    >
      {LeftComponent}

      <View style={$alignmentWrapperStyle}>
        {children}
      </View>

      {RightComponent}
    </Wrapper>
  )
}
