/**
 * Card Header Component
 * 
 * Displays the heading section of a card
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { CardHeaderProps } from "./types"
import { $headingPresets } from "./presets"

export const CardHeader: React.FC<CardHeaderProps> = ({
  heading,
  headingTx,
  headingTxOptions,
  headingStyle: $headingStyleOverride,
  HeadingTextProps,
  HeadingComponent,
  preset = "default",
  hasContentOrFooter = false,
}) => {
  const {
    themed,
    theme: { spacing },
  } = useAppTheme()

  const isHeadingPresent = !!(HeadingComponent || heading || headingTx)

  if (!isHeadingPresent) return null

  const $headingStyle = [
    themed($headingPresets[preset]),
    hasContentOrFooter && { marginBottom: spacing.xxxs },
    $headingStyleOverride,
    HeadingTextProps?.style,
  ]

  if (HeadingComponent) return HeadingComponent

  return (
    <Text
      weight="bold"
      text={heading}
      tx={headingTx}
      txOptions={headingTxOptions}
      {...HeadingTextProps}
      style={$headingStyle}
    />
  )
}
