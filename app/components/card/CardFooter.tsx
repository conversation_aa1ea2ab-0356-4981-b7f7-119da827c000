/**
 * Card Footer Component
 * 
 * Displays the footer section of a card
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { CardFooterProps } from "./types"
import { $footerPresets } from "./presets"

export const CardFooter: React.FC<CardFooterProps> = ({
  footer,
  footerTx,
  footerTxOptions,
  footerStyle: $footerStyleOverride,
  FooterTextProps,
  FooterComponent,
  preset = "default",
  hasHeadingOrContent = false,
}) => {
  const {
    themed,
    theme: { spacing },
  } = useAppTheme()

  const isFooterPresent = !!(FooterComponent || footer || footerTx)

  if (!isFooterPresent) return null

  const $footerStyle = [
    themed($footerPresets[preset]),
    hasHeadingOrContent && { marginTop: spacing.xxxs },
    $footerStyleOverride,
    FooterTextProps?.style,
  ]

  if (FooterComponent) return FooterComponent

  return (
    <Text
      preset="description"
      size="xs"
      text={footer}
      tx={footerTx}
      txOptions={footerTxOptions}
      {...FooterTextProps}
      style={$footerStyle}
    />
  )
}
