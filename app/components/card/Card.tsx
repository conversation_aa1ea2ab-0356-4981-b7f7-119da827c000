/**
 * Card Component - Refactored
 * 
 * Main card component using smaller, focused sub-components.
 * Reduced from 314 lines to ~80 lines for better maintainability.
 */

import React, { Fragment, View } from "react"
import type { CardProps } from "./types"
import { CardWrapper } from "./CardWrapper"
import { <PERSON>Header } from "./CardHeader"
import { CardContent } from "./CardContent"
import { <PERSON>Footer } from "./CardFooter"

/**
 * Cards are useful for displaying related information in a contained way.
 * If a ListItem displays content horizontally, a Card can be used to display content vertically.
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Card/}
 * @param {CardProps} props - The props for the `Card` component.
 * @returns {JSX.Element} The rendered `Card` component.
 */
export const Card: React.FC<CardProps> = ({
  content,
  contentTx,
  contentTxOptions,
  footer,
  footerTx,
  footerTxOptions,
  heading,
  headingTx,
  headingTxOptions,
  ContentComponent,
  HeadingComponent,
  FooterComponent,
  LeftComponent,
  RightComponent,
  verticalAlignment = "top",
  style: $containerStyleOverride,
  contentStyle: $contentStyleOverride,
  headingStyle: $headingStyleOverride,
  footerStyle: $footerStyleOverride,
  ContentTextProps,
  HeadingTextProps,
  FooterTextProps,
  preset = "default",
  ...WrapperProps
}) => {
  const isHeadingPresent = !!(HeadingComponent || heading || headingTx)
  const isContentPresent = !!(ContentComponent || content || contentTx)
  const isFooterPresent = !!(FooterComponent || footer || footerTx)

  const HeaderContentWrapper = verticalAlignment === "force-footer-bottom" ? View : Fragment

  return (
    <CardWrapper
      preset={preset}
      verticalAlignment={verticalAlignment}
      LeftComponent={LeftComponent}
      RightComponent={RightComponent}
      style={$containerStyleOverride}
      {...WrapperProps}
    >
      <HeaderContentWrapper>
        <CardHeader
          heading={heading}
          headingTx={headingTx}
          headingTxOptions={headingTxOptions}
          headingStyle={$headingStyleOverride}
          HeadingTextProps={HeadingTextProps}
          HeadingComponent={HeadingComponent}
          preset={preset}
          hasContentOrFooter={isFooterPresent || isContentPresent}
        />

        <CardContent
          content={content}
          contentTx={contentTx}
          contentTxOptions={contentTxOptions}
          contentStyle={$contentStyleOverride}
          ContentTextProps={ContentTextProps}
          ContentComponent={ContentComponent}
          preset={preset}
          hasHeading={isHeadingPresent}
          hasFooter={isFooterPresent}
        />
      </HeaderContentWrapper>

      <CardFooter
        footer={footer}
        footerTx={footerTx}
        footerTxOptions={footerTxOptions}
        footerStyle={$footerStyleOverride}
        FooterTextProps={FooterTextProps}
        FooterComponent={FooterComponent}
        preset={preset}
        hasHeadingOrContent={isHeadingPresent || isContentPresent}
      />
    </CardWrapper>
  )
}
