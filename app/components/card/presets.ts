/**
 * Presets and styles for Card components
 */

import type { TextStyle, ViewStyle } from "react-native"
import type { ThemedStyle, ThemedStyleArray } from "@/theme"
import { $styles } from "../../theme"
import type { CardPresets, CardVerticalAlignment } from "./types"

// Base container style
export const $containerBase: ThemedStyle<ViewStyle> = (theme) => ({
  borderRadius: theme.spacing.md,
  paddingHorizontal: theme.spacing.md, // 16px - MANDATORY horizontal padding
  paddingVertical: theme.spacing.md,   // 16px - Standard vertical padding
  borderWidth: 1,
  shadowColor: theme.colors.palette.neutral800,
  shadowOffset: { width: 0, height: 12 },
  shadowOpacity: 0.08,
  shadowRadius: 12.81,
  elevation: 16,
  minHeight: 96,
})

// Alignment wrapper
export const $alignmentWrapper: ViewStyle = {
  flex: 1,
  alignSelf: "stretch",
}

// Alignment options mapping
export const $alignmentWrapperFlexOptions: Record<CardVerticalAlignment, string> = {
  "top": "flex-start",
  "center": "center",
  "space-between": "space-between",
  "force-footer-bottom": "space-between",
} as const

// Container presets
export const $containerPresets: Record<CardPresets, ThemedStyleArray<ViewStyle>> = {
  default: [
    $styles.row,
    $containerBase,
    (theme) => ({
      backgroundColor: theme.colors.palette.neutral100,
      borderColor: theme.colors.palette.neutral300,
    }),
  ],
  reversed: [
    $styles.row,
    $containerBase,
    (theme) => ({
      backgroundColor: theme.colors.palette.neutral800,
      borderColor: theme.colors.palette.neutral500,
    }),
  ],
}

// Heading presets
export const $headingPresets: Record<CardPresets, ThemedStyleArray<TextStyle>> = {
  default: [],
  reversed: [(theme) => ({ color: theme.colors.palette.neutral100 })],
}

// Content presets
export const $contentPresets: Record<CardPresets, ThemedStyleArray<TextStyle>> = {
  default: [],
  reversed: [(theme) => ({ color: theme.colors.palette.neutral100 })],
}

// Footer presets
export const $footerPresets: Record<CardPresets, ThemedStyleArray<TextStyle>> = {
  default: [],
  reversed: [(theme) => ({ color: theme.colors.palette.neutral100 })],
}
