/**
 * Header Component - Refactored
 * 
 * Main header component using smaller, focused sub-components.
 * Reduced from 330 lines to ~80 lines for better maintainability.
 */

import React from "react"
import type { HeaderProps } from "./types"
import { HeaderContainer } from "./HeaderContainer"
import { HeaderTitle } from "./HeaderTitle"
import { HeaderAction } from "./HeaderAction"

/**
 * Header that appears on many screens. Will hold navigation buttons and screen title.
 * The Header is meant to be used with the `screenOptions.header` option on navigators, routes, or screen components via `navigation.setOptions({ header })`.
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Header/}
 * @param {HeaderProps} props - The props for the `Header` component.
 * @returns {JSX.Element} The rendered `Header` component.
 */
export const Header: React.FC<HeaderProps> = ({
  backgroundColor,
  LeftActionComponent,
  leftIcon,
  leftIconColor,
  leftText,
  leftTx,
  leftTxOptions,
  onLeftPress,
  onRightPress,
  RightActionComponent,
  rightIcon,
  rightIconColor,
  rightText,
  rightTx,
  rightTxOptions,
  safeAreaEdges = ["top"],
  title,
  titleMode = "center",
  titleTx,
  titleTxOptions,
  titleContainerStyle,
  style,
  titleStyle,
  containerStyle,
}) => {
  return (
    <HeaderContainer
      backgroundColor={backgroundColor}
      safeAreaEdges={safeAreaEdges}
      containerStyle={containerStyle}
      style={style}
    >
      {/* Left Action */}
      <HeaderAction
        tx={leftTx}
        text={leftText}
        icon={leftIcon}
        iconColor={leftIconColor}
        onPress={onLeftPress}
        txOptions={leftTxOptions}
        backgroundColor={backgroundColor}
        ActionComponent={LeftActionComponent}
      />

      {/* Title */}
      <HeaderTitle
        title={title}
        titleTx={titleTx}
        titleTxOptions={titleTxOptions}
        titleMode={titleMode}
        titleStyle={titleStyle}
        titleContainerStyle={titleContainerStyle}
      />

      {/* Right Action */}
      <HeaderAction
        tx={rightTx}
        text={rightText}
        icon={rightIcon}
        iconColor={rightIconColor}
        onPress={onRightPress}
        txOptions={rightTxOptions}
        backgroundColor={backgroundColor}
        ActionComponent={RightActionComponent}
      />
    </HeaderContainer>
  )
}
