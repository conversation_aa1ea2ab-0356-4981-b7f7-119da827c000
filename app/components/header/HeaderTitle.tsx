/**
 * Header Title Component
 * 
 * Displays the header title with proper layout modes
 */

import React from "react"
import { View } from "react-native"
import { translate } from "@/i18n"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { HeaderTitleProps } from "./types"
import {
  $title,
  $titleWrapperPointerEvents,
  $titleWrapperCenter,
  $titleWrapperFlex,
} from "./styles"

export const HeaderTitle: React.FC<HeaderTitleProps> = ({
  title,
  titleTx,
  titleTxOptions,
  titleMode = "center",
  titleStyle: $titleStyleOverride,
  titleContainerStyle: $titleContainerStyleOverride,
}) => {
  const { themed } = useAppTheme()

  const titleContent = titleTx ? translate(titleTx, titleTxOptions) : title

  if (!titleContent) return null

  return (
    <View
      style={[
        $titleWrapperPointerEvents,
        titleMode === "center" && themed($titleWrapperCenter),
        titleMode === "flex" && $titleWrapperFlex,
        $titleContainerStyleOverride,
      ]}
    >
      <Text
        weight="medium"
        size="md"
        text={titleContent}
        style={[$title, $titleStyleOverride]}
      />
    </View>
  )
}
