/**
 * Header Container Component
 * 
 * Provides safe area wrapper and background for header content
 */

import React from "react"
import { View } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { useSafeAreaInsetsStyle } from "../../utils/useSafeAreaInsetsStyle"
import { $styles } from "../../theme"
import type { HeaderContainerProps } from "./types"
import { $container, $wrapper } from "./styles"

export const HeaderContainer: React.FC<HeaderContainerProps> = ({
  backgroundColor,
  safeAreaEdges = ["top"],
  containerStyle: $containerStyleOverride,
  style: $styleOverride,
  children,
}) => {
  const {
    theme: { colors },
  } = useAppTheme()

  const $containerInsets = useSafeAreaInsetsStyle(safeAreaEdges)
  const finalBackgroundColor = backgroundColor || colors.background

  return (
    <View style={[$container, $containerInsets, { backgroundColor: finalBackgroundColor }, $containerStyleOverride]}>
      <View style={[$styles.row, $wrapper, $styleOverride]}>
        {children}
      </View>
    </View>
  )
}
