/**
 * Header Action Component
 * 
 * Displays action buttons (text, icon, or custom component) in header
 */

import React from "react"
import { TouchableOpacity, View } from "react-native"
import { isRTL, translate } from "@/i18n"
import { Text } from "../Text"
import { PressableIcon } from "../Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { HeaderActionProps } from "./types"
import {
  $actionTextContainer,
  $actionText,
  $actionIconContainer,
  $actionFillerContainer,
} from "./styles"

export const HeaderAction: React.FC<HeaderActionProps> = ({
  backgroundColor,
  icon,
  text,
  tx,
  txOptions,
  onPress,
  ActionComponent,
  iconColor,
}) => {
  const { themed } = useAppTheme()

  const content = tx ? translate(tx, txOptions) : text

  // Custom component takes priority
  if (ActionComponent) return ActionComponent

  // Text action
  if (content) {
    return (
      <TouchableOpacity
        style={themed([$actionTextContainer, { backgroundColor }])}
        onPress={onPress}
        disabled={!onPress}
        activeOpacity={0.8}
      >
        <Text weight="medium" size="md" text={content} style={themed($actionText)} />
      </TouchableOpacity>
    )
  }

  // Icon action
  if (icon) {
    return (
      <PressableIcon
        size={24}
        icon={icon}
        color={iconColor}
        onPress={onPress}
        containerStyle={themed([$actionIconContainer, { backgroundColor }])}
        style={isRTL ? { transform: [{ rotate: "180deg" }] } : {}}
      />
    )
  }

  // Filler for spacing
  return <View style={[$actionFillerContainer, { backgroundColor }]} />
}
