/**
 * Field Label Component
 *
 * Reusable label component for toggle fields
 */

import React from "react"
import { Text } from "../../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { FieldLabelProps } from "./types"
import { $label, $labelRight, $labelLeft } from "./styles"

export function FieldLabel<T>(props: FieldLabelProps<T>) {
  const {
    status,
    label,
    labelTx,
    labelTxOptions,
    LabelTextProps,
    labelPosition = "right",
    labelStyle: $labelStyleOverride,
  } = props

  const {
    theme: { colors },
    themed,
  } = useAppTheme()

  if (!label && !labelTx && !LabelTextProps?.children) return null

  const $labelStyles = [
    $label,
    labelPosition === "right" && themed($labelRight),
    labelPosition === "left" && themed($labelLeft),
    status === "error" && { color: colors.error },
    $labelStyleOverride,
    LabelTextProps?.style,
  ]

  return (
    <Text
      preset="formLabel"
      text={label}
      tx={labelTx}
      txOptions={labelTxOptions}
      {...LabelTextProps}
      style={$labelStyles}
    />
  )
}
