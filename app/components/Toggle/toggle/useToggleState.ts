/**
 * useToggleState Hook
 * 
 * Custom hook that manages toggle state and interactions
 */

import { useMemo } from "react"
import { ComponentType, GestureResponderEvent } from "react-native"
import { TouchableOpacity, TouchableOpacityProps, View, ViewProps } from "react-native"
import type { ToggleProps } from "./types"

export function useToggleState<T>(props: ToggleProps<T>) {
  const {
    editable = true,
    status,
    value,
    onPress,
    onValueChange,
  } = props

  const disabled = editable === false || status === "disabled" || props.disabled

  const Wrapper = useMemo(
    () => (disabled ? View : TouchableOpacity) as ComponentType<TouchableOpacityProps | ViewProps>,
    [disabled],
  )

  /**
   * Handle press event
   */
  function handlePress(e: GestureResponderEvent) {
    if (disabled) return
    onValueChange?.(!value)
    onPress?.(e)
  }

  return {
    disabled,
    Wrapper,
    handlePress,
  }
}
