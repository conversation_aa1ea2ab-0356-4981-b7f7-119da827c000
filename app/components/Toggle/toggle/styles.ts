/**
 * Styles for Toggle components
 */

import type { ViewStyle, TextStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

// Input wrapper styles
export const $inputWrapper: ViewStyle = {
  alignItems: "center",
}

// Base input outer style
export const $inputOuterBase: ViewStyle = {
  height: 24,
  width: 24,
  borderWidth: 2,
  alignItems: "center",
  overflow: "hidden",
  flexGrow: 0,
  flexShrink: 0,
  justifyContent: "space-between",
  flexDirection: "row",
}

// Helper text styles
export const $helper: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginTop: spacing.xs,
})

// Label styles
export const $label: TextStyle = {
  flex: 1,
}

export const $labelRight: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginStart: spacing.md,
})

export const $labelLeft: ThemedStyle<TextStyle> = ({ spacing }) => ({
  marginEnd: spacing.md,
})
