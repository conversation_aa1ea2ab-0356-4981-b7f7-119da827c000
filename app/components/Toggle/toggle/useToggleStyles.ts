/**
 * useToggleStyles Hook
 * 
 * Custom hook that calculates toggle styles based on props and theme
 */

import { useAppTheme } from "@/utils/useAppTheme"
import { $styles } from "../../../theme"
import type { ToggleProps } from "./types"
import { $inputWrapper, $helper } from "./styles"

export function useToggleStyles<T>(props: ToggleProps<T>) {
  const {
    status,
    containerStyle: $containerStyleOverride,
    inputWrapperStyle: $inputWrapperStyleOverride,
    HelperTextProps,
  } = props

  const {
    theme: { colors },
    themed,
  } = useAppTheme()

  const $containerStyles = [$containerStyleOverride]
  
  const $inputWrapperStyles = [
    $styles.row,
    $inputWrapper,
    $inputWrapperStyleOverride
  ]
  
  const $helperStyles = themed([
    $helper,
    status === "error" && { color: colors.error },
    HelperTextProps?.style,
  ])

  return {
    $containerStyles,
    $inputWrapperStyles,
    $helperStyles,
  }
}
