/**
 * Toggle Component - Refactored
 *
 * Main toggle component using smaller, focused sub-components.
 * Reduced from 284 lines to ~60 lines for better maintainability.
 */

import React from "react"
import { View } from "react-native"
import { Text } from "../../Text"
import type { ToggleProps, BaseToggleInputProps } from "./types"
import { FieldLabel } from "./FieldLabel"
import { useToggleState } from "./useToggleState"
import { useToggleStyles } from "./useToggleStyles"
import { $inputOuterBase } from "./styles"

/**
 * Toggle Component
 *
 * Renders a boolean input.
 * This is a controlled component that requires an onValueChange callback that updates the value prop
 * in order for the component to reflect user actions. If the value prop is not updated, the component
 * will continue to render the supplied value prop instead of the expected result of any user actions.
 */
export function Toggle<T>(props: ToggleProps<T>) {
  const {
    helper,
    helperTx,
    helperTxOptions,
    HelperTextProps,
    label,
    labelTx,
    labelTxOptions,
    labelPosition,
    labelStyle,
    LabelTextProps,
    ToggleInput,
    inputOuterStyle: $inputOuterStyleOverride,
    inputInnerStyle: $inputInnerStyleOverride,
    inputDetailStyle: $inputDetailStyleOverride,
    value = false,
    status,
  } = props

  const { disabled, Wrapper, handlePress } = useToggleState(props)
  const { $containerStyles, $inputWrapperStyles, $helperStyles } = useToggleStyles(props)

  const $inputOuterStyles = [$inputOuterBase, $inputOuterStyleOverride]

  return (
    <Wrapper
      activeOpacity={1}
      accessibilityRole="switch"
      accessibilityState={{ checked: value, disabled }}
      {...props}
      style={$containerStyles}
      onPress={handlePress}
    >
      <View style={$inputWrapperStyles}>
        {labelPosition === "left" && (
          <FieldLabel
            status={status}
            label={label}
            labelTx={labelTx}
            labelTxOptions={labelTxOptions}
            LabelTextProps={LabelTextProps}
            labelPosition={labelPosition}
            labelStyle={labelStyle}
          />
        )}

        <ToggleInput
          on={value}
          status={status}
          disabled={disabled}
          outerStyle={$inputOuterStyles}
          innerStyle={$inputInnerStyleOverride}
          detailStyle={$inputDetailStyleOverride}
        />

        {labelPosition === "right" && (
          <FieldLabel
            status={status}
            label={label}
            labelTx={labelTx}
            labelTxOptions={labelTxOptions}
            LabelTextProps={LabelTextProps}
            labelPosition={labelPosition}
            labelStyle={labelStyle}
          />
        )}
      </View>

      {!!(helper || helperTx) && (
        <Text
          preset="formHelper"
          text={helper}
          tx={helperTx}
          txOptions={helperTxOptions}
          {...HelperTextProps}
          style={$helperStyles}
        />
      )}
    </Wrapper>
  )
}
