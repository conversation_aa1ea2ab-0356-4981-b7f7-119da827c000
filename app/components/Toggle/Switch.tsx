/**
 * Switch Components
 *
 * Refactored from a single 318-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the switch module
export {
  Switch,
  SwitchInput,
  SwitchAccessibilityLabel,
  useSwitchAnimations,
  useSwitchColors,
  type SwitchToggleProps,
  type SwitchInputProps,
  type SwitchAccessibilityLabelProps,
  type SwitchAnimationsResult,
  type SwitchColorsResult,
} from "./switch"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
