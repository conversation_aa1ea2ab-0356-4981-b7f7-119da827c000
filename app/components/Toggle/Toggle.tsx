/**
 * Toggle Components
 *
 * Refactored from a single 284-line file into smaller, focused components.
 * Much cleaner and more maintainable architecture.
 */

// Re-export all components from the toggle module
export {
  Toggle,
  FieldLabel,
  useToggleState,
  useToggleStyles,
  type ToggleProps,
  type BaseToggleInputProps,
  type FieldLabelProps,
  $inputOuterBase,
} from "./toggle"

// This file now serves as a clean re-export module
// All implementation has been moved to smaller, focused components
