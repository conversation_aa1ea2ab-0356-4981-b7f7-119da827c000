/**
 * Styles for Switch components
 */

import type { ImageStyle, StyleProp, TextStyle, ViewStyle } from "react-native"
import type { ThemedStyle } from "@/theme"
import { $inputOuterBase } from "../Toggle"
import type { SwitchToggleProps } from "./types"

export const $inputOuter: StyleProp<ViewStyle> = [
  $inputOuterBase,
  {
    height: 32,
    width: 56,
    borderRadius: 16,
    overflow: "visible", // Override hidden overflow to prevent corner clipping
  },
]

export const $switchInner: ThemedStyle<ViewStyle> = ({ colors }) => ({
  borderColor: colors.transparent,
  position: "absolute",
  paddingStart: 4,
  paddingEnd: 4,
  borderRadius: 16, // Match outer border radius for smooth appearance
  overflow: "visible", // Ensure knob is not clipped
})

export const $switchDetail: SwitchToggleProps["inputDetailStyle"] = {
  borderRadius: 12,
  position: "absolute",
  width: 24,
  height: 24,
}

export const $switchIndicator: ViewStyle = {
  position: "absolute",
  right: 8, // Position on the right side for off state
  top: "50%",
  marginTop: -4, // Center vertically (half of height)
  width: 8,
  height: 8,
  borderRadius: 4, // Perfect circle
}

export const $switchAccessibility: TextStyle = {
  width: "40%",
  justifyContent: "center",
  alignItems: "center",
}

export const $switchAccessibilityIcon: ImageStyle = {
  width: 14,
  height: 14,
  resizeMode: "contain",
}

export const $switchAccessibilityLine: ViewStyle = {
  width: 2,
  height: 12,
}

export const $switchAccessibilityCircle: ViewStyle = {
  borderWidth: 2,
  width: 12,
  height: 12,
  borderRadius: 6,
}
