/**
 * useSwitchColors Hook
 * 
 * Custom hook that calculates switch colors based on state
 */

import { useAppTheme } from "@/utils/useAppTheme"
import type { SwitchInputProps, SwitchColorsResult } from "./types"

export function useSwitchColors(props: SwitchInputProps): SwitchColorsResult {
  const { on, status, disabled, innerStyle: $innerStyleOverride, detailStyle: $detailStyleOverride } = props
  const { theme: { colors } } = useAppTheme()

  const offBackgroundColor = [
    disabled && colors.palette.neutral400,
    status === "error" && colors.errorBackground,
    colors.palette.neutral100, // Very light, bright background (#F9FAFB)
  ].filter(Boolean)[0]

  const onBackgroundColor = [
    disabled && colors.transparent,
    status === "error" && colors.errorBackground,
    colors.tint, // Use primary interactive color (#23408B)
  ].filter(Boolean)[0]

  const knobBackgroundColor = (function () {
    if (on) {
      return [
        $detailStyleOverride?.backgroundColor,
        status === "error" && colors.error,
        disabled && colors.palette.neutral600,
        colors.palette.neutral100, // Bright white knob for on state
      ].filter(Boolean)[0]
    } else {
      return [
        $innerStyleOverride?.backgroundColor,
        disabled && colors.palette.neutral600,
        status === "error" && colors.error,
        colors.palette.neutral100, // Bright white knob for off state too
      ].filter(Boolean)[0]
    }
  })()

  const outerBorderColor = [
    disabled && colors.palette.neutral300,
    status === "error" && colors.error,
    !on && colors.palette.neutral200, // Light border for off state visibility
    colors.transparent, // No border for on state (blue background is visible)
  ].filter(Boolean)[0]

  const indicatorColor = [
    disabled && colors.palette.neutral400,
    status === "error" && colors.error,
    colors.tint, // Blue indicator for interactivity
  ].filter(Boolean)[0]

  return {
    offBackgroundColor,
    onBackgroundColor,
    knobBackgroundColor,
    outerBorderColor,
    indicatorColor,
  }
}
