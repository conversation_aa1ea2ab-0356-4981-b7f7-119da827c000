/**
 * useSwitchAnimations Hook
 * 
 * Custom hook that handles switch animations and positioning
 */

import { useEffect, useMemo, useRef } from "react"
import { Animated, Platform } from "react-native"
import { isRTL } from "@/i18n"
import { $styles } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import type { SwitchInputProps, SwitchAnimationsResult } from "./types"
import { $switchInner } from "./styles"

export function useSwitchAnimations(props: SwitchInputProps): SwitchAnimationsResult {
  const { on, innerStyle: $innerStyleOverride, detailStyle: $detailStyleOverride } = props
  const { themed } = useAppTheme()

  const animate = useRef(new Animated.Value(on ? 1 : 0))
  const opacity = useRef(new Animated.Value(0))

  useEffect(() => {
    Animated.timing(animate.current, {
      toValue: on ? 1 : 0,
      duration: 300,
      useNativeDriver: true, // Enable native driver for smoother animations
    }).start()
  }, [on])

  useEffect(() => {
    Animated.timing(opacity.current, {
      toValue: on ? 1 : 0,
      duration: 300,
      useNativeDriver: true,
    }).start()
  }, [on])

  const knobSizeFallback = 2
  const knobWidth = [$detailStyleOverride?.width, 24, knobSizeFallback].find(
    (v) => typeof v === "number",
  )
  const knobHeight = [$detailStyleOverride?.height, 24, knobSizeFallback].find(
    (v) => typeof v === "number",
  )

  const rtlAdjustment = isRTL ? -1 : 1
  const $themedSwitchInner = useMemo(() => themed([$styles.toggleInner, $switchInner]), [themed])

  const offsetLeft = ($innerStyleOverride?.paddingStart ||
    $innerStyleOverride?.paddingLeft ||
    $themedSwitchInner?.paddingStart ||
    $themedSwitchInner?.paddingLeft ||
    0) as number

  const offsetRight = ($innerStyleOverride?.paddingEnd ||
    $innerStyleOverride?.paddingRight ||
    $themedSwitchInner?.paddingEnd ||
    $themedSwitchInner?.paddingRight ||
    0) as number

  const outputRange =
    Platform.OS === "web"
      ? isRTL
        ? [+(knobWidth || 0) + offsetRight, offsetLeft]
        : [offsetLeft, +(knobWidth || 0) + offsetRight]
      : [rtlAdjustment * offsetLeft, rtlAdjustment * (+(knobWidth || 0) + offsetRight)]

  const animatedSwitchKnob = animate.current.interpolate({
    inputRange: [0, 1],
    outputRange,
  })

  return {
    animate,
    opacity,
    animatedSwitchKnob,
  }
}
