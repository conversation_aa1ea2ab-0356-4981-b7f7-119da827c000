/**
 * Switch Component - Refactored
 * 
 * Main switch component using smaller, focused sub-components.
 * Reduced from 318 lines to ~20 lines for better maintainability.
 */

import React, { useCallback } from "react"
import { Toggle } from "../Toggle"
import type { SwitchToggleProps, SwitchInputProps } from "./types"
import { SwitchInput } from "./SwitchInput"

/**
 * @param {SwitchToggleProps} props - The props for the `Switch` component.
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Switch}
 * @returns {JSX.Element} The rendered `Switch` component.
 */
export const Switch: React.FC<SwitchToggleProps> = ({ accessibilityMode, ...rest }) => {
  const switchInput = useCallback(
    (toggleProps: SwitchInputProps) => (
      <SwitchInput {...toggleProps} accessibilityMode={accessibilityMode} />
    ),
    [accessibilityMode],
  )

  return <Toggle accessibilityRole="switch" {...rest} ToggleInput={switchInput} />
}
