/**
 * Switch Accessibility Label Component
 * 
 * Displays accessibility indicators for switch on/off states
 */

import React from "react"
import { Image, StyleProp, View, ViewStyle } from "react-native"
import { iconRegistry } from "@/components/Icon"
import { useAppTheme } from "@/utils/useAppTheme"
import type { SwitchAccessibilityLabelProps } from "./types"
import {
  $switchAccessibility,
  $switchAccessibilityIcon,
  $switchAccessibilityLine,
  $switchAccessibilityCircle,
} from "./styles"

export const SwitchAccessibilityLabel: React.FC<SwitchAccessibilityLabelProps> = ({
  on,
  disabled,
  status,
  accessibilityMode,
  role,
  innerStyle,
  detailStyle,
}) => {
  const { theme: { colors } } = useAppTheme()

  if (!accessibilityMode) return null

  const shouldLabelBeVisible = (on && role === "on") || (!on && role === "off")

  const $switchAccessibilityStyle: StyleProp<ViewStyle> = [
    $switchAccessibility,
    role === "off" && { end: "5%" },
    role === "on" && { left: "5%" },
  ]

  const color = (function () {
    if (disabled) return colors.palette.neutral600
    if (status === "error") return colors.error
    if (!on) return innerStyle?.backgroundColor || colors.tint // Use primary interactive color
    return detailStyle?.backgroundColor || colors.palette.neutral100
  })()

  return (
    <View style={$switchAccessibilityStyle}>
      {accessibilityMode === "text" && shouldLabelBeVisible && (
        <View
          style={[
            role === "on" && $switchAccessibilityLine,
            role === "on" && { backgroundColor: color },
            role === "off" && $switchAccessibilityCircle,
            role === "off" && { borderColor: color },
          ]}
        />
      )}

      {accessibilityMode === "icon" && shouldLabelBeVisible && (
        <Image
          style={[$switchAccessibilityIcon, { tintColor: color }]}
          source={role === "off" ? iconRegistry.hidden : iconRegistry.view}
        />
      )}
    </View>
  )
}
