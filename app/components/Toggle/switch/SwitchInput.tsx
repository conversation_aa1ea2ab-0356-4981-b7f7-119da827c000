/**
 * Switch Input Component
 * 
 * Core switch input with animations and visual states
 */

import React, { useMemo } from "react"
import { Animated, View } from "react-native"
import { $styles } from "@/theme"
import { useAppTheme } from "@/utils/useAppTheme"
import type { SwitchInputProps } from "./types"
import { SwitchAccessibilityLabel } from "./SwitchAccessibilityLabel"
import { useSwitchAnimations } from "./useSwitchAnimations"
import { useSwitchColors } from "./useSwitchColors"
import { $inputOuter, $switchInner, $switchDetail, $switchIndicator } from "./styles"

export const SwitchInput: React.FC<SwitchInputProps> = (props) => {
  const {
    on,
    disabled,
    outerStyle: $outerStyleOverride,
    innerStyle: $innerStyleOverride,
    detailStyle: $detailStyleOverride,
  } = props

  const { themed } = useAppTheme()

  const { animate, opacity, animatedSwitchKnob } = useSwitchAnimations(props)
  const {
    offBackgroundColor,
    onBackgroundColor,
    knobBackgroundColor,
    outerBorderColor,
    indicatorColor,
  } = useSwitchColors(props)

  const knobSizeFallback = 2
  const knobWidth = [$detailStyleOverride?.width, $switchDetail?.width, knobSizeFallback].find(
    (v) => typeof v === "number",
  )
  const knobHeight = [$detailStyleOverride?.height, $switchDetail?.height, knobSizeFallback].find(
    (v) => typeof v === "number",
  )

  const $themedSwitchInner = useMemo(() => themed([$styles.toggleInner, $switchInner]), [themed])

  return (
    <View
      style={[
        $inputOuter,
        { backgroundColor: offBackgroundColor, borderColor: outerBorderColor, borderWidth: 1 },
        $outerStyleOverride,
      ]}
    >
      <Animated.View
        style={[
          $themedSwitchInner,
          { backgroundColor: onBackgroundColor },
          $innerStyleOverride,
          { opacity: opacity.current },
        ]}
      />

      {/* Interactive indicator circle for off state */}
      {!on && (
        <View
          style={[
            $switchIndicator,
            { backgroundColor: indicatorColor },
            { opacity: disabled ? 0.3 : 0.6 }, // Subtle but visible
          ]}
        />
      )}

      <SwitchAccessibilityLabel {...props} role="on" />
      <SwitchAccessibilityLabel {...props} role="off" />

      <Animated.View
        style={[
          $switchDetail,
          $detailStyleOverride,
          { transform: [{ translateX: animatedSwitchKnob }] },
          { width: knobWidth, height: knobHeight },
          { backgroundColor: knobBackgroundColor },
        ]}
      />
    </View>
  )
}
