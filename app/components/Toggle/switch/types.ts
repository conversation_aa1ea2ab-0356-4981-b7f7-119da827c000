/**
 * Types for Switch components
 */

import type { Animated, ViewStyle } from "react-native"
import type { BaseToggleInputProps, ToggleProps } from "../Toggle"

export interface SwitchToggleProps extends Omit<ToggleProps<SwitchInputProps>, "ToggleInput"> {
  /**
   * Switch-only prop that adds a text/icon label for on/off states.
   */
  accessibilityMode?: "text" | "icon"
  /**
   * Optional style prop that affects the knob View.
   * Note: `width` and `height` rules should be points (numbers), not percentages.
   */
  inputDetailStyle?: Omit<ViewStyle, "width" | "height"> & { width?: number; height?: number }
}

export interface SwitchInputProps extends BaseToggleInputProps<SwitchToggleProps> {
  accessibilityMode?: SwitchToggleProps["accessibilityMode"]
}

export interface SwitchAccessibilityLabelProps extends SwitchInputProps {
  role: "on" | "off"
}

export interface SwitchAnimationsResult {
  animate: React.MutableRefObject<Animated.Value>
  opacity: React.MutableRefObject<Animated.Value>
  animatedSwitchKnob: Animated.AnimatedAddition
}

export interface SwitchColorsResult {
  offBackgroundColor: string
  onBackgroundColor: string
  knobBackgroundColor: string
  outerBorderColor: string
  indicatorColor: string
}
