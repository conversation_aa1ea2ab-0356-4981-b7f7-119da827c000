/**
 * OTP Helper Component
 * 
 * Displays helper text for OTP input
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { OTPHelperProps } from "./types"
import { $helper, $errorHelper } from "./styles"

export const OTPHelper: React.FC<OTPHelperProps> = ({
  helper,
  status,
}) => {
  const { themed } = useAppTheme()

  if (!helper) return null

  const isError = status === "error"

  return (
    <Text
      text={helper}
      preset="formHelper"
      style={[themed($helper), isError && themed($errorHelper)]}
    />
  )
}
