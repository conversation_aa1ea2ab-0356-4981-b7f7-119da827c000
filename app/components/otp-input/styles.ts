/**
 * Styles for OTPInput components
 */

import type { TextStyle, ViewStyle } from "react-native"
import type { ThemedStyle } from "@/theme"

// Container styles
export const $container: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

export const $inputContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  gap: spacing.sm,
})

export const $inputWrapper: ThemedStyle<ViewStyle> = () => ({
  flex: 1,
  maxWidth: 50,
})

// Label styles
export const $label: ThemedStyle<TextStyle> = ({ colors, spacing, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 14,
  color: colors.text,
  marginBottom: spacing.sm,
  textAlign: "center",
})

// Input styles
export const $input: ThemedStyle<TextStyle> = ({ colors, spacing, typography }) => ({
  fontFamily: typography.primary.bold,
  fontSize: 24,
  color: colors.text,
  backgroundColor: colors.palette.neutral100,
  borderWidth: 2,
  borderColor: colors.border,
  borderRadius: 8,
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.xs,
  textAlign: "center",
  minHeight: 56,
})

export const $focusedInput: ThemedStyle<TextStyle> = ({ colors }) => ({
  borderColor: colors.tint,
  backgroundColor: colors.palette.neutral50,
})

// Helper styles
export const $helper: ThemedStyle<TextStyle> = ({ colors, spacing, typography }) => ({
  fontFamily: typography.primary.normal,
  fontSize: 12,
  color: colors.textDim,
  marginTop: spacing.sm,
  textAlign: "center",
})

// Error states
export const $errorLabel: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.error,
})

export const $errorInput: ThemedStyle<TextStyle> = ({ colors }) => ({
  borderColor: colors.error,
  backgroundColor: colors.errorBackground || colors.palette.angry100,
})

export const $errorHelper: ThemedStyle<TextStyle> = ({ colors }) => ({
  color: colors.error,
})

// Disabled states
export const $disabledInput: ThemedStyle<TextStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral200,
  borderColor: colors.palette.neutral300,
  color: colors.textDim,
})
