/**
 * Types for OTPInput components
 */

import type { TextInput, TextStyle, ViewStyle } from "react-native"

export interface OTPInputProps {
  /**
   * Number of OTP digits (default: 6)
   */
  length?: number

  /**
   * Current OTP value
   */
  value?: string

  /**
   * Called when OTP changes
   */
  onChangeText?: (otp: string) => void

  /**
   * Called when OTP is complete
   */
  onComplete?: (otp: string) => void

  /**
   * Label text
   */
  label?: string

  /**
   * Helper text
   */
  helper?: string

  /**
   * Error status
   */
  status?: "error" | "disabled"

  /**
   * Auto focus first input
   */
  autoFocus?: boolean

  /**
   * Container style override
   */
  containerStyle?: ViewStyle

  /**
   * Individual input style override
   */
  inputStyle?: TextStyle
}

export interface OTPLabelProps {
  label?: string
  status?: "error" | "disabled"
}

export interface OTPHelperProps {
  helper?: string
  status?: "error" | "disabled"
}

export interface OTPInputFieldProps {
  index: number
  value: string
  onChangeText: (text: string, index: number) => void
  onKeyPress: (key: string, index: number) => void
  onFocus: (index: number) => void
  onBlur: () => void
  onPress: (index: number) => void
  isFocused: boolean
  status?: "error" | "disabled"
  inputStyle?: TextStyle
  inputRef: (ref: TextInput | null) => void
}

export interface OTPInputContainerProps {
  length: number
  value: string
  onChangeText: (text: string, index: number) => void
  onKeyPress: (key: string, index: number) => void
  onFocus: (index: number) => void
  onBlur: () => void
  onInputPress: (index: number) => void
  focusedIndex: number
  status?: "error" | "disabled"
  inputStyle?: TextStyle
  inputRefs: React.MutableRefObject<(TextInput | null)[]>
}

export interface UseOTPLogicResult {
  inputRefs: React.MutableRefObject<(TextInput | null)[]>
  focusedIndex: number
  handleChangeText: (text: string, index: number) => void
  handleKeyPress: (key: string, index: number) => void
  handleFocus: (index: number) => void
  handleBlur: () => void
  handleInputPress: (index: number) => void
}
