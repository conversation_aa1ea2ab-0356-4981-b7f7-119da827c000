/**
 * OTP Input Component - Refactored
 * 
 * Main OTP input component using smaller, focused sub-components.
 * Reduced from 290 lines to ~80 lines for better maintainability.
 */

import React from "react"
import { View } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { OTPInputProps } from "./types"
import { OTPLabel } from "./OTPLabel"
import { OTPInputContainer } from "./OTPInputContainer"
import { OTPHelper } from "./OTPHelper"
import { useOTPLogic } from "./useOTPLogic"
import { $container } from "./styles"

export const OTPInput: React.FC<OTPInputProps> = (props) => {
  const {
    length = 6,
    value = "",
    onChangeText,
    onComplete,
    label,
    helper,
    status,
    autoFocus = true,
    containerStyle: $containerStyleOverride,
    inputStyle: $inputStyleOverride,
  } = props

  const { themed } = useAppTheme()

  const {
    inputRefs,
    focusedIndex,
    handleChangeText,
    handleKeyPress,
    handleFocus,
    handleBlur,
    handleInputPress,
  } = useOTPLogic(length, value, onChangeText, onComplete, autoFocus)

  const $containerStyles = [
    themed($container),
    $containerStyleOverride,
  ]

  return (
    <View style={$containerStyles}>
      <OTPLabel
        label={label}
        status={status}
      />

      <OTPInputContainer
        length={length}
        value={value}
        onChangeText={handleChangeText}
        onKeyPress={handleKeyPress}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onInputPress={handleInputPress}
        focusedIndex={focusedIndex}
        status={status}
        inputStyle={$inputStyleOverride}
        inputRefs={inputRefs}
      />

      <OTPHelper
        helper={helper}
        status={status}
      />
    </View>
  )
}
