/**
 * useOTPLogic Hook
 * 
 * Custom hook that handles OTP input logic and state management
 */

import { useRef, useEffect, useState, useCallback } from "react"
import type { TextInput } from "react-native"
import type { UseOTPLogicResult } from "./types"

export function useOTPLogic(
  length: number,
  value: string,
  onChangeText?: (otp: string) => void,
  onComplete?: (otp: string) => void,
  autoFocus: boolean = true
): UseOTPLogicResult {
  const inputRefs = useRef<(TextInput | null)[]>([])
  const [focusedIndex, setFocusedIndex] = useState(autoFocus ? 0 : -1)

  // Initialize input refs array
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length)
  }, [length])

  // Auto focus first input
  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus()
    }
  }, [autoFocus])

  // Check if OTP is complete
  useEffect(() => {
    if (value.length === length && onComplete) {
      onComplete(value)
    }
  }, [value, length, onComplete])

  const handleChangeText = useCallback((text: string, index: number) => {
    // Only allow digits
    const digit = text.replace(/\D/g, "").slice(-1)
    
    // Update the OTP value
    const newOTP = value.split("")
    newOTP[index] = digit
    const updatedOTP = newOTP.join("").slice(0, length)
    
    onChangeText?.(updatedOTP)

    // Move to next input if digit entered
    if (digit && index < length - 1) {
      inputRefs.current[index + 1]?.focus()
      setFocusedIndex(index + 1)
    }
  }, [value, length, onChangeText])

  const handleKeyPress = useCallback((key: string, index: number) => {
    if (key === "Backspace") {
      // If current input is empty, move to previous input
      if (!value[index] && index > 0) {
        inputRefs.current[index - 1]?.focus()
        setFocusedIndex(index - 1)
        
        // Clear the previous input
        const newOTP = value.split("")
        newOTP[index - 1] = ""
        onChangeText?.(newOTP.join(""))
      } else {
        // Clear current input
        const newOTP = value.split("")
        newOTP[index] = ""
        onChangeText?.(newOTP.join(""))
      }
    }
  }, [value, onChangeText])

  const handleFocus = useCallback((index: number) => {
    setFocusedIndex(index)
  }, [])

  const handleBlur = useCallback(() => {
    setFocusedIndex(-1)
  }, [])

  const handleInputPress = useCallback((index: number) => {
    inputRefs.current[index]?.focus()
  }, [])

  return {
    inputRefs,
    focusedIndex,
    handleChangeText,
    handleKeyPress,
    handleFocus,
    handleBlur,
    handleInputPress,
  }
}
