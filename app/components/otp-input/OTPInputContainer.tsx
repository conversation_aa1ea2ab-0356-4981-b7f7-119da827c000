/**
 * OTP Input Container Component
 * 
 * Container that renders all OTP input fields
 */

import React from "react"
import { View } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { OTPInputContainerProps } from "./types"
import { OTPInputField } from "./OTPInputField"
import { $inputContainer } from "./styles"

export const OTPInputContainer: React.FC<OTPInputContainerProps> = ({
  length,
  value,
  onChangeText,
  onKeyPress,
  onFocus,
  onBlur,
  onInputPress,
  focusedIndex,
  status,
  inputStyle,
  inputRefs,
}) => {
  const { themed } = useAppTheme()

  return (
    <View style={themed($inputContainer)}>
      {Array.from({ length }, (_, index) => (
        <OTPInputField
          key={index}
          index={index}
          value={value[index] || ""}
          onChangeText={onChangeText}
          onKeyPress={onKeyPress}
          onFocus={onFocus}
          onBlur={onBlur}
          onPress={onInputPress}
          isFocused={focusedIndex === index}
          status={status}
          inputStyle={inputStyle}
          inputRef={(ref) => (inputRefs.current[index] = ref)}
        />
      ))}
    </View>
  )
}
