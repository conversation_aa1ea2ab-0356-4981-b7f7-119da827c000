/**
 * OTP Input Field Component
 * 
 * Individual input field for OTP digits
 */

import React from "react"
import { TextInput, Pressable } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { OTPInputFieldProps } from "./types"
import { $inputWrapper, $input, $focusedInput, $errorInput, $disabledInput } from "./styles"

export const OTPInputField: React.FC<OTPInputFieldProps> = ({
  index,
  value,
  onChangeText,
  onKeyPress,
  onFocus,
  onBlur,
  onPress,
  isFocused,
  status,
  inputStyle: $inputStyleOverride,
  inputRef,
}) => {
  const { themed } = useAppTheme()

  const isError = status === "error"
  const isDisabled = status === "disabled"

  const $inputStyles = [
    themed($input),
    isFocused && themed($focusedInput),
    isError && themed($errorInput),
    isDisabled && themed($disabledInput),
    $inputStyleOverride,
  ]

  return (
    <Pressable
      style={themed($inputWrapper)}
      onPress={() => onPress(index)}
    >
      <TextInput
        ref={inputRef}
        style={$inputStyles}
        value={value}
        onChangeText={(text) => onChangeText(text, index)}
        onKeyPress={({ nativeEvent }) => onKeyPress(nativeEvent.key, index)}
        onFocus={() => onFocus(index)}
        onBlur={onBlur}
        keyboardType="number-pad"
        maxLength={1}
        selectTextOnFocus
        editable={!isDisabled}
        textAlign="center"
      />
    </Pressable>
  )
}
