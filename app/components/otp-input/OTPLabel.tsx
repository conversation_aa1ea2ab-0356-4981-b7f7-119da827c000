/**
 * OTP Label Component
 * 
 * Displays the label for OTP input
 */

import React from "react"
import { Text } from "../Text"
import { useAppTheme } from "@/utils/useAppTheme"
import type { OTPLabelProps } from "./types"
import { $label, $errorLabel } from "./styles"

export const OTPLabel: React.FC<OTPLabelProps> = ({
  label,
  status,
}) => {
  const { themed } = useAppTheme()

  if (!label) return null

  const isError = status === "error"

  return (
    <Text
      text={label}
      preset="formLabel"
      style={[themed($label), isError && themed($errorLabel)]}
    />
  )
}
