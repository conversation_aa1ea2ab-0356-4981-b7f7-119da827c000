import React, { useEffect, useRef } from "react"
import { View, ViewStyle, Animated, Easing } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import type { ThemedStyle } from "@/theme"

interface SkeletonLoaderProps {
  width?: number | string
  height?: number
  borderRadius?: number
  style?: ViewStyle
}

/**
 * SkeletonLoader - Animated loading placeholder
 * 
 * Features:
 * - Smooth shimmer animation
 * - Customizable dimensions and border radius
 * - Follows app design guidelines
 */
export function SkeletonLoader({
  width = "100%",
  height = 20,
  borderRadius = 8,
  style: $styleOverride,
}: SkeletonLoaderProps) {
  const { themed } = useAppTheme()
  const shimmerAnimation = useRef(new Animated.Value(0)).current

  useEffect(() => {
    const startShimmer = () => {
      Animated.loop(
        Animated.timing(shimmerAnimation, {
          toValue: 1,
          duration: 1500,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      ).start()
    }

    startShimmer()
  }, [shimmerAnimation])

  const shimmerTranslateX = shimmerAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: [-200, 200],
  })

  return (
    <View
      style={[
        themed($container),
        {
          width,
          height,
          borderRadius,
        },
        $styleOverride,
      ]}
    >
      <Animated.View
        style={[
          themed($shimmer),
          {
            transform: [{ translateX: shimmerTranslateX }],
          },
        ]}
      />
    </View>
  )
}

// Skeleton variants for common use cases
export function SkeletonText({ lines = 1, style }: { lines?: number; style?: ViewStyle }) {
  const { themed } = useAppTheme()
  
  return (
    <View style={[themed($textContainer), style]}>
      {Array.from({ length: lines }).map((_, index) => (
        <SkeletonLoader
          key={index}
          height={16}
          width={index === lines - 1 ? "80%" : "100%"}
          style={themed($textLine)}
        />
      ))}
    </View>
  )
}

export function SkeletonCard({ style }: { style?: ViewStyle }) {
  const { themed } = useAppTheme()
  
  return (
    <View style={[themed($cardContainer), style]}>
      <SkeletonLoader height={120} borderRadius={12} style={themed($cardImage)} />
      <View style={themed($cardContent)}>
        <SkeletonLoader height={20} width="80%" style={themed($cardTitle)} />
        <SkeletonLoader height={16} width="60%" style={themed($cardSubtitle)} />
        <View style={themed($cardFooter)}>
          <SkeletonLoader height={14} width="40%" />
          <SkeletonLoader height={14} width="30%" />
        </View>
      </View>
    </View>
  )
}

// Themed styles
const $container: ThemedStyle<ViewStyle> = ({ colors }) => ({
  backgroundColor: colors.palette.neutral200,
  overflow: "hidden",
})

const $shimmer: ThemedStyle<ViewStyle> = ({ colors }) => ({
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: colors.palette.neutral100,
  opacity: 0.6,
  width: 200,
})

const $textContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.xs,
})

const $textLine: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xs,
})

const $cardContainer: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.background,
  borderRadius: 12,
  padding: spacing.md,
  marginBottom: spacing.md,
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
})

const $cardImage: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.md,
})

const $cardContent: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  gap: spacing.sm,
})

const $cardTitle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xs,
})

const $cardSubtitle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.sm,
})

const $cardFooter: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  flexDirection: "row",
  justifyContent: "space-between",
  marginTop: spacing.sm,
})
