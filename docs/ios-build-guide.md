# iOS Build Guide - eb-lms-app

## Tổng quan

Hướng dẫn chi tiết về quá trình build iOS app cho eb-lms-app, bao gồm các lỗi phát sinh và cách khắc phục. Tài liệu này được tạo dựa trên kinh nghiệm thực tế khi gặp và giải quyết các vấn đề build iOS.

## 📋 Prerequisites

### System Requirements
- **macOS:** 12.0+ (Monterey hoặc mới hơn)
- **Xcode:** 15.0+ với iOS SDK 17.0+
- **Node.js:** 18.0+ (khuyến nghị sử dụng nvm)
- **npm/yarn:** Latest version
- **Homebrew:** Latest version

### Development Tools
```bash
# Kiểm tra versions
xcode-select --version
node --version
npm --version
brew --version
```

### Apple Developer Account
- **Free account:** <PERSON><PERSON>ể build và test trên simulator + device (7 ngày)
- **Paid account:** Cần thiết cho App Store distribution

## 🔧 Environment Setup

### 1. Cài đặt Xcode Command Line Tools
```bash
xcode-select --install
```

### 2. Cài đặt Node.js (nếu chưa có)
```bash
# Sử dụng nvm (khuyến nghị)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18

# Hoặc sử dụng Homebrew
brew install node@18
```

### 3. Cài đặt Expo CLI
```bash
npm install -g @expo/cli
```

### 4. Clone và setup project
```bash
git clone <repository-url>
cd eb-lms-app
npm install
```

## 🚨 Các vấn đề ban đầu

### 1. Lỗi CocoaPods và Ruby Version
**Triệu chứng:**
```bash
Could not find 'activesupport' (>= 5.0, < 8) among 89 total gem(s)
activesupport requires Ruby version >= 3.2.0. The current ruby version is 2.6.10.210.
```

**Nguyên nhân:** 
- Ruby system version (2.6) quá cũ cho CocoaPods hiện tại
- CocoaPods yêu cầu Ruby >= 3.1.0

### 2. Lỗi Firebase Module Conflicts
**Triệu chứng:**
```bash
The following Swift pods cannot yet be integrated as static libraries:
The Swift pod `FirebaseCoreInternal` depends upon `GoogleUtilities`, which does not define modules.
```

**Nguyên nhân:**
- Firebase Swift pods yêu cầu modular headers
- Conflict với ReactCommon module definitions

### 3. Lỗi Xcode Build Module Redefinition
**Triệu chứng:**
```bash
❌ Pods/React-RCTAppDelegate: Redefinition of module 'ReactCommon'
└─[app]/ios/Pods/Headers/Public/ReactCommon/ReactCommon.modulemap:1:8
```

**Nguyên nhân:**
- Global `use_modular_headers!` gây conflict với React Native modules
- Multiple module definitions cho ReactCommon

## 🛠️ Giải pháp từng bước

### Bước 1: Cập nhật Ruby và CocoaPods

#### 1.1 Cài đặt Ruby mới qua Homebrew
```bash
brew install ruby
```

#### 1.2 Cấu hình PATH để sử dụng Ruby mới
```bash
export PATH="/opt/homebrew/opt/ruby/bin:$PATH"
export PATH="/opt/homebrew/lib/ruby/gems/3.4.0/bin:$PATH"
```

#### 1.3 Cài đặt CocoaPods với Ruby mới
```bash
gem install cocoapods
```

#### 1.4 Xác minh cài đặt
```bash
which pod
gem environment
```

### Bước 2: Khắc phục Firebase Module Issues

#### 2.1 Chỉnh sửa Podfile
Thay vì sử dụng global `use_modular_headers!`, chỉ enable cho Firebase pods:

```ruby
# ios/Podfile
target 'Vantis' do
  use_expo_modules!
  
  # Enable modular headers for Firebase dependencies only
  pod 'GoogleUtilities', :modular_headers => true
  pod 'FirebaseCore', :modular_headers => true
  pod 'FirebaseCoreInternal', :modular_headers => true
  
  # ... rest of configuration
end
```

#### 2.2 Thêm post_install configuration
```ruby
post_install do |installer|
  react_native_post_install(
    installer,
    config[:reactNativePath],
    :mac_catalyst_enabled => false,
    :ccache_enabled => podfile_properties['apple.ccacheEnabled'] == 'true',
  )

  # Enable modular headers for specific Firebase pods
  installer.pods_project.targets.each do |target|
    if ['FirebaseCoreInternal', 'GoogleUtilities', 'FirebaseCore'].include?(target.name)
      target.build_configurations.each do |config|
        config.build_settings['DEFINES_MODULE'] = 'YES'
      end
    end
  end
  
  # ... rest of post_install
end
```

### Bước 3: Clean và Reinstall Pods

#### 3.1 Clean pods
```bash
cd ios
rm -rf Pods Podfile.lock
```

#### 3.2 Install pods với Ruby mới
```bash
export PATH="/opt/homebrew/lib/ruby/gems/3.4.0/bin:/opt/homebrew/opt/ruby/bin:$PATH"
pod install
```

### Bước 4: Cấu hình Code Signing

#### 4.1 Thêm Automatic Code Signing
Chỉnh sửa file `ios/Vantis.xcodeproj/project.pbxproj`:

```xml
CODE_SIGN_STYLE = Automatic;
DEVELOPMENT_TEAM = TCDD8WRJJ4; // Your team ID
```

#### 4.2 Kiểm tra Entitlements
Đảm bảo file `ios/Vantis/Vantis.entitlements` tồn tại và có nội dung phù hợp:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Add required entitlements here -->
</dict>
</plist>
```

### Bước 5: Build và Test

#### 4.1 Build với Expo CLI
```bash
npx expo run:ios
```

#### 4.2 Build với Xcode Command Line Tools

**Debug build cho iOS Simulator:**
```bash
xcodebuild -workspace Vantis.xcworkspace -scheme Vantis -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 16,OS=latest' build
```

**Release build cho iOS Simulator:**
```bash
xcodebuild -workspace Vantis.xcworkspace -scheme Vantis -configuration Release -destination 'platform=iOS Simulator,name=iPhone 16,OS=latest' build
```

**Build cho iOS Device:**
```bash
xcodebuild -workspace Vantis.xcworkspace -scheme Vantis -configuration Debug -sdk iphoneos build
```

#### 4.3 Archive cho Distribution
```bash
xcodebuild -workspace Vantis.xcworkspace -scheme Vantis -configuration Release -destination generic/platform=iOS archive -archivePath /tmp/Vantis.xcarchive
```

## 🎯 Kết quả cuối cùng

### ✅ Thành công
- ✅ CocoaPods installation hoàn thành
- ✅ Firebase dependencies resolved
- ✅ Debug build cho iOS Simulator
- ✅ Release build cho iOS Simulator  
- ✅ Build cho iOS Device
- ✅ Archive cho distribution
- ✅ App chạy thành công trên iOS Simulator

### ⚠️ Lưu ý
- Archive yêu cầu proper code signing setup
- Một số pods có deployment target warnings (không ảnh hưởng build)
- Test targets không có sẵn (bình thường cho React Native apps)

## ❌ Các lỗi thường gặp và cách khắc phục

### Lỗi 1: "pod: command not found"
**Nguyên nhân:** CocoaPods chưa được cài đặt hoặc PATH chưa đúng

**Khắc phục:**
```bash
# Kiểm tra PATH
echo $PATH | grep ruby

# Nếu không có, thêm vào shell profile
echo 'export PATH="/opt/homebrew/lib/ruby/gems/3.4.0/bin:/opt/homebrew/opt/ruby/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc

# Cài đặt lại CocoaPods
gem install cocoapods
```

### Lỗi 2: "The following Swift pods cannot yet be integrated as static libraries"
**Nguyên nhân:** Firebase pods yêu cầu modular headers

**Khắc phục:** Đã được giải quyết trong Bước 2.1 - chỉ enable modular headers cho Firebase pods

### Lỗi 3: "Redefinition of module 'ReactCommon'"
**Nguyên nhân:** Conflict giữa global modular headers và React Native modules

**Khắc phục:** Không sử dụng global `use_modular_headers!`, chỉ enable cho specific pods

### Lỗi 4: "Provisioning profile doesn't include the currently selected device"
**Nguyên nhân:** Archive cần provisioning profile hợp lệ

**Khắc phục:**
```bash
# Sử dụng generic iOS device
xcodebuild -workspace Vantis.xcworkspace -scheme Vantis -configuration Release -destination generic/platform=iOS archive -archivePath /tmp/Vantis.xcarchive
```

### Lỗi 5: "Build input file cannot be found"
**Nguyên nhân:** Missing files hoặc incorrect paths

**Khắc phục:**
```bash
# Clean và rebuild
cd ios
rm -rf Pods Podfile.lock
pod install

# Clean Xcode derived data
rm -rf ~/Library/Developer/Xcode/DerivedData
```

### Lỗi 6: "Multiple commands produce"
**Nguyên nhân:** Duplicate files trong build phases

**Khắc phục:**
1. Mở Xcode → Project Navigator
2. Select target → Build Phases
3. Kiểm tra duplicate entries trong "Copy Bundle Resources"
4. Remove duplicates

### Lỗi 7: "The iOS deployment target is set to X.X, but the range of supported deployment target versions is Y.Y to Z.Z"
**Nguyên nhân:** Pods có deployment target cũ

**Khắc phục:** Thêm vào Podfile post_install:
```ruby
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.1'
    end
  end
end
```

## 🔧 Troubleshooting

### Nếu gặp lỗi Ruby/CocoaPods
```bash
# Kiểm tra Ruby version
ruby --version

# Kiểm tra gem environment
gem environment

# Reinstall CocoaPods
gem uninstall cocoapods
gem install cocoapods
```

### Nếu gặp lỗi Module conflicts
```bash
# Clean derived data
rm -rf ~/Library/Developer/Xcode/DerivedData

# Clean pods
cd ios && rm -rf Pods Podfile.lock && pod install
```

### Nếu Archive thất bại
1. Mở Xcode → Project Settings → Signing & Capabilities
2. Enable "Automatically manage signing"
3. Chọn đúng Development Team
4. Hoặc sử dụng generic iOS device destination

## 📱 Chạy app

### Với Expo CLI
```bash
npx expo run:ios
```

### Với Xcode
1. Mở `ios/Vantis.xcworkspace`
2. Chọn scheme "Vantis"
3. Chọn simulator iPhone 16
4. Press ⌘+R để build và run

## 🚀 Distribution

### Archive thành công
```bash
xcodebuild -workspace Vantis.xcworkspace -scheme Vantis -configuration Release -destination generic/platform=iOS archive -archivePath /tmp/Vantis.xcarchive
```

### Upload lên App Store Connect
1. Mở Xcode → Window → Organizer
2. Chọn archive vừa tạo
3. Click "Distribute App"
4. Chọn "App Store Connect"
5. Follow the upload process

## 📋 Checklist hoàn chỉnh

### Trước khi bắt đầu
- [ ] Xcode đã được cài đặt (version 15.0+)
- [ ] Node.js đã được cài đặt (version 18+)
- [ ] Homebrew đã được cài đặt
- [ ] Apple Developer Account (cho distribution)

### Quá trình setup
- [ ] Ruby mới đã được cài đặt qua Homebrew
- [ ] PATH đã được cấu hình đúng
- [ ] CocoaPods đã được cài đặt với Ruby mới
- [ ] Podfile đã được chỉnh sửa cho Firebase
- [ ] Pods đã được install thành công

### Testing builds
- [ ] Debug build cho simulator thành công
- [ ] Release build cho simulator thành công
- [ ] Build cho device thành công
- [ ] Archive thành công
- [ ] App chạy được trên simulator

## 🔍 Kiểm tra chi tiết

### Kiểm tra Ruby và CocoaPods
```bash
# Kiểm tra Ruby version (should be 3.4+)
ruby --version

# Kiểm tra CocoaPods version
pod --version

# Kiểm tra gem environment
gem environment | grep "EXECUTABLE DIRECTORY"
```

### Kiểm tra Xcode build settings
```bash
# List available schemes
xcodebuild -workspace ios/Vantis.xcworkspace -list

# Show build settings
xcodebuild -workspace ios/Vantis.xcworkspace -scheme Vantis -showBuildSettings | grep CODE_SIGN
```

### Kiểm tra simulators
```bash
# List available simulators
xcrun simctl list devices
```

## 🐛 Debug thông thường

### Lỗi "Command not found: pod"
```bash
# Kiểm tra PATH
echo $PATH

# Thêm vào ~/.zshrc hoặc ~/.bash_profile
export PATH="/opt/homebrew/lib/ruby/gems/3.4.0/bin:/opt/homebrew/opt/ruby/bin:$PATH"

# Reload shell
source ~/.zshrc
```

### Lỗi "Module not found"
```bash
# Clean Metro cache
npx react-native start --reset-cache

# Clean Xcode derived data
rm -rf ~/Library/Developer/Xcode/DerivedData

# Reinstall node modules
rm -rf node_modules && npm install
```

### Lỗi Code Signing
1. Mở Xcode → Preferences → Accounts
2. Add Apple ID nếu chưa có
3. Download Manual Profiles
4. Project Settings → Signing & Capabilities → Enable "Automatically manage signing"

## 📊 Performance Tips

### Tăng tốc build
```bash
# Enable ccache trong Podfile
podfile_properties['apple.ccacheEnabled'] = 'true'

# Sử dụng parallel builds
xcodebuild -workspace Vantis.xcworkspace -scheme Vantis -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 16' build -parallelizeTargets
```

### Giảm build time
1. Sử dụng incremental builds
2. Tránh clean build không cần thiết
3. Sử dụng simulator thay vì device cho development
4. Enable "Build Active Architecture Only" trong Debug

## 🔄 Workflow khuyến nghị

### Development workflow
1. **Daily development:**
   ```bash
   npx expo run:ios
   ```

2. **Testing builds:**
   ```bash
   xcodebuild -workspace ios/Vantis.xcworkspace -scheme Vantis -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 16' build
   ```

3. **Release testing:**
   ```bash
   xcodebuild -workspace ios/Vantis.xcworkspace -scheme Vantis -configuration Release -destination 'platform=iOS Simulator,name=iPhone 16' build
   ```

4. **Distribution:**
   ```bash
   xcodebuild -workspace ios/Vantis.xcworkspace -scheme Vantis -configuration Release -destination generic/platform=iOS archive -archivePath ./build/Vantis.xcarchive
   ```

### CI/CD Integration
```yaml
# Example GitHub Actions workflow
- name: Install dependencies
  run: |
    brew install ruby
    export PATH="/opt/homebrew/lib/ruby/gems/3.4.0/bin:/opt/homebrew/opt/ruby/bin:$PATH"
    gem install cocoapods

- name: Install pods
  run: |
    cd ios
    pod install

- name: Build iOS
  run: |
    xcodebuild -workspace ios/Vantis.xcworkspace -scheme Vantis -configuration Release -destination generic/platform=iOS build
```

## 📚 Tài liệu tham khảo

### Official Documentation
- [React Native iOS Setup](https://reactnative.dev/docs/environment-setup)
- [Expo Development Build](https://docs.expo.dev/development/build/)
- [CocoaPods Guides](https://guides.cocoapods.org/)
- [Xcode Build Settings Reference](https://developer.apple.com/documentation/xcode/build-settings-reference)

### Troubleshooting Resources
- [React Native Troubleshooting](https://reactnative.dev/docs/troubleshooting)
- [Expo Troubleshooting](https://docs.expo.dev/troubleshooting/build-errors/)
- [Firebase iOS Setup](https://firebase.google.com/docs/ios/setup)

---

**Tác giả:** AI Assistant
**Ngày cập nhật:** 25/07/2025
**Version:** 1.0
**Tested on:** macOS Sonoma, Xcode 15.2, React Native 0.76.9
